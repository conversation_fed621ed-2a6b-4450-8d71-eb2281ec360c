<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库检查与修复 - 《大模型技术与油气应用概论》协作系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .check-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .check-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .check-title {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .check-subtitle {
            opacity: 0.9;
        }
        
        .check-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.3rem;
            color: #1f2937;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .check-item:last-child {
            border-bottom: none;
        }
        
        .check-name {
            font-weight: 500;
            color: #374151;
        }
        
        .check-status {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
        }
        
        .status-checking {
            color: #f59e0b;
        }
        
        .status-success {
            color: #10b981;
        }
        
        .status-error {
            color: #ef4444;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4338ca;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .actions {
            text-align: center;
            margin: 20px 0;
        }
        
        .log-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
            white-space: pre-wrap;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #4f46e5;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="check-container">
        <div class="check-header">
            <h1 class="check-title">
                <i class="fas fa-database"></i> 数据库检查与修复
            </h1>
            <p class="check-subtitle">检查并修复多用户协作功能所需的数据库结构</p>
        </div>
        
        <div class="check-section">
            <h2 class="section-title">
                <i class="fas fa-table"></i> 数据库表检查
            </h2>
            
            <div id="table-checks">
                <div class="check-item">
                    <span class="check-name">user_profiles 表</span>
                    <span class="check-status status-checking" id="check-user-profiles">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
                <div class="check-item">
                    <span class="check-name">project_members 表</span>
                    <span class="check-status status-checking" id="check-project-members">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
                <div class="check-item">
                    <span class="check-name">chapter_assignments 表</span>
                    <span class="check-status status-checking" id="check-chapter-assignments">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
                <div class="check-item">
                    <span class="check-name">user_invitations 表</span>
                    <span class="check-status status-checking" id="check-user-invitations">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
                <div class="check-item">
                    <span class="check-name">review_processes 表</span>
                    <span class="check-status status-checking" id="check-review-processes">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
            </div>
        </div>
        
        <div class="check-section">
            <h2 class="section-title">
                <i class="fas fa-columns"></i> 字段检查
            </h2>
            
            <div id="field-checks">
                <div class="check-item">
                    <span class="check-name">user_profiles.last_login_at</span>
                    <span class="check-status status-checking" id="check-last-login-at">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
                <div class="check-item">
                    <span class="check-name">user_profiles.is_active</span>
                    <span class="check-status status-checking" id="check-is-active">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
                <div class="check-item">
                    <span class="check-name">project_members.status</span>
                    <span class="check-status status-checking" id="check-member-status">
                        <i class="fas fa-spinner fa-spin"></i> 检查中...
                    </span>
                </div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        
        <div class="actions">
            <button class="btn btn-primary" onclick="runDatabaseCheck()">
                <i class="fas fa-search"></i> 开始检查
            </button>
            <button class="btn btn-success" onclick="fixDatabase()" id="fix-btn" style="display: none;">
                <i class="fas fa-wrench"></i> 自动修复
            </button>
            <a href="user-management.html" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回用户管理
            </a>
        </div>
        
        <div id="log-section" style="display: none;">
            <h3>检查日志</h3>
            <div class="log-output" id="log-output"></div>
        </div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script>
        class DatabaseChecker {
            constructor() {
                this.checkResults = {};
                this.totalChecks = 8;
                this.completedChecks = 0;
            }

            async runDatabaseCheck() {
                this.log('开始数据库检查...');
                this.showLogSection();
                this.resetProgress();

                // 检查表
                await this.checkTable('user_profiles');
                await this.checkTable('project_members');
                await this.checkTable('chapter_assignments');
                await this.checkTable('user_invitations');
                await this.checkTable('review_processes');

                // 检查字段
                await this.checkField('user_profiles', 'last_login_at');
                await this.checkField('user_profiles', 'is_active');
                await this.checkField('project_members', 'status');

                this.showSummary();
            }

            async checkTable(tableName) {
                try {
                    const { data, error } = await supabaseManager.supabase
                        .from(tableName)
                        .select('*')
                        .limit(1);

                    const exists = !error;
                    this.updateCheckStatus(`check-${tableName.replace('_', '-')}`, exists, exists ? '存在' : '不存在');
                    this.checkResults[tableName] = exists;
                    this.log(`表 ${tableName}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
                } catch (e) {
                    this.updateCheckStatus(`check-${tableName.replace('_', '-')}`, false, '检查失败');
                    this.checkResults[tableName] = false;
                    this.log(`表 ${tableName}: ❌ 检查失败 - ${e.message}`);
                }
                
                this.updateProgress();
            }

            async checkField(tableName, fieldName) {
                try {
                    const { data, error } = await supabaseManager.supabase
                        .from(tableName)
                        .select(fieldName)
                        .limit(1);

                    const exists = !error;
                    const checkId = fieldName === 'last_login_at' ? 'check-last-login-at' : 
                                   fieldName === 'is_active' ? 'check-is-active' : 
                                   'check-member-status';
                    
                    this.updateCheckStatus(checkId, exists, exists ? '存在' : '不存在');
                    this.checkResults[`${tableName}.${fieldName}`] = exists;
                    this.log(`字段 ${tableName}.${fieldName}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
                } catch (e) {
                    const checkId = fieldName === 'last_login_at' ? 'check-last-login-at' : 
                                   fieldName === 'is_active' ? 'check-is-active' : 
                                   'check-member-status';
                    
                    this.updateCheckStatus(checkId, false, '检查失败');
                    this.checkResults[`${tableName}.${fieldName}`] = false;
                    this.log(`字段 ${tableName}.${fieldName}: ❌ 检查失败 - ${e.message}`);
                }
                
                this.updateProgress();
            }

            updateCheckStatus(elementId, success, message) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.className = `check-status ${success ? 'status-success' : 'status-error'}`;
                    element.innerHTML = `<i class="fas fa-${success ? 'check' : 'times'}"></i> ${message}`;
                }
            }

            updateProgress() {
                this.completedChecks++;
                const progress = (this.completedChecks / this.totalChecks) * 100;
                document.getElementById('progress-fill').style.width = `${progress}%`;
            }

            resetProgress() {
                this.completedChecks = 0;
                document.getElementById('progress-fill').style.width = '0%';
            }

            showSummary() {
                const issues = Object.entries(this.checkResults).filter(([key, value]) => !value);
                
                if (issues.length === 0) {
                    this.log('\n🎉 所有检查都通过了！数据库结构正常。');
                } else {
                    this.log(`\n⚠️ 发现 ${issues.length} 个问题:`);
                    issues.forEach(([key, value]) => {
                        this.log(`  - ${key}: 缺失`);
                    });
                    
                    document.getElementById('fix-btn').style.display = 'inline-flex';
                    this.log('\n💡 点击"自动修复"按钮尝试修复这些问题。');
                }
            }

            async fixDatabase() {
                this.log('\n🔧 开始自动修复数据库...');
                
                try {
                    // 这里我们只能修复一些基本的字段问题
                    // 对于表的创建，需要管理员权限
                    
                    this.log('⚠️ 自动修复功能有限，建议手动执行以下SQL脚本:');
                    this.log('1. 在Supabase控制台的SQL编辑器中执行 database-fix.sql');
                    this.log('2. 或者联系数据库管理员执行迁移脚本');
                    this.log('3. 修复完成后重新运行检查');
                    
                } catch (error) {
                    this.log(`❌ 修复失败: ${error.message}`);
                }
            }

            log(message) {
                const logOutput = document.getElementById('log-output');
                const timestamp = new Date().toLocaleTimeString();
                logOutput.textContent += `[${timestamp}] ${message}\n`;
                logOutput.scrollTop = logOutput.scrollHeight;
            }

            showLogSection() {
                document.getElementById('log-section').style.display = 'block';
            }
        }

        const dbChecker = new DatabaseChecker();

        function runDatabaseCheck() {
            dbChecker.runDatabaseCheck();
        }

        function fixDatabase() {
            dbChecker.fixDatabase();
        }

        // 页面加载时自动运行检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                runDatabaseCheck();
            }, 1000);
        });
    </script>
</body>
</html>
