// 角色权限管理系统
// 专业学术协作编著平台

class RolePermissionManager {
    constructor() {
        this.currentUser = null;
        this.currentUserRole = null;
        this.projectRoles = new Map();
        this.permissions = new Map();
        
        this.initializePermissions();
    }
    
    // 初始化权限定义
    initializePermissions() {
        // 定义所有可能的权限
        const allPermissions = [
            // 项目管理权限
            'project.create',
            'project.edit',
            'project.delete',
            'project.view',
            
            // 成员管理权限
            'member.invite',
            'member.remove',
            'member.edit_role',
            'member.view',
            
            // 章节管理权限
            'chapter.create',
            'chapter.edit',
            'chapter.delete',
            'chapter.view',
            'chapter.assign',
            
            // 内容编辑权限
            'content.write',
            'content.edit',
            'content.delete',
            'content.view',
            
            // 审核权限
            'review.create',
            'review.submit',
            'review.approve',
            'review.reject',
            'review.view',
            
            // 讨论权限
            'discussion.create',
            'discussion.reply',
            'discussion.moderate',
            'discussion.view',
            
            // 文件管理权限
            'file.upload',
            'file.download',
            'file.delete',
            'file.view',
            
            // 报告权限
            'report.generate',
            'report.export',
            'report.view',
            
            // 系统设置权限
            'settings.edit',
            'settings.view'
        ];
        
        // 定义角色权限映射
        const rolePermissions = {
            'chief_editor': allPermissions, // 主编拥有所有权限
            
            'associate_editor': [
                'project.view', 'project.edit',
                'member.invite', 'member.edit_role', 'member.view',
                'chapter.create', 'chapter.edit', 'chapter.view', 'chapter.assign',
                'content.write', 'content.edit', 'content.view',
                'review.create', 'review.submit', 'review.approve', 'review.reject', 'review.view',
                'discussion.create', 'discussion.reply', 'discussion.moderate', 'discussion.view',
                'file.upload', 'file.download', 'file.view',
                'report.generate', 'report.export', 'report.view',
                'settings.view'
            ],
            
            'lead_author': [
                'project.view',
                'member.view',
                'chapter.view', 'chapter.edit',
                'content.write', 'content.edit', 'content.view',
                'review.view',
                'discussion.create', 'discussion.reply', 'discussion.view',
                'file.upload', 'file.download', 'file.view',
                'report.view'
            ],
            
            'co_author': [
                'project.view',
                'member.view',
                'chapter.view',
                'content.write', 'content.edit', 'content.view',
                'discussion.create', 'discussion.reply', 'discussion.view',
                'file.upload', 'file.download', 'file.view',
                'report.view'
            ],
            
            'reviewer': [
                'project.view',
                'member.view',
                'chapter.view',
                'content.view',
                'review.create', 'review.submit', 'review.view',
                'discussion.create', 'discussion.reply', 'discussion.view',
                'file.download', 'file.view',
                'report.view'
            ],
            
            'editorial_assistant': [
                'project.view',
                'member.view',
                'chapter.view',
                'content.edit', 'content.view',
                'discussion.create', 'discussion.reply', 'discussion.view',
                'file.upload', 'file.download', 'file.view',
                'report.view'
            ]
        };
        
        // 存储权限映射
        for (const [role, permissions] of Object.entries(rolePermissions)) {
            this.permissions.set(role, new Set(permissions));
        }
    }
    
    // 设置当前用户
    setCurrentUser(user, role) {
        this.currentUser = user;
        this.currentUserRole = role;
    }
    
    // 检查用户是否有特定权限
    hasPermission(permission, userRole = null) {
        const role = userRole || this.currentUserRole;
        if (!role) return false;
        
        const rolePermissions = this.permissions.get(role);
        return rolePermissions ? rolePermissions.has(permission) : false;
    }
    
    // 检查用户是否有多个权限中的任意一个
    hasAnyPermission(permissions, userRole = null) {
        return permissions.some(permission => this.hasPermission(permission, userRole));
    }
    
    // 检查用户是否有所有指定权限
    hasAllPermissions(permissions, userRole = null) {
        return permissions.every(permission => this.hasPermission(permission, userRole));
    }
    
    // 获取用户的所有权限
    getUserPermissions(userRole = null) {
        const role = userRole || this.currentUserRole;
        if (!role) return new Set();
        
        return this.permissions.get(role) || new Set();
    }
    
    // 检查是否可以管理成员
    canManageMembers(userRole = null) {
        return this.hasAnyPermission(['member.invite', 'member.remove', 'member.edit_role'], userRole);
    }
    
    // 检查是否可以分配章节
    canAssignChapters(userRole = null) {
        return this.hasPermission('chapter.assign', userRole);
    }
    
    // 检查是否可以审核内容
    canReviewContent(userRole = null) {
        return this.hasAnyPermission(['review.approve', 'review.reject'], userRole);
    }
    
    // 检查是否可以编辑项目设置
    canEditProject(userRole = null) {
        return this.hasPermission('project.edit', userRole);
    }
    
    // 检查是否可以删除内容
    canDeleteContent(userRole = null) {
        return this.hasAnyPermission(['content.delete', 'chapter.delete'], userRole);
    }
    
    // 检查是否可以生成报告
    canGenerateReports(userRole = null) {
        return this.hasPermission('report.generate', userRole);
    }
    
    // 获取角色显示名称
    getRoleDisplayName(role) {
        const roleNames = {
            'chief_editor': '主编',
            'associate_editor': '副主编',
            'lead_author': '章节主笔',
            'co_author': '协作作者',
            'reviewer': '审稿人',
            'editorial_assistant': '编辑助理'
        };
        return roleNames[role] || role;
    }
    
    // 获取角色描述
    getRoleDescription(role) {
        const descriptions = {
            'chief_editor': '项目总负责人，拥有最高权限，负责整体规划、质量把控、最终审定',
            'associate_editor': '协助主编管理项目，可以分配章节、管理作者、审核内容',
            'lead_author': '章节的主要撰写者和负责人，负责章节的整体结构、主要内容编写',
            'co_author': '参与章节特定部分的撰写，提供专业内容、数据、案例等',
            'reviewer': '对章节内容进行专业审核，提供修改建议和质量评估',
            'editorial_assistant': '协助格式整理、文献核查，负责技术性编辑工作'
        };
        return descriptions[role] || '';
    }
    
    // 获取角色权限列表
    getRolePermissions(role) {
        return Array.from(this.permissions.get(role) || []);
    }
    
    // 检查角色层级关系
    isHigherRole(role1, role2) {
        const hierarchy = {
            'chief_editor': 6,
            'associate_editor': 5,
            'lead_author': 4,
            'reviewer': 3,
            'co_author': 2,
            'editorial_assistant': 1
        };
        
        return (hierarchy[role1] || 0) > (hierarchy[role2] || 0);
    }
    
    // 检查是否可以分配特定角色
    canAssignRole(assignerRole, targetRole) {
        // 主编可以分配所有角色
        if (assignerRole === 'chief_editor') return true;
        
        // 副主编可以分配除主编外的所有角色
        if (assignerRole === 'associate_editor' && targetRole !== 'chief_editor') return true;
        
        // 其他角色不能分配角色
        return false;
    }
    
    // 验证操作权限
    validateOperation(operation, context = {}) {
        const validations = {
            'create_assignment': () => this.canAssignChapters(),
            'edit_assignment': (ctx) => {
                // 创建者或有管理权限的用户可以编辑
                return ctx.isCreator || this.canAssignChapters();
            },
            'delete_assignment': (ctx) => {
                // 只有创建者或主编/副主编可以删除
                return ctx.isCreator || this.hasAnyPermission(['project.edit']);
            },
            'submit_review': () => this.hasPermission('review.submit'),
            'approve_review': () => this.hasPermission('review.approve'),
            'manage_members': () => this.canManageMembers(),
            'upload_file': () => this.hasPermission('file.upload'),
            'delete_file': (ctx) => {
                // 上传者或有删除权限的用户可以删除
                return ctx.isUploader || this.hasPermission('file.delete');
            }
        };
        
        const validator = validations[operation];
        if (!validator) {
            console.warn(`未知操作: ${operation}`);
            return false;
        }
        
        return validator(context);
    }
    
    // 获取用户可执行的操作列表
    getAvailableOperations(context = {}) {
        const operations = [
            'create_assignment',
            'edit_assignment',
            'delete_assignment',
            'submit_review',
            'approve_review',
            'manage_members',
            'upload_file',
            'delete_file'
        ];
        
        return operations.filter(op => this.validateOperation(op, context));
    }
    
    // 生成权限矩阵
    generatePermissionMatrix() {
        const roles = ['chief_editor', 'associate_editor', 'lead_author', 'co_author', 'reviewer', 'editorial_assistant'];
        const permissionCategories = {
            '项目管理': ['project.create', 'project.edit', 'project.delete', 'project.view'],
            '成员管理': ['member.invite', 'member.remove', 'member.edit_role', 'member.view'],
            '章节管理': ['chapter.create', 'chapter.edit', 'chapter.delete', 'chapter.view', 'chapter.assign'],
            '内容编辑': ['content.write', 'content.edit', 'content.delete', 'content.view'],
            '审核管理': ['review.create', 'review.submit', 'review.approve', 'review.reject', 'review.view'],
            '讨论管理': ['discussion.create', 'discussion.reply', 'discussion.moderate', 'discussion.view'],
            '文件管理': ['file.upload', 'file.download', 'file.delete', 'file.view'],
            '报告管理': ['report.generate', 'report.export', 'report.view']
        };
        
        const matrix = {};
        
        for (const [category, permissions] of Object.entries(permissionCategories)) {
            matrix[category] = {};
            for (const role of roles) {
                matrix[category][role] = permissions.map(permission => 
                    this.hasPermission(permission, role)
                );
            }
        }
        
        return matrix;
    }
}

// 创建全局权限管理器实例
const rolePermissionManager = new RolePermissionManager();

// 导出权限检查函数供其他模块使用
window.hasPermission = (permission, role) => rolePermissionManager.hasPermission(permission, role);
window.canManageMembers = (role) => rolePermissionManager.canManageMembers(role);
window.canAssignChapters = (role) => rolePermissionManager.canAssignChapters(role);
window.canReviewContent = (role) => rolePermissionManager.canReviewContent(role);
window.validateOperation = (operation, context) => rolePermissionManager.validateOperation(operation, context);

// 导出权限管理器
window.rolePermissionManager = rolePermissionManager;
