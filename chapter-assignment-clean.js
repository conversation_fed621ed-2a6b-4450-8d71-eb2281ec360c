// 章节分配管理系统 - 清理版本
// 专业学术协作编著平台

class ChapterAssignmentManager {
    constructor() {
        this.currentProject = null;
        this.currentUser = null;
        this.currentUserRole = null;
        this.assignments = [];
        this.collaborators = [];
        this.reviews = [];
        this.discussions = [];
        this.workLogs = [];
        
        // UI状态
        this.activeTab = 'overview';
        this.filters = {
            status: '',
            author: '',
            search: ''
        };
        
        // 图表实例
        this.progressChart = null;
        
        this.init();
    }
    
    // 初始化系统
    async init() {
        try {
            await this.checkAuthentication();
            await this.loadCurrentProject();
            this.setupEventListeners();
            this.setupNavigation();
            await this.loadInitialData();
            
            console.log('章节分配管理系统初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showNotification('系统初始化失败', 'error');
        }
    }
    
    // 检查用户认证
    async checkAuthentication() {
        try {
            const user = await supabaseManager.getCurrentUser();
            if (!user) {
                console.log('用户未登录，使用演示用户');
                // 使用演示用户而不是跳转
                this.currentUser = {
                    id: 'demo-user-1',
                    email: '<EMAIL>',
                    user_metadata: {
                        full_name: '演示用户'
                    },
                    created_at: new Date().toISOString()
                };
            } else {
                this.currentUser = user;
            }

            await this.loadUserRole();
            this.updateUserDisplay();

        } catch (error) {
            console.error('认证检查失败:', error);
            // 使用演示用户作为后备
            this.currentUser = {
                id: 'demo-user-1',
                email: '<EMAIL>',
                user_metadata: {
                    full_name: '演示用户'
                },
                created_at: new Date().toISOString()
            };
            await this.loadUserRole();
            this.updateUserDisplay();
        }
    }
    
    // 加载用户角色
    async loadUserRole() {
        if (!this.currentUser || !this.currentProject) return;
        
        try {
            // 使用默认角色进行演示
            this.currentUserRole = 'chief_editor'; // 默认为主编角色
            
            // 设置权限管理器的当前用户
            if (window.rolePermissionManager) {
                window.rolePermissionManager.setCurrentUser(this.currentUser, this.currentUserRole);
            }
            
            // 更新UI权限
            this.updateUIPermissions();
            
            console.log('用户角色已设置:', this.currentUserRole);
            
        } catch (error) {
            console.error('加载用户角色失败:', error);
            // 使用默认角色
            this.currentUserRole = 'chief_editor';
            if (window.rolePermissionManager) {
                window.rolePermissionManager.setCurrentUser(this.currentUser, this.currentUserRole);
            }
            this.updateUIPermissions();
        }
    }
    
    // 更新UI权限显示
    updateUIPermissions() {
        if (!window.rolePermissionManager || !this.currentUserRole) return;
        
        // 检查各种权限并更新UI
        const canAssign = window.rolePermissionManager.canAssignChapters(this.currentUserRole);
        const canManage = window.rolePermissionManager.canManageMembers(this.currentUserRole);
        const canReview = window.rolePermissionManager.canReviewContent(this.currentUserRole);
        
        // 更新按钮显示
        this.updateButtonVisibility('create-assignment-btn', canAssign);
        this.updateButtonVisibility('manage-members-btn', canManage);
        this.updateButtonVisibility('review-content-btn', canReview);
        
        // 更新导航菜单
        this.updateNavPermissions();
    }
    
    // 更新按钮可见性
    updateButtonVisibility(buttonId, hasPermission) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = hasPermission ? 'inline-flex' : 'none';
        }
    }
    
    // 更新导航权限
    updateNavPermissions() {
        const navItems = {
            'assignments': window.rolePermissionManager.hasPermission('chapter.view', this.currentUserRole),
            'reviews': window.rolePermissionManager.hasPermission('review.view', this.currentUserRole),
            'files': window.rolePermissionManager.hasPermission('file.view', this.currentUserRole),
            'reports': window.rolePermissionManager.hasPermission('report.view', this.currentUserRole)
        };
        
        for (const [tab, hasPermission] of Object.entries(navItems)) {
            const navItem = document.querySelector(`[data-tab="${tab}"]`);
            if (navItem) {
                const listItem = navItem.closest('.nav-item');
                if (listItem) {
                    listItem.style.display = hasPermission ? 'block' : 'none';
                }
            }
        }
    }
    
    // 加载当前项目
    async loadCurrentProject() {
        console.log('使用演示项目进行功能展示');
        this.currentProject = {
            id: 'demo-project-1',
            title: '演示项目 - 学术著作编纂',
            description: '这是一个用于演示章节分配系统的模拟项目',
            status: 'active',
            owner_id: this.currentUser?.id,
            created_at: new Date().toISOString()
        };
        this.updateProjectDisplay();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('assignment-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.filterAssignments();
            });
        }
        
        // 状态过滤
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.filterAssignments();
            });
        }
        
        // 图表周期选择
        const chartPeriod = document.getElementById('chart-period');
        if (chartPeriod) {
            chartPeriod.addEventListener('change', (e) => {
                this.updateProgressChart(e.target.value);
            });
        }
        
        // 进度视图切换
        const progressView = document.getElementById('progress-view');
        if (progressView) {
            progressView.addEventListener('change', (e) => {
                this.switchProgressView(e.target.value);
            });
        }
        
        // 模态框关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-selector')) {
                document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // 设置导航
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item a');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = e.target.closest('a').dataset.tab;
                this.switchTab(tab);
            });
        });
    }
    
    // 切换标签页
    switchTab(tabName) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).closest('.nav-item').classList.add('active');
        
        // 更新面板显示
        document.querySelectorAll('.content-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(`${tabName}-panel`).classList.add('active');
        
        this.activeTab = tabName;
        
        // 加载对应数据
        this.loadTabData(tabName);
    }
    
    // 加载标签页数据
    async loadTabData(tabName) {
        switch (tabName) {
            case 'overview':
                await this.loadOverviewData();
                break;
            case 'assignments':
                await this.loadAssignments();
                break;
            case 'progress':
                await this.loadProgressData();
                break;
            case 'reviews':
                await this.loadReviews();
                break;
            case 'discussions':
                await this.loadDiscussions();
                break;
            case 'files':
                await this.loadFiles();
                break;
            case 'reports':
                await this.loadReports();
                break;
        }
    }
    
    // 加载初始数据
    async loadInitialData() {
        await this.loadOverviewData();
    }
    
    // 加载概览数据
    async loadOverviewData() {
        try {
            // 加载统计数据
            await this.loadStatistics();
            
            // 加载进度图表
            await this.loadProgressChart();
            
            // 加载最近活动
            await this.loadRecentActivity();
            
        } catch (error) {
            console.error('加载概览数据失败:', error);
            this.showNotification('加载数据失败', 'error');
        }
    }
    
    // 加载统计数据
    async loadStatistics() {
        // 使用模拟数据
        const stats = {
            totalChapters: 2,
            totalAuthors: <AUTHORS>
            completedChapters: 0,
            pendingReviews: 1
        };
        
        this.updateStatistics(stats);
    }
    
    // 更新统计数据显示
    updateStatistics(stats) {
        const elements = {
            'total-chapters': stats.totalChapters,
            'total-authors': stats.totalAuthors,
            'completed-chapters': stats.completedChapters,
            'pending-reviews': stats.pendingReviews
        };
        
        for (const [id, value] of Object.entries(elements)) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }
    }
    
    // 加载进度图表
    async loadProgressChart(period = 'month') {
        const ctx = document.getElementById('progress-chart');
        if (!ctx) return;
        
        try {
            const data = this.generateMockProgressData(period);
            
            if (this.progressChart) {
                this.progressChart.destroy();
            }
            
            this.progressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: '完成章节数',
                        data: data.completed,
                        borderColor: '#1e40af',
                        backgroundColor: 'rgba(30, 64, 175, 0.1)',
                        tension: 0.4
                    }, {
                        label: '分配章节数',
                        data: data.assigned,
                        borderColor: '#059669',
                        backgroundColor: 'rgba(5, 150, 105, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
        } catch (error) {
            console.error('加载进度图表失败:', error);
        }
    }
    
    // 生成模拟进度数据
    generateMockProgressData(period) {
        const now = new Date();
        const data = { labels: [], completed: [], assigned: [] };
        
        let days = 30;
        if (period === 'week') days = 7;
        else if (period === 'quarter') days = 90;
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            
            data.labels.push(date.toLocaleDateString('zh-CN', { 
                month: 'short', 
                day: 'numeric' 
            }));
            
            // 模拟数据
            data.completed.push(Math.floor(Math.random() * 3) + i * 0.1);
            data.assigned.push(Math.floor(Math.random() * 5) + i * 0.2);
        }
        
        return data;
    }
    
    // 加载最近活动
    async loadRecentActivity() {
        const activities = [
            {
                icon: 'fas fa-user-plus',
                title: '系统初始化完成',
                time: '刚刚',
                type: 'system'
            },
            {
                icon: 'fas fa-project-diagram',
                title: '演示项目已创建',
                time: '1分钟前',
                type: 'project'
            }
        ];
        
        this.renderRecentActivity(activities);
    }
    
    // 渲染最近活动
    renderRecentActivity(activities) {
        const container = document.getElementById('activity-list');
        if (!container) return;
        
        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-meta">${activity.time}</div>
                </div>
            </div>
        `).join('');
    }
    
    // 加载章节分配
    async loadAssignments() {
        if (!this.currentProject) return;
        
        try {
            // 使用模拟数据进行演示
            const mockAssignments = [
                {
                    id: '1',
                    title: '第0章：前言',
                    description: '介绍本书的写作背景、目标读者、主要内容和结构安排',
                    status: 'writing',
                    lead_author_id: this.currentUser?.id,
                    lead_author: {
                        id: this.currentUser?.id,
                        email: this.currentUser?.email,
                        full_name: this.currentUser?.user_metadata?.full_name || '演示用户'
                    },
                    collaborators: [
                        { id: 'collab1', full_name: '王研究员', email: '<EMAIL>', role: '研究员' }
                    ],
                    reviewer: {
                        id: 'reviewer1',
                        full_name: '刘院长',
                        email: '<EMAIL>',
                        role: '院长'
                    },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 5000,
                    priority: 'high'
                },
                {
                    id: '2',
                    title: '第1章：大模型基本概念与内涵',
                    description: '阐述大模型的定义、特点、分类和发展历程',
                    status: 'reviewing',
                    lead_author_id: 'author2',
                    lead_author: {
                        id: 'author2',
                        email: '<EMAIL>',
                        full_name: '李博士'
                    },
                    collaborators: [
                        { id: 'collab2', full_name: '陈助教', email: '<EMAIL>', role: '助教' }
                    ],
                    reviewer: {
                        id: this.currentUser?.id,
                        full_name: this.currentUser?.user_metadata?.full_name || '演示用户',
                        email: this.currentUser?.email,
                        role: '主编'
                    },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 8000,
                    priority: 'medium'
                },
                {
                    id: '3',
                    title: '第2章：大模型技术原理',
                    description: '深入讲解Transformer架构、注意力机制等核心技术原理',
                    status: 'pending',
                    lead_author_id: 'author3',
                    lead_author: {
                        id: 'author3',
                        email: '<EMAIL>',
                        full_name: '王研究员'
                    },
                    collaborators: [],
                    reviewer: {
                        id: 'reviewer1',
                        full_name: '刘院长',
                        email: '<EMAIL>',
                        role: '院长'
                    },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 6000,
                    priority: 'medium'
                },
                {
                    id: '4',
                    title: '第四章：修复地质问题',
                    description: '大模型在地质问题修复中的应用',
                    status: 'writing',
                    lead_author_id: 'author4',
                    lead_author: {
                        id: 'author4',
                        email: '<EMAIL>',
                        full_name: '赵副教授'
                    },
                    collaborators: [
                        { id: 'collab3', full_name: '陈助教', email: '<EMAIL>', role: '助教' }
                    ],
                    reviewer: {
                        id: this.currentUser?.id,
                        full_name: this.currentUser?.user_metadata?.full_name || '演示用户',
                        email: this.currentUser?.email,
                        role: '主编'
                    },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 7000,
                    priority: 'high'
                },
                {
                    id: '5',
                    title: '第五章：地质环境建模',
                    description: '使用大模型进行地质环境建模',
                    status: 'completed',
                    lead_author_id: 'author5',
                    lead_author: {
                        id: 'author5',
                        email: '<EMAIL>',
                        full_name: '李博士'
                    },
                    collaborators: [
                        { id: 'collab4', full_name: '王研究员', email: '<EMAIL>', role: '研究员' }
                    ],
                    reviewer: {
                        id: 'reviewer1',
                        full_name: '刘院长',
                        email: '<EMAIL>',
                        role: '院长'
                    },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 8500,
                    priority: 'high'
                },
                {
                    id: '6',
                    title: '第六章：地质灾害预测',
                    description: '大模型在地质灾害预测中的应用',
                    status: 'reviewing',
                    lead_author_id: 'author6',
                    lead_author: {
                        id: 'author6',
                        email: '<EMAIL>',
                        full_name: '陈助教'
                    },
                    collaborators: [
                        { id: 'collab5', full_name: '赵副教授', email: '<EMAIL>', role: '副教授' }
                    ],
                    reviewer: {
                        id: this.currentUser?.id,
                        full_name: this.currentUser?.user_metadata?.full_name || '演示用户',
                        email: this.currentUser?.email,
                        role: '主编'
                    },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 7500,
                    priority: 'medium'
                }
            ];
            
            this.assignments = mockAssignments;
            this.renderAssignments();
            
        } catch (error) {
            console.error('加载章节分配失败:', error);
            this.showNotification('加载章节分配失败', 'error');
        }
    }
    
    // 渲染章节分配列表
    renderAssignments() {
        const container = document.getElementById('assignments-list');
        if (!container) return;
        
        if (this.assignments.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>暂无章节分配</h3>
                    <p>点击"新建分配"开始分配章节给团队成员</p>
                    <button class="btn btn-primary" onclick="showCreateAssignmentModal()">
                        <i class="fas fa-plus"></i>
                        新建分配
                    </button>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.assignments.map(assignment => this.renderAssignmentItem(assignment)).join('');
    }
    
    // 渲染单个分配项目
    renderAssignmentItem(assignment) {
        const progress = this.calculateProgress(assignment);
        const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
        const isOverdue = dueDate && dueDate < new Date() && assignment.status !== 'completed';
        
        return `
            <div class="assignment-item" data-id="${assignment.id}">
                <div>
                    <div class="assignment-title">${assignment.title}</div>
                    <div class="assignment-subtitle">${assignment.description || '无描述'}</div>
                </div>
                <div>
                    <div class="user-badge">
                        <i class="fas fa-user"></i>
                        ${assignment.lead_author?.full_name || assignment.lead_author?.email?.split('@')[0] || '未分配'}
                    </div>
                </div>
                <div>
                    <div class="user-badge">
                        <i class="fas fa-users"></i>
                        ${assignment.collaborators && assignment.collaborators.length > 0
                            ? assignment.collaborators.map(c => c.full_name || c.email?.split('@')[0]).join(', ')
                            : '无'}
                    </div>
                </div>
                <div>
                    <div class="user-badge">
                        <i class="fas fa-user-check"></i>
                        ${assignment.reviewer?.full_name || assignment.reviewer?.email?.split('@')[0] || '未分配'}
                    </div>
                </div>
                <div>
                    <span class="status-badge status-${assignment.status}">
                        ${this.getStatusText(assignment.status)}
                    </span>
                </div>
                <div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <div style="font-size: 11px; color: var(--gray-500); margin-top: 2px;">
                        ${progress}%
                    </div>
                </div>
                <div>
                    <div class="due-date ${isOverdue ? 'overdue' : ''}">
                        ${dueDate ? dueDate.toLocaleDateString('zh-CN') : '无截止日期'}
                    </div>
                </div>
                <div class="actions">
                    <button class="action-btn" onclick="editAssignment('${assignment.id}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn" onclick="viewAssignmentDetails('${assignment.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" onclick="deleteAssignment('${assignment.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    // 计算进度
    calculateProgress(assignment) {
        const statusProgress = {
            'pending': 5,
            'writing': 50,
            'reviewing': 85,
            'completed': 100,
            'rejected': 0
        };

        return statusProgress[assignment.status] || 0;
    }
    
    // 获取状态文本
    getStatusText(status) {
        const statusTexts = {
            'pending': '待分配',
            'writing': '编制中',
            'reviewing': '审核中',
            'completed': '已完成',
            'rejected': '已拒绝'
        };

        return statusTexts[status] || status;
    }
    
    // 过滤分配
    filterAssignments() {
        const container = document.getElementById('assignments-list');
        if (!container) return;

        let filteredAssignments = [...this.assignments];

        // 状态过滤
        if (this.filters.status) {
            filteredAssignments = filteredAssignments.filter(assignment =>
                assignment.status === this.filters.status
            );
        }

        // 搜索过滤
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filteredAssignments = filteredAssignments.filter(assignment =>
                assignment.title.toLowerCase().includes(searchTerm) ||
                assignment.description?.toLowerCase().includes(searchTerm) ||
                assignment.lead_author?.full_name?.toLowerCase().includes(searchTerm) ||
                assignment.lead_author?.email?.toLowerCase().includes(searchTerm)
            );
        }

        // 渲染过滤后的结果
        if (filteredAssignments.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🔍</div>
                    <h3>未找到匹配的分配</h3>
                    <p>尝试调整搜索条件或过滤器</p>
                </div>
            `;
        } else {
            container.innerHTML = filteredAssignments.map(assignment => this.renderAssignmentItem(assignment)).join('');
        }
    }
    
    // 其他方法的简化实现
    async loadProgressData() {
        console.log('加载进度数据');
    }
    
    async loadReviews() {
        console.log('加载审核数据');
    }
    
    async loadDiscussions() {
        console.log('加载讨论数据');
    }
    
    async loadFiles() {
        console.log('加载文件数据');
    }
    
    async loadReports() {
        console.log('加载报告数据');
    }
    
    // 更新用户显示
    updateUserDisplay() {
        const userNameElement = document.getElementById('user-name');
        if (userNameElement && this.currentUser) {
            userNameElement.textContent = this.currentUser.email.split('@')[0];
        }
    }
    
    // 更新项目显示
    updateProjectDisplay() {
        const projectNameElement = document.getElementById('current-project-name');
        if (projectNameElement && this.currentProject) {
            projectNameElement.textContent = this.currentProject.title;
        }
    }
    
    // 显示通知
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        if (!container) return;
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">${this.getNotificationTitle(type)}</div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-message">${message}</div>
        `;
        
        container.appendChild(notification);
        
        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
    
    // 获取通知标题
    getNotificationTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '信息'
        };
        return titles[type] || '通知';
    }
    
    // 显示模态框
    showModal(content) {
        const container = document.getElementById('modal-container');
        if (!container) return;
        
        container.innerHTML = `
            <div class="modal-overlay active">
                <div class="modal">
                    ${content}
                </div>
            </div>
        `;
    }
    
    // 关闭模态框
    closeModal() {
        const overlay = document.querySelector('.modal-overlay');
        if (overlay) {
            overlay.classList.remove('active');
            setTimeout(() => overlay.remove(), 300);
        }
    }

    // 获取项目成员列表
    getProjectMembers() {
        // 默认项目所有者作为所有角色
        const owner = {
            id: this.currentUser?.id || 'demo-user-1',
            full_name: this.currentUser?.user_metadata?.full_name || '演示用户',
            email: this.currentUser?.email || '<EMAIL>',
            role: '项目所有者',
            avatar: (this.currentUser?.user_metadata?.full_name || '演示用户').charAt(0)
        };

        return [
            owner,
            { id: 'user2', full_name: '李博士', email: '<EMAIL>', role: '博士', avatar: 'L' },
            { id: 'user3', full_name: '王研究员', email: '<EMAIL>', role: '研究员', avatar: 'W' },
            { id: 'user4', full_name: '刘院长', email: '<EMAIL>', role: '院长', avatar: 'L' },
            { id: 'user5', full_name: '陈助教', email: '<EMAIL>', role: '助教', avatar: 'C' },
            { id: 'user6', full_name: '赵副教授', email: '<EMAIL>', role: '副教授', avatar: 'Z' }
        ];
    }

    // 显示编辑分配模态对话框
    showEditAssignmentModal(assignment) {
        const members = this.getProjectMembers();

        // 创建模态对话框HTML
        const modalHtml = `
            <div class="assignment-modal" id="editAssignmentModal">
                <div class="assignment-modal-content">
                    <div class="assignment-modal-header">
                        <h3 class="assignment-modal-title">
                            <i class="fas fa-edit"></i>
                            编辑章节分配
                        </h3>
                    </div>
                    <div class="assignment-modal-body">
                        <form id="editAssignmentForm">
                            <input type="hidden" id="assignmentId" value="${assignment.id}">

                            <div class="form-group">
                                <label class="form-label">章节标题</label>
                                <input type="text" class="form-input" id="assignmentTitle" value="${assignment.title}" readonly>
                            </div>

                            <div class="form-group">
                                <label class="form-label">章节描述</label>
                                <textarea class="form-textarea" id="assignmentDescription" placeholder="请输入章节描述">${assignment.description}</textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">主笔作者</label>
                                    <div class="user-selector">
                                        <input type="text" class="form-input user-selector-input" id="leadAuthorInput"
                                               value="${assignment.lead_author?.full_name || ''}" readonly
                                               placeholder="选择主笔作者" onclick="window.assignmentManager.toggleUserDropdown('leadAuthor')">
                                        <div class="user-dropdown" id="leadAuthorDropdown">
                                            ${members.map(member => `
                                                <div class="user-option ${member.id === assignment.lead_author?.id ? 'selected' : ''}"
                                                     onclick="window.assignmentManager.selectUser('leadAuthor', '${member.id}', '${member.full_name}')">
                                                    <div class="user-avatar-small">${member.avatar}</div>
                                                    <div class="user-info">
                                                        <div class="user-name">${member.full_name}</div>
                                                        <div class="user-role">${member.role}</div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">审核者</label>
                                    <div class="user-selector">
                                        <input type="text" class="form-input user-selector-input" id="reviewerInput"
                                               value="${assignment.reviewer?.full_name || ''}" readonly
                                               placeholder="选择审核者" onclick="window.assignmentManager.toggleUserDropdown('reviewer')">
                                        <div class="user-dropdown" id="reviewerDropdown">
                                            ${members.map(member => `
                                                <div class="user-option ${member.id === assignment.reviewer?.id ? 'selected' : ''}"
                                                     onclick="window.assignmentManager.selectUser('reviewer', '${member.id}', '${member.full_name}')">
                                                    <div class="user-avatar-small">${member.avatar}</div>
                                                    <div class="user-info">
                                                        <div class="user-name">${member.full_name}</div>
                                                        <div class="user-role">${member.role}</div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">协作者</label>
                                <div class="user-selector">
                                    <input type="text" class="form-input user-selector-input" id="collaboratorsInput"
                                           value="${assignment.collaborators?.map(c => c.full_name).join(', ') || ''}" readonly
                                           placeholder="选择协作者（可多选）" onclick="window.assignmentManager.toggleUserDropdown('collaborators')">
                                    <div class="user-dropdown" id="collaboratorsDropdown">
                                        ${members.map(member => {
                                            const isSelected = assignment.collaborators?.some(c => c.id === member.id);
                                            return `
                                                <div class="user-option ${isSelected ? 'selected' : ''}"
                                                     onclick="window.assignmentManager.toggleCollaborator('${member.id}', '${member.full_name}')">
                                                    <div class="user-avatar-small">${member.avatar}</div>
                                                    <div class="user-info">
                                                        <div class="user-name">${member.full_name}</div>
                                                        <div class="user-role">${member.role}</div>
                                                    </div>
                                                    ${isSelected ? '<i class="fas fa-check" style="color: #10b981;"></i>' : ''}
                                                </div>
                                            `;
                                        }).join('')}
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="assignmentStatus">
                                        <option value="pending" ${assignment.status === 'pending' ? 'selected' : ''}>待分配</option>
                                        <option value="writing" ${assignment.status === 'writing' ? 'selected' : ''}>编制中</option>
                                        <option value="reviewing" ${assignment.status === 'reviewing' ? 'selected' : ''}>审核中</option>
                                        <option value="completed" ${assignment.status === 'completed' ? 'selected' : ''}>已完成</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">截止日期</label>
                                    <input type="date" class="form-input" id="assignmentDueDate"
                                           value="${assignment.due_date ? new Date(assignment.due_date).toISOString().split('T')[0] : ''}">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="assignment-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="window.assignmentManager.closeEditModal()">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="window.assignmentManager.saveAssignment()">
                            <i class="fas fa-save"></i>
                            保存
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态对话框
        setTimeout(() => {
            document.getElementById('editAssignmentModal').classList.add('active');
        }, 10);

        // 初始化选中的协作者
        this.selectedCollaborators = assignment.collaborators ? [...assignment.collaborators] : [];
    }

    // 切换用户下拉框显示
    toggleUserDropdown(type) {
        const dropdown = document.getElementById(`${type}Dropdown`);
        const isActive = dropdown.classList.contains('active');

        // 关闭所有下拉框
        document.querySelectorAll('.user-dropdown').forEach(d => d.classList.remove('active'));

        // 切换当前下拉框
        if (!isActive) {
            dropdown.classList.add('active');
        }
    }

    // 选择用户
    selectUser(type, userId, userName) {
        const input = document.getElementById(`${type}Input`);
        const dropdown = document.getElementById(`${type}Dropdown`);

        input.value = userName;
        dropdown.classList.remove('active');

        // 更新选中状态
        dropdown.querySelectorAll('.user-option').forEach(option => {
            option.classList.remove('selected');
        });
        dropdown.querySelector(`[onclick*="${userId}"]`).classList.add('selected');
    }

    // 切换协作者选择
    toggleCollaborator(userId, userName) {
        const members = this.getProjectMembers();
        const member = members.find(m => m.id === userId);

        if (!member) return;

        const existingIndex = this.selectedCollaborators.findIndex(c => c.id === userId);

        if (existingIndex >= 0) {
            // 移除协作者
            this.selectedCollaborators.splice(existingIndex, 1);
        } else {
            // 添加协作者
            this.selectedCollaborators.push(member);
        }

        // 更新输入框显示
        const input = document.getElementById('collaboratorsInput');
        input.value = this.selectedCollaborators.map(c => c.full_name).join(', ');

        // 更新选中状态
        const option = document.querySelector(`[onclick*="toggleCollaborator('${userId}'"]`);
        if (option) {
            if (existingIndex >= 0) {
                option.classList.remove('selected');
                const checkIcon = option.querySelector('.fa-check');
                if (checkIcon) checkIcon.remove();
            } else {
                option.classList.add('selected');
                option.insertAdjacentHTML('beforeend', '<i class="fas fa-check" style="color: #10b981;"></i>');
            }
        }
    }

    // 关闭编辑模态对话框
    closeEditModal() {
        const modal = document.getElementById('editAssignmentModal');
        if (modal) {
            modal.classList.remove('active');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    // 保存分配
    saveAssignment() {
        const assignmentData = {
            id: document.getElementById('assignmentId').value,
            title: document.getElementById('assignmentTitle').value,
            description: document.getElementById('assignmentDescription').value,
            status: document.getElementById('assignmentStatus').value,
            due_date: document.getElementById('assignmentDueDate').value,
            lead_author: this.getSelectedUser('leadAuthor'),
            reviewer: this.getSelectedUser('reviewer'),
            collaborators: this.selectedCollaborators
        };

        console.log('保存分配数据:', assignmentData);

        // 更新本地数据
        const assignmentIndex = this.assignments.findIndex(a => a.id === assignmentData.id);
        if (assignmentIndex >= 0) {
            this.assignments[assignmentIndex] = { ...this.assignments[assignmentIndex], ...assignmentData };
            this.renderAssignments();
        }

        this.showNotification('分配已保存！', 'success');
        this.closeEditModal();
    }

    // 获取选中的用户
    getSelectedUser(type) {
        const input = document.getElementById(`${type}Input`);
        const userName = input.value;

        if (!userName) return null;

        const members = this.getProjectMembers();
        return members.find(m => m.full_name === userName);
    }
}

// 全局函数
window.showCreateAssignmentModal = function() {
    if (!window.assignmentManager) return;
    
    const modalContent = `
        <div class="modal-header">
            <h2 class="modal-title">新建章节分配</h2>
            <button class="modal-close" onclick="window.assignmentManager.closeModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>这是一个演示模态框。在完整版本中，这里会有创建章节分配的表单。</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="window.assignmentManager.closeModal()">
                取消
            </button>
            <button type="button" class="btn btn-primary" onclick="window.assignmentManager.closeModal()">
                创建分配
            </button>
        </div>
    `;
    
    window.assignmentManager.showModal(modalContent);
};

window.editAssignment = function(assignmentId) {
    console.log('编辑分配:', assignmentId);

    // 查找要编辑的分配
    const assignment = window.assignmentManager.assignments.find(a => a.id === assignmentId);
    if (!assignment) {
        alert('未找到指定的分配记录');
        return;
    }

    window.assignmentManager.showEditAssignmentModal(assignment);
};

window.viewAssignmentDetails = function(assignmentId) {
    console.log('查看分配详情:', assignmentId);
    alert('查看详情功能演示 - 分配ID: ' + assignmentId);
};

window.deleteAssignment = function(assignmentId) {
    if (confirm('确定要删除这个章节分配吗？')) {
        console.log('删除分配:', assignmentId);
        alert('删除功能演示 - 分配ID: ' + assignmentId);
    }
};

window.refreshOverview = function() {
    if (window.assignmentManager) {
        window.assignmentManager.loadOverviewData();
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.assignmentManager = new ChapterAssignmentManager();
});
