-- 修复重复的章节分配问题
-- 这个脚本会查找并删除重复的章节分配记录

-- 1. 查看当前章节分配状态，检查是否有重复
SELECT 
    ca.id,
    ca.title,
    ca.project_id,
    ca.chapter_id,
    ca.lead_author_id,
    ca.status,
    ca.created_at,
    COUNT(*) OVER (PARTITION BY ca.project_id, ca.title) as duplicate_count
FROM chapter_assignments ca
ORDER BY ca.project_id, ca.title, ca.created_at;

-- 2. 查找重复的分配（相同项目、相同标题）
WITH duplicate_assignments AS (
    SELECT 
        ca.id,
        ca.title,
        ca.project_id,
        ca.created_at,
        ROW_NUMBER() OVER (
            PARTITION BY ca.project_id, ca.title 
            ORDER BY ca.created_at ASC
        ) as row_num
    FROM chapter_assignments ca
)
SELECT 
    da.id,
    da.title,
    da.project_id,
    da.created_at,
    da.row_num,
    CASE WHEN da.row_num = 1 THEN '保留' ELSE '删除' END as action
FROM duplicate_assignments da
WHERE da.id IN (
    SELECT id FROM duplicate_assignments 
    WHERE (project_id, title) IN (
        SELECT project_id, title 
        FROM duplicate_assignments 
        GROUP BY project_id, title 
        HAVING COUNT(*) > 1
    )
)
ORDER BY da.project_id, da.title, da.created_at;

-- 3. 删除重复的分配记录（保留最早创建的）
-- 注意：这个操作会删除数据，请先备份！
WITH duplicate_assignments AS (
    SELECT 
        ca.id,
        ROW_NUMBER() OVER (
            PARTITION BY ca.project_id, ca.title 
            ORDER BY ca.created_at ASC
        ) as row_num
    FROM chapter_assignments ca
)
DELETE FROM chapter_assignments 
WHERE id IN (
    SELECT id FROM duplicate_assignments 
    WHERE row_num > 1
);

-- 4. 验证清理结果
SELECT 
    ca.project_id,
    COUNT(*) as total_assignments,
    COUNT(DISTINCT ca.title) as unique_titles,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT ca.title) THEN '无重复'
        ELSE '仍有重复'
    END as status
FROM chapter_assignments ca
GROUP BY ca.project_id
ORDER BY ca.project_id;

-- 5. 查看清理后的章节分配
SELECT 
    ca.id,
    ca.title,
    ca.project_id,
    ca.chapter_id,
    ca.status,
    ca.created_at,
    up.full_name as lead_author
FROM chapter_assignments ca
LEFT JOIN user_profiles up ON ca.lead_author_id = up.id
ORDER BY ca.project_id, ca.created_at;
