-- 快速修复 RLS 问题的临时解决方案
-- 注意：这是临时方案，生产环境建议使用完整的 RLS 策略

-- 方案1：临时禁用 user_profiles 表的 RLS（快速但不安全）
-- ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;

-- 方案2：添加宽松的 RLS 策略（推荐）

-- 1. 为 user_profiles 表添加宽松的插入策略
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON public.user_profiles;
CREATE POLICY "Allow insert for authenticated users" 
ON public.user_profiles
FOR INSERT 
TO authenticated
WITH CHECK (true);

-- 2. 为 user_profiles 表添加宽松的查询策略
DROP POLICY IF EXISTS "Allow select for authenticated users" ON public.user_profiles;
CREATE POLICY "Allow select for authenticated users" 
ON public.user_profiles
FOR SELECT 
TO authenticated
USING (true);

-- 3. 为 project_members 表添加宽松的插入策略
DROP POLICY IF EXISTS "Allow member insert for authenticated users" ON public.project_members;
CREATE POLICY "Allow member insert for authenticated users" 
ON public.project_members
FOR INSERT 
TO authenticated
WITH CHECK (true);

-- 4. 为 project_members 表添加宽松的查询策略
DROP POLICY IF EXISTS "Allow member select for authenticated users" ON public.project_members;
CREATE POLICY "Allow member select for authenticated users" 
ON public.project_members
FOR SELECT 
TO authenticated
USING (true);

-- 5. 检查当前用户的认证状态
SELECT 
    auth.uid() as current_user_id,
    auth.role() as current_role,
    CASE 
        WHEN auth.uid() IS NOT NULL THEN '已认证'
        ELSE '未认证'
    END as auth_status;

-- 6. 验证策略是否生效
SELECT 
    tablename,
    policyname,
    cmd,
    permissive
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('user_profiles', 'project_members')
ORDER BY tablename, policyname;

-- 完成提示
SELECT 'Quick RLS fix applied successfully!' as result;
