// 示例数据文件 - 《大模型技术与油气应用概论》
const sampleData = {
    title: "《大模型技术与油气应用概论》",
    outline: [
        {
            id: 'ch0',
            title: '第0章 前言',
            level: 0,
            children: [
                { 
                    id: 'ch0-1', 
                    title: '0.1 人工智能发展与大模型时代', 
                    level: 1,
                    summary: '介绍人工智能发展的主要阶段和大模型时代的特征'
                },
                { 
                    id: 'ch0-2', 
                    title: '0.2 大模型技术的油气应用', 
                    level: 1,
                    summary: '分析大模型技术在油气行业的应用现状和挑战'
                },
                { 
                    id: 'ch0-3', 
                    title: '0.3 本书编写目的与基本结构', 
                    level: 1,
                    summary: '说明本书的编写目的、目标读者和章节结构'
                }
            ]
        },
        {
            id: 'part1',
            title: '【第一篇 理论技术篇】',
            level: 0,
            children: [
                {
                    id: 'ch1',
                    title: '第1章 大模型基本概念与内涵',
                    level: 1,
                    children: [
                        { 
                            id: 'ch1-1', 
                            title: '1.1 人工智能进入大模型时代', 
                            level: 2,
                            summary: '分析人工智能演进阶段和大模型时代的主要特征'
                        },
                        { 
                            id: 'ch1-2', 
                            title: '1.2 大模型的定义与类型', 
                            level: 2,
                            summary: '定义大模型概念，介绍不同类型的大模型'
                        },
                        { 
                            id: 'ch1-3', 
                            title: '1.3 大模型能力的内涵', 
                            level: 2,
                            summary: '探讨扩展法则、涌现能力和价值对齐等核心概念'
                        },
                        { 
                            id: 'ch1-4', 
                            title: '1.4 大模型的优势与挑战', 
                            level: 2,
                            summary: '分析大模型技术的优势和面临的挑战'
                        }
                    ]
                },
                {
                    id: 'ch2',
                    title: '第2章 大模型架构与关键技术',
                    level: 1,
                    children: [
                        { 
                            id: 'ch2-1', 
                            title: '2.1 Transformer基础模型', 
                            level: 2,
                            summary: '介绍Transformer架构的基本原理和关键技术'
                        },
                        { 
                            id: 'ch2-2', 
                            title: '2.2 编码器架构模型（Encoder-Only）', 
                            level: 2,
                            summary: '分析编码器架构模型的特点和应用'
                        },
                        { 
                            id: 'ch2-3', 
                            title: '2.3 解码器架构模型（Decoder-Only）', 
                            level: 2,
                            summary: '探讨解码器架构模型的技术特点'
                        },
                        { 
                            id: 'ch2-4', 
                            title: '2.4 编码器-解码器架构模型', 
                            level: 2,
                            summary: '介绍编码器-解码器架构的设计和应用'
                        },
                        { 
                            id: 'ch2-5', 
                            title: '2.5 混合架构模型（MoE）', 
                            level: 2,
                            summary: '分析混合专家模型的架构和技术特点'
                        }
                    ]
                }
            ]
        },
        {
            id: 'part2',
            title: '【第二篇 应用模式篇】',
            level: 0,
            children: [
                {
                    id: 'ch5',
                    title: '第5章 大模型提示工程与思维链',
                    level: 1,
                    children: [
                        { 
                            id: 'ch5-1', 
                            title: '5.1 大模型提示工程应用', 
                            level: 2,
                            summary: '介绍提示工程的基本方法和技术关键'
                        },
                        { 
                            id: 'ch5-2', 
                            title: '5.2 大模型思维链应用', 
                            level: 2,
                            summary: '探讨思维链技术的应用模式'
                        }
                    ]
                }
            ]
        },
        {
            id: 'part3',
            title: '【第三篇 专业实践篇】',
            level: 0,
            children: [
                {
                    id: 'ch9',
                    title: '第9章 油气大模型应用与新质生产力',
                    level: 1,
                    children: [
                        { 
                            id: 'ch9-1', 
                            title: '9.1 工业大模型体系架构', 
                            level: 2,
                            summary: '分析工业大模型的体系架构设计'
                        },
                        { 
                            id: 'ch9-2', 
                            title: '9.2 油气大模型的特点与实践', 
                            level: 2,
                            summary: '探讨油气行业大模型的特殊性和实践案例'
                        }
                    ]
                }
            ]
        }
    ],
    chapters: {
        'ch0-1': {
            title: '0.1 人工智能发展与大模型时代',
            summary: '介绍人工智能发展的主要阶段和大模型时代的特征',
            content: {
                ops: [
                    { insert: '# 0.1 人工智能发展与大模型时代\n\n' },
                    { insert: '## 人工智能发展的主要阶段\n\n' },
                    { insert: '人工智能的发展可以分为以下几个主要阶段：\n\n' },
                    { insert: '1. ', attributes: { bold: true } },
                    { insert: '符号主义阶段（1950s-1980s）\n' },
                    { insert: '   - 基于逻辑推理和知识表示\n' },
                    { insert: '   - 专家系统的兴起\n\n' },
                    { insert: '2. ', attributes: { bold: true } },
                    { insert: '机器学习阶段（1980s-2010s）\n' },
                    { insert: '   - 统计学习方法\n' },
                    { insert: '   - 支持向量机、随机森林等算法\n\n' },
                    { insert: '3. ', attributes: { bold: true } },
                    { insert: '深度学习阶段（2010s-2020s）\n' },
                    { insert: '   - 神经网络的复兴\n' },
                    { insert: '   - CNN、RNN等架构的突破\n\n' },
                    { insert: '4. ', attributes: { bold: true } },
                    { insert: '大模型时代（2020s至今）\n' },
                    { insert: '   - Transformer架构的革命性影响\n' },
                    { insert: '   - 大规模预训练模型的涌现\n\n' },
                    { insert: '## AI2.0时代的特征\n\n' },
                    { insert: '大模型时代，也被称为AI2.0时代，具有以下显著特征：\n\n' },
                    { insert: '• ', attributes: { bold: true } },
                    { insert: '规模化：模型参数量达到数十亿甚至万亿级别\n' },
                    { insert: '• ', attributes: { bold: true } },
                    { insert: '通用性：单一模型可以处理多种任务\n' },
                    { insert: '• ', attributes: { bold: true } },
                    { insert: '涌现性：展现出训练时未明确设计的能力\n' },
                    { insert: '• ', attributes: { bold: true } },
                    { insert: '交互性：支持自然语言交互和对话\n\n' },
                    { insert: '## 大模型时代的主要标志\n\n' },
                    { insert: '大模型时代的到来有以下几个重要标志：\n\n' },
                    { insert: '1. GPT系列模型的突破性进展\n' },
                    { insert: '2. Transformer架构成为主流\n' },
                    { insert: '3. 预训练-微调范式的确立\n' },
                    { insert: '4. 多模态能力的实现\n' },
                    { insert: '5. 商业化应用的广泛部署\n' }
                ]
            },
            lastModified: new Date().toISOString()
        }
    },
    references: [
        {
            id: '1',
            title: 'Attention Is All You Need',
            authors: 'Vaswani, A., Shazeer, N., Parmar, N., et al.',
            source: 'Advances in Neural Information Processing Systems',
            year: '2017',
            url: 'https://arxiv.org/abs/1706.03762'
        },
        {
            id: '2',
            title: 'Language Models are Few-Shot Learners',
            authors: 'Brown, T., Mann, B., Ryder, N., et al.',
            source: 'Advances in Neural Information Processing Systems',
            year: '2020',
            url: 'https://arxiv.org/abs/2005.14165'
        },
        {
            id: '3',
            title: '大模型落地路线图',
            authors: '中国信息通信研究院',
            source: '中国信通院',
            year: '2023',
            url: ''
        },
        {
            id: '4',
            title: '人工智能大模型体系架构',
            authors: '腾讯研究院',
            source: '腾讯研究院报告',
            year: '2023',
            url: ''
        }
    ],
    diagrams: {
        'ai-evolution': {
            type: 'flowchart',
            title: 'AI发展演进图',
            code: `flowchart LR
    A[符号主义AI] --> B[机器学习]
    B --> C[深度学习]
    C --> D[大模型时代]
    
    A1[专家系统<br/>知识图谱] --> A
    B1[SVM<br/>随机森林] --> B
    C1[CNN<br/>RNN<br/>LSTM] --> C
    D1[Transformer<br/>GPT<br/>BERT] --> D`
        },
        'llm-architecture': {
            type: 'architecture',
            title: '大模型技术架构',
            code: `graph TB
    subgraph "应用层"
        A1[智能问答]
        A2[内容生成]
        A3[代码生成]
        A4[多模态理解]
    end
    
    subgraph "模型层"
        B1[大语言模型]
        B2[多模态模型]
        B3[代码模型]
        B4[专业模型]
    end
    
    subgraph "训练层"
        C1[预训练]
        C2[指令微调]
        C3[强化学习]
        C4[对齐训练]
    end
    
    subgraph "数据层"
        D1[文本数据]
        D2[多模态数据]
        D3[代码数据]
        D4[专业数据]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B3
    A4 --> B2
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4`
        }
    },
    progress: {
        totalChapters: 12,
        completedChapters: 1,
        lastUpdate: new Date().toISOString()
    }
};

// 导出示例数据
if (typeof module !== 'undefined' && module.exports) {
    module.exports = sampleData;
}
