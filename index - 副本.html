<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业著作智能编纂系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/quill/1.3.7/quill.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/quill/1.3.7/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="header-left">
                <div class="brand">
                    <i class="fas fa-feather-alt brand-icon"></i>
                    <h1 class="brand-title">书籍智能编纂系统</h1>
                </div>
                <div class="project-info" id="project-info" style="display: none;">
                    <span class="project-name" id="current-project-name">未选择项目</span>
                </div>
            </div>
            <div class="header-center">
                <div class="online-users" id="online-users">
                    <!-- 在线用户列表 -->
                </div>
            </div>
            <div class="header-right">
                <div class="user-menu" id="user-menu" style="display: none;">
                    <div class="user-avatar" onclick="toggleUserDropdown()">
                        <img id="user-avatar" src="" alt="用户头像">
                        <span id="user-name">用户</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="user-dropdown" id="user-dropdown">
                        <a href="#" onclick="showUserProfile()"><i class="fas fa-user"></i> 个人资料</a>
                        <a href="#" onclick="showProjectSettings()"><i class="fas fa-cog"></i> 项目设置</a>
                        <a href="#" onclick="showUserManagement()"><i class="fas fa-users"></i> 用户管理</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" onclick="signOut()"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                    </div>
                </div>
                <button class="btn btn-icon" onclick="showHelp()" title="帮助">
                    <i class="fas fa-question-circle"></i>
                </button>
                <button class="btn btn-icon" onclick="showPanel('settings')" title="系统设置">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧边栏 - 功能导航 -->
            <aside class="sidebar" id="sidebar">
                <!-- 侧边栏头部 -->
                <div class="sidebar-header">
                    <div class="logo">
                        <i class="logo-icon fas fa-feather-alt"></i>
                        <span class="logo-text">书籍编纂</span>
                    </div>
                    <button class="toggle-btn" id="toggleBtn">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <!-- 导航菜单 -->
                <nav class="nav-menu">
                    <div class="nav-item">
                        <a href="#" class="nav-link active" data-tab="overview">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <span class="nav-text">项目概览</span>
                        </a>
                        <div class="tooltip">项目概览</div>
                    </div>

                    <div class="nav-item">
                        <a href="#" class="nav-link" data-tab="outline">
                            <i class="nav-icon fas fa-book-open"></i>
                            <span class="nav-text">书籍目录</span>
                        </a>
                        <div class="tooltip">书籍目录</div>
                    </div>

                    <div class="nav-item">
                        <a href="#" class="nav-link" data-tab="editor">
                            <i class="nav-icon fas fa-edit"></i>
                            <span class="nav-text">章节编写</span>
                        </a>
                        <div class="tooltip">章节编写</div>
                    </div>

                    <div class="nav-item">
                        <a href="#" class="nav-link" data-tab="references">
                            <i class="nav-icon fas fa-bookmark"></i>
                            <span class="nav-text">文献管理</span>
                        </a>
                        <div class="tooltip">文献管理</div>
                    </div>

                    <div class="nav-item">
                        <a href="#" class="nav-link" data-tab="collaboration">
                            <i class="nav-icon fas fa-users"></i>
                            <span class="nav-text">协作编著</span>
                        </a>
                        <div class="tooltip">协作编著</div>
                    </div>

                    <div class="nav-item">
                        <a href="#" class="nav-link" data-tab="settings">
                            <i class="nav-icon fas fa-cog"></i>
                            <span class="nav-text">系统设置</span>
                        </a>
                        <div class="tooltip">系统设置</div>
                    </div>

                    <!-- 数据库管理分隔线 -->
                    <div class="nav-divider"></div>

                    <div class="nav-item">
                        <a href="#" class="nav-link nav-admin" onclick="openDatabaseCleanupTool()">
                            <i class="nav-icon fas fa-database"></i>
                            <span class="nav-text">数据库管理</span>
                        </a>
                        <div class="tooltip">数据库管理</div>
                    </div>
                </nav>
            </aside>

            <!-- 中间内容区域 -->
            <main class="content-area">
                <!-- 项目概览面板 -->
                <div id="overview-panel" class="panel active">
                    <div class="panel-header">
                        <div class="panel-header-left">
                            <h2><i class="fas fa-tachometer-alt"></i> 项目概览</h2>
                            <!-- 全新项目选择器 -->
                            <div class="project-selector-inline" id="project-selector-inline">
                                <button class="project-btn-inline" id="project-btn-inline">
                                    <div class="project-btn-content">
                                        <i class="fas fa-folder project-btn-icon"></i>
                                        <span class="project-btn-text" id="selected-project-inline">选择项目</span>
                                    </div>
                                    <i class="fas fa-chevron-down project-btn-arrow"></i>
                                </button>
                                <div class="project-list" id="project-list-inline">
                                    <div class="project-list-content">
                                        <div class="project-item create-new" onclick="showCreateProject()">
                                            <i class="fas fa-plus project-item-icon"></i>
                                            <span class="project-item-text">创建新项目</span>
                                        </div>
                                        <div class="project-divider"></div>
                                        <!-- 项目列表将在这里动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel-actions">
                            <button class="btn btn-sm btn-primary" onclick="goToProjectManagement()">
                                <i class="fas fa-folder-open"></i> 项目管理
                            </button>
                        </div>
                    </div>
                    <div class="dashboard-container">
                        <!-- 项目基本信息 -->
                        <div class="project-overview">
                            <div class="project-header">
                                <div class="project-title">
                                    <h3 id="dashboard-project-title">《大模型技术与油气应用概论》</h3>
                                    <p id="dashboard-project-description">面向大学生的大模型技术教材</p>
                                </div>
                                <div class="project-actions">
                                    <div class="project-status-container">
                                        <div class="project-status active" id="dashboard-project-status-indicator" title="项目状态：进行中"></div>
                                    </div>
                                    <div class="project-tools">
                                        <button class="btn btn-icon btn-sm" onclick="editProjectInfo()" title="编辑项目信息">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-icon btn-sm" onclick="shareProject()" title="分享项目">
                                            <i class="fas fa-share-alt"></i>
                                        </button>
                                        <button class="btn btn-icon btn-sm" onclick="projectSettings()" title="项目设置">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 总体进度 -->
                        <div class="progress-overview">
                            <div class="progress-header">
                                <h4><i class="fas fa-chart-line"></i> 总体进度</h4>
                                <span class="progress-percentage" id="overall-progress-text">0%</span>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="overall-progress-bar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="progress-details">
                                <div class="progress-item">
                                    <span class="progress-label">已完成章节</span>
                                    <span class="progress-value" id="completed-chapters-count">0</span>
                                </div>
                                <div class="progress-item">
                                    <span class="progress-label">总章节数</span>
                                    <span class="progress-value" id="total-chapters-count">0</span>
                                </div>
                                <div class="progress-item">
                                    <span class="progress-label">总字数</span>
                                    <span class="progress-value" id="total-word-count">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="dashboard-stats">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-edit"></i></div>
                                <div class="stat-info">
                                    <h3 id="writing-chapters">0</h3>
                                    <p>编写中</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-eye"></i></div>
                                <div class="stat-info">
                                    <h3 id="review-chapters">0</h3>
                                    <p>待审阅</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-users"></i></div>
                                <div class="stat-info">
                                    <h3 id="team-members-count">0</h3>
                                    <p>团队成员</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                                <div class="stat-info">
                                    <h3 id="days-remaining">--</h3>
                                    <p>剩余天数</p>
                                </div>
                            </div>
                        </div>

                        <!-- 章节进度详情 -->
                        <div class="chapter-progress-section">
                            <div class="section-header">
                                <h4><i class="fas fa-list-check"></i> 章节进度</h4>
                                <button class="btn btn-sm btn-outline" onclick="showChapterProgress()">
                                    <i class="fas fa-expand"></i> 详细视图
                                </button>
                            </div>
                            <div class="chapter-progress-list" id="chapter-progress-list">
                                <!-- 章节进度列表 -->
                            </div>
                        </div>

                        <!-- 仪表板内容区域 -->
                        <div class="dashboard-content">
                            <div class="dashboard-section">
                                <div class="section-header">
                                    <h4><i class="fas fa-history"></i> 最近活动</h4>
                                    <a href="#" onclick="showAllActivities()" class="view-all-link">查看全部</a>
                                </div>
                                <div class="activity-list" id="recent-activities-list">
                                    <!-- 最近活动列表 -->
                                </div>
                            </div>

                            <div class="dashboard-section">
                                <div class="section-header">
                                    <h4><i class="fas fa-tasks"></i> 我的任务</h4>
                                    <a href="#" onclick="showAllTasks()" class="view-all-link">查看全部</a>
                                </div>
                                <div class="task-list" id="my-tasks-list">
                                    <!-- 我的任务列表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 本书目录面板 -->
                <div id="outline-panel" class="panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-book-open"></i> 本书目录</h2>
                        <div class="panel-actions">
                            <button class="btn btn-sm btn-primary" onclick="addOutlineItem()">
                                <i class="fas fa-plus"></i> 添加章节
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="importOutline()">
                                <i class="fas fa-upload"></i> 导入大纲
                            </button>
                            <button class="btn btn-sm btn-success" onclick="generateAIOutline()">
                                <i class="fas fa-magic"></i> AI生成大纲
                            </button>
                        </div>
                    </div>
                    <div class="outline-container">
                        <div class="outline-tree" id="outline-tree">
                            <!-- 大纲树结构将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 章节编写面板 -->
                <div id="editor-panel" class="panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-edit"></i> 章节编写</h2>
                        <div class="panel-actions">
                            <select id="chapter-selector" onchange="loadChapter()">
                                <option value="">选择章节</option>
                            </select>
                            <button class="btn btn-sm btn-primary" onclick="saveChapter()">
                                <i class="fas fa-save"></i> 保存
                            </button>
                        </div>
                    </div>
                    <div class="chapter-editor">
                        <div class="chapter-meta">
                            <input type="text" id="chapter-title" placeholder="章节标题" class="form-input">
                            <textarea id="chapter-summary" placeholder="章节摘要" class="form-textarea"></textarea>
                        </div>
                        <div class="editor-wrapper">
                            <div id="chapter-content-editor" class="editor-container">
                                <!-- Quill编辑器将在这里初始化 -->
                            </div>

                            <!-- AI助手悬浮按钮 -->
                            <div id="ai-assistant-fab" class="ai-fab">
                                <button class="fab-main-btn" title="AI助手">
                                    <i class="fas fa-magic"></i>
                                </button>
                                <div class="fab-menu" id="ai-fab-menu">
                                    <button class="fab-menu-item" data-action="polish" title="内容润色">
                                        <i class="fas fa-magic"></i>
                                        <span>润色</span>
                                    </button>
                                    <button class="fab-menu-item" data-action="translate" title="翻译">
                                        <i class="fas fa-language"></i>
                                        <span>翻译</span>
                                    </button>
                                    <button class="fab-menu-item" data-action="explain" title="解读">
                                        <i class="fas fa-lightbulb"></i>
                                        <span>解读</span>
                                    </button>
                                    <button class="fab-menu-item" data-action="rewrite" title="重写">
                                        <i class="fas fa-edit"></i>
                                        <span>重写</span>
                                    </button>
                                    <div class="fab-menu-divider"></div>
                                    <button class="fab-menu-item" data-action="generate-image" title="文生图">
                                        <i class="fas fa-image"></i>
                                        <span>文生图</span>
                                    </button>
                                    <button class="fab-menu-item" data-action="text-to-speech" title="文本播放">
                                        <i class="fas fa-volume-up"></i>
                                        <span>文本播放</span>
                                    </button>
                                    <button class="fab-menu-item" data-action="speech-to-text" title="语音转文字">
                                        <i class="fas fa-microphone"></i>
                                        <span>语音转文字</span>
                                    </button>
                                    <button class="fab-menu-item" data-action="analyze-image" title="图片识别">
                                        <i class="fas fa-eye"></i>
                                        <span>图片识别</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 参考文献面板 -->
                <div id="references-panel" class="panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-bookmark"></i> 参考文献管理</h2>
                        <div class="panel-actions">
                            <button class="btn btn-sm btn-primary" onclick="addReference()">
                                <i class="fas fa-plus"></i> 添加文献
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="importReferences()">
                                <i class="fas fa-upload"></i> 导入文献
                            </button>
                        </div>
                    </div>
                    <div class="references-container">
                        <div class="references-list" id="references-list">
                            <!-- 参考文献列表 -->
                        </div>
                    </div>
                </div>

                <!-- 协作管理面板 -->
                <div id="collaboration-panel" class="panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-users"></i> 协作管理</h2>
                        <div class="panel-actions">
                            <button class="btn btn-sm btn-primary" onclick="inviteUser()">
                                <i class="fas fa-user-plus"></i> 邀请用户
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="managePermissions()">
                                <i class="fas fa-key"></i> 权限管理
                            </button>
                        </div>
                    </div>
                    <div class="collaboration-container">
                        <div class="collaboration-tabs">
                            <div class="collab-tab active" data-tab="members">团队成员</div>
                            <div class="collab-tab" data-tab="assignments">章节分配</div>
                            <div class="collab-tab" data-tab="permissions">权限设置</div>
                        </div>

                        <!-- 团队成员 -->
                        <div id="members-tab" class="collab-content active">
                            <div class="members-list" id="team-members-list">
                                <!-- 团队成员列表 -->
                            </div>
                        </div>

                        <!-- 章节分配 -->
                        <div id="assignments-tab" class="collab-content">
                            <div class="assignments-list" id="chapter-assignments">
                                <!-- 章节分配列表 -->
                            </div>
                        </div>

                        <!-- 权限设置 -->
                        <div id="permissions-tab" class="collab-content">
                            <div class="permissions-matrix" id="permissions-matrix">
                                <!-- 权限矩阵 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置面板 -->
                <div id="settings-panel" class="panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-cog"></i> 系统设置</h2>
                        <div class="panel-actions">
                            <button class="btn btn-sm btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </div>
                    <div class="settings-container">
                        <!-- AI服务配置 -->
                        <div class="settings-section">
                            <h3><i class="fas fa-robot"></i> AI服务配置</h3>
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label for="ai-provider">服务商：</label>
                                    <select id="ai-provider" class="form-select">
                                        <option value="openrouter">OpenRouter</option>
                                        <option value="openai">OpenAI</option>
                                        <option value="anthropic">Anthropic</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label for="ai-api-url">API地址：</label>
                                    <input type="text" id="ai-api-url" class="form-input"
                                           value="https://openrouter.ai/api/v1/chat/completions" readonly>
                                </div>
                                <div class="setting-item">
                                    <label for="ai-api-key">API密钥：</label>
                                    <div class="input-group">
                                        <input type="password" id="ai-api-key" class="form-input"
                                               placeholder="输入API密钥">
                                        <button type="button" class="btn-icon" onclick="toggleApiKeyVisibility()">
                                            <i class="fas fa-eye" id="api-key-toggle"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <label for="ai-model">默认模型：</label>
                                    <select id="ai-model" class="form-select">
                                        <option value="deepseek/deepseek-chat-v3-0324:free">DeepSeek Chat V3 (Free)</option>
                                        <option value="meta-llama/llama-3.2-3b-instruct:free">Llama 3.2 3B (Free)</option>
                                        <option value="microsoft/phi-3-mini-128k-instruct:free">Phi-3 Mini (Free)</option>
                                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                        <option value="gpt-4">GPT-4</option>
                                    </select>
                                </div>
                            </div>
                            <div class="setting-actions">
                                <button class="btn btn-secondary" onclick="testAIConnection()">
                                    <i class="fas fa-flask"></i> 测试连接
                                </button>
                                <button class="btn btn-info" onclick="resetToDefaults()">
                                    <i class="fas fa-undo"></i> 恢复默认
                                </button>
                            </div>
                        </div>

                        <!-- Pollinations多媒体服务配置 -->
                        <div class="settings-section">
                            <h3><i class="fas fa-images"></i> 多媒体服务配置</h3>
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label for="pollinations-api-key">API密钥（可选）：</label>
                                    <input type="password" id="pollinations-api-key" class="form-input"
                                           placeholder="输入Pollinations API密钥">
                                    <small class="setting-help">API密钥可提高请求限制和优先级</small>
                                </div>
                                <div class="setting-item">
                                    <label for="pollinations-referrer">引用标识：</label>
                                    <input type="text" id="pollinations-referrer" class="form-input"
                                           value="llm-book-system" placeholder="应用标识">
                                    <small class="setting-help">用于标识请求来源</small>
                                </div>
                                <div class="setting-item">
                                    <label for="default-image-model">默认图片模型：</label>
                                    <select id="default-image-model" class="form-select">
                                        <option value="flux">Flux (推荐)</option>
                                        <option value="turbo">Turbo (快速)</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label for="default-voice">默认语音：</label>
                                    <select id="default-voice" class="form-select">
                                        <option value="nova">Nova (女声)</option>
                                        <option value="alloy">Alloy (中性)</option>
                                        <option value="echo">Echo (男声)</option>
                                        <option value="fable">Fable (英式)</option>
                                        <option value="onyx">Onyx (深沉)</option>
                                        <option value="shimmer">Shimmer (温和)</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label for="request-timeout">请求超时（秒）：</label>
                                    <input type="number" id="request-timeout" class="form-input"
                                           value="300" min="30" max="600">
                                    <small class="setting-help">多媒体处理可能需要较长时间</small>
                                </div>
                                <div class="setting-item">
                                    <label for="max-retries">最大重试次数：</label>
                                    <input type="number" id="max-retries" class="form-input"
                                           value="3" min="0" max="10">
                                </div>
                            </div>
                            <div class="setting-actions">
                                <button class="btn btn-secondary" onclick="testPollinationsConnection()">
                                    <i class="fas fa-plug"></i> 测试连接
                                </button>
                                <button class="btn btn-outline" onclick="resetPollinationsSettings()">
                                    <i class="fas fa-undo"></i> 重置为默认
                                </button>
                            </div>
                        </div>

                        <!-- 编辑器配置 -->
                        <div class="settings-section">
                            <h3><i class="fas fa-edit"></i> 编辑器配置</h3>
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label for="auto-save-interval">自动保存间隔（秒）：</label>
                                    <input type="number" id="auto-save-interval" class="form-input"
                                           value="2" min="1" max="60">
                                </div>
                                <div class="setting-item">
                                    <label for="editor-theme">编辑器主题：</label>
                                    <select id="editor-theme" class="form-select">
                                        <option value="snow">Snow (默认)</option>
                                        <option value="bubble">Bubble</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 系统配置 -->
                        <div class="settings-section">
                            <h3><i class="fas fa-cogs"></i> 系统配置</h3>
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" id="enable-notifications" checked>
                                        启用系统通知
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" id="enable-auto-backup" checked>
                                        启用自动备份
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label>
                                        <input type="checkbox" id="enable-ai-suggestions">
                                        启用AI写作建议
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 测试结果显示区域 -->
                        <div id="test-results" class="test-results" style="display: none;">
                            <!-- 测试结果将在这里显示 -->
                        </div>
                    </div>
                </div>

            </main>

            <!-- 右侧工具栏 -->
            <aside class="right-toolbar" id="rightToolbar">
                <!-- 拖拽调整宽度手柄 -->
                <div class="resize-handle" id="resizeHandle"></div>

                <!-- 工具栏头部 -->
                <div class="right-toolbar-header">
                    <div class="toolbar-title">
                        <span class="toolbar-icon" onclick="openToolManagement()" style="cursor: pointer;" title="点击管理工具">🛠️</span>
                        <span class="toolbar-title-text">工具集</span>
                    </div>
                    <button class="right-toggle-btn" id="rightToggleBtn" onclick="toggleRightToolbar()">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- 工具选择器 -->
                <div class="tool-selector">
                    <div class="tool-select-wrapper">
                        <select class="tool-select" id="toolSelect" onchange="loadTool()">
                            <option value="">选择工具...</option>
                        </select>
                    </div>
                </div>

                <!-- 工具信息 -->
                <div class="tool-info" id="toolInfo">
                    选择一个工具开始使用
                </div>

                <!-- iframe容器 -->
                <div class="tool-iframe-container">
                    <iframe class="tool-iframe" id="toolIframe" src="about:blank"></iframe>
                </div>

                <!-- 收缩状态的工具图标 -->
                <div class="collapsed-tools" id="collapsedTools">
                    <!-- 动态生成工具图标 -->
                </div>
            </aside>
        </div>
    </div>

    <!-- 移动端遮罩 -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <!-- 工具管理模态框 -->
    <div class="tool-management-modal" id="toolManagementModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">工具管理</h3>
                <button class="modal-close" onclick="closeToolManagement()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 添加工具表单 -->
                <div class="tool-form">
                    <div class="form-group">
                        <label class="form-label">工具名称</label>
                        <input type="text" class="form-input" id="toolName" placeholder="输入工具名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">工具URL</label>
                        <input type="url" class="form-input" id="toolUrl" placeholder="输入工具URL地址">
                    </div>
                    <div class="form-group">
                        <label class="form-label">工具介绍</label>
                        <textarea class="form-textarea" id="toolDescription" placeholder="输入工具介绍"></textarea>
                    </div>
                    <div class="form-actions">
                        <button class="btn-secondary" onclick="clearForm()">清空</button>
                        <button class="btn-primary" onclick="saveTool()">保存工具</button>
                    </div>
                </div>

                <!-- 工具列表 -->
                <table class="tools-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>URL</th>
                            <th>介绍</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="toolsTableBody">
                        <!-- 动态生成工具列表 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div id="modal-container" class="modal-container">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div id="modal-content" class="modal-content">
                <!-- 模态对话框内容 -->
            </div>
            <!-- 保持向后兼容 -->
            <div class="modal-body" id="modal-body">
                <!-- 模态对话框内容 -->
            </div>
            <div class="modal-footer">
                <div class="modal-actions">
                    <!-- 按钮将通过JavaScript动态生成 -->
                </div>
                <div id="modal-buttons" class="modal-buttons">
                    <!-- AI助手按钮将在这里生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <script src="supabase-config.js"></script>
    <script src="ai-service.js"></script>
    <script src="pollinations-service.js"></script>
    <script src="multimedia-handlers.js"></script>
    <script src="app.js"></script>
    <script src="collaboration.js"></script>
</body>
</html>
