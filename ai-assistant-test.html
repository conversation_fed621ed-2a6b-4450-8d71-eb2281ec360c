<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        .test-header p {
            margin: 0;
            opacity: 0.9;
        }
        
        .test-content {
            padding: 2rem;
        }
        
        .editor-section {
            margin-bottom: 2rem;
        }
        
        .editor-section h3 {
            margin: 0 0 1rem 0;
            color: #374151;
        }
        
        .editor-wrapper {
            position: relative;
        }

        .test-editor {
            border: 1px solid #d1d5db;
            border-radius: 8px;
            min-height: 300px;
        }
        
        .instructions {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .instructions h4 {
            margin: 0 0 1rem 0;
            color: #0369a1;
        }
        
        .instructions ol {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
            color: #374151;
        }
        
        .sample-content {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .sample-content h5 {
            margin: 0 0 0.5rem 0;
            color: #92400e;
        }
        
        .load-sample-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        
        .load-sample-btn:hover {
            background: #2563eb;
        }

        /* 通知样式 */
        #notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .notification {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 1rem;
            min-width: 300px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideIn 0.3s ease;
        }

        .notification-success {
            border-left: 4px solid #10b981;
        }

        .notification-error {
            border-left: 4px solid #ef4444;
        }

        .notification-warning {
            border-left: 4px solid #f59e0b;
        }

        .notification-info {
            border-left: 4px solid #3b82f6;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .notification-close {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.25rem;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .modal-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            max-width: 90vw;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header h3 {
            margin: 0;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 6px;
        }

        .modal-close:hover {
            background: #f3f4f6;
        }

        .modal-content {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .modal-buttons {
            display: flex;
            gap: 0.75rem;
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
            justify-content: flex-end;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-magic"></i> AI助手功能测试</h1>
            <p>测试内容润色、翻译、解读、重写等AI功能</p>
        </div>
        
        <div class="test-content">
            <div class="instructions">
                <h4><i class="fas fa-info-circle"></i> 使用说明</h4>
                <ol>
                    <li>在下方编辑器中输入或加载示例内容</li>
                    <li>选中要处理的文本（或不选择处理全部内容）</li>
                    <li>点击右上角的AI助手悬浮按钮</li>
                    <li>选择要执行的操作：润色、翻译、解读或重写</li>
                    <li>在弹出的对话框中输入具体要求</li>
                    <li>等待AI处理完成后，可以选择应用结果</li>
                </ol>
            </div>
            
            <div class="sample-content">
                <h5><i class="fas fa-lightbulb"></i> 示例内容</h5>
                <p>点击下方按钮加载不同类型的示例文本进行测试：</p>
                <button class="load-sample-btn" onclick="loadSample('academic')">学术文本</button>
                <button class="load-sample-btn" onclick="loadSample('technical')">技术文档</button>
                <button class="load-sample-btn" onclick="loadSample('business')">商务文档</button>
                <button class="load-sample-btn" onclick="loadSample('creative')">创意写作</button>
            </div>
            
            <div class="editor-section">
                <h3><i class="fas fa-edit"></i> 文本编辑器</h3>
                <div class="editor-wrapper">
                    <div id="test-editor" class="test-editor"></div>
                    <!-- AI助手悬浮按钮 -->
                    <div id="ai-assistant-fab" class="ai-fab">
                        <button class="fab-main-btn" title="AI助手">
                            <i class="fas fa-magic"></i>
                        </button>
                        <div class="fab-menu" id="ai-fab-menu">
                            <button class="fab-menu-item" data-action="polish" title="内容润色">
                                <i class="fas fa-magic"></i>
                                <span>润色</span>
                            </button>
                            <button class="fab-menu-item" data-action="translate" title="翻译">
                                <i class="fas fa-language"></i>
                                <span>翻译</span>
                            </button>
                            <button class="fab-menu-item" data-action="explain" title="解读">
                                <i class="fas fa-lightbulb"></i>
                                <span>解读</span>
                            </button>
                            <button class="fab-menu-item" data-action="rewrite" title="重写">
                                <i class="fas fa-edit"></i>
                                <span>重写</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div id="modal-container" class="modal-container">
            <div class="modal-header">
                <h3 id="modal-title"></h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modal-content" class="modal-content"></div>
            <div id="modal-buttons" class="modal-buttons"></div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="ai-service.js"></script>
    <script>
        // 全局变量
        let quillEditor = null;
        let currentChapter = null;

        // 示例内容
        const sampleTexts = {
            academic: `大模型（Large Language Model, LLM）是指参数量达到数十亿甚至数千亿级别的深度学习模型，通常基于Transformer架构构建。这些模型通过在大规模文本数据上进行预训练，能够理解和生成人类语言，展现出强大的语言理解和生成能力。

大模型的核心特点包括：规模庞大的参数量、强大的泛化能力、涌现性能力以及多任务处理能力。随着模型规模的不断扩大，大模型在各种自然语言处理任务中都表现出了前所未有的性能。`,

            technical: `在石油勘探开发过程中，大模型技术可以应用于多个关键环节。首先，在地震数据解释方面，大模型能够自动识别地层结构、断层系统和储层特征，大幅提高解释效率和准确性。

其次，在钻井工程中，大模型可以实时分析钻井参数，预测钻井风险，优化钻井轨迹，从而降低钻井成本并提高钻井成功率。此外，大模型还能够处理生产数据，进行产量预测和设备故障诊断。`,

            business: `随着人工智能技术的快速发展，我公司计划在未来三年内投资5000万元用于大模型技术的研发和应用。该项目将分为三个阶段实施：第一阶段为技术调研和团队建设，第二阶段为核心算法开发和平台搭建，第三阶段为产品化和市场推广。

预计该项目将为公司带来显著的竞争优势，提升业务效率30%以上，并在行业内建立技术领先地位。`,

            creative: `在那个充满可能性的时代，人工智能就像一位神秘的魔法师，用数据和算法编织着未来的蓝图。大模型如同一座知识的宝库，储存着人类文明的智慧结晶，等待着被唤醒和释放。

当我们与这些智能助手对话时，仿佛在与未来的自己交流，探索着无限的创意空间。每一次交互都是一场思维的碰撞，每一个回答都可能开启新的思路。`
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeTestEditor();
            setupAIAssistantListeners();
        });

        // 初始化测试编辑器
        function initializeTestEditor() {
            const editorContainer = document.getElementById('test-editor');
            
            quillEditor = new Quill(editorContainer, {
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['clean']
                    ]
                },
                theme: 'snow',
                placeholder: '在此输入要测试的文本内容...'
            });

            // 加载默认示例
            loadSample('academic');
        }

        // 加载示例内容
        function loadSample(type) {
            if (quillEditor && sampleTexts[type]) {
                quillEditor.setText(sampleTexts[type]);
                showNotification(`已加载${type === 'academic' ? '学术' : type === 'technical' ? '技术' : type === 'business' ? '商务' : '创意'}文本示例`, 'success');
            }
        }
    </script>
    
    <!-- 引入主应用的AI助手功能 -->
    <script>
        // ==================== 通知系统 ====================
        function showNotification(message, type = 'info', duration = 3000) {
            const container = document.getElementById('notification-container');
            if (!container) return;

            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }

        // ==================== 模态框系统 ====================
        function showModal(options) {
            const overlay = document.getElementById('modal-overlay');
            const container = document.getElementById('modal-container');
            const title = document.getElementById('modal-title');
            const content = document.getElementById('modal-content');
            const buttons = document.getElementById('modal-buttons');

            if (!overlay || !container || !title || !content || !buttons) return;

            title.textContent = options.title || '';
            content.innerHTML = options.content || '';

            // 设置自定义类名
            if (options.className) {
                container.className = `modal-container ${options.className}`;
            } else {
                container.className = 'modal-container';
            }

            // 创建按钮
            buttons.innerHTML = '';
            if (options.buttons) {
                options.buttons.forEach(btn => {
                    const button = document.createElement('button');
                    button.textContent = btn.text;
                    button.className = `btn ${btn.className || ''}`;
                    if (btn.id) button.id = btn.id;
                    if (btn.style) button.style.cssText = btn.style;
                    if (btn.onclick) button.onclick = btn.onclick;
                    buttons.appendChild(button);
                });
            }

            overlay.style.display = 'flex';
        }

        function closeModal() {
            const overlay = document.getElementById('modal-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // ==================== AI助手功能 ====================

        // 设置AI助手事件监听器
        function setupAIAssistantListeners() {
            // 监听AI助手菜单项点击
            document.addEventListener('click', function(e) {
                if (e.target.closest('.fab-menu-item')) {
                    const action = e.target.closest('.fab-menu-item').getAttribute('data-action');
                    handleAIAction(action);
                }
            });
        }

        // 处理AI操作
        async function handleAIAction(action) {
            // 检查编辑器是否存在
            if (!quillEditor) {
                showNotification('编辑器未初始化', 'warning');
                return;
            }

            // 获取选中的文本
            const selection = quillEditor.getSelection();
            let selectedText = '';

            if (selection && selection.length > 0) {
                selectedText = quillEditor.getText(selection.index, selection.length);
            } else {
                // 如果没有选中文本，获取全部内容
                selectedText = quillEditor.getText();
            }

            if (!selectedText.trim()) {
                showNotification('请先输入或选择要处理的文本内容', 'warning');
                return;
            }

            // 显示AI交互对话框
            showAIDialog(action, selectedText);
        }

        // 显示AI交互对话框
        function showAIDialog(action, selectedText) {
            const actionConfig = {
                polish: {
                    title: '内容润色',
                    description: '优化文本表达，提升可读性和流畅性',
                    icon: 'fas fa-magic',
                    placeholder: '请描述您的润色要求（可选）...'
                },
                translate: {
                    title: '内容翻译',
                    description: '将内容翻译为指定语言',
                    icon: 'fas fa-language',
                    placeholder: '请指定目标语言和特殊要求...'
                },
                explain: {
                    title: '内容解读',
                    description: '深入解析内容的含义和背景',
                    icon: 'fas fa-lightbulb',
                    placeholder: '请描述您希望了解的方面（可选）...'
                },
                rewrite: {
                    title: '内容重写',
                    description: '用不同风格重新组织和表达内容',
                    icon: 'fas fa-edit',
                    placeholder: '请指定重写风格和要求...'
                }
            };

            const config = actionConfig[action];
            if (!config) return;

            const dialogHTML = `
                <div class="ai-dialog-header">
                    <div class="ai-dialog-icon">
                        <i class="${config.icon}"></i>
                    </div>
                    <div class="ai-dialog-title">
                        <h3>${config.title}</h3>
                        <p>${config.description}</p>
                    </div>
                </div>
                <div class="ai-dialog-content">
                    <div class="ai-selected-text">
                        <h4>选中的内容：</h4>
                        <div class="ai-selected-text-content">${selectedText}</div>
                    </div>

                    <div class="ai-input-section">
                        <textarea class="ai-prompt-input" placeholder="${config.placeholder}"></textarea>

                        <div class="ai-file-upload">
                            <label>上传相关文件（可选）：</label>
                            <input type="file" class="ai-file-input" multiple accept="image/*,.pdf,.doc,.docx,.txt">
                            <div class="ai-file-preview"></div>
                        </div>
                    </div>

                    <div class="ai-response-section" style="display: none;">
                        <h4>AI处理结果：</h4>
                        <div class="ai-response-content"></div>
                    </div>
                </div>
            `;

            showModal({
                title: `AI助手 - ${config.title}`,
                content: dialogHTML,
                className: 'ai-dialog',
                buttons: [
                    {
                        text: '取消',
                        className: 'btn-secondary',
                        onclick: () => closeModal()
                    },
                    {
                        text: '开始处理',
                        className: 'btn-primary',
                        onclick: () => processAIRequest(action, selectedText)
                    },
                    {
                        text: '应用结果',
                        className: 'btn-success',
                        id: 'apply-ai-result',
                        style: 'display: none;',
                        onclick: () => applyAIResult()
                    }
                ]
            });

            // 设置文件上传监听器
            setupFileUploadListener();
        }

        // 设置文件上传监听器
        function setupFileUploadListener() {
            const fileInput = document.querySelector('.ai-file-input');
            const filePreview = document.querySelector('.ai-file-preview');

            if (!fileInput || !filePreview) return;

            fileInput.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                updateFilePreview(files, filePreview);
            });
        }

        // 更新文件预览
        function updateFilePreview(files, container) {
            container.innerHTML = '';

            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'ai-file-item';
                fileItem.innerHTML = `
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                    <button type="button" onclick="removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(fileItem);
            });
        }

        // 移除文件
        function removeFile(index) {
            const fileInput = document.querySelector('.ai-file-input');
            const filePreview = document.querySelector('.ai-file-preview');

            if (!fileInput) return;

            const dt = new DataTransfer();
            const files = Array.from(fileInput.files);

            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });

            fileInput.files = dt.files;
            updateFilePreview(Array.from(dt.files), filePreview);
        }

        // 处理AI请求
        async function processAIRequest(action, selectedText) {
            const promptInput = document.querySelector('.ai-prompt-input');
            const fileInput = document.querySelector('.ai-file-input');
            const responseSection = document.querySelector('.ai-response-section');
            const responseContent = document.querySelector('.ai-response-content');
            const applyButton = document.getElementById('apply-ai-result');

            if (!promptInput || !responseSection || !responseContent) return;

            const requirements = promptInput.value.trim();
            const files = Array.from(fileInput?.files || []);

            // 显示加载状态
            responseSection.style.display = 'block';
            responseContent.innerHTML = `
                <div class="ai-loading">
                    <i class="fas fa-spinner"></i>
                    <span>AI正在处理中，请稍候...</span>
                </div>
            `;

            try {
                let result;

                // 根据操作类型调用相应的AI服务
                switch (action) {
                    case 'polish':
                        result = await aiServiceManager.polishContent(selectedText, requirements);
                        break;
                    case 'translate':
                        const targetLanguage = requirements || '英文';
                        result = await aiServiceManager.translateContent(selectedText, targetLanguage);
                        break;
                    case 'explain':
                        result = await aiServiceManager.explainContent(selectedText, requirements);
                        break;
                    case 'rewrite':
                        const style = requirements || '学术风格';
                        result = await aiServiceManager.rewriteContent(selectedText, style);
                        break;
                    default:
                        throw new Error('未知的操作类型');
                }

                if (result.success) {
                    responseContent.textContent = result.result;
                    applyButton.style.display = 'inline-block';

                    // 存储结果用于应用
                    window.currentAIResult = {
                        action,
                        originalText: selectedText,
                        processedText: result.result
                    };
                } else {
                    responseContent.innerHTML = `
                        <div style="color: #ef4444;">
                            <i class="fas fa-exclamation-triangle"></i>
                            处理失败: ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                responseContent.innerHTML = `
                    <div style="color: #ef4444;">
                        <i class="fas fa-exclamation-triangle"></i>
                        处理失败: ${error.message}
                    </div>
                `;
            }
        }

        // 应用AI处理结果
        function applyAIResult() {
            if (!window.currentAIResult || !quillEditor) {
                showNotification('没有可应用的结果', 'warning');
                return;
            }

            const { action, originalText, processedText } = window.currentAIResult;

            // 获取当前选择
            const selection = quillEditor.getSelection();

            if (selection && selection.length > 0) {
                // 替换选中的文本
                quillEditor.deleteText(selection.index, selection.length);
                quillEditor.insertText(selection.index, processedText);
                quillEditor.setSelection(selection.index, processedText.length);
            } else {
                // 如果没有选择，在光标位置插入
                const cursorPosition = selection ? selection.index : quillEditor.getLength();
                quillEditor.insertText(cursorPosition, processedText);
                quillEditor.setSelection(cursorPosition, processedText.length);
            }

            // 显示成功通知
            const actionNames = {
                polish: '润色',
                translate: '翻译',
                explain: '解读',
                rewrite: '重写'
            };

            showNotification(`${actionNames[action]}结果已应用到编辑器`, 'success');

            // 关闭对话框
            closeModal();

            // 清理临时数据
            delete window.currentAIResult;
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
