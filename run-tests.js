/**
 * 章节功能测试运行脚本
 * 用于验证章节保存和导航功能的修复
 */

// 测试配置
const TEST_CONFIG = {
    timeout: 5000,
    retries: 3,
    verbose: true
};

// 测试结果收集器
class TestCollector {
    constructor() {
        this.results = [];
        this.startTime = Date.now();
    }

    addResult(testName, status, message, details = null) {
        this.results.push({
            name: testName,
            status: status, // 'pass', 'fail', 'skip'
            message: message,
            details: details,
            timestamp: Date.now()
        });
        
        if (TEST_CONFIG.verbose) {
            const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⏭️';
            console.log(`${statusIcon} ${testName}: ${message}`);
            if (details) {
                console.log(`   详情: ${details}`);
            }
        }
    }

    getSummary() {
        const total = this.results.length;
        const passed = this.results.filter(r => r.status === 'pass').length;
        const failed = this.results.filter(r => r.status === 'fail').length;
        const skipped = this.results.filter(r => r.status === 'skip').length;
        const duration = Date.now() - this.startTime;

        return {
            total,
            passed,
            failed,
            skipped,
            duration,
            success: failed === 0
        };
    }

    printSummary() {
        const summary = this.getSummary();
        console.log('\n' + '='.repeat(50));
        console.log('测试总结');
        console.log('='.repeat(50));
        console.log(`总计: ${summary.total}`);
        console.log(`通过: ${summary.passed}`);
        console.log(`失败: ${summary.failed}`);
        console.log(`跳过: ${summary.skipped}`);
        console.log(`耗时: ${summary.duration}ms`);
        console.log(`结果: ${summary.success ? '✅ 成功' : '❌ 失败'}`);
        console.log('='.repeat(50));

        if (summary.failed > 0) {
            console.log('\n失败的测试:');
            this.results.filter(r => r.status === 'fail').forEach(result => {
                console.log(`❌ ${result.name}: ${result.message}`);
                if (result.details) {
                    console.log(`   ${result.details}`);
                }
            });
        }
    }
}

// 测试工具函数
class TestUtils {
    static async waitFor(condition, timeout = TEST_CONFIG.timeout) {
        const start = Date.now();
        while (Date.now() - start < timeout) {
            if (await condition()) {
                return true;
            }
            await this.sleep(100);
        }
        return false;
    }

    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    static async retry(fn, retries = TEST_CONFIG.retries) {
        let lastError;
        for (let i = 0; i <= retries; i++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                if (i < retries) {
                    await this.sleep(1000 * (i + 1)); // 递增延迟
                }
            }
        }
        throw lastError;
    }
}

// 章节保存功能测试
class ChapterSaveTests {
    constructor(collector) {
        this.collector = collector;
    }

    async runAll() {
        console.log('\n🔍 开始章节保存功能测试...');
        
        await this.testSaveFunctionExists();
        await this.testDeltaFormatHandling();
        await this.testAutoSaveFunction();
        await this.testServerSaveFunction();
    }

    async testSaveFunctionExists() {
        try {
            if (typeof saveChapter === 'function' && 
                typeof autoSaveChapter === 'function' && 
                typeof saveChapterToServer === 'function') {
                this.collector.addResult(
                    '保存函数存在性检查', 
                    'pass', 
                    '所有必要的保存函数都存在'
                );
            } else {
                this.collector.addResult(
                    '保存函数存在性检查', 
                    'fail', 
                    '部分保存函数缺失'
                );
            }
        } catch (error) {
            this.collector.addResult(
                '保存函数存在性检查', 
                'fail', 
                '检查保存函数时发生错误',
                error.message
            );
        }
    }

    async testDeltaFormatHandling() {
        try {
            // 模拟Quill Delta格式
            const testDelta = {
                ops: [
                    { insert: '测试标题\n', attributes: { header: 1 } },
                    { insert: '这是测试内容。\n' },
                    { insert: '这是另一段内容。\n' }
                ]
            };

            // 检查Delta格式是否有效
            if (testDelta.ops && Array.isArray(testDelta.ops) && testDelta.ops.length > 0) {
                this.collector.addResult(
                    'Delta格式处理', 
                    'pass', 
                    'Delta格式处理正确'
                );
            } else {
                this.collector.addResult(
                    'Delta格式处理', 
                    'fail', 
                    'Delta格式处理错误'
                );
            }
        } catch (error) {
            this.collector.addResult(
                'Delta格式处理', 
                'fail', 
                'Delta格式测试失败',
                error.message
            );
        }
    }

    async testAutoSaveFunction() {
        try {
            // 检查自动保存函数的实现
            const autoSaveCode = autoSaveChapter.toString();
            
            if (autoSaveCode.includes('getContents') && autoSaveCode.includes('deltaContent')) {
                this.collector.addResult(
                    '自动保存函数修复', 
                    'pass', 
                    '自动保存函数已正确使用Delta格式'
                );
            } else {
                this.collector.addResult(
                    '自动保存函数修复', 
                    'fail', 
                    '自动保存函数未使用Delta格式'
                );
            }
        } catch (error) {
            this.collector.addResult(
                '自动保存函数修复', 
                'fail', 
                '检查自动保存函数时发生错误',
                error.message
            );
        }
    }

    async testServerSaveFunction() {
        try {
            // 检查服务器保存函数的实现
            const serverSaveCode = saveChapterToServer.toString();
            
            if (serverSaveCode.includes('deltaContent') && 
                serverSaveCode.includes('content: deltaContent')) {
                this.collector.addResult(
                    '服务器保存函数修复', 
                    'pass', 
                    '服务器保存函数已正确处理Delta格式'
                );
            } else {
                this.collector.addResult(
                    '服务器保存函数修复', 
                    'fail', 
                    '服务器保存函数未正确处理Delta格式'
                );
            }
        } catch (error) {
            this.collector.addResult(
                '服务器保存函数修复', 
                'fail', 
                '检查服务器保存函数时发生错误',
                error.message
            );
        }
    }
}

// 导航功能测试
class NavigationTests {
    constructor(collector) {
        this.collector = collector;
    }

    async runAll() {
        console.log('\n🧭 开始导航功能测试...');
        
        await this.testNavigationFunctionExists();
        await this.testShowPanelFunction();
        await this.testUpdateNavActiveState();
        await this.testEnterChapterEditMode();
    }

    async testNavigationFunctionExists() {
        try {
            if (typeof showPanel === 'function' && 
                typeof updateNavActiveState === 'function' && 
                typeof enterChapterEditMode === 'function') {
                this.collector.addResult(
                    '导航函数存在性检查', 
                    'pass', 
                    '所有必要的导航函数都存在'
                );
            } else {
                this.collector.addResult(
                    '导航函数存在性检查', 
                    'fail', 
                    '部分导航函数缺失'
                );
            }
        } catch (error) {
            this.collector.addResult(
                '导航函数存在性检查', 
                'fail', 
                '检查导航函数时发生错误',
                error.message
            );
        }
    }

    async testShowPanelFunction() {
        try {
            // 检查showPanel函数是否调用updateNavActiveState
            const showPanelCode = showPanel.toString();
            
            if (showPanelCode.includes('updateNavActiveState')) {
                this.collector.addResult(
                    'showPanel函数修复', 
                    'pass', 
                    'showPanel函数已正确调用updateNavActiveState'
                );
            } else {
                this.collector.addResult(
                    'showPanel函数修复', 
                    'fail', 
                    'showPanel函数未调用updateNavActiveState'
                );
            }
        } catch (error) {
            this.collector.addResult(
                'showPanel函数修复', 
                'fail', 
                '检查showPanel函数时发生错误',
                error.message
            );
        }
    }

    async testUpdateNavActiveState() {
        try {
            // 检查updateNavActiveState函数的实现
            const updateNavCode = updateNavActiveState.toString();
            
            if (updateNavCode.includes('classList.remove') && 
                updateNavCode.includes('classList.add') &&
                updateNavCode.includes('data-tab')) {
                this.collector.addResult(
                    'updateNavActiveState函数', 
                    'pass', 
                    'updateNavActiveState函数实现正确'
                );
            } else {
                this.collector.addResult(
                    'updateNavActiveState函数', 
                    'fail', 
                    'updateNavActiveState函数实现不正确'
                );
            }
        } catch (error) {
            this.collector.addResult(
                'updateNavActiveState函数', 
                'fail', 
                '检查updateNavActiveState函数时发生错误',
                error.message
            );
        }
    }

    async testEnterChapterEditMode() {
        try {
            // 检查enterChapterEditMode函数是否调用showPanel
            const editModeCode = enterChapterEditMode.toString();
            
            if (editModeCode.includes('showPanel') && editModeCode.includes('editor')) {
                this.collector.addResult(
                    'enterChapterEditMode函数修复', 
                    'pass', 
                    'enterChapterEditMode函数已正确调用showPanel'
                );
            } else {
                this.collector.addResult(
                    'enterChapterEditMode函数修复', 
                    'fail', 
                    'enterChapterEditMode函数未正确调用showPanel'
                );
            }
        } catch (error) {
            this.collector.addResult(
                'enterChapterEditMode函数修复', 
                'fail', 
                '检查enterChapterEditMode函数时发生错误',
                error.message
            );
        }
    }
}

// 主测试运行器
async function runAllTests() {
    console.log('🚀 开始运行章节功能修复测试...\n');
    
    const collector = new TestCollector();
    
    try {
        // 运行章节保存测试
        const saveTests = new ChapterSaveTests(collector);
        await saveTests.runAll();
        
        // 运行导航功能测试
        const navTests = new NavigationTests(collector);
        await navTests.runAll();
        
    } catch (error) {
        collector.addResult(
            '测试运行器', 
            'fail', 
            '测试运行过程中发生错误',
            error.message
        );
    }
    
    // 打印测试总结
    collector.printSummary();
    
    return collector.getSummary();
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
    // 等待页面加载完成后运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllTests);
    } else {
        runAllTests();
    }
}

// 导出测试函数（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runAllTests,
        ChapterSaveTests,
        NavigationTests,
        TestCollector,
        TestUtils
    };
}
