<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品牌标题颜色测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 测试页面样式 */
        .test-container {
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .test-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }
        
        /* 修复后的品牌标题样式 */
        .brand-title-fixed {
            font-size: 1.8rem;
            color: #000000 !important;
            margin: 0;
            font-weight: 600;
            background: none !important;
            -webkit-background-clip: initial !important;
            -webkit-text-fill-color: #000000 !important;
            background-clip: initial !important;
        }
        
        /* 原始有问题的样式（来自styles.css） */
        .brand-title-original {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            background: linear-gradient(45deg, #ffffff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .result {
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
        }
        
        .result.success {
            background: #dcfce7;
            border: 1px solid #16a34a;
            color: #15803d;
        }
        
        .result.error {
            background: #fef2f2;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>品牌标题颜色修复测试</h1>
        
        <div class="test-section">
            <div class="test-title">测试1: 修复后的品牌标题</div>
            <div class="test-description">应该显示为黑色文字</div>
            <h1 class="brand-title-fixed">项目管理</h1>
            <div class="result success">
                ✅ 修复成功：文字颜色为黑色，覆盖了webkit渐变效果
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试2: 原始有问题的样式</div>
            <div class="test-description">会显示为透明文字（看不见或很淡）</div>
            <h1 class="brand-title-original">项目管理</h1>
            <div class="result error">
                ❌ 问题样式：使用了webkit渐变，文字可能不可见
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">修复说明</div>
            <div class="test-description">
                <strong>问题原因：</strong><br>
                原始的 <code>.brand-title</code> 样式使用了：<br>
                • <code>-webkit-text-fill-color: transparent</code><br>
                • <code>background: linear-gradient(...)</code><br>
                • <code>-webkit-background-clip: text</code><br>
                <br>
                <strong>修复方法：</strong><br>
                在 project-management.html 中添加了覆盖样式：<br>
                • <code>color: #000000 !important</code><br>
                • <code>-webkit-text-fill-color: #000000 !important</code><br>
                • <code>background: none !important</code><br>
                • <code>-webkit-background-clip: initial !important</code>
            </div>
        </div>
    </div>
    
    <script>
        // 验证修复效果
        document.addEventListener('DOMContentLoaded', function() {
            const fixedTitle = document.querySelector('.brand-title-fixed');
            const originalTitle = document.querySelector('.brand-title-original');
            
            if (fixedTitle) {
                const computedStyle = window.getComputedStyle(fixedTitle);
                console.log('修复后的样式:', {
                    color: computedStyle.color,
                    webkitTextFillColor: computedStyle.webkitTextFillColor,
                    background: computedStyle.background
                });
            }
            
            if (originalTitle) {
                const computedStyle = window.getComputedStyle(originalTitle);
                console.log('原始问题样式:', {
                    color: computedStyle.color,
                    webkitTextFillColor: computedStyle.webkitTextFillColor,
                    background: computedStyle.background
                });
            }
        });
    </script>
</body>
</html>
