<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 《大模型技术与油气应用概论》协作系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .auth-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .auth-header h1 {
            color: #1f2937;
            font-size: 1.8rem;
            margin-bottom: 8px;
        }
        
        .auth-header p {
            color: #6b7280;
            font-size: 0.95rem;
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .auth-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            color: #6b7280;
        }
        
        .auth-tab.active {
            color: #4f46e5;
            border-bottom-color: #4f46e5;
        }
        
        .auth-form {
            display: none;
        }
        
        .auth-form.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            color: #374151;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .auth-button {
            width: 100%;
            padding: 12px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
            margin-bottom: 15px;
        }
        
        .auth-button:hover {
            background: #4338ca;
        }
        
        .auth-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .auth-divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #6b7280;
        }
        
        .auth-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
        }
        
        .auth-divider span {
            background: white;
            padding: 0 15px;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 15px;
        }
        
        .forgot-password a {
            color: #4f46e5;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        .auth-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #fef2f2;
            color: #991b1b;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #fecaca;
        }
        
        .success-message {
            background: #ecfdf5;
            color: #065f46;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #a7f3d0;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1><i class="fas fa-book"></i> 协作编写系统</h1>
                <p>《大模型技术与油气应用概论》</p>
            </div>
            
            <div class="auth-tabs">
                <div class="auth-tab active" onclick="switchTab('login')">登录</div>
                <div class="auth-tab" onclick="switchTab('register')">注册</div>
            </div>
            
            <div id="auth-messages"></div>
            
            <!-- 登录表单 -->
            <form id="login-form" class="auth-form active" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" class="form-input" name="email" required placeholder="请输入邮箱地址">
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" name="password" required placeholder="请输入密码">
                </div>
                
                <button type="submit" class="auth-button" id="login-btn">
                    <span class="btn-text">登录</span>
                    <span class="btn-loading" style="display: none;"><span class="loading"></span> 登录中...</span>
                </button>
                
                <div class="forgot-password">
                    <a href="#" onclick="showForgotPassword()">忘记密码？</a>
                </div>
            </form>
            
            <!-- 注册表单 -->
            <form id="register-form" class="auth-form" onsubmit="handleRegister(event)">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">姓名</label>
                        <input type="text" class="form-input" name="fullName" required placeholder="请输入姓名">
                    </div>
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-input" name="username" required placeholder="请输入用户名">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" class="form-input" name="email" required placeholder="请输入邮箱地址">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">所属机构</label>
                        <input type="text" class="form-input" name="institution" placeholder="如：中国石油大学">
                    </div>
                    <div class="form-group">
                        <label class="form-label">部门</label>
                        <input type="text" class="form-input" name="department" placeholder="如：人工智能学院">
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" name="password" required placeholder="请输入密码（至少6位）" minlength="6">
                </div>
                
                <div class="form-group">
                    <label class="form-label">确认密码</label>
                    <input type="password" class="form-input" name="confirmPassword" required placeholder="请再次输入密码">
                </div>
                
                <button type="submit" class="auth-button" id="register-btn">
                    <span class="btn-text">注册</span>
                    <span class="btn-loading" style="display: none;"><span class="loading"></span> 注册中...</span>
                </button>
            </form>
            
            <div class="auth-footer">
                <p>© 2024 《大模型技术与油气应用概论》编写团队</p>
                <p>中国石油大学（北京）人工智能学院</p>
            </div>
        </div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script>
        // 切换登录/注册标签
        function switchTab(tab) {
            // 更新标签状态
            document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
            document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');
            
            // 更新表单显示
            document.querySelectorAll('.auth-form').forEach(f => f.classList.remove('active'));
            document.getElementById(`${tab}-form`).classList.add('active');
            
            // 清除消息
            clearMessages();
        }
        
        // 处理登录
        async function handleLogin(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const email = formData.get('email');
            const password = formData.get('password');
            
            setLoading('login', true);
            clearMessages();
            
            try {
                await supabaseManager.signIn(email, password);
                showMessage('登录成功！正在跳转...', 'success');
                
                // 延迟跳转到项目管理页面
                setTimeout(() => {
                    window.location.href = 'project-management.html';
                }, 1500);
                
            } catch (error) {
                showMessage('登录失败: ' + error.message, 'error');
            } finally {
                setLoading('login', false);
            }
        }
        
        // 处理注册
        async function handleRegister(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            
            // 验证密码
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            
            if (password !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }
            
            setLoading('register', true);
            clearMessages();
            
            try {
                const userData = {
                    full_name: formData.get('fullName'),
                    username: formData.get('username'),
                    institution: formData.get('institution'),
                    department: formData.get('department')
                };
                
                await supabaseManager.signUp(formData.get('email'), password, userData);
                showMessage('注册成功！请检查邮箱验证链接。', 'success');
                
                // 切换到登录表单
                setTimeout(() => {
                    switchTab('login');
                }, 2000);
                
            } catch (error) {
                showMessage('注册失败: ' + error.message, 'error');
            } finally {
                setLoading('register', false);
            }
        }
        
        // 设置加载状态
        function setLoading(type, loading) {
            const btn = document.getElementById(`${type}-btn`);
            const textSpan = btn.querySelector('.btn-text');
            const loadingSpan = btn.querySelector('.btn-loading');
            
            if (loading) {
                textSpan.style.display = 'none';
                loadingSpan.style.display = 'inline';
                btn.disabled = true;
            } else {
                textSpan.style.display = 'inline';
                loadingSpan.style.display = 'none';
                btn.disabled = false;
            }
        }
        
        // 显示消息
        function showMessage(message, type) {
            const messagesDiv = document.getElementById('auth-messages');
            const messageClass = type === 'error' ? 'error-message' : 'success-message';
            
            messagesDiv.innerHTML = `
                <div class="${messageClass}">
                    <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'}"></i>
                    ${message}
                </div>
            `;
        }
        
        // 清除消息
        function clearMessages() {
            document.getElementById('auth-messages').innerHTML = '';
        }
        
        // 忘记密码
        function showForgotPassword() {
            const email = prompt('请输入您的邮箱地址：');
            if (email) {
                // 这里可以集成密码重置功能
                alert('密码重置链接已发送到您的邮箱（功能开发中）');
            }
        }
        
        // 检查用户登录状态
        async function checkAuthStatus() {
            const user = await supabaseManager.getCurrentUser();
            if (user) {
                // 用户已登录，跳转到项目管理页面
                window.location.href = 'project-management.html';
            }
        }
        
        // 页面加载时检查登录状态
        window.addEventListener('load', checkAuthStatus);
    </script>
</body>
</html>
