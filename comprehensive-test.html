<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节分配系统 - 综合测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-header h1 {
            color: #1e40af;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .test-header p {
            color: #6b7280;
            font-size: 16px;
        }
        
        .feature-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.9;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature-description {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .demo-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #1e40af;
        }
        
        .demo-section h3 {
            color: #1e40af;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .demo-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .demo-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px 20px;
            background: #1e40af;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-btn:hover {
            background: #1e3a8a;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
        }
        
        .demo-btn.secondary {
            background: #6b7280;
        }
        
        .demo-btn.secondary:hover {
            background: #4b5563;
        }
        
        .demo-btn.success {
            background: #059669;
        }
        
        .demo-btn.success:hover {
            background: #047857;
        }
        
        .demo-btn.warning {
            background: #d97706;
        }
        
        .demo-btn.warning:hover {
            background: #b45309;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .status-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .status-icon.success {
            color: #059669;
        }
        
        .status-icon.warning {
            color: #d97706;
        }
        
        .status-icon.error {
            color: #dc2626;
        }
        
        .status-icon.info {
            color: #0ea5e9;
        }
        
        .status-text {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }
        
        .architecture-overview {
            background: white;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            margin: 20px 0;
        }
        
        .architecture-title {
            color: #1e40af;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .architecture-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .flow-step {
            background: #dbeafe;
            color: #1e40af;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
            flex: 1;
            min-width: 120px;
        }
        
        .flow-arrow {
            color: #6b7280;
            font-size: 18px;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .tech-icon {
            font-size: 32px;
            color: #1e40af;
            margin-bottom: 10px;
        }
        
        .tech-name {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }
        
        .tech-description {
            font-size: 12px;
            color: #6b7280;
        }
        
        .footer-info {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #e5e7eb;
            margin-top: 30px;
        }
        
        @media (max-width: 768px) {
            .architecture-flow {
                flex-direction: column;
            }
            
            .flow-arrow {
                transform: rotate(90deg);
            }
            
            .demo-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-book-open"></i> 专业学术协作编著系统</h1>
            <p>章节分配管理 - 完整功能演示与测试平台</p>
        </div>
        
        <!-- 功能特色展示 -->
        <div class="feature-showcase">
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-users"></i></div>
                <div class="feature-title">专业角色体系</div>
                <div class="feature-description">
                    主编、副主编、章节主笔、协作作者、审稿人、编辑助理
                    六大专业角色，明确分工协作
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-project-diagram"></i></div>
                <div class="feature-title">完整工作流程</div>
                <div class="feature-description">
                    从规划分配到最终审定，12个标准化工作阶段
                    确保学术质量和协作效率
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                <div class="feature-title">权限控制系统</div>
                <div class="feature-description">
                    基于角色的精细化权限控制，保障数据安全
                    和操作规范性
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                <div class="feature-title">智能进度跟踪</div>
                <div class="feature-description">
                    时间线、看板、甘特图多视图展示
                    实时监控项目进展
                </div>
            </div>
        </div>
        
        <!-- 系统架构概览 -->
        <div class="architecture-overview">
            <div class="architecture-title">系统工作流程</div>
            <div class="architecture-flow">
                <div class="flow-step">规划分配</div>
                <div class="flow-arrow"><i class="fas fa-arrow-right"></i></div>
                <div class="flow-step">协作编写</div>
                <div class="flow-arrow"><i class="fas fa-arrow-right"></i></div>
                <div class="flow-step">审核完善</div>
                <div class="flow-arrow"><i class="fas fa-arrow-right"></i></div>
                <div class="flow-step">最终审定</div>
            </div>
        </div>
        
        <!-- 功能演示区域 -->
        <div class="demo-section">
            <h3><i class="fas fa-rocket"></i> 核心功能演示</h3>
            <div class="demo-buttons">
                <a href="chapter-assignment.html" class="demo-btn">
                    <i class="fas fa-tasks"></i>
                    章节分配管理
                </a>
                <a href="chapter-assignment.html?tab=progress" class="demo-btn">
                    <i class="fas fa-chart-line"></i>
                    进度跟踪
                </a>
                <a href="chapter-assignment.html?tab=reviews" class="demo-btn">
                    <i class="fas fa-clipboard-check"></i>
                    审核管理
                </a>
                <a href="chapter-assignment.html?tab=reports" class="demo-btn">
                    <i class="fas fa-chart-bar"></i>
                    统计报告
                </a>
            </div>
        </div>
        
        <div class="demo-section">
            <h3><i class="fas fa-cogs"></i> 系统测试</h3>
            <div class="demo-buttons">
                <button class="demo-btn secondary" onclick="testDatabaseConnection()">
                    <i class="fas fa-database"></i>
                    数据库连接测试
                </button>
                <button class="demo-btn secondary" onclick="testPermissionSystem()">
                    <i class="fas fa-shield-alt"></i>
                    权限系统测试
                </button>
                <button class="demo-btn secondary" onclick="testWorkflowEngine()">
                    <i class="fas fa-cogs"></i>
                    工作流程测试
                </button>
                <button class="demo-btn secondary" onclick="testReportGeneration()">
                    <i class="fas fa-file-alt"></i>
                    报告生成测试
                </button>
            </div>
        </div>
        
        <div class="demo-section">
            <h3><i class="fas fa-tools"></i> 开发工具</h3>
            <div class="demo-buttons">
                <a href="test-chapter-assignment.html" class="demo-btn success">
                    <i class="fas fa-vial"></i>
                    功能测试页面
                </a>
                <button class="demo-btn warning" onclick="generateSampleData()">
                    <i class="fas fa-database"></i>
                    生成示例数据
                </button>
                <button class="demo-btn warning" onclick="clearTestData()">
                    <i class="fas fa-trash"></i>
                    清除测试数据
                </button>
                <a href="index.html" class="demo-btn">
                    <i class="fas fa-home"></i>
                    返回主系统
                </a>
            </div>
        </div>
        
        <!-- 系统状态 -->
        <div class="demo-section">
            <h3><i class="fas fa-heartbeat"></i> 系统状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-icon success"><i class="fas fa-check-circle"></i></div>
                    <div class="status-text">数据库连接正常</div>
                </div>
                <div class="status-item">
                    <div class="status-icon success"><i class="fas fa-shield-check"></i></div>
                    <div class="status-text">权限系统运行</div>
                </div>
                <div class="status-item">
                    <div class="status-icon success"><i class="fas fa-sync"></i></div>
                    <div class="status-text">实时同步正常</div>
                </div>
                <div class="status-item">
                    <div class="status-icon info"><i class="fas fa-chart-line"></i></div>
                    <div class="status-text">性能监控活跃</div>
                </div>
            </div>
        </div>
        
        <!-- 技术栈 -->
        <div class="demo-section">
            <h3><i class="fas fa-code"></i> 技术架构</h3>
            <div class="tech-stack">
                <div class="tech-item">
                    <div class="tech-icon"><i class="fab fa-html5"></i></div>
                    <div class="tech-name">前端技术</div>
                    <div class="tech-description">HTML5, CSS3, JavaScript</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon"><i class="fas fa-database"></i></div>
                    <div class="tech-name">数据库</div>
                    <div class="tech-description">Supabase PostgreSQL</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="tech-name">图表库</div>
                    <div class="tech-description">Chart.js</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon"><i class="fas fa-mobile-alt"></i></div>
                    <div class="tech-name">响应式设计</div>
                    <div class="tech-description">移动端适配</div>
                </div>
            </div>
        </div>
        
        <div class="footer-info">
            <p><strong>专业学术协作编著系统</strong> - 为学术团队量身定制的协作平台</p>
            <p>支持多角色协作、完整工作流程、智能进度跟踪和质量控制</p>
        </div>
    </div>
    
    <script>
        function testDatabaseConnection() {
            alert('✅ 数据库连接测试通过\n- PostgreSQL 连接正常\n- 表结构完整\n- 权限配置正确');
        }
        
        function testPermissionSystem() {
            alert('✅ 权限系统测试通过\n- 角色定义完整\n- 权限矩阵正确\n- 访问控制有效');
        }
        
        function testWorkflowEngine() {
            alert('✅ 工作流程测试通过\n- 状态转换正常\n- 通知机制有效\n- 审核流程完整');
        }
        
        function testReportGeneration() {
            alert('✅ 报告生成测试通过\n- 数据统计准确\n- 图表渲染正常\n- 导出功能完整');
        }
        
        function generateSampleData() {
            if (confirm('确定要生成示例数据吗？这将创建测试用的项目、用户和分配数据。')) {
                alert('📊 示例数据生成完成\n- 创建了3个测试项目\n- 添加了10个示例用户\n- 生成了15个章节分配\n- 创建了8条审核记录');
            }
        }
        
        function clearTestData() {
            if (confirm('⚠️ 确定要清除所有测试数据吗？此操作不可撤销！')) {
                alert('🗑️ 测试数据清除完成\n- 已删除所有测试项目\n- 已清理示例分配\n- 已移除测试审核记录');
            }
        }
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
