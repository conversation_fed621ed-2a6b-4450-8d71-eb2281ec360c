# AI增强学术专著编写系统 - 服务器部署方案

## 🖥️ 服务器配置要求

### 推荐配置
- **CPU**: 4核心 Intel/AMD 处理器
- **内存**: 8GB RAM (最低) / 16GB RAM (推荐)
- **存储**: 
  - 系统盘: 100GB SSD
  - 数据盘: 500GB SSD (用于数据库和文件存储)
- **带宽**: 10Mbps 上行/下行
- **操作系统**: Ubuntu 22.04 LTS

### 云服务器推荐
| 云厂商 | 实例规格 | 月费用 | 备注 |
|--------|----------|--------|------|
| 阿里云 | ecs.c6.xlarge | ¥600-800 | 4核8G，适合生产环境 |
| 腾讯云 | SA2.LARGE8 | ¥550-750 | 4核8G，性价比高 |
| 华为云 | c6.xlarge.2 | ¥580-780 | 4核8G，稳定性好 |

## 🐳 Docker环境搭建

### 1. 系统初始化脚本

```bash
#!/bin/bash
# 系统初始化脚本 - init_server.sh

echo "开始初始化服务器环境..."

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git vim htop tree unzip

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 创建项目目录
sudo mkdir -p /opt/llm-book-system
sudo chown $USER:$USER /opt/llm-book-system

# 配置防火墙
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3000  # 开发端口
sudo ufw --force enable

echo "服务器初始化完成！请重新登录以使Docker权限生效。"
```

### 2. 目录结构设计

```
/opt/llm-book-system/
├── docker-compose.yml          # Docker编排文件
├── .env                        # 环境变量配置
├── nginx/                      # Nginx配置
│   ├── nginx.conf
│   └── ssl/                    # SSL证书目录
├── supabase/                   # Supabase配置
│   ├── config.toml
│   ├── seed.sql
│   └── migrations/
├── volumes/                    # 数据持久化目录
│   ├── postgres_data/
│   ├── storage_data/
│   └── logs/
└── scripts/                    # 部署脚本
    ├── backup.sh
    ├── restore.sh
    └── update.sh
```

## 🗄️ 开源Supabase部署配置

### 1. Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: llm-book-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - ./volumes/postgres_data:/var/lib/postgresql/data
      - ./supabase/seed.sql:/docker-entrypoint-initdb.d/seed.sql
    ports:
      - "5432:5432"
    command: postgres -c wal_level=logical

  # PostgREST API服务
  postgrest:
    image: postgrest/postgrest:v11.2.0
    container_name: llm-book-postgrest
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
    ports:
      - "3001:3000"
    depends_on:
      - postgres

  # GoTrue认证服务
  gotrue:
    image: supabase/gotrue:v2.99.0
    container_name: llm-book-gotrue
    restart: unless-stopped
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      GOTRUE_SITE_URL: ${SITE_URL}
      GOTRUE_URI_ALLOW_LIST: ${ADDITIONAL_REDIRECT_URLS}
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_SECRET: ${JWT_SECRET}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: true
      GOTRUE_MAILER_AUTOCONFIRM: false
      GOTRUE_SMTP_HOST: ${SMTP_HOST}
      GOTRUE_SMTP_PORT: ${SMTP_PORT}
      GOTRUE_SMTP_USER: ${SMTP_USER}
      GOTRUE_SMTP_PASS: ${SMTP_PASS}
      GOTRUE_SMTP_ADMIN_EMAIL: ${SMTP_ADMIN_EMAIL}
    ports:
      - "9999:9999"
    depends_on:
      - postgres

  # Realtime实时服务
  realtime:
    image: supabase/realtime:v2.25.35
    container_name: llm-book-realtime
    restart: unless-stopped
    environment:
      PORT: 4000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      DB_NAME: ${POSTGRES_DB}
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: ${JWT_SECRET}
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: ${SECRET_KEY_BASE}
      ERL_AFLAGS: -proto_dist inet_tcp
      ENABLE_TAILSCALE: "false"
      DNS_NODES: "''"
    ports:
      - "4000:4000"
    depends_on:
      - postgres

  # Storage存储服务
  storage:
    image: supabase/storage-api:v0.40.4
    container_name: llm-book-storage
    restart: unless-stopped
    environment:
      ANON_KEY: ${ANON_KEY}
      SERVICE_KEY: ${SERVICE_ROLE_KEY}
      POSTGREST_URL: http://postgrest:3000
      PGRST_JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
    volumes:
      - ./volumes/storage_data:/var/lib/storage
    ports:
      - "5000:5000"
    depends_on:
      - postgres

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: llm-book-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./volumes/logs/nginx:/var/log/nginx
    depends_on:
      - postgrest
      - gotrue
      - realtime
      - storage

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: llm-book-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - ./volumes/redis_data:/data

volumes:
  postgres_data:
  storage_data:
  redis_data:
```

### 2. 环境变量配置

```bash
# .env文件
# 数据库配置
POSTGRES_DB=llm_book_system
POSTGRES_USER=supabase
POSTGRES_PASSWORD=your_secure_password_here

# JWT配置
JWT_SECRET=your_jwt_secret_here_32_characters_minimum
SECRET_KEY_BASE=your_secret_key_base_here_64_characters_minimum

# API密钥
ANON_KEY=your_anon_key_here
SERVICE_ROLE_KEY=your_service_role_key_here

# 站点配置
SITE_URL=https://your-domain.com
ADDITIONAL_REDIRECT_URLS=https://your-domain.com/**

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_ADMIN_EMAIL=<EMAIL>

# AI服务配置
OPENROUTER_API_KEY=your_openrouter_api_key
DEEPSEEK_MODEL=deepseek/deepseek-chat
```

### 3. Nginx配置

```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream postgrest {
        server postgrest:3000;
    }
    
    upstream gotrue {
        server gotrue:9999;
    }
    
    upstream realtime {
        server realtime:4000;
    }
    
    upstream storage {
        server storage:5000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        # 安全头部
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # API路由
        location /rest/v1/ {
            proxy_pass http://postgrest/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /auth/v1/ {
            proxy_pass http://gotrue/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /realtime/v1/ {
            proxy_pass http://realtime/socket/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /storage/v1/ {
            proxy_pass http://storage/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 静态文件服务
        location / {
            root /var/www/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }
}
```

## 🔧 部署脚本

### 1. 一键部署脚本

```bash
#!/bin/bash
# deploy.sh - 一键部署脚本

set -e

echo "开始部署AI增强学术专著编写系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "Docker未安装，请先运行init_server.sh"
    exit 1
fi

# 创建必要目录
mkdir -p volumes/{postgres_data,storage_data,redis_data,logs/nginx}
mkdir -p nginx/ssl
mkdir -p supabase/migrations

# 生成JWT密钥
if [ ! -f .env ]; then
    echo "生成环境变量文件..."
    JWT_SECRET=$(openssl rand -base64 32)
    SECRET_KEY_BASE=$(openssl rand -base64 64)
    
    cp .env.example .env
    sed -i "s/your_jwt_secret_here_32_characters_minimum/$JWT_SECRET/g" .env
    sed -i "s/your_secret_key_base_here_64_characters_minimum/$SECRET_KEY_BASE/g" .env
    
    echo "请编辑.env文件，配置您的域名和邮件设置"
    exit 1
fi

# 启动服务
echo "启动Docker服务..."
docker-compose up -d

# 等待数据库启动
echo "等待数据库启动..."
sleep 30

# 运行数据库迁移
echo "运行数据库迁移..."
docker-compose exec postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -f /docker-entrypoint-initdb.d/seed.sql

echo "部署完成！"
echo "请访问 https://your-domain.com 查看系统"
echo "API端点: https://your-domain.com/rest/v1/"
echo "认证端点: https://your-domain.com/auth/v1/"
```

### 2. 备份脚本

```bash
#!/bin/bash
# backup.sh - 数据备份脚本

BACKUP_DIR="/opt/backups/llm-book-system"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

echo "开始备份数据库..."
docker-compose exec postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB > $BACKUP_DIR/database_$DATE.sql

echo "备份存储文件..."
tar -czf $BACKUP_DIR/storage_$DATE.tar.gz volumes/storage_data/

echo "备份配置文件..."
tar -czf $BACKUP_DIR/config_$DATE.tar.gz .env docker-compose.yml nginx/

echo "备份完成: $BACKUP_DIR"
```

## 📊 监控和维护

### 1. 系统监控配置

```yaml
# monitoring/docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  grafana_data:
```

### 2. 日志管理

```bash
# 日志轮转配置
# /etc/logrotate.d/llm-book-system
/opt/llm-book-system/volumes/logs/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose exec nginx nginx -s reload
    endscript
}
```

## 🔒 安全配置

### 1. SSL证书配置

```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到nginx目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem
```

### 2. 防火墙配置

```bash
# 配置UFW防火墙
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

这个部署方案提供了完整的自托管Supabase环境，确保数据安全性的同时保持了功能的完整性。您觉得这个方案如何？我们可以继续下一步：AI服务集成架构的设计。
