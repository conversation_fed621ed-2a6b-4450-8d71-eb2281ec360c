-- 修复用户关系问题
-- 解决 chapter_assignments 和 user_profiles 之间的外键关系

-- 1. 首先检查 user_profiles 表是否存在
DO $$
BEGIN
    -- 检查 user_profiles 表是否存在
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles' AND table_schema = 'public') THEN
        -- 如果不存在，创建 user_profiles 表
        CREATE TABLE public.user_profiles (
            id UUID PRIMARY KEY,
            username VARCHAR(50) UNIQUE,
            full_name VARCHAR(100),
            email VARCHAR(255),
            avatar_url TEXT,
            institution VARCHAR(100),
            department VARCHAR(100),
            global_role VARCHAR(20) DEFAULT 'author',
            bio TEXT,
            is_active BOOLEAN DEFAULT true,
            last_login_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- 启用RLS
        ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
        
        -- 创建基本策略
        CREATE POLICY "Users can view all profiles" ON public.user_profiles
            FOR SELECT USING (true);
            
        CREATE POLICY "Users can update own profile" ON public.user_profiles
            FOR UPDATE USING (auth.uid() = id);
            
        RAISE NOTICE 'user_profiles 表已创建';
    ELSE
        RAISE NOTICE 'user_profiles 表已存在';
    END IF;
END $$;

-- 2. 检查 projects 表是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'projects' AND table_schema = 'public') THEN
        -- 创建 projects 表
        CREATE TABLE public.projects (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            title VARCHAR(255) NOT NULL,
            description TEXT,
            type VARCHAR(50) DEFAULT 'book',
            status VARCHAR(50) DEFAULT 'active',
            owner_id UUID,
            settings JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- 启用RLS
        ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
        
        -- 创建基本策略
        CREATE POLICY "Users can view projects" ON public.projects
            FOR SELECT USING (true);
            
        RAISE NOTICE 'projects 表已创建';
    ELSE
        RAISE NOTICE 'projects 表已存在';
    END IF;
END $$;

-- 3. 添加缺失的外键约束到 chapter_assignments 表
DO $$
BEGIN
    -- 添加到 user_profiles 的外键约束
    
    -- lead_author_id 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_chapter_assignments_lead_author' 
        AND table_name = 'chapter_assignments'
    ) THEN
        ALTER TABLE public.chapter_assignments 
        ADD CONSTRAINT fk_chapter_assignments_lead_author 
        FOREIGN KEY (lead_author_id) REFERENCES public.user_profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '添加了 lead_author_id 外键约束';
    END IF;
    
    -- reviewer_id 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_chapter_assignments_reviewer' 
        AND table_name = 'chapter_assignments'
    ) THEN
        ALTER TABLE public.chapter_assignments 
        ADD CONSTRAINT fk_chapter_assignments_reviewer 
        FOREIGN KEY (reviewer_id) REFERENCES public.user_profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '添加了 reviewer_id 外键约束';
    END IF;
    
    -- assigned_by 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_chapter_assignments_assigned_by' 
        AND table_name = 'chapter_assignments'
    ) THEN
        ALTER TABLE public.chapter_assignments 
        ADD CONSTRAINT fk_chapter_assignments_assigned_by 
        FOREIGN KEY (assigned_by) REFERENCES public.user_profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '添加了 assigned_by 外键约束';
    END IF;
    
END $$;

-- 4. 确保 chapter_collaborators 表也有正确的外键
DO $$
BEGIN
    -- user_id 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_chapter_collaborators_user' 
        AND table_name = 'chapter_collaborators'
    ) THEN
        ALTER TABLE public.chapter_collaborators 
        ADD CONSTRAINT fk_chapter_collaborators_user 
        FOREIGN KEY (user_id) REFERENCES public.user_profiles(id) ON DELETE CASCADE;
        RAISE NOTICE '添加了 chapter_collaborators user_id 外键约束';
    END IF;
END $$;

-- 5. 检查并修复 chapters 表的外键
DO $$
BEGIN
    -- created_by 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_chapters_created_by' 
        AND table_name = 'chapters'
    ) THEN
        ALTER TABLE public.chapters 
        ADD CONSTRAINT fk_chapters_created_by 
        FOREIGN KEY (created_by) REFERENCES public.user_profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '添加了 chapters created_by 外键约束';
    END IF;
    
    -- updated_by 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_chapters_updated_by' 
        AND table_name = 'chapters'
    ) THEN
        ALTER TABLE public.chapters 
        ADD CONSTRAINT fk_chapters_updated_by 
        FOREIGN KEY (updated_by) REFERENCES public.user_profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '添加了 chapters updated_by 外键约束';
    END IF;
    
    -- project_id 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_chapters_project' 
        AND table_name = 'chapters'
    ) THEN
        ALTER TABLE public.chapters 
        ADD CONSTRAINT fk_chapters_project 
        FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
        RAISE NOTICE '添加了 chapters project_id 外键约束';
    END IF;
END $$;

-- 6. 刷新 Supabase 的模式缓存
-- 注意：这个命令可能需要超级用户权限，如果失败可以忽略
DO $$
BEGIN
    -- 尝试刷新模式缓存
    PERFORM pg_notify('pgrst', 'reload schema');
    RAISE NOTICE '已尝试刷新模式缓存';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '无法刷新模式缓存，这是正常的';
END $$;

-- 7. 验证所有外键约束
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name IN ('chapter_assignments', 'chapter_collaborators', 'chapters')
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, tc.constraint_name;

-- 完成提示
SELECT 'User relationships fix completed successfully!' as status;
