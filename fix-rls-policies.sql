-- 修复行级安全策略（RLS）脚本
-- 解决创建用户时的权限问题

-- 1. 为 user_profiles 表添加插入策略
-- 允许已认证用户创建新的用户配置
DROP POLICY IF EXISTS "Allow authenticated users to insert profiles" ON public.user_profiles;
CREATE POLICY "Allow authenticated users to insert profiles" 
ON public.user_profiles
FOR INSERT 
TO authenticated
WITH CHECK (true);

-- 2. 为 user_profiles 表添加查询策略
-- 允许用户查看自己的配置和项目成员的配置
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
CREATE POLICY "Users can view own profile" 
ON public.user_profiles
FOR SELECT 
TO authenticated
USING (
    auth.uid() = id OR
    EXISTS (
        SELECT 1 FROM public.project_members pm1
        JOIN public.project_members pm2 ON pm1.project_id = pm2.project_id
        WHERE pm1.user_id = auth.uid() 
        AND pm2.user_id = user_profiles.id
    )
);

-- 3. 为 user_profiles 表添加更新策略
-- 允许用户更新自己的配置
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;
CREATE POLICY "Users can update own profile" 
ON public.user_profiles
FOR UPDATE 
TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- 4. 为 project_members 表添加插入策略
-- 允许项目所有者和管理员添加新成员
DROP POLICY IF EXISTS "Project owners and admins can add members" ON public.project_members;
CREATE POLICY "Project owners and admins can add members" 
ON public.project_members
FOR INSERT 
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.project_members pm
        WHERE pm.project_id = project_members.project_id
        AND pm.user_id = auth.uid()
        AND pm.role IN ('owner', 'admin')
    )
);

-- 5. 为 project_members 表添加查询策略
-- 允许项目成员查看同项目的其他成员
DROP POLICY IF EXISTS "Project members can view other members" ON public.project_members;
CREATE POLICY "Project members can view other members" 
ON public.project_members
FOR SELECT 
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.project_members pm
        WHERE pm.project_id = project_members.project_id
        AND pm.user_id = auth.uid()
    )
);

-- 6. 为 project_members 表添加更新策略
-- 允许项目所有者和管理员更新成员信息
DROP POLICY IF EXISTS "Project owners and admins can update members" ON public.project_members;
CREATE POLICY "Project owners and admins can update members" 
ON public.project_members
FOR UPDATE 
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.project_members pm
        WHERE pm.project_id = project_members.project_id
        AND pm.user_id = auth.uid()
        AND pm.role IN ('owner', 'admin')
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.project_members pm
        WHERE pm.project_id = project_members.project_id
        AND pm.user_id = auth.uid()
        AND pm.role IN ('owner', 'admin')
    )
);

-- 7. 为 projects 表添加策略（如果需要）
-- 允许已认证用户创建项目
DROP POLICY IF EXISTS "Authenticated users can create projects" ON public.projects;
CREATE POLICY "Authenticated users can create projects" 
ON public.projects
FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = owner_id);

-- 允许项目成员查看项目
DROP POLICY IF EXISTS "Project members can view projects" ON public.projects;
CREATE POLICY "Project members can view projects" 
ON public.projects
FOR SELECT 
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.project_members pm
        WHERE pm.project_id = projects.id
        AND pm.user_id = auth.uid()
    )
);

-- 8. 为 user_invitations 表添加策略（如果表存在）
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_invitations') THEN
        -- 允许项目管理员创建邀请
        DROP POLICY IF EXISTS "Project admins can create invitations" ON public.user_invitations;
        EXECUTE 'CREATE POLICY "Project admins can create invitations" 
        ON public.user_invitations
        FOR INSERT 
        TO authenticated
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM public.project_members pm
                WHERE pm.project_id = user_invitations.project_id
                AND pm.user_id = auth.uid()
                AND pm.role IN (''owner'', ''admin'')
            )
        )';

        -- 允许项目管理员查看邀请
        DROP POLICY IF EXISTS "Project admins can view invitations" ON public.user_invitations;
        EXECUTE 'CREATE POLICY "Project admins can view invitations" 
        ON public.user_invitations
        FOR SELECT 
        TO authenticated
        USING (
            EXISTS (
                SELECT 1 FROM public.project_members pm
                WHERE pm.project_id = user_invitations.project_id
                AND pm.user_id = auth.uid()
                AND pm.role IN (''owner'', ''admin'')
            )
        )';
    END IF;
END $$;

-- 9. 验证策略创建结果
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('user_profiles', 'project_members', 'projects', 'user_invitations')
ORDER BY tablename, policyname;

-- 完成提示
SELECT 'RLS policies updated successfully!' as result;
