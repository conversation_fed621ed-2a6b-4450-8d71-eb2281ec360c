# 《大模型技术与油气应用概论》专著编写系统使用指南

## 系统概述

本系统是专门为编写《大模型技术与油气应用概论》而开发的专用编写工具，集成了大纲管理、章节编写、结构图绘制、参考文献管理和进度跟踪等功能。

## 启动系统

### 方法一：双击启动
1. 双击 `start.bat` 文件
2. 系统会自动在浏览器中打开

### 方法二：手动启动
1. 用浏览器打开 `index.html` 文件
2. 推荐使用Chrome、Firefox或Edge浏览器

## 功能模块详解

### 1. 大纲管理

#### 功能说明
- 管理书籍的整体结构
- 支持多级层次（篇-章-节-小节）
- 可视化的树形结构展示

#### 操作步骤
1. 点击左侧导航"大纲管理"
2. 点击"添加章节"创建新章节
3. 填写章节标题和选择层级
4. 可以编辑或删除已有章节

#### 使用技巧
- 层级0：篇章（如"第一篇 理论技术篇"）
- 层级1：章（如"第1章 大模型基本概念"）
- 层级2：节（如"1.1 人工智能进入大模型时代"）
- 层级3：小节（如"1.1.1 发展历程"）

### 2. 章节编写

#### 功能说明
- 富文本编辑器，支持格式化
- 自动保存功能
- 章节元信息管理

#### 操作步骤
1. 点击"章节编写"切换到编辑模式
2. 从下拉菜单选择要编辑的章节
3. 填写章节标题和摘要
4. 在编辑器中编写正文内容
5. 点击"保存"按钮保存内容

#### 编辑器功能
- **格式化**：粗体、斜体、下划线
- **标题**：支持多级标题
- **列表**：有序和无序列表
- **链接**：插入超链接
- **图片**：插入图片
- **表格**：创建表格

### 3. 结构图绘制

#### 功能说明
- 基于Mermaid语法的图表绘制
- 支持思维导图、流程图、架构图
- 实时预览功能

#### 操作步骤
1. 点击"结构图"进入绘图模式
2. 选择图表类型
3. 在左侧编辑器中输入Mermaid代码
4. 右侧实时预览图表效果
5. 点击"生成图表"使用预设模板

#### 图表类型

**思维导图示例：**
```mermaid
mindmap
  root((大模型技术))
    理论基础
      Transformer
      预训练
    应用模式
      提示工程
      RAG
    油气实践
      勘探
      开发
```

**流程图示例：**
```mermaid
flowchart TD
    A[数据收集] --> B[预处理]
    B --> C[模型训练]
    C --> D[评估优化]
    D --> E[部署应用]
```

**架构图示例：**
```mermaid
graph TB
    subgraph "应用层"
        A1[智能问答]
        A2[内容生成]
    end
    subgraph "模型层"
        B1[大语言模型]
        B2[专业模型]
    end
    A1 --> B1
    A2 --> B2
```

### 4. 参考文献管理

#### 功能说明
- 文献信息的录入和管理
- 支持多种格式导入
- 自动格式化引用

#### 操作步骤
1. 点击"参考文献"进入管理界面
2. 点击"添加文献"录入新文献
3. 填写标题、作者、来源、年份等信息
4. 可以编辑或删除已有文献

#### 导入功能
- **JSON格式**：完整的文献数据
- **CSV格式**：表格形式的文献列表
- **手动录入**：逐条添加文献信息

### 5. 进度管理

#### 功能说明
- 跟踪整体写作进度
- 显示章节完成状态
- 统计分析功能

#### 查看方式
1. 点击"进度管理"查看总体进度
2. 进度条显示完成百分比
3. 章节列表显示每章的状态
4. 绿色表示已完成，橙色表示未开始

## 数据管理

### 保存功能
- **自动保存**：编辑内容自动保存到浏览器本地存储
- **手动保存**：点击"保存项目"按钮
- **快捷键**：Ctrl+S 快速保存

### 导出功能
1. 点击顶部"导出"按钮
2. 系统生成JSON格式的项目文件
3. 文件包含所有大纲、章节、文献等数据
4. 可用于备份或迁移

### 导入功能
- 在大纲管理中可以导入大纲结构
- 在参考文献中可以导入文献数据
- 支持JSON和文本格式

## 实用技巧

### 1. 快速导航
- 点击大纲中的章节可快速跳转到编辑界面
- 右侧工具面板提供快速导航链接

### 2. 模板使用
右侧工具面板提供常用模板：
- **章节模板**：标准的学术章节结构
- **表格模板**：快速插入表格
- **图片模板**：图片引用格式
- **公式模板**：数学公式格式

### 3. 协作编写
- 通过导出/导入功能实现多人协作
- 定期备份项目文件
- 使用版本控制管理不同版本

### 4. 内容组织
建议的写作流程：
1. 先完善大纲结构
2. 为每个章节添加摘要
3. 逐章编写详细内容
4. 添加相关的结构图
5. 整理参考文献
6. 定期检查进度

## 常见问题

### Q: 数据会丢失吗？
A: 系统使用浏览器本地存储，数据保存在本地。建议定期导出备份。

### Q: 可以在不同电脑上使用吗？
A: 可以。通过导出/导入功能在不同设备间同步数据。

### Q: 支持哪些浏览器？
A: 推荐使用Chrome、Firefox、Safari或Edge的最新版本。

### Q: 如何插入数学公式？
A: 使用公式模板，支持LaTeX语法，格式为 $$公式内容$$

### Q: 图表不显示怎么办？
A: 检查Mermaid语法是否正确，确保网络连接正常（需要加载外部库）。

## 技术支持

如遇到问题或需要功能建议，可以：
1. 查看README.md文件
2. 检查浏览器控制台错误信息
3. 尝试刷新页面或重新启动

## 版本更新

系统会不断改进和添加新功能，建议：
- 定期备份项目数据
- 关注版本更新说明
- 测试新功能前先备份

---

**祝您写作顺利！**
