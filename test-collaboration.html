<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协作功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>协作功能测试</h1>
        
        <div class="test-section">
            <h2>章节分配测试</h2>
            <button onclick="testChapterAssignments()" class="btn btn-primary">测试章节分配</button>
            <div id="test-results"></div>
        </div>

        <!-- 章节分配列表 -->
        <div class="assignments-container">
            <div class="assignments-header-row">
                <div class="header-cell">章节</div>
                <div class="header-cell">主笔作者</div>
                <div class="header-cell">协作者</div>
                <div class="header-cell">审核者</div>
                <div class="header-cell">状态</div>
                <div class="header-cell">进度</div>
                <div class="header-cell">截止日期</div>
                <div class="header-cell">操作</div>
            </div>
            <div class="assignments-list" id="chapter-assignments-list">
                <div class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>加载章节分配中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 模拟Supabase -->
    <script>
        window.supabaseManager = {
            getCurrentUser: () => Promise.resolve({
                id: 'test-user',
                email: '<EMAIL>',
                user_metadata: { full_name: '测试用户' }
            }),
            supabase: {
                from: () => ({
                    select: () => ({
                        eq: () => ({
                            order: () => Promise.resolve({ data: [], error: null })
                        })
                    })
                })
            }
        };
    </script>

    <script src="collaboration.js"></script>

    <script>
        async function testChapterAssignments() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>开始测试...</p>';

            try {
                // 设置模拟项目
                collaborationManager.currentProject = {
                    id: 'test-project-1',
                    title: '测试项目',
                    description: '这是一个测试项目'
                };
                collaborationManager.currentProjectId = 'test-project-1';

                // 测试渲染模拟数据
                collaborationManager.renderMockAssignments();

                resultsDiv.innerHTML = '<p style="color: green;">✅ 章节分配渲染成功！</p>';
            } catch (error) {
                resultsDiv.innerHTML = `<p style="color: red;">❌ 测试失败: ${error.message}</p>`;
                console.error('测试失败:', error);
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testChapterAssignments();
            }, 1000);
        });
    </script>
</body>
</html>
