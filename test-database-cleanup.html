<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库清理功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1e293b;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin: 5px;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
        }
        
        .btn-success {
            background: #059669;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .status-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #1e293b;
        }
        
        .log-container {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 3px;
        }
        
        .log-entry.success {
            color: #10b981;
        }
        
        .log-entry.error {
            color: #ef4444;
        }
        
        .log-entry.warning {
            color: #f59e0b;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 数据库清理功能测试</h1>
            <p>测试和验证数据库清理功能的正确性</p>
        </div>
        
        <div class="alert alert-info">
            <strong>测试说明：</strong>此页面用于测试数据库清理功能，包括检查数据状态、模拟问题数据、执行清理等操作。
        </div>
        
        <!-- 连接状态 -->
        <div class="test-section">
            <h3>🔌 连接状态</h3>
            <div id="connectionStatus">
                <p>正在检查Supabase连接...</p>
            </div>
            <button class="btn btn-secondary" onclick="checkConnection()">重新检查连接</button>
        </div>
        
        <!-- 数据状态 -->
        <div class="test-section">
            <h3>📊 当前数据状态</h3>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-label">总章节数</div>
                    <div class="status-value" id="totalChapters">-</div>
                </div>
                <div class="status-card">
                    <div class="status-label">outline_id为null</div>
                    <div class="status-value" id="nullOutlineId">-</div>
                </div>
                <div class="status-card">
                    <div class="status-label">有效关联</div>
                    <div class="status-value" id="validOutlineId">-</div>
                </div>
                <div class="status-card">
                    <div class="status-label">总大纲数</div>
                    <div class="status-value" id="totalOutlines">-</div>
                </div>
            </div>
            <button class="btn btn-primary" onclick="analyzeData()">分析数据</button>
        </div>
        
        <!-- 测试数据操作 -->
        <div class="test-section">
            <h3>🧪 测试数据操作</h3>
            <p>用于创建测试数据和模拟问题场景</p>
            <button class="btn btn-secondary" onclick="createTestData()">创建测试数据</button>
            <button class="btn btn-danger" onclick="simulateProblems()">模拟数据问题</button>
            <button class="btn btn-secondary" onclick="cleanTestData()">清理测试数据</button>
        </div>
        
        <!-- 清理操作 -->
        <div class="test-section">
            <h3>🛠️ 数据库清理</h3>
            <p>执行数据库清理和修复操作</p>
            <button class="btn btn-success" onclick="runCleanup()" id="cleanupBtn">开始清理</button>
            <button class="btn btn-primary" onclick="verifyResults()">验证结果</button>
        </div>
        
        <!-- 日志输出 -->
        <div class="test-section">
            <h3>📝 操作日志</h3>
            <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
            <div class="log-container" id="logContainer">
                <div class="log-entry">等待操作...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-config.js"></script>
    <script>
        // 日志系统
        const logs = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="log-entry ${type}">[${timestamp}] ${message}</div>`;
            logs.push(logEntry);
            
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = logs.join('');
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            logs.length = 0;
            document.getElementById('logContainer').innerHTML = '<div class="log-entry">日志已清空</div>';
        }
        
        // 检查连接状态
        async function checkConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.innerHTML = '<p>正在检查连接...</p>';
            
            try {
                if (!supabaseManager || !supabaseManager.supabase) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                // 测试连接
                const { data, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('count')
                    .limit(1);
                
                if (error) {
                    throw error;
                }
                
                statusDiv.innerHTML = '<div class="alert alert-success">✅ Supabase连接正常</div>';
                addLog('Supabase连接检查成功', 'success');
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="alert alert-error">❌ 连接失败: ${error.message}</div>`;
                addLog(`连接检查失败: ${error.message}`, 'error');
            }
        }
        
        // 分析数据状态
        async function analyzeData() {
            addLog('开始分析数据状态...', 'info');
            
            try {
                // 获取章节数据
                const { data: chapters, error: chaptersError } = await supabaseManager.supabase
                    .from('chapters')
                    .select('id, outline_id');
                
                if (chaptersError) throw chaptersError;
                
                // 获取大纲数据
                const { data: outlines, error: outlinesError } = await supabaseManager.supabase
                    .from('outlines')
                    .select('id');
                
                if (outlinesError) throw outlinesError;
                
                const totalChapters = chapters?.length || 0;
                const nullOutlineId = chapters?.filter(c => !c.outline_id).length || 0;
                const validOutlineId = totalChapters - nullOutlineId;
                const totalOutlines = outlines?.length || 0;
                
                // 更新显示
                document.getElementById('totalChapters').textContent = totalChapters;
                document.getElementById('nullOutlineId').textContent = nullOutlineId;
                document.getElementById('validOutlineId').textContent = validOutlineId;
                document.getElementById('totalOutlines').textContent = totalOutlines;
                
                addLog(`数据分析完成: 总章节${totalChapters}, 问题章节${nullOutlineId}, 总大纲${totalOutlines}`, 'success');
                
                if (nullOutlineId > 0) {
                    addLog(`⚠️ 发现${nullOutlineId}个章节的outline_id为null`, 'warning');
                }
                
            } catch (error) {
                addLog(`数据分析失败: ${error.message}`, 'error');
            }
        }
        
        // 创建测试数据
        async function createTestData() {
            addLog('开始创建测试数据...', 'info');
            
            try {
                // 这里可以添加创建测试数据的逻辑
                addLog('测试数据创建功能待实现', 'warning');
                
            } catch (error) {
                addLog(`创建测试数据失败: ${error.message}`, 'error');
            }
        }
        
        // 模拟数据问题
        async function simulateProblems() {
            if (!confirm('确定要模拟数据问题吗？这将修改现有数据。')) {
                return;
            }
            
            addLog('开始模拟数据问题...', 'warning');
            
            try {
                // 这里可以添加模拟问题的逻辑
                addLog('数据问题模拟功能待实现', 'warning');
                
            } catch (error) {
                addLog(`模拟数据问题失败: ${error.message}`, 'error');
            }
        }
        
        // 清理测试数据
        async function cleanTestData() {
            if (!confirm('确定要清理测试数据吗？')) {
                return;
            }
            
            addLog('开始清理测试数据...', 'info');
            
            try {
                // 这里可以添加清理测试数据的逻辑
                addLog('测试数据清理功能待实现', 'warning');
                
            } catch (error) {
                addLog(`清理测试数据失败: ${error.message}`, 'error');
            }
        }
        
        // 运行清理
        async function runCleanup() {
            const btn = document.getElementById('cleanupBtn');
            btn.disabled = true;
            btn.textContent = '清理中...';
            
            addLog('开始数据库清理...', 'info');
            
            try {
                // 这里调用实际的清理函数
                if (typeof startDatabaseCleanup === 'function') {
                    await startDatabaseCleanup();
                } else {
                    addLog('清理函数未找到，请确保已加载app.js', 'error');
                }
                
            } catch (error) {
                addLog(`数据库清理失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '开始清理';
            }
        }
        
        // 验证结果
        async function verifyResults() {
            addLog('开始验证清理结果...', 'info');
            
            try {
                await analyzeData();
                addLog('结果验证完成', 'success');
                
            } catch (error) {
                addLog(`结果验证失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查连接和分析数据
        window.addEventListener('load', async () => {
            addLog('测试页面已加载', 'info');
            await checkConnection();
            await analyzeData();
        });
    </script>
</body>
</html>
