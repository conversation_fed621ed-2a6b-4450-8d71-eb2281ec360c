# 《大模型技术与油气应用概论》多用户协作系统快速开始指南

## 🚀 5分钟快速体验

### 步骤1：创建测试用户（2分钟）

1. **打开测试用户创建工具**
   ```
   访问：create-test-users.html
   ```

2. **批量创建测试用户**
   - 点击"批量创建所有用户"按钮
   - 等待用户创建完成（约1-2分钟）
   - 记录测试账户信息

3. **测试账户列表**
   ```
   项目所有者：<EMAIL>
   管理员：    <EMAIL>
   编辑者：    <EMAIL>
   作者：      <EMAIL>
   审阅者：    <EMAIL>
   
   统一密码：test123456
   ```

### 步骤2：体验协作功能（3分钟）

1. **登录项目所有者账户**
   ```
   访问：auth.html
   邮箱：<EMAIL>
   密码：test123456
   ```

2. **创建测试项目**
   - 进入项目管理页面
   - 创建新项目："协作测试项目"
   - 设置项目描述和基本信息

3. **邀请团队成员**
   - 点击项目的"用户管理"按钮
   - 使用邀请功能添加其他测试用户
   - 分配不同角色权限

4. **测试协作编辑**
   - 进入主编辑器
   - 创建章节内容
   - 观察在线用户状态
   - 测试实时协作功能

## 🎯 核心功能演示

### 用户角色权限演示

| 功能 | 所有者 | 管理员 | 编辑者 | 作者 | 审阅者 |
|------|--------|--------|--------|------|--------|
| 项目管理 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 用户邀请 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 章节分配 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 内容编写 | ✅ | ✅ | ✅ | ✅* | ❌ |
| 内容审核 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 评论反馈 | ✅ | ✅ | ✅ | ✅ | ✅ |

*作者只能编写分配给自己的章节

### 协作流程演示

```mermaid
graph TD
    A[项目创建] --> B[邀请成员]
    B --> C[角色分配]
    C --> D[章节分配]
    D --> E[协作编写]
    E --> F[内容审核]
    F --> G{审核通过?}
    G -->|是| H[发布完成]
    G -->|否| I[修改建议]
    I --> E
```

## 🔧 功能测试清单

### 基础功能测试

- [ ] **用户注册登录**
  - 新用户注册
  - 用户登录/登出
  - 密码重置

- [ ] **项目管理**
  - 创建新项目
  - 编辑项目信息
  - 删除项目

- [ ] **用户管理**
  - 邀请新用户
  - 分配用户角色
  - 移除项目成员

### 协作功能测试

- [ ] **权限控制**
  - 不同角色权限验证
  - 越权访问防护
  - 数据安全检查

- [ ] **实时协作**
  - 多用户同时在线
  - 内容实时同步
  - 冲突检测处理

- [ ] **审核流程**
  - 章节分配管理
  - 内容审核流程
  - 评论反馈系统

## 🛠️ 故障排除

### 常见问题

1. **用户无法登录**
   ```
   检查项：
   - 邮箱地址是否正确
   - 密码是否正确
   - 网络连接是否正常
   - Supabase服务是否可用
   ```

2. **权限错误**
   ```
   检查项：
   - 用户角色是否正确分配
   - 项目成员状态是否活跃
   - RLS策略是否正确配置
   ```

3. **邀请失败**
   ```
   检查项：
   - 邮箱地址格式是否正确
   - 邀请是否已过期
   - 用户是否已是项目成员
   ```

### 调试工具

1. **浏览器开发者工具**
   ```
   F12 → Console → 查看错误信息
   F12 → Network → 检查网络请求
   ```

2. **系统验证脚本**
   ```javascript
   // 在浏览器控制台运行
   runSystemVerification()
   ```

3. **协作功能测试**
   ```javascript
   // 在浏览器控制台运行
   runCollaborationTests()
   ```

## 📚 进阶使用

### 自定义配置

1. **修改用户角色权限**
   - 编辑 `collaboration.js` 中的权限矩阵
   - 更新数据库RLS策略
   - 重新部署应用

2. **扩展协作功能**
   - 添加新的用户角色
   - 实现自定义审核流程
   - 集成第三方通知服务

3. **性能优化**
   - 优化数据库查询
   - 实现数据缓存
   - 配置CDN加速

### 集成指南

1. **邮件服务集成**
   ```javascript
   // 在 user-management.js 中配置
   const emailService = {
     provider: 'sendgrid', // 或其他邮件服务
     apiKey: 'your-api-key',
     templates: {
       invitation: 'template-id'
     }
   };
   ```

2. **实时通知集成**
   ```javascript
   // 配置WebSocket或Server-Sent Events
   const realtimeConfig = {
     enabled: true,
     provider: 'supabase-realtime',
     channels: ['project-updates', 'user-activity']
   };
   ```

## 🎉 完成！

恭喜您已经成功体验了多用户协作功能！

### 下一步建议

1. **深入测试**
   - 使用不同角色账户测试各种场景
   - 验证权限边界和数据安全
   - 测试并发协作和冲突处理

2. **定制化配置**
   - 根据团队需求调整角色权限
   - 配置邮件通知和提醒
   - 优化用户界面和体验

3. **生产部署**
   - 配置生产环境数据库
   - 设置域名和SSL证书
   - 配置监控和备份策略

### 技术支持

如需帮助，请：

1. 查看 `多用户协作功能部署指南.md`
2. 运行系统验证脚本检查问题
3. 检查浏览器控制台错误信息
4. 联系技术支持团队

---

**祝您使用愉快！** 🎊

*《大模型技术与油气应用概论》编写团队*
