<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库诊断 - 专业著作智能编纂系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-stethoscope"></i> 数据库诊断工具</h1>
            <p>检查章节分配功能的数据库状态</p>
        </div>

        <div class="diagnosis-controls">
            <button id="run-diagnosis" class="btn btn-primary">
                <i class="fas fa-play"></i> 开始诊断
            </button>
            <button id="clear-results" class="btn btn-secondary">
                <i class="fas fa-trash"></i> 清除结果
            </button>
            <button id="create-sample-data" class="btn btn-success">
                <i class="fas fa-plus"></i> 创建示例数据
            </button>
        </div>

        <div id="diagnosis-results" class="diagnosis-results">
            <div class="loading-state">
                <p>点击"开始诊断"来检查数据库状态</p>
            </div>
        </div>

        <div id="sample-data-section" class="sample-data-section" style="display: none;">
            <h3>创建示例数据</h3>
            <p>如果数据库中缺少测试数据，可以创建一些示例数据用于测试章节分配功能。</p>
            <div class="sample-data-controls">
                <button id="create-project" class="btn btn-outline">创建示例项目</button>
                <button id="create-chapters" class="btn btn-outline">创建示例章节</button>
                <button id="create-assignments" class="btn btn-outline">创建示例分配</button>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-config.js"></script>
    <script src="supabase-config-manager.js"></script>
    <script src="database-diagnosis.js"></script>

    <script>
        let currentUser = null;
        let diagnosisResults = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await initializeDiagnosis();
            setupEventListeners();
        });

        async function initializeDiagnosis() {
            try {
                // 检查用户认证状态
                currentUser = await supabaseManager.getCurrentUser();
                if (!currentUser) {
                    showMessage('用户未登录，某些功能可能无法使用', 'warning');
                }
                
                showMessage('诊断工具已准备就绪', 'success');
            } catch (error) {
                console.error('初始化失败:', error);
                showMessage('初始化失败: ' + error.message, 'error');
            }
        }

        function setupEventListeners() {
            document.getElementById('run-diagnosis').addEventListener('click', runDiagnosis);
            document.getElementById('clear-results').addEventListener('click', clearResults);
            document.getElementById('create-sample-data').addEventListener('click', toggleSampleDataSection);
            
            // 示例数据创建按钮
            document.getElementById('create-project').addEventListener('click', createSampleProject);
            document.getElementById('create-chapters').addEventListener('click', createSampleChapters);
            document.getElementById('create-assignments').addEventListener('click', createSampleAssignments);
        }

        async function runDiagnosis() {
            const resultsContainer = document.getElementById('diagnosis-results');
            const runButton = document.getElementById('run-diagnosis');
            
            runButton.disabled = true;
            runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 诊断中...';
            
            resultsContainer.innerHTML = '<div class="loading-state"><i class="fas fa-spinner fa-spin"></i><p>正在检查数据库状态...</p></div>';

            try {
                const diagnosis = new DatabaseDiagnosis();
                await diagnosis.runDiagnosis();
                diagnosisResults = diagnosis.results;
                
                displayResults(diagnosisResults);
            } catch (error) {
                console.error('诊断失败:', error);
                resultsContainer.innerHTML = `<div class="error-state"><p>诊断失败: ${error.message}</p></div>`;
            } finally {
                runButton.disabled = false;
                runButton.innerHTML = '<i class="fas fa-play"></i> 重新诊断';
            }
        }

        function displayResults(results) {
            const resultsContainer = document.getElementById('diagnosis-results');
            
            let html = '<div class="diagnosis-report">';
            html += '<h3>诊断结果</h3>';
            
            results.forEach(result => {
                const statusClass = result.status.toLowerCase();
                const statusIcon = {
                    'ok': 'fas fa-check-circle',
                    'warning': 'fas fa-exclamation-triangle',
                    'error': 'fas fa-times-circle',
                    'critical': 'fas fa-exclamation-circle'
                }[statusClass] || 'fas fa-question-circle';
                
                html += `
                    <div class="result-item ${statusClass}">
                        <div class="result-header">
                            <i class="${statusIcon}"></i>
                            <strong>${result.category}</strong>
                            <span class="status">${result.status}</span>
                        </div>
                        <div class="result-message">${result.message}</div>
                        ${result.data ? `<div class="result-data"><pre>${JSON.stringify(result.data, null, 2)}</pre></div>` : ''}
                    </div>
                `;
            });
            
            html += '</div>';
            
            // 添加建议
            html += generateRecommendationsHTML(results);
            
            resultsContainer.innerHTML = html;
        }

        function generateRecommendationsHTML(results) {
            const errors = results.filter(r => r.status === 'ERROR' || r.status === 'CRITICAL');
            const warnings = results.filter(r => r.status === 'WARNING');
            
            let html = '<div class="recommendations">';
            html += '<h4>建议操作</h4>';
            
            if (errors.length > 0) {
                html += '<div class="critical-issues">';
                html += '<h5><i class="fas fa-exclamation-circle"></i> 严重问题</h5>';
                html += '<ul>';
                errors.forEach(error => {
                    html += `<li>${error.message}</li>`;
                });
                html += '</ul>';
                html += '</div>';
            }
            
            if (warnings.length > 0) {
                html += '<div class="warning-issues">';
                html += '<h5><i class="fas fa-exclamation-triangle"></i> 需要注意</h5>';
                html += '<ul>';
                warnings.forEach(warning => {
                    html += `<li>${warning.message}</li>`;
                });
                html += '</ul>';
                html += '</div>';
            }
            
            if (errors.length === 0 && warnings.length === 0) {
                html += '<div class="success-message"><i class="fas fa-check"></i> 数据库状态良好！</div>';
            }
            
            html += '</div>';
            return html;
        }

        function clearResults() {
            document.getElementById('diagnosis-results').innerHTML = '<div class="loading-state"><p>点击"开始诊断"来检查数据库状态</p></div>';
            diagnosisResults = [];
        }

        function toggleSampleDataSection() {
            const section = document.getElementById('sample-data-section');
            section.style.display = section.style.display === 'none' ? 'block' : 'none';
        }

        async function createSampleProject() {
            // 这里需要您协助实现
            showMessage('创建示例项目功能需要进一步实现', 'info');
        }

        async function createSampleChapters() {
            // 这里需要您协助实现
            showMessage('创建示例章节功能需要进一步实现', 'info');
        }

        async function createSampleAssignments() {
            // 这里需要您协助实现
            showMessage('创建示例分配功能需要进一步实现', 'info');
        }

        function showMessage(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);
            // 可以添加更好的消息显示机制
        }
    </script>

    <style>
        .diagnosis-results {
            margin-top: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }

        .result-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .result-item.ok {
            border-left-color: #10b981;
            background: #ecfdf5;
        }

        .result-item.warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
        }

        .result-item.error,
        .result-item.critical {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .result-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .result-data {
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }

        .result-data pre {
            background: #f3f4f6;
            padding: 0.5rem;
            border-radius: 4px;
            overflow-x: auto;
        }

        .recommendations {
            margin-top: 2rem;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }

        .sample-data-section {
            margin-top: 2rem;
            padding: 1rem;
            background: #f0f9ff;
            border-radius: 8px;
            border: 1px solid #0ea5e9;
        }

        .sample-data-controls {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .diagnosis-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</body>
</html>
