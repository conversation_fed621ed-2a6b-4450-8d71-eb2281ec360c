<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能简单测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #4338ca;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 导出功能简单测试</h1>
        
        <div class="test-section">
            <h3>📄 PDF导出测试</h3>
            <p style="font-size: 12px; color: #666;">将在新窗口中打开，使用浏览器打印功能保存为PDF</p>
            <button class="btn" onclick="testPDF()" id="pdf-btn">测试PDF导出</button>
            <div id="pdf-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📝 DOCX导出测试</h3>
            <button class="btn" onclick="testDOCX()" id="docx-btn">测试DOCX导出</button>
            <div id="docx-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🌐 HTML导出测试</h3>
            <button class="btn" onclick="testHTML()" id="html-btn">测试HTML导出</button>
            <div id="html-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💾 JSON导出测试</h3>
            <button class="btn" onclick="testJSON()" id="json-btn">测试JSON导出</button>
            <div id="json-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 测试状态</h3>
            <div id="service-status" class="status info">正在检查导出服务...</div>
        </div>
    </div>

    <script src="export-service.js"></script>
    <script>
        // 测试数据
        const testData = {
            project: {
                id: 'test-001',
                title: '导出功能测试项目',
                description: '这是一个用于测试导出功能的示例项目，包含中文内容测试。',
                created_at: new Date().toISOString(),
                status: 'active'
            },
            outlines: [
                {
                    id: 'outline-1',
                    title: '第一章 测试章节',
                    level: 1,
                    sort_order: 1,
                    children: [
                        {
                            id: 'outline-1-1',
                            title: '1.1 子章节测试',
                            level: 2,
                            sort_order: 1,
                            children: []
                        }
                    ]
                }
            ],
            chapters: [
                {
                    id: 'chapter-1',
                    title: '第一章 测试章节',
                    summary: '这是一个测试章节的摘要内容，用于验证导出功能是否正常工作。',
                    outline_id: 'outline-1',
                    project_id: 'test-001'
                }
            ],
            references: [],
            members: [],
            exportDate: new Date().toISOString(),
            exportVersion: '1.0'
        };

        function showStatus(elementId, message, type) {
            const statusDiv = document.getElementById(elementId);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        async function testPDF() {
            const btn = document.getElementById('pdf-btn');
            btn.disabled = true;
            btn.textContent = '导出中...';

            try {
                if (typeof exportService === 'undefined') {
                    throw new Error('导出服务未加载');
                }
                
                const result = await exportService.exportToPDF(testData);
                showStatus('pdf-status', 'PDF导出成功！' + (result.note || ''), 'success');
            } catch (error) {
                showStatus('pdf-status', 'PDF导出失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试PDF导出';
            }
        }

        async function testDOCX() {
            const btn = document.getElementById('docx-btn');
            btn.disabled = true;
            btn.textContent = '导出中...';

            try {
                if (typeof exportService === 'undefined') {
                    throw new Error('导出服务未加载');
                }
                
                const result = await exportService.exportToDOCX(testData);
                showStatus('docx-status', 'DOCX导出成功！' + (result.note || ''), 'success');
            } catch (error) {
                showStatus('docx-status', 'DOCX导出失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试DOCX导出';
            }
        }

        async function testHTML() {
            const btn = document.getElementById('html-btn');
            btn.disabled = true;
            btn.textContent = '导出中...';

            try {
                if (typeof exportService === 'undefined') {
                    throw new Error('导出服务未加载');
                }
                
                const result = await exportService.exportToHTML(testData);
                showStatus('html-status', 'HTML导出成功！' + (result.note || ''), 'success');
            } catch (error) {
                showStatus('html-status', 'HTML导出失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试HTML导出';
            }
        }

        async function testJSON() {
            const btn = document.getElementById('json-btn');
            btn.disabled = true;
            btn.textContent = '导出中...';

            try {
                if (typeof exportService === 'undefined') {
                    throw new Error('导出服务未加载');
                }
                
                const result = await exportService.exportToJSON(testData);
                showStatus('json-status', 'JSON导出成功！', 'success');
            } catch (error) {
                showStatus('json-status', 'JSON导出失败: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试JSON导出';
            }
        }

        // 页面加载完成后检查服务状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (typeof exportService !== 'undefined') {
                    showStatus('service-status', '✅ 导出服务已加载，支持格式: ' + exportService.supportedFormats.join(', '), 'success');
                } else {
                    showStatus('service-status', '❌ 导出服务未加载，请检查export-service.js文件', 'error');
                }
            }, 100);
        });
    </script>
</body>
</html>
