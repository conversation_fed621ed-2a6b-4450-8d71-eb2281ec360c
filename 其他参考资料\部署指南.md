# 《大模型技术与油气应用概论》多用户协作系统部署指南

## 系统概述

本系统是一个基于 Web 的多用户协作编写平台，支持：
- 用户注册和认证
- 项目管理和权限控制
- 实时协作编辑
- 章节分配和进度跟踪
- 参考文献管理
- 结构图绘制

## 技术架构

- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **实时通信**: Supabase Realtime
- **富文本编辑**: Quill.js
- **图表绘制**: Mermaid.js

## 部署前准备

### 1. 环境要求
- 现代 Web 浏览器（Chrome 80+, Firefox 75+, Safari 13+, Edge 80+）
- Supabase 账户
- Web 服务器（可选，用于生产环境）

### 2. 文件清单
确保您有以下文件：
```
├── index.html              # 主应用界面
├── auth.html              # 用户认证界面
├── styles.css             # 样式文件
├── app.js                 # 主应用逻辑
├── collaboration.js       # 协作功能
├── supabase-config.js     # Supabase 配置
├── database-schema.sql    # 数据库结构
├── backup-helper.html     # 备份助手
├── sample-data.js         # 示例数据
├── README.md              # 项目说明
├── 使用指南.md            # 使用指南
├── Supabase配置指南.md    # 数据库配置指南
└── 部署指南.md            # 本文件
```

## 部署步骤

### 第一步：配置 Supabase 数据库

1. **创建 Supabase 项目**
   - 访问 [https://supabase.com](https://supabase.com)
   - 注册账户并创建新项目
   - 记录项目 URL 和 API 密钥

2. **执行数据库脚本**
   - 在 Supabase SQL Editor 中执行 `database-schema.sql`
   - 确认所有表和策略创建成功

3. **配置认证设置**
   - 启用邮箱认证
   - 配置邮箱模板（可选）

详细步骤请参考 `Supabase配置指南.md`

### 第二步：配置应用

1. **更新 Supabase 配置**
   编辑 `supabase-config.js` 文件：
   ```javascript
   this.supabaseUrl = 'https://your-project-ref.supabase.co';
   this.supabaseKey = 'your-anon-key';
   ```

2. **测试配置**
   - 在浏览器中打开 `auth.html`
   - 尝试注册新用户
   - 验证数据库连接

### 第三步：本地部署

#### 方法一：直接打开文件
1. 将所有文件放在同一目录下
2. 双击 `auth.html` 开始使用
3. 适用于个人使用或小团队测试

#### 方法二：本地 Web 服务器
1. **使用 Python 简单服务器**：
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```

2. **使用 Node.js 服务器**：
   ```bash
   npx http-server -p 8000
   ```

3. **使用 PHP 内置服务器**：
   ```bash
   php -S localhost:8000
   ```

4. 访问 `http://localhost:8000/auth.html`

### 第四步：生产环境部署

#### 选项一：静态网站托管

**1. Netlify 部署**
1. 将代码上传到 GitHub 仓库
2. 在 Netlify 中连接 GitHub 仓库
3. 设置构建命令（无需构建）
4. 部署完成后配置自定义域名

**2. Vercel 部署**
1. 安装 Vercel CLI: `npm i -g vercel`
2. 在项目目录运行: `vercel`
3. 按提示完成部署

**3. GitHub Pages**
1. 将代码推送到 GitHub 仓库
2. 在仓库设置中启用 GitHub Pages
3. 选择源分支（通常是 main）

#### 选项二：传统 Web 服务器

**1. Apache 配置**
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/your/app
    
    <Directory /path/to/your/app>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

**2. Nginx 配置**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/app;
    index auth.html;
    
    location / {
        try_files $uri $uri/ =404;
    }
}
```

### 第五步：域名和 SSL 配置

1. **配置域名**
   - 将域名指向服务器 IP
   - 更新 Supabase 项目的 Site URL

2. **配置 SSL 证书**
   - 使用 Let's Encrypt 免费证书
   - 或购买商业 SSL 证书

3. **更新 Supabase 设置**
   - 在 Supabase 项目设置中添加生产域名
   - 配置 CORS 设置

## 环境配置

### 开发环境
- 使用本地文件或本地服务器
- 可以使用测试数据库
- 启用详细日志

### 测试环境
- 部署到测试服务器
- 使用独立的 Supabase 项目
- 进行功能和性能测试

### 生产环境
- 使用 HTTPS
- 配置 CDN 加速
- 启用监控和日志
- 设置备份策略

## 性能优化

### 1. 前端优化
- 压缩 CSS 和 JavaScript 文件
- 使用 CDN 加载第三方库
- 启用浏览器缓存
- 优化图片资源

### 2. 数据库优化
- 创建适当的索引
- 优化查询语句
- 配置连接池
- 监控查询性能

### 3. 网络优化
- 启用 Gzip 压缩
- 配置 CDN
- 优化 DNS 解析
- 使用 HTTP/2

## 安全配置

### 1. Supabase 安全
- 配置行级安全策略 (RLS)
- 限制 API 访问域名
- 定期更新密钥
- 监控异常访问

### 2. Web 服务器安全
- 配置防火墙
- 启用 HTTPS
- 设置安全头部
- 定期更新服务器

### 3. 应用安全
- 输入验证和清理
- XSS 防护
- CSRF 防护
- 权限验证

## 监控和维护

### 1. 监控指标
- 用户活跃度
- 系统性能
- 错误率
- 数据库使用情况

### 2. 日志管理
- 应用日志
- 访问日志
- 错误日志
- 安全日志

### 3. 备份策略
- 数据库自动备份
- 代码版本控制
- 配置文件备份
- 恢复测试

## 故障排除

### 常见问题

**1. 用户无法登录**
- 检查 Supabase 配置
- 验证邮箱设置
- 查看浏览器控制台错误

**2. 实时功能不工作**
- 检查 WebSocket 连接
- 验证实时订阅配置
- 检查网络防火墙设置

**3. 页面加载缓慢**
- 检查网络连接
- 优化资源加载
- 检查服务器性能

**4. 数据同步问题**
- 检查数据库连接
- 验证权限设置
- 查看网络延迟

### 调试工具
- 浏览器开发者工具
- Supabase 仪表板
- 网络监控工具
- 性能分析工具

## 扩展和定制

### 1. 功能扩展
- 添加新的编辑工具
- 集成第三方服务
- 自定义工作流程
- 增加导出格式

### 2. 界面定制
- 修改主题样式
- 自定义布局
- 添加品牌元素
- 响应式优化

### 3. 集成选项
- 单点登录 (SSO)
- 第三方存储
- 消息通知
- 版本控制系统

## 支持和维护

### 技术支持
- 查看项目文档
- 检查 GitHub Issues
- 联系开发团队
- 社区支持论坛

### 更新维护
- 定期更新依赖库
- 应用安全补丁
- 功能升级
- 性能优化

## 部署检查清单

### 部署前检查
- [ ] Supabase 项目已配置
- [ ] 数据库表已创建
- [ ] 应用配置已更新
- [ ] 本地测试通过
- [ ] 安全设置已配置

### 部署后验证
- [ ] 用户注册功能正常
- [ ] 登录功能正常
- [ ] 项目创建功能正常
- [ ] 实时协作功能正常
- [ ] 数据保存功能正常
- [ ] 权限控制正常

### 生产环境检查
- [ ] HTTPS 已启用
- [ ] 域名已配置
- [ ] 监控已设置
- [ ] 备份已配置
- [ ] 性能已优化

完成以上步骤后，您的多用户协作系统就可以正式投入使用了！
