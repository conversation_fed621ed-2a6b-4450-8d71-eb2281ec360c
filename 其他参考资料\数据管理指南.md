# 《大模型技术与油气应用概论》数据管理指南

## 数据存储位置详解

### 1. 当前存储方式：浏览器本地存储（LocalStorage）

#### 存储位置
- **Chrome浏览器**：
  ```
  C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default\Local Storage\leveldb\
  ```
- **Firefox浏览器**：
  ```
  C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\[配置文件]\storage\default\
  ```
- **Edge浏览器**：
  ```
  C:\Users\<USER>\AppData\Local\Microsoft\Edge\User Data\Default\Local Storage\leveldb\
  ```

#### 存储键名
- 数据存储键：`llm-book-project`
- 数据格式：JSON字符串

### 2. 存储的数据内容

```json
{
  "title": "《大模型技术与油气应用概论》",
  "outline": [...],          // 大纲结构
  "chapters": {...},         // 章节内容
  "references": [...],       // 参考文献
  "diagrams": {...},        // 图表数据
  "progress": {...}         // 进度信息
}
```

## 数据安全风险与对策

### ⚠️ 潜在风险

1. **浏览器数据清理**
   - 清除浏览器数据时会丢失
   - 浏览器重装会丢失数据
   - 系统重装会丢失数据

2. **设备限制**
   - 数据绑定到特定浏览器
   - 无法在不同设备间自动同步
   - 浏览器配置文件损坏可能导致数据丢失

3. **存储空间限制**
   - LocalStorage通常限制5-10MB
   - 大量内容可能超出限制

### ✅ 推荐的数据管理策略

#### 1. 定期备份（重要！）

**每日备份**：
- 编写完成后点击"导出项目"
- 将JSON文件保存到安全位置
- 建议文件命名：`llm-book-backup-YYYY-MM-DD.json`

**每周备份**：
- 创建专门的备份文件夹
- 保留多个版本的备份文件
- 可以上传到云盘（OneDrive、百度网盘等）

#### 2. 多重备份策略

```
备份文件夹结构：
├── 每日备份/
│   ├── llm-book-backup-2024-01-15.json
│   ├── llm-book-backup-2024-01-16.json
│   └── ...
├── 每周备份/
│   ├── llm-book-week-2024-W03.json
│   └── ...
├── 重要节点备份/
│   ├── llm-book-第一篇完成.json
│   ├── llm-book-第二篇完成.json
│   └── ...
└── 云盘同步/
    └── [同步到云盘的备份文件]
```

#### 3. 版本控制建议

**Git版本控制**（推荐给技术用户）：
```bash
# 初始化Git仓库
git init
git add *.json
git commit -m "初始版本"

# 每次重要更新后
git add llm-book-backup-latest.json
git commit -m "完成第X章编写"
```

## 数据恢复方法

### 1. 从备份文件恢复

1. 打开系统，点击"大纲管理"
2. 点击"导入大纲"
3. 选择"JSON文件"
4. 选择备份的JSON文件
5. 系统会恢复所有数据

### 2. 浏览器数据恢复

如果忘记备份，可以尝试：

1. **浏览器历史恢复**：
   - 检查浏览器是否有数据恢复功能
   - 查看是否有自动备份

2. **系统还原**：
   - 使用Windows系统还原点
   - 恢复到数据丢失前的状态

## 改进建议：云端存储方案

为了更好的数据安全，我可以为您升级系统，添加以下功能：

### 1. 自动云端备份
- 集成OneDrive、Google Drive等云盘API
- 自动定期备份到云端
- 多设备同步功能

### 2. 本地文件存储
- 改为直接保存到本地文件
- 支持选择存储位置
- 自动备份功能

### 3. 数据库存储
- 使用本地SQLite数据库
- 更好的数据管理和查询
- 支持数据导入导出

## 立即行动建议

### 🚨 紧急备份步骤

1. **立即备份当前数据**：
   - 打开系统
   - 点击右上角"导出"按钮
   - 保存JSON文件到安全位置

2. **设置备份提醒**：
   - 在手机或电脑设置每日提醒
   - 提醒内容："备份专著编写数据"

3. **创建备份文件夹**：
   ```
   D:\专著备份\
   ├── 每日备份\
   ├── 重要节点\
   └── 云盘同步\
   ```

### 📋 备份检查清单

- [ ] 已导出当前最新数据
- [ ] 备份文件保存到安全位置
- [ ] 设置了定期备份提醒
- [ ] 备份文件已上传云盘
- [ ] 测试过数据恢复流程

## 技术升级选项

如果您需要更安全的数据存储方案，我可以为您：

1. **升级为本地文件存储版本**
   - 数据直接保存为文件
   - 可以选择存储位置
   - 支持自动备份

2. **添加云端同步功能**
   - 集成云盘API
   - 自动同步到云端
   - 多设备访问

3. **开发桌面应用版本**
   - 使用Electron框架
   - 更好的文件管理
   - 离线使用

请告诉我您希望采用哪种方案，我可以为您进行相应的升级开发。

## 联系支持

如果遇到数据丢失或其他问题：
1. 首先检查备份文件
2. 尝试浏览器数据恢复
3. 联系技术支持获取帮助

---

**重要提醒**：数据安全是写作过程中最重要的环节，请务必养成定期备份的习惯！
