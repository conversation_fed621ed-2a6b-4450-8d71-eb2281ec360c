<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节功能自动化测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-button.success {
            background: #27ae60;
        }
        .test-button.error {
            background: #e74c3c;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .test-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background: #f39c12; }
        .status-running { background: #3498db; }
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .test-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>章节功能自动化测试</h1>
            <p>测试章节保存功能和导航栏选中状态功能</p>
        </div>

        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="runChapterSaveTests()">测试章节保存</button>
            <button class="test-button" onclick="runNavigationTests()">测试导航状态</button>
            <button class="test-button" onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-section">
            <h3>测试结果</h3>
            <div id="test-results" class="test-results">
                <div class="test-item">
                    <span class="status-indicator status-pending"></span>
                    <span>等待测试开始...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <div id="test-log" class="test-log">
                测试日志将在这里显示...
            </div>
        </div>
    </div>

    <script>
        // 测试状态
        let testResults = [];
        let testLog = [];

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }

        // 更新日志显示
        function updateLogDisplay() {
            const logElement = document.getElementById('test-log');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 更新测试结果显示
        function updateResultsDisplay() {
            const resultsElement = document.getElementById('test-results');
            if (testResults.length === 0) {
                resultsElement.innerHTML = '<div class="test-item"><span class="status-indicator status-pending"></span><span>等待测试开始...</span></div>';
                return;
            }

            resultsElement.innerHTML = testResults.map(result => {
                const statusClass = result.status === 'success' ? 'status-success' : 
                                  result.status === 'error' ? 'status-error' : 
                                  result.status === 'running' ? 'status-running' : 'status-pending';
                return `<div class="test-item">
                    <span class="status-indicator ${statusClass}"></span>
                    <span>${result.name}: ${result.message}</span>
                </div>`;
            }).join('');
        }

        // 添加测试结果
        function addTestResult(name, status, message) {
            testResults.push({ name, status, message });
            updateResultsDisplay();
        }

        // 清除结果
        function clearResults() {
            testResults = [];
            testLog = [];
            updateResultsDisplay();
            updateLogDisplay();
            log('测试结果已清除');
        }

        // 模拟章节保存测试
        async function runChapterSaveTests() {
            log('开始章节保存功能测试');
            
            // 测试1: 检查Quill编辑器是否存在
            addTestResult('编辑器检查', 'running', '检查Quill编辑器...');
            try {
                if (typeof Quill !== 'undefined') {
                    addTestResult('编辑器检查', 'success', 'Quill编辑器已加载');
                    log('Quill编辑器检查通过');
                } else {
                    addTestResult('编辑器检查', 'error', 'Quill编辑器未加载');
                    log('Quill编辑器检查失败', 'error');
                    return;
                }
            } catch (error) {
                addTestResult('编辑器检查', 'error', `错误: ${error.message}`);
                log(`编辑器检查异常: ${error.message}`, 'error');
                return;
            }

            // 测试2: 检查保存函数是否存在
            addTestResult('保存函数检查', 'running', '检查保存函数...');
            try {
                if (typeof saveChapter === 'function' && typeof autoSaveChapter === 'function') {
                    addTestResult('保存函数检查', 'success', '保存函数存在');
                    log('保存函数检查通过');
                } else {
                    addTestResult('保存函数检查', 'error', '保存函数不存在');
                    log('保存函数检查失败', 'error');
                }
            } catch (error) {
                addTestResult('保存函数检查', 'error', `错误: ${error.message}`);
                log(`保存函数检查异常: ${error.message}`, 'error');
            }

            // 测试3: 检查数据格式转换
            addTestResult('数据格式测试', 'running', '测试Delta格式转换...');
            try {
                // 模拟创建一个简单的Delta对象
                const testDelta = { ops: [{ insert: '测试内容\n' }] };
                if (testDelta.ops && Array.isArray(testDelta.ops)) {
                    addTestResult('数据格式测试', 'success', 'Delta格式正确');
                    log('Delta格式测试通过');
                } else {
                    addTestResult('数据格式测试', 'error', 'Delta格式错误');
                    log('Delta格式测试失败', 'error');
                }
            } catch (error) {
                addTestResult('数据格式测试', 'error', `错误: ${error.message}`);
                log(`数据格式测试异常: ${error.message}`, 'error');
            }
        }

        // 模拟导航状态测试
        async function runNavigationTests() {
            log('开始导航状态功能测试');
            
            // 测试1: 检查showPanel函数
            addTestResult('面板函数检查', 'running', '检查showPanel函数...');
            try {
                if (typeof showPanel === 'function') {
                    addTestResult('面板函数检查', 'success', 'showPanel函数存在');
                    log('showPanel函数检查通过');
                } else {
                    addTestResult('面板函数检查', 'error', 'showPanel函数不存在');
                    log('showPanel函数检查失败', 'error');
                    return;
                }
            } catch (error) {
                addTestResult('面板函数检查', 'error', `错误: ${error.message}`);
                log(`面板函数检查异常: ${error.message}`, 'error');
                return;
            }

            // 测试2: 检查导航更新函数
            addTestResult('导航更新检查', 'running', '检查updateNavActiveState函数...');
            try {
                if (typeof updateNavActiveState === 'function') {
                    addTestResult('导航更新检查', 'success', 'updateNavActiveState函数存在');
                    log('updateNavActiveState函数检查通过');
                } else {
                    addTestResult('导航更新检查', 'error', 'updateNavActiveState函数不存在');
                    log('updateNavActiveState函数检查失败', 'error');
                }
            } catch (error) {
                addTestResult('导航更新检查', 'error', `错误: ${error.message}`);
                log(`导航更新检查异常: ${error.message}`, 'error');
            }

            // 测试3: 检查enterChapterEditMode函数
            addTestResult('编辑模式检查', 'running', '检查enterChapterEditMode函数...');
            try {
                if (typeof enterChapterEditMode === 'function') {
                    addTestResult('编辑模式检查', 'success', 'enterChapterEditMode函数存在');
                    log('enterChapterEditMode函数检查通过');
                } else {
                    addTestResult('编辑模式检查', 'error', 'enterChapterEditMode函数不存在');
                    log('enterChapterEditMode函数检查失败', 'error');
                }
            } catch (error) {
                addTestResult('编辑模式检查', 'error', `错误: ${error.message}`);
                log(`编辑模式检查异常: ${error.message}`, 'error');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            log('开始运行所有测试');
            clearResults();
            
            await runChapterSaveTests();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            await runNavigationTests();
            
            log('所有测试完成');
            
            // 统计结果
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            const totalCount = testResults.length;
            
            log(`测试总结: ${successCount}/${totalCount} 通过, ${errorCount} 失败`);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('章节功能测试页面已加载');
            log('点击"运行所有测试"开始测试');
        });
    </script>
</body>
</html>
