<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主应用程序集成测试 - 专业学术协作编著系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 24px;
            text-align: center;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .main-container {
            margin-top: 70px;
            display: flex;
            height: calc(100vh - 70px);
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e5e7eb;
            padding: 20px 0;
        }
        
        .nav-item {
            display: block;
            padding: 12px 24px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }
        
        .nav-item:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .nav-item.active {
            background: #eff6ff;
            color: #2563eb;
            border-right: 3px solid #2563eb;
        }
        
        .nav-item i {
            margin-right: 12px;
            width: 16px;
        }
        
        .content-area {
            flex: 1;
            background: white;
            overflow: auto;
        }
        
        .tab-content {
            display: none;
            padding: 24px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .placeholder {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .placeholder i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .placeholder h3 {
            margin: 0 0 8px 0;
            color: #374151;
            font-size: 18px;
            font-weight: 600;
        }
        
        .placeholder p {
            margin: 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>主应用程序章节分配集成测试</h1>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <button class="nav-item active" onclick="switchTab('dashboard')">
                <i class="fas fa-tachometer-alt"></i>
                项目概览
            </button>
            <button class="nav-item" onclick="switchTab('outline')">
                <i class="fas fa-list"></i>
                书籍目录
            </button>
            <button class="nav-item" onclick="switchTab('editor')">
                <i class="fas fa-edit"></i>
                章节编写
            </button>
            <button class="nav-item" onclick="switchTab('references')">
                <i class="fas fa-book"></i>
                文献管理
            </button>
            <button class="nav-item" onclick="switchTab('collaboration')">
                <i class="fas fa-users"></i>
                协作编著
            </button>
            <button class="nav-item" onclick="switchTab('settings')">
                <i class="fas fa-cog"></i>
                系统设置
            </button>
        </div>

        <div class="content-area">
            <!-- 项目概览 -->
            <div id="dashboard-tab" class="tab-content active">
                <div class="placeholder">
                    <i class="fas fa-tachometer-alt"></i>
                    <h3>项目概览</h3>
                    <p>项目仪表板和统计信息</p>
                </div>
            </div>

            <!-- 书籍目录 -->
            <div id="outline-tab" class="tab-content">
                <div class="placeholder">
                    <i class="fas fa-list"></i>
                    <h3>书籍目录</h3>
                    <p>管理书籍章节结构和大纲</p>
                </div>
            </div>

            <!-- 章节编写 -->
            <div id="editor-tab" class="tab-content">
                <div class="placeholder">
                    <i class="fas fa-edit"></i>
                    <h3>章节编写</h3>
                    <p>富文本编辑器和内容管理</p>
                </div>
            </div>

            <!-- 文献管理 -->
            <div id="references-tab" class="tab-content">
                <div class="placeholder">
                    <i class="fas fa-book"></i>
                    <h3>文献管理</h3>
                    <p>管理参考文献和引用</p>
                </div>
            </div>

            <!-- 协作编著 -->
            <div id="collaboration-tab" class="tab-content">
                <div class="collaboration-container">
                    <div class="collaboration-tabs">
                        <div class="collab-tab active" data-tab="members" onclick="switchCollabTab('members')">
                            <i class="fas fa-users"></i> 团队成员
                        </div>
                        <div class="collab-tab" data-tab="assignments" onclick="switchCollabTab('assignments')">
                            <i class="fas fa-tasks"></i> 章节分配
                        </div>
                        <div class="collab-tab" data-tab="permissions" onclick="switchCollabTab('permissions')">
                            <i class="fas fa-shield-alt"></i> 权限设置
                        </div>
                    </div>

                    <!-- 团队成员 -->
                    <div id="members-tab" class="collab-content active">
                        <div class="placeholder">
                            <i class="fas fa-users"></i>
                            <h3>团队成员</h3>
                            <p>管理项目团队成员和角色</p>
                        </div>
                    </div>

                    <!-- 章节分配 -->
                    <div id="assignments-tab" class="collab-content">
                        <div class="chapter-assignment-container">
                            <!-- 章节分配头部 -->
                            <div class="assignment-header">
                                <div class="header-left">
                                    <h3>章节分配</h3>
                                    <div class="assignment-stats" id="assignment-stats">
                                        <span class="stat-item">
                                            <i class="fas fa-book"></i>
                                            <span id="total-chapters">2</span> 总章节
                                        </span>
                                        <span class="stat-item">
                                            <i class="fas fa-users"></i>
                                            <span id="total-authors">2</span> 参与作者
                                        </span>
                                        <span class="stat-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span id="completed-chapters">0</span> 已完成
                                        </span>
                                        <span class="stat-item">
                                            <i class="fas fa-clock"></i>
                                            <span id="pending-reviews">1</span> 待审核
                                        </span>
                                    </div>
                                </div>
                                <div class="header-actions">
                                    <div class="search-box">
                                        <i class="fas fa-search"></i>
                                        <input type="text" id="assignment-search" placeholder="搜索章节或作者...">
                                    </div>
                                    <select id="status-filter" class="status-filter">
                                        <option value="">所有状态</option>
                                        <option value="pending">待确认</option>
                                        <option value="in_progress">进行中</option>
                                        <option value="reviewing">审核中</option>
                                        <option value="completed">已完成</option>
                                    </select>
                                    <button class="btn btn-primary" onclick="showCreateModal()">
                                        <i class="fas fa-plus"></i>
                                        新建分配
                                    </button>
                                    <button class="btn btn-secondary" onclick="refreshData()">
                                        <i class="fas fa-sync-alt"></i>
                                        刷新
                                    </button>
                                </div>
                            </div>

                            <!-- 章节分配列表 -->
                            <div class="assignments-container">
                                <div class="assignments-header-row">
                                    <div class="header-cell">章节</div>
                                    <div class="header-cell">主笔作者</div>
                                    <div class="header-cell">协作者</div>
                                    <div class="header-cell">状态</div>
                                    <div class="header-cell">进度</div>
                                    <div class="header-cell">截止日期</div>
                                    <div class="header-cell">操作</div>
                                </div>
                                <div class="assignments-list" id="chapter-assignments-list">
                                    <!-- 动态内容将在这里加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 权限设置 -->
                    <div id="permissions-tab" class="collab-content">
                        <div class="placeholder">
                            <i class="fas fa-shield-alt"></i>
                            <h3>权限设置</h3>
                            <p>管理项目权限和访问控制</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div id="settings-tab" class="tab-content">
                <div class="placeholder">
                    <i class="fas fa-cog"></i>
                    <h3>系统设置</h3>
                    <p>配置系统参数和偏好设置</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟协作管理器
        const collaborationManager = {
            mockAssignments: [
                {
                    id: '1',
                    title: '第一章：绪论',
                    description: '介绍大模型技术发展背景和研究意义',
                    status: 'in_progress',
                    user_profiles: { full_name: '张教授', email: '<EMAIL>' },
                    collaborators: '李博士',
                    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    priority: 'high'
                },
                {
                    id: '2',
                    title: '第二章：文献综述',
                    description: '相关研究领域的文献综述和分析',
                    status: 'reviewing',
                    user_profiles: { full_name: '李博士', email: '<EMAIL>' },
                    collaborators: '王研究员',
                    due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
                    priority: 'medium'
                }
            ],

            calculateProgress(assignment) {
                if (assignment.status === 'completed') return 100;
                if (assignment.status === 'reviewing') return 90;
                if (assignment.status === 'in_progress') return 50;
                if (assignment.status === 'pending') return 10;
                return 0;
            },

            getStatusText(status) {
                const statusMap = {
                    'pending': '待确认',
                    'in_progress': '进行中',
                    'reviewing': '审核中',
                    'completed': '已完成',
                    'rejected': '已拒绝'
                };
                return statusMap[status] || status;
            },

            renderChapterAssignments() {
                const assignmentsContainer = document.getElementById('chapter-assignments-list');
                if (!assignmentsContainer) return;

                const assignments = this.mockAssignments;

                assignmentsContainer.innerHTML = assignments.map(assignment => {
                    const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
                    const isOverdue = dueDate && dueDate < new Date();
                    const progress = this.calculateProgress(assignment);
                    
                    return `
                        <div class="assignment-item">
                            <div class="assignment-title">
                                ${assignment.title}
                                <div class="assignment-subtitle">
                                    ${assignment.description}
                                </div>
                            </div>
                            <div class="user-badge">
                                <i class="fas fa-user"></i>
                                ${assignment.user_profiles.full_name}
                            </div>
                            <div class="user-badge">
                                <i class="fas fa-users"></i>
                                ${assignment.collaborators}
                            </div>
                            <div>
                                <span class="status-badge status-${assignment.status}">
                                    ${this.getStatusText(assignment.status)}
                                </span>
                            </div>
                            <div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progress}%"></div>
                                </div>
                                <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                                    ${progress}%
                                </div>
                            </div>
                            <div class="due-date ${isOverdue ? 'overdue' : ''}">
                                ${dueDate ? dueDate.toLocaleDateString() : '无截止日期'}
                            </div>
                            <div class="actions">
                                <button class="action-btn" onclick="editAssignment('${assignment.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" onclick="viewAssignment('${assignment.id}')" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" onclick="deleteAssignment('${assignment.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');
            }
        };

        // 主标签切换
        function switchTab(tabName) {
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-tab`).classList.add('active');

            // 如果切换到协作编著，初始化章节分配
            if (tabName === 'collaboration') {
                setTimeout(() => {
                    collaborationManager.renderChapterAssignments();
                }, 100);
            }
        }

        // 协作标签切换
        function switchCollabTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.collab-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.collab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-tab`).classList.add('active');

            // 如果切换到章节分配，渲染数据
            if (tabName === 'assignments') {
                collaborationManager.renderChapterAssignments();
            }
        }

        // 功能按钮
        function showCreateModal() {
            alert('新建分配功能已集成到主应用程序！');
        }

        function refreshData() {
            collaborationManager.renderChapterAssignments();
            alert('数据已刷新！');
        }

        function editAssignment(id) {
            alert(`编辑分配: ${id}`);
        }

        function viewAssignment(id) {
            alert(`查看分配详情: ${id}`);
        }

        function deleteAssignment(id) {
            if (confirm('确定要删除这个分配吗？')) {
                alert(`删除分配: ${id}`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('主应用程序章节分配集成测试已加载');
        });
    </script>
</body>
</html>
