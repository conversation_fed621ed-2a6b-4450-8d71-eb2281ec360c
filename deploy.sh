#!/bin/bash

# AI增强学术专著编写系统 - 一键部署脚本
# 版本: 1.0
# 作者: AI Assistant

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "此脚本仅支持Linux系统"
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先运行: curl -fsSL https://get.docker.com | sh"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    # 检查磁盘空间（至少需要10GB）
    available_space=$(df / | awk 'NR==2 {print $4}')
    required_space=10485760  # 10GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        log_error "磁盘空间不足，至少需要10GB可用空间"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p volumes/{postgres_data,storage_data,redis_data,logs/nginx}
    mkdir -p nginx/ssl
    mkdir -p supabase/migrations
    mkdir -p scripts
    mkdir -p backups
    
    log_success "目录结构创建完成"
}

# 生成环境变量文件
generate_env_file() {
    log_info "生成环境变量文件..."
    
    if [ -f .env ]; then
        log_warning ".env文件已存在，跳过生成"
        return
    fi
    
    # 生成随机密钥
    JWT_SECRET=$(openssl rand -base64 32)
    SECRET_KEY_BASE=$(openssl rand -base64 64)
    POSTGRES_PASSWORD=$(openssl rand -base64 16)
    ANON_KEY=$(openssl rand -base64 32)
    SERVICE_ROLE_KEY=$(openssl rand -base64 32)
    
    cat > .env << EOF
# 数据库配置
POSTGRES_DB=llm_book_system
POSTGRES_USER=supabase
POSTGRES_PASSWORD=${POSTGRES_PASSWORD}

# JWT配置
JWT_SECRET=${JWT_SECRET}
SECRET_KEY_BASE=${SECRET_KEY_BASE}

# API密钥
ANON_KEY=${ANON_KEY}
SERVICE_ROLE_KEY=${SERVICE_ROLE_KEY}

# 站点配置
SITE_URL=http://localhost
ADDITIONAL_REDIRECT_URLS=http://localhost/**

# 邮件配置（请根据实际情况修改）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_ADMIN_EMAIL=<EMAIL>

# AI服务配置（请填入您的API密钥）
OPENROUTER_API_KEY=your_openrouter_api_key_here
DEEPSEEK_MODEL=deepseek/deepseek-chat

# 系统配置
ENVIRONMENT=development
LOG_LEVEL=info
EOF
    
    log_success "环境变量文件生成完成"
    log_warning "请编辑 .env 文件，配置您的域名、邮件和AI服务设置"
}

# 生成Docker Compose文件
generate_docker_compose() {
    log_info "生成Docker Compose配置..."
    
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: llm-book-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - ./volumes/postgres_data:/var/lib/postgresql/data
      - ./seed-data.sql:/docker-entrypoint-initdb.d/01-seed.sql
      - ./database-schema.sql:/docker-entrypoint-initdb.d/00-schema.sql
    ports:
      - "5432:5432"
    command: postgres -c wal_level=logical
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgREST API服务
  postgrest:
    image: postgrest/postgrest:v11.2.0
    container_name: llm-book-postgrest
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
    ports:
      - "3001:3000"
    depends_on:
      postgres:
        condition: service_healthy

  # GoTrue认证服务
  gotrue:
    image: supabase/gotrue:v2.99.0
    container_name: llm-book-gotrue
    restart: unless-stopped
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      GOTRUE_SITE_URL: ${SITE_URL}
      GOTRUE_URI_ALLOW_LIST: ${ADDITIONAL_REDIRECT_URLS}
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_SECRET: ${JWT_SECRET}
      GOTRUE_EXTERNAL_EMAIL_ENABLED: true
      GOTRUE_MAILER_AUTOCONFIRM: false
      GOTRUE_SMTP_HOST: ${SMTP_HOST}
      GOTRUE_SMTP_PORT: ${SMTP_PORT}
      GOTRUE_SMTP_USER: ${SMTP_USER}
      GOTRUE_SMTP_PASS: ${SMTP_PASS}
      GOTRUE_SMTP_ADMIN_EMAIL: ${SMTP_ADMIN_EMAIL}
    ports:
      - "9999:9999"
    depends_on:
      postgres:
        condition: service_healthy

  # Realtime实时服务
  realtime:
    image: supabase/realtime:v2.25.35
    container_name: llm-book-realtime
    restart: unless-stopped
    environment:
      PORT: 4000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      DB_NAME: ${POSTGRES_DB}
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: ${JWT_SECRET}
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: ${SECRET_KEY_BASE}
      ERL_AFLAGS: -proto_dist inet_tcp
      ENABLE_TAILSCALE: "false"
      DNS_NODES: "''"
    ports:
      - "4000:4000"
    depends_on:
      postgres:
        condition: service_healthy

  # Storage存储服务
  storage:
    image: supabase/storage-api:v0.40.4
    container_name: llm-book-storage
    restart: unless-stopped
    environment:
      ANON_KEY: ${ANON_KEY}
      SERVICE_KEY: ${SERVICE_ROLE_KEY}
      POSTGREST_URL: http://postgrest:3000
      PGRST_JWT_SECRET: ${JWT_SECRET}
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
    volumes:
      - ./volumes/storage_data:/var/lib/storage
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_healthy

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: llm-book-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - ./volumes/redis_data:/data
    command: redis-server --appendonly yes

volumes:
  postgres_data:
  storage_data:
  redis_data:
EOF
    
    log_success "Docker Compose配置生成完成"
}

# 生成Nginx配置
generate_nginx_config() {
    log_info "生成Nginx配置..."
    
    mkdir -p nginx
    
    cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    upstream postgrest {
        server postgrest:3000;
    }
    
    upstream gotrue {
        server gotrue:9999;
    }
    
    upstream realtime {
        server realtime:4000;
    }
    
    upstream storage {
        server storage:5000;
    }

    server {
        listen 80;
        server_name _;

        # 安全头部
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # API路由
        location /rest/v1/ {
            proxy_pass http://postgrest/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /auth/v1/ {
            proxy_pass http://gotrue/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /realtime/v1/ {
            proxy_pass http://realtime/socket/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /storage/v1/ {
            proxy_pass http://storage/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 静态文件服务（前端应用）
        location / {
            root /var/www/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }
}
EOF
    
    log_success "Nginx配置生成完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    
    # 拉取镜像
    docker-compose pull
    
    # 启动服务
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    # 检查数据库连接
    if docker-compose exec -T postgres pg_isready -U supabase -d llm_book_system; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        exit 1
    fi
    
    # 检查API服务
    if curl -f http://localhost:3001/health &>/dev/null; then
        log_success "API服务正常"
    else
        log_warning "API服务可能未完全启动，请稍后检查"
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "=== 部署完成 ==="
    echo ""
    echo "服务访问地址："
    echo "  - 主应用: http://localhost"
    echo "  - API端点: http://localhost:3001"
    echo "  - 认证服务: http://localhost:9999"
    echo "  - 实时服务: http://localhost:4000"
    echo "  - 存储服务: http://localhost:5000"
    echo ""
    echo "数据库连接："
    echo "  - 主机: localhost"
    echo "  - 端口: 5432"
    echo "  - 数据库: llm_book_system"
    echo "  - 用户: supabase"
    echo ""
    echo "管理命令："
    echo "  - 查看日志: docker-compose logs"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    echo "  - 备份数据: ./scripts/backup.sh"
    echo ""
    log_warning "请记得："
    echo "1. 编辑 .env 文件配置您的域名和邮件设置"
    echo "2. 配置 OpenRouter API 密钥以启用AI功能"
    echo "3. 设置SSL证书以启用HTTPS（生产环境）"
    echo "4. 定期备份数据库和配置文件"
}

# 主函数
main() {
    echo "=== AI增强学术专著编写系统部署脚本 ==="
    echo ""
    
    check_requirements
    create_directories
    generate_env_file
    generate_docker_compose
    generate_nginx_config
    start_services
    verify_deployment
    show_deployment_info
    
    log_success "部署完成！"
}

# 执行主函数
main "$@"
