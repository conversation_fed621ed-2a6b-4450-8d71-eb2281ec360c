<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节功能集成测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-button.success {
            background: #27ae60;
        }
        .test-button.error {
            background: #e74c3c;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .test-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background: #f39c12; }
        .status-running { background: #3498db; }
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .test-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>章节功能集成测试</h1>
            <p>在实际应用环境中测试章节保存和导航功能</p>
        </div>

        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="loadMainApp()">加载主应用</button>
            <button class="test-button" onclick="runIntegrationTests()">运行集成测试</button>
            <button class="test-button" onclick="testChapterSaveFlow()">测试保存流程</button>
            <button class="test-button" onclick="testNavigationFlow()">测试导航流程</button>
            <button class="test-button" onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-section">
            <h3>主应用</h3>
            <div class="iframe-container">
                <iframe id="main-app" src="about:blank"></iframe>
            </div>
        </div>

        <div class="test-section">
            <h3>测试结果</h3>
            <div id="test-results" class="test-results">
                <div class="test-item">
                    <span class="status-indicator status-pending"></span>
                    <span>等待测试开始...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <div id="test-log" class="test-log">
                集成测试日志将在这里显示...
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let testLog = [];
        let mainAppFrame = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }

        // 更新日志显示
        function updateLogDisplay() {
            const logElement = document.getElementById('test-log');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 更新测试结果显示
        function updateResultsDisplay() {
            const resultsElement = document.getElementById('test-results');
            if (testResults.length === 0) {
                resultsElement.innerHTML = '<div class="test-item"><span class="status-indicator status-pending"></span><span>等待测试开始...</span></div>';
                return;
            }

            resultsElement.innerHTML = testResults.map(result => {
                const statusClass = result.status === 'success' ? 'status-success' : 
                                  result.status === 'error' ? 'status-error' : 
                                  result.status === 'running' ? 'status-running' : 'status-pending';
                return `<div class="test-item">
                    <span class="status-indicator ${statusClass}"></span>
                    <span>${result.name}: ${result.message}</span>
                </div>`;
            }).join('');
        }

        // 添加测试结果
        function addTestResult(name, status, message) {
            testResults.push({ name, status, message });
            updateResultsDisplay();
        }

        // 清除结果
        function clearResults() {
            testResults = [];
            testLog = [];
            updateResultsDisplay();
            updateLogDisplay();
            log('测试结果已清除');
        }

        // 加载主应用
        function loadMainApp() {
            log('正在加载主应用...');
            mainAppFrame = document.getElementById('main-app');
            mainAppFrame.src = './index.html';
            
            mainAppFrame.onload = function() {
                log('主应用加载完成');
                addTestResult('应用加载', 'success', '主应用已成功加载');
            };
            
            mainAppFrame.onerror = function() {
                log('主应用加载失败', 'error');
                addTestResult('应用加载', 'error', '主应用加载失败');
            };
        }

        // 获取iframe中的window对象
        function getAppWindow() {
            if (!mainAppFrame || !mainAppFrame.contentWindow) {
                throw new Error('主应用未加载或无法访问');
            }
            return mainAppFrame.contentWindow;
        }

        // 测试章节保存流程
        async function testChapterSaveFlow() {
            log('开始测试章节保存流程');
            
            try {
                const appWindow = getAppWindow();
                
                // 测试1: 检查必要的函数是否存在
                addTestResult('保存函数检查', 'running', '检查保存相关函数...');
                if (typeof appWindow.saveChapter === 'function' && 
                    typeof appWindow.autoSaveChapter === 'function' &&
                    typeof appWindow.saveChapterToServer === 'function') {
                    addTestResult('保存函数检查', 'success', '所有保存函数存在');
                    log('保存函数检查通过');
                } else {
                    addTestResult('保存函数检查', 'error', '部分保存函数缺失');
                    log('保存函数检查失败', 'error');
                    return;
                }

                // 测试2: 检查Quill编辑器
                addTestResult('编辑器检查', 'running', '检查Quill编辑器状态...');
                if (appWindow.quillEditor && typeof appWindow.quillEditor.getContents === 'function') {
                    addTestResult('编辑器检查', 'success', 'Quill编辑器正常');
                    log('Quill编辑器检查通过');
                } else {
                    addTestResult('编辑器检查', 'error', 'Quill编辑器未初始化');
                    log('Quill编辑器检查失败', 'error');
                }

                // 测试3: 检查数据格式
                addTestResult('数据格式检查', 'running', '检查Delta格式处理...');
                try {
                    const testDelta = { ops: [{ insert: '测试内容\n' }] };
                    if (testDelta.ops && Array.isArray(testDelta.ops)) {
                        addTestResult('数据格式检查', 'success', 'Delta格式处理正确');
                        log('数据格式检查通过');
                    }
                } catch (error) {
                    addTestResult('数据格式检查', 'error', `格式检查失败: ${error.message}`);
                    log(`数据格式检查失败: ${error.message}`, 'error');
                }

            } catch (error) {
                addTestResult('保存流程测试', 'error', `测试异常: ${error.message}`);
                log(`保存流程测试异常: ${error.message}`, 'error');
            }
        }

        // 测试导航流程
        async function testNavigationFlow() {
            log('开始测试导航流程');
            
            try {
                const appWindow = getAppWindow();
                
                // 测试1: 检查导航函数
                addTestResult('导航函数检查', 'running', '检查导航相关函数...');
                if (typeof appWindow.showPanel === 'function' && 
                    typeof appWindow.updateNavActiveState === 'function' &&
                    typeof appWindow.enterChapterEditMode === 'function') {
                    addTestResult('导航函数检查', 'success', '所有导航函数存在');
                    log('导航函数检查通过');
                } else {
                    addTestResult('导航函数检查', 'error', '部分导航函数缺失');
                    log('导航函数检查失败', 'error');
                    return;
                }

                // 测试2: 检查DOM元素
                addTestResult('DOM元素检查', 'running', '检查导航DOM元素...');
                const appDoc = appWindow.document;
                const navItems = appDoc.querySelectorAll('.nav-item');
                const editorNavItem = appDoc.querySelector('.nav-item[data-tab="editor"]');
                
                if (navItems.length > 0 && editorNavItem) {
                    addTestResult('DOM元素检查', 'success', '导航元素存在');
                    log('DOM元素检查通过');
                } else {
                    addTestResult('DOM元素检查', 'error', '导航元素缺失');
                    log('DOM元素检查失败', 'error');
                }

                // 测试3: 模拟导航切换
                addTestResult('导航切换测试', 'running', '测试导航状态切换...');
                try {
                    // 调用showPanel函数切换到编辑器面板
                    appWindow.showPanel('editor');
                    
                    // 检查导航状态是否正确更新
                    setTimeout(() => {
                        const activeNavItem = appDoc.querySelector('.nav-item.active[data-tab="editor"]');
                        if (activeNavItem) {
                            addTestResult('导航切换测试', 'success', '导航状态切换正确');
                            log('导航切换测试通过');
                        } else {
                            addTestResult('导航切换测试', 'error', '导航状态未正确更新');
                            log('导航切换测试失败', 'error');
                        }
                    }, 500);
                } catch (error) {
                    addTestResult('导航切换测试', 'error', `切换测试失败: ${error.message}`);
                    log(`导航切换测试失败: ${error.message}`, 'error');
                }

            } catch (error) {
                addTestResult('导航流程测试', 'error', `测试异常: ${error.message}`);
                log(`导航流程测试异常: ${error.message}`, 'error');
            }
        }

        // 运行所有集成测试
        async function runIntegrationTests() {
            log('开始运行集成测试');
            clearResults();

            if (!mainAppFrame || !mainAppFrame.contentWindow) {
                addTestResult('前置检查', 'error', '请先加载主应用');
                log('集成测试失败: 主应用未加载', 'error');
                return;
            }

            await testChapterSaveFlow();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testNavigationFlow();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testSpecificFixes();

            log('集成测试完成');

            // 统计结果
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            const totalCount = testResults.length;

            log(`测试总结: ${successCount}/${totalCount} 通过, ${errorCount} 失败`);
        }

        // 测试具体修复的功能
        async function testSpecificFixes() {
            log('开始测试具体修复功能');

            try {
                const appWindow = getAppWindow();
                const appDoc = appWindow.document;

                // 测试修复1: 章节内容保存到数据库
                addTestResult('章节保存修复', 'running', '测试Delta格式保存...');
                try {
                    // 检查保存函数是否使用Delta格式
                    const saveFunction = appWindow.saveChapterToServer.toString();
                    if (saveFunction.includes('deltaContent') || saveFunction.includes('getContents')) {
                        addTestResult('章节保存修复', 'success', 'Delta格式保存已修复');
                        log('章节保存修复验证通过');
                    } else {
                        addTestResult('章节保存修复', 'error', 'Delta格式保存未修复');
                        log('章节保存修复验证失败', 'error');
                    }
                } catch (error) {
                    addTestResult('章节保存修复', 'error', `验证失败: ${error.message}`);
                    log(`章节保存修复验证异常: ${error.message}`, 'error');
                }

                // 测试修复2: 导航栏选中状态
                addTestResult('导航状态修复', 'running', '测试导航状态更新...');
                try {
                    // 检查showPanel函数是否调用updateNavActiveState
                    const showPanelFunction = appWindow.showPanel.toString();
                    if (showPanelFunction.includes('updateNavActiveState')) {
                        addTestResult('导航状态修复', 'success', '导航状态更新已修复');
                        log('导航状态修复验证通过');
                    } else {
                        addTestResult('导航状态修复', 'error', '导航状态更新未修复');
                        log('导航状态修复验证失败', 'error');
                    }
                } catch (error) {
                    addTestResult('导航状态修复', 'error', `验证失败: ${error.message}`);
                    log(`导航状态修复验证异常: ${error.message}`, 'error');
                }

                // 测试修复3: 双击章节进入编写模式
                addTestResult('双击编写修复', 'running', '测试双击进入编写模式...');
                try {
                    // 检查enterChapterEditMode函数是否调用showPanel
                    if (typeof appWindow.enterChapterEditMode === 'function') {
                        const editModeFunction = appWindow.enterChapterEditMode.toString();
                        if (editModeFunction.includes('showPanel') && editModeFunction.includes('editor')) {
                            addTestResult('双击编写修复', 'success', '双击编写模式已修复');
                            log('双击编写修复验证通过');
                        } else {
                            addTestResult('双击编写修复', 'error', '双击编写模式未修复');
                            log('双击编写修复验证失败', 'error');
                        }
                    } else {
                        addTestResult('双击编写修复', 'error', 'enterChapterEditMode函数不存在');
                        log('双击编写修复验证失败: 函数不存在', 'error');
                    }
                } catch (error) {
                    addTestResult('双击编写修复', 'error', `验证失败: ${error.message}`);
                    log(`双击编写修复验证异常: ${error.message}`, 'error');
                }

            } catch (error) {
                addTestResult('修复功能测试', 'error', `测试异常: ${error.message}`);
                log(`修复功能测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('集成测试页面已加载');
            log('请先点击"加载主应用"，然后运行测试');
        });
    </script>
</body>
</html>
