<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>私有化Supabase URL验证测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-urls {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-urls h4 {
            margin-top: 0;
            color: #495057;
        }
        .url-example {
            font-family: monospace;
            background-color: white;
            padding: 5px 8px;
            border-radius: 3px;
            margin: 5px 0;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 私有化Supabase URL验证测试</h1>
        
        <div class="test-urls">
            <h4>测试URL示例：</h4>
            <div class="url-example">官方域名: https://your-project.supabase.co</div>
            <div class="url-example">私有化部署: https://supabase.your-domain.com</div>
            <div class="url-example">自定义域名: https://api.yourcompany.com</div>
            <div class="url-example">本地开发: https://localhost:8000</div>
        </div>

        <div class="test-section">
            <h3>URL验证测试</h3>
            <div class="input-group">
                <label for="test-url">输入Supabase URL:</label>
                <input type="url" id="test-url" placeholder="https://your-supabase-url.com">
            </div>
            <button onclick="testUrlValidation()">验证URL</button>
            <button onclick="fillOfficialExample()">填入官方示例</button>
            <button onclick="fillPrivateExample()">填入私有化示例</button>
            <div id="validation-result"></div>
        </div>

        <div class="test-section">
            <h3>批量测试</h3>
            <button onclick="runBatchTests()">运行所有测试用例</button>
            <div id="batch-results"></div>
        </div>
    </div>

    <!-- 引入Supabase配置管理器 -->
    <script src="supabase-config-manager.js"></script>

    <script>
        // 测试用例
        const testCases = [
            // 官方域名 - 应该通过
            { url: 'https://bigzfjlaypptochqpxzu.supabase.co', expected: true, description: '官方Supabase域名' },
            { url: 'https://test-project.supabase.co', expected: true, description: '官方Supabase域名（测试）' },
            
            // 私有化部署 - 应该通过
            { url: 'https://supabase.example.com', expected: true, description: '私有化部署域名' },
            { url: 'https://api.mycompany.com', expected: true, description: '自定义API域名' },
            { url: 'https://db.internal.corp', expected: true, description: '内部企业域名' },
            
            // 本地开发 - 应该通过
            { url: 'https://localhost:8000', expected: true, description: '本地开发环境' },
            { url: 'https://127.0.0.1:3000', expected: true, description: '本地IP地址' },
            { url: 'https://*************:8080', expected: true, description: '局域网IP地址' },
            
            // 无效URL - 应该失败
            { url: 'http://insecure.com', expected: false, description: 'HTTP协议（不安全）' },
            { url: 'https://invalid', expected: false, description: '无效域名格式' },
            { url: 'not-a-url', expected: false, description: '不是URL格式' },
            { url: '', expected: false, description: '空URL' },
            { url: 'https://*******', expected: false, description: '公网IP地址' }
        ];

        function testUrlValidation() {
            const url = document.getElementById('test-url').value.trim();
            const resultDiv = document.getElementById('validation-result');
            
            if (!url) {
                showResult(resultDiv, 'error', '请输入URL');
                return;
            }

            // 测试基本URL格式
            const isValidUrl = window.supabaseConfigManager.isValidUrl(url);
            if (!isValidUrl) {
                showResult(resultDiv, 'error', 'URL格式不正确');
                return;
            }

            // 测试Supabase URL验证
            const isValidSupabase = window.supabaseConfigManager.isValidSupabaseUrl(url);
            if (isValidSupabase) {
                const isOfficial = url.includes('supabase.co');
                const type = isOfficial ? '官方域名' : '私有化部署';
                showResult(resultDiv, 'success', `✅ 验证通过 - ${type}`);
            } else {
                showResult(resultDiv, 'error', '❌ 不是有效的Supabase服务地址');
            }
        }

        function fillOfficialExample() {
            document.getElementById('test-url').value = 'https://bigzfjlaypptochqpxzu.supabase.co';
        }

        function fillPrivateExample() {
            document.getElementById('test-url').value = 'https://supabase.example.com';
        }

        function runBatchTests() {
            const resultsDiv = document.getElementById('batch-results');
            resultsDiv.innerHTML = '<h4>测试结果:</h4>';
            
            let passCount = 0;
            let failCount = 0;

            testCases.forEach((testCase, index) => {
                const isValidUrl = window.supabaseConfigManager.isValidUrl(testCase.url);
                const isValidSupabase = isValidUrl ? window.supabaseConfigManager.isValidSupabaseUrl(testCase.url) : false;
                const actualResult = isValidSupabase;
                const passed = actualResult === testCase.expected;
                
                if (passed) {
                    passCount++;
                } else {
                    failCount++;
                }

                const resultClass = passed ? 'success' : 'error';
                const resultIcon = passed ? '✅' : '❌';
                
                resultsDiv.innerHTML += `
                    <div class="${resultClass}" style="margin: 5px 0; padding: 8px; border-radius: 4px;">
                        ${resultIcon} ${testCase.description}<br>
                        <small>URL: ${testCase.url}</small><br>
                        <small>期望: ${testCase.expected ? '通过' : '失败'}, 实际: ${actualResult ? '通过' : '失败'}</small>
                    </div>
                `;
            });

            resultsDiv.innerHTML += `
                <div class="info" style="margin-top: 15px; padding: 10px; border-radius: 4px;">
                    <strong>测试总结:</strong> 通过 ${passCount} 个，失败 ${failCount} 个，总计 ${testCases.length} 个测试用例
                </div>
            `;
        }

        function showResult(element, type, message) {
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            console.log('私有化Supabase URL验证测试页面已加载');
            console.log('Supabase配置管理器状态:', window.supabaseConfigManager ? '已加载' : '未加载');
        });
    </script>
</body>
</html>
