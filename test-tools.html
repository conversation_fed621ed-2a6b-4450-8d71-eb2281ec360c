<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具集测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .tool-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .tool-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .tool-name {
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }
        .tool-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .tool-url {
            font-size: 12px;
            color: #888;
            word-break: break-all;
        }
        .tool-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            margin-top: 10px;
        }
        .tool-link:hover {
            background: #0056b3;
        }
        .stats {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 8px;
        }
        .reset-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
        .reset-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ 工具集测试页面</h1>
        
        <div class="stats">
            <p><strong>当前工具数量：</strong><span id="toolCount">0</span></p>
            <button class="reset-btn" onclick="resetTools()">重置为默认工具</button>
            <button class="reset-btn" onclick="clearAllTools()">清空所有工具</button>
        </div>
        
        <div class="tools-grid" id="toolsGrid">
            <!-- 工具卡片将在这里动态生成 -->
        </div>
    </div>

    <script>
        // 从 app.js 复制的默认工具配置
        const defaultTools = [
            {
                id: 'calculator',
                name: '计算器',
                url: 'https://www.calculator.net/basic-calculator.html',
                description: '基础计算器工具，支持基本数学运算'
            },
            {
                id: 'color-picker',
                name: '颜色选择器',
                url: 'https://htmlcolorcodes.com/color-picker/',
                description: '在线颜色选择器，支持多种颜色格式'
            },
            {
                id: 'ichat',
                name: 'AI chat',
                url: 'http://ichat.gonghe.net.cn',
                description: 'AI 对话服务工具'
            },
            {
                id: 'json-formatter',
                name: 'JSON格式化',
                url: 'https://jsonformatter.curiousconcept.com/',
                description: 'JSON数据格式化和验证工具'
            },
            {
                id: 'markdown-editor',
                name: 'Markdown编辑器',
                url: 'https://stackedit.io/app',
                description: '在线Markdown编辑器，支持实时预览'
            },
            {
                id: 'regex-tester',
                name: '正则表达式测试',
                url: 'https://regex101.com/',
                description: '正则表达式测试和调试工具'
            },
            {
                id: 'base64-encoder',
                name: 'Base64编码',
                url: 'https://www.base64encode.org/',
                description: 'Base64编码和解码工具'
            },
            {
                id: 'qr-generator',
                name: '二维码生成器',
                url: 'https://www.qr-code-generator.com/',
                description: '在线二维码生成工具'
            },
            {
                id: 'password-generator',
                name: '密码生成器',
                url: 'https://passwordsgenerator.net/',
                description: '安全密码生成工具'
            },
            {
                id: 'url-shortener',
                name: 'URL短链接',
                url: 'https://tinyurl.com/',
                description: 'URL短链接生成工具'
            },
            {
                id: 'image-compressor',
                name: '图片压缩',
                url: 'https://tinypng.com/',
                description: '在线图片压缩工具'
            },
            {
                id: 'pdf-tools',
                name: 'PDF工具',
                url: 'https://smallpdf.com/',
                description: 'PDF转换、合并、分割等工具'
            },
            {
                id: 'translator',
                name: '翻译工具',
                url: 'https://translate.google.com/',
                description: 'Google翻译工具'
            },
            {
                id: 'weather',
                name: '天气查询',
                url: 'https://weather.com/',
                description: '天气预报查询工具'
            },
            {
                id: 'world-clock',
                name: '世界时钟',
                url: 'https://www.timeanddate.com/worldclock/',
                description: '世界各地时间查询'
            },
            {
                id: 'unit-converter',
                name: '单位转换',
                url: 'https://www.unitconverters.net/',
                description: '各种单位转换工具'
            },
            {
                id: 'css-generator',
                name: 'CSS生成器',
                url: 'https://css3generator.com/',
                description: 'CSS样式生成工具'
            },
            {
                id: 'gradient-generator',
                name: '渐变生成器',
                url: 'https://cssgradient.io/',
                description: 'CSS渐变背景生成工具'
            },
            {
                id: 'font-awesome',
                name: '图标库',
                url: 'https://fontawesome.com/icons',
                description: 'Font Awesome图标库'
            },
            {
                id: 'lorem-ipsum',
                name: '文本生成器',
                url: 'https://www.lipsum.com/',
                description: 'Lorem Ipsum占位文本生成器'
            }
        ];

        // 图标映射函数
        function getToolIcon(toolName) {
            const iconMap = {
                '计算器': '🧮',
                '颜色选择器': '🎨',
                'JSON格式化': '📄',
                'JSON': '📄',
                'Markdown': '📝',
                '编辑器': '📝',
                '正则': '🔍',
                'Base64': '🔐',
                '编码': '🔐',
                '二维码': '📱',
                '密码': '🔑',
                'URL': '🔗',
                '短链接': '🔗',
                '图片': '🖼️',
                '压缩': '📦',
                'PDF': '📄',
                '翻译': '🌐',
                '天气': '🌤️',
                '时钟': '⏰',
                '世界': '🌍',
                '单位': '📏',
                '转换': '🔄',
                'CSS': '🎨',
                '渐变': '🌈',
                '图标': '⭐',
                '文本': '📝',
                '生成器': '⚙️',
                'AI': '🤖',
                'chat': '💬'
            };

            for (const [key, icon] of Object.entries(iconMap)) {
                if (toolName.includes(key)) {
                    return icon;
                }
            }
            return '🛠️';
        }

        // 渲染工具列表
        function renderTools() {
            const tools = JSON.parse(localStorage.getItem('webTools') || '[]');
            const toolsGrid = document.getElementById('toolsGrid');
            const toolCount = document.getElementById('toolCount');
            
            toolCount.textContent = tools.length;
            
            if (tools.length === 0) {
                toolsGrid.innerHTML = '<p style="text-align: center; color: #666; grid-column: 1 / -1;">暂无工具，点击"重置为默认工具"加载默认工具集</p>';
                return;
            }
            
            toolsGrid.innerHTML = tools.map(tool => `
                <div class="tool-card">
                    <div class="tool-header">
                        <span class="tool-icon">${getToolIcon(tool.name)}</span>
                        <span class="tool-name">${tool.name}</span>
                    </div>
                    <div class="tool-description">${tool.description}</div>
                    <div class="tool-url">${tool.url}</div>
                    <a href="${tool.url}" target="_blank" class="tool-link">打开工具</a>
                </div>
            `).join('');
        }

        // 重置为默认工具
        function resetTools() {
            localStorage.setItem('webTools', JSON.stringify(defaultTools));
            renderTools();
            alert('已重置为默认工具集！');
        }

        // 清空所有工具
        function clearAllTools() {
            if (confirm('确定要清空所有工具吗？')) {
                localStorage.removeItem('webTools');
                renderTools();
            }
        }

        // 页面加载时渲染工具
        document.addEventListener('DOMContentLoaded', renderTools);
    </script>
</body>
</html>
