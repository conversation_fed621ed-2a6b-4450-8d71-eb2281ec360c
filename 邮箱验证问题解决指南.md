# 邮箱验证问题解决指南

## 🚨 常见错误

### 错误信息：`Email address "xxx" is invalid`

这个错误表示您输入的邮箱地址不被Supabase接受。

## 🔍 问题原因

### 1. 使用了测试邮箱域名
Supabase不允许使用以下测试域名：
- `@example.com`
- `@test.com`
- `@localhost`
- `@fake.com`
- `@dummy.com`
- `@sample.com`
- `@testing.com`

### 2. 邮箱格式不正确
- 缺少 `@` 符号
- 域名格式错误
- 包含特殊字符

## ✅ 解决方案

### 方案1：使用真实邮箱地址（推荐）

#### 常用邮箱服务商
```
✅ 推荐使用：
- Gmail: <EMAIL>
- Outlook: <EMAIL>
- 网易邮箱: <EMAIL>
- QQ邮箱: <EMAIL>
- 新浪邮箱: <EMAIL>
- 126邮箱: <EMAIL>
```

#### 示例
```
❌ 错误：<EMAIL>
✅ 正确：<EMAIL>

❌ 错误：<EMAIL>
✅ 正确：<EMAIL>

❌ 错误：<EMAIL>
✅ 正确：<EMAIL>
```

### 方案2：使用测试用户创建工具

如果您只是想测试功能，建议使用专门的测试工具：

1. **访问测试用户创建工具**
   ```
   打开：create-test-users.html
   ```

2. **批量创建测试用户**
   - 工具会自动生成有效的测试邮箱
   - 使用真实域名但虚拟账户
   - 适合功能测试和演示

### 方案3：临时邮箱服务

如果不想使用个人邮箱，可以使用临时邮箱服务：

```
推荐的临时邮箱服务：
- 10minutemail.com
- guerrillamail.com
- tempmail.org
```

## 🛠️ 修复步骤

### 步骤1：检查邮箱格式
确保邮箱格式正确：
```
格式：用户名@域名.后缀
示例：<EMAIL>
```

### 步骤2：避免测试域名
不要使用以下域名：
```
❌ @example.com
❌ @test.com
❌ @localhost
❌ @fake.com
```

### 步骤3：使用真实域名
选择真实的邮箱服务商：
```
✅ @gmail.com
✅ @outlook.com
✅ @163.com
✅ @qq.com
```

### 步骤4：重新创建用户
1. 在创建用户表单中输入正确的邮箱
2. 确认其他信息无误
3. 点击"创建用户"按钮

## 📋 邮箱验证清单

在创建用户前，请检查：

- [ ] 邮箱包含 `@` 符号
- [ ] 域名是真实的邮箱服务商
- [ ] 没有使用测试域名
- [ ] 格式符合标准邮箱格式
- [ ] 邮箱地址未被其他用户使用

## 🎯 最佳实践

### 1. 团队邮箱管理
```
建议格式：
- 姓名.角色@域名.com
- 部门.姓名@域名.com
- 项目.成员@域名.com

示例：
- <EMAIL>
- <EMAIL>
- <EMAIL>
```

### 2. 测试环境
```
测试时使用：
- 个人备用邮箱
- 临时邮箱服务
- 测试用户创建工具
```

### 3. 生产环境
```
生产环境使用：
- 工作邮箱
- 机构邮箱
- 专用项目邮箱
```

## 🔧 技术说明

### Supabase邮箱验证规则
1. **格式验证**：必须符合标准邮箱格式
2. **域名验证**：域名必须是真实存在的
3. **黑名单过滤**：过滤常见的测试域名
4. **唯一性检查**：每个邮箱只能注册一次

### 系统增强
我们已经在系统中添加了：
- **前端验证**：实时检查邮箱格式
- **域名过滤**：阻止测试域名
- **友好提示**：提供具体的错误信息
- **建议域名**：推荐可用的邮箱服务商

## 🆘 仍然有问题？

如果按照指南操作后仍有问题：

1. **检查网络连接**：确保能正常访问Supabase
2. **清除缓存**：清除浏览器缓存后重试
3. **更换邮箱**：尝试使用不同的邮箱地址
4. **联系管理员**：如问题持续，请联系系统管理员

## 📞 技术支持

需要帮助时：
1. 提供具体的错误信息
2. 说明使用的邮箱格式
3. 描述操作步骤
4. 截图相关错误页面

---

**使用真实邮箱地址，让用户创建更顺畅！** 📧✨
