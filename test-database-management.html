<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        .form-input, .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .input-group {
            display: flex;
            align-items: stretch;
        }
        .input-group .form-input {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-right: none;
        }
        .input-group .btn-icon {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border: 1px solid #ddd;
            background: #f9fafb;
            color: #6b7280;
            padding: 10px;
            cursor: pointer;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .btn:hover { opacity: 0.9; }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .management-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        .management-buttons .btn {
            flex: 1;
            min-width: 120px;
            font-size: 12px;
            padding: 8px 12px;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1>🗄️ 数据库管理功能测试</h1>
        
        <!-- Supabase配置测试 -->
        <div class="test-section">
            <h3>1. Supabase配置管理</h3>
            <div class="config-grid">
                <div>
                    <div class="form-group">
                        <label class="form-label">项目URL</label>
                        <div class="input-group">
                            <input type="url" id="test-supabase-url" class="form-input" 
                                   placeholder="https://your-project.supabase.co">
                            <button type="button" class="btn-icon" onclick="validateUrl()" title="验证URL">
                                <i class="fas fa-check">✓</i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">匿名密钥</label>
                        <div class="input-group">
                            <input type="password" id="test-supabase-key" class="form-input" 
                                   placeholder="输入匿名密钥">
                            <button type="button" class="btn-icon" onclick="toggleKeyVisibility()" title="显示/隐藏">
                                <i class="fas fa-eye">👁</i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">数据库区域</label>
                        <select id="test-database-region" class="form-select">
                            <option value="auto">自动检测</option>
                            <option value="ap-southeast-1">新加坡</option>
                            <option value="ap-northeast-1">东京</option>
                            <option value="us-east-1">美国东部</option>
                        </select>
                    </div>
                </div>
                <div>
                    <div class="management-buttons">
                        <button class="btn btn-primary" onclick="testSaveConfig()">保存配置</button>
                        <button class="btn btn-secondary" onclick="testLoadConfig()">加载配置</button>
                        <button class="btn btn-success" onclick="testConnection()">测试连接</button>
                        <button class="btn btn-info" onclick="testDatabaseInfo()">数据库信息</button>
                    </div>
                </div>
            </div>
            <div id="config-status"></div>
        </div>

        <!-- 数据库管理工具测试 -->
        <div class="test-section">
            <h3>2. 数据库管理工具</h3>
            <div class="management-buttons">
                <button class="btn btn-warning" onclick="testMigration()">测试迁移</button>
                <button class="btn btn-info" onclick="testMigrationStatus()">迁移状态</button>
                <button class="btn btn-success" onclick="testBackup()">创建备份</button>
                <button class="btn btn-secondary" onclick="testBackupHistory()">备份历史</button>
                <button class="btn btn-danger" onclick="testRestore()">数据恢复</button>
                <button class="btn btn-info" onclick="testIntegrityCheck()">完整性检查</button>
            </div>
            <div id="management-status"></div>
        </div>

        <!-- 性能监控测试 -->
        <div class="test-section">
            <h3>3. 性能监控</h3>
            <div class="management-buttons">
                <button class="btn btn-info" onclick="testDatabaseStats()">数据库统计</button>
                <button class="btn btn-secondary" onclick="testConnectionPool()">连接池状态</button>
                <button class="btn btn-primary" onclick="testPerformanceCheck()">性能检查</button>
            </div>
            <div id="performance-status"></div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h3>4. 测试日志</h3>
            <button class="btn btn-secondary" onclick="clearTestLog()">清空日志</button>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script src="supabase-config-manager.js"></script>
    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('test-log');
            logElement.innerHTML = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearTestLog() {
            testLog = [];
            document.getElementById('test-log').innerHTML = '';
        }

        // 配置管理测试
        function testSaveConfig() {
            log('测试保存配置...');
            const config = {
                url: document.getElementById('test-supabase-url').value,
                anonKey: document.getElementById('test-supabase-key').value,
                region: document.getElementById('test-database-region').value
            };

            if (window.supabaseConfigManager) {
                const result = window.supabaseConfigManager.saveConfig(config);
                if (result) {
                    showStatus('config-status', '✅ 配置保存成功', 'success');
                    log('配置保存成功');
                } else {
                    showStatus('config-status', '❌ 配置保存失败', 'error');
                    log('配置保存失败', 'error');
                }
            } else {
                showStatus('config-status', '❌ 配置管理器未加载', 'error');
                log('配置管理器未加载', 'error');
            }
        }

        function testLoadConfig() {
            log('测试加载配置...');
            if (window.supabaseConfigManager) {
                const config = window.supabaseConfigManager.getCurrentConfig();
                document.getElementById('test-supabase-url').value = config.url || '';
                document.getElementById('test-supabase-key').value = config.anonKey || '';
                document.getElementById('test-database-region').value = config.region || 'auto';
                
                showStatus('config-status', '✅ 配置加载成功', 'success');
                log('配置加载成功');
            } else {
                showStatus('config-status', '❌ 配置管理器未加载', 'error');
                log('配置管理器未加载', 'error');
            }
        }

        async function testConnection() {
            log('测试数据库连接...');
            if (!window.supabaseConfigManager) {
                showStatus('config-status', '❌ 配置管理器未加载', 'error');
                return;
            }

            const config = {
                url: document.getElementById('test-supabase-url').value,
                anonKey: document.getElementById('test-supabase-key').value,
                region: document.getElementById('test-database-region').value
            };

            try {
                const result = await window.supabaseConfigManager.testConnection(config);
                if (result.success) {
                    showStatus('config-status', `✅ ${result.message}`, 'success');
                    log(`连接测试成功: ${result.message}`);
                } else {
                    showStatus('config-status', `❌ ${result.message}: ${result.error}`, 'error');
                    log(`连接测试失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showStatus('config-status', `❌ 连接测试异常: ${error.message}`, 'error');
                log(`连接测试异常: ${error.message}`, 'error');
            }
        }

        async function testDatabaseInfo() {
            log('获取数据库信息...');
            if (!window.supabaseConfigManager) {
                showStatus('config-status', '❌ 配置管理器未加载', 'error');
                return;
            }

            try {
                const result = await window.supabaseConfigManager.getDatabaseInfo();
                if (result.success) {
                    showStatus('config-status', '✅ 数据库信息获取成功', 'success');
                    log('数据库信息获取成功');
                    console.log('数据库信息:', result.data);
                } else {
                    showStatus('config-status', `❌ 获取失败: ${result.error}`, 'error');
                    log(`数据库信息获取失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showStatus('config-status', `❌ 获取异常: ${error.message}`, 'error');
                log(`数据库信息获取异常: ${error.message}`, 'error');
            }
        }

        // 工具函数
        function validateUrl() {
            const url = document.getElementById('test-supabase-url').value;
            if (url && window.supabaseConfigManager) {
                const isValid = window.supabaseConfigManager.isValidUrl(url);
                const isValidSupabase = window.supabaseConfigManager.isValidSupabaseUrl(url);

                if (isValid && isValidSupabase) {
                    const isOfficialDomain = url.includes('supabase.co');
                    const isHttps = url.startsWith('https://');

                    let message;
                    if (isOfficialDomain) {
                        message = '✅ Supabase官方URL格式正确';
                    } else {
                        message = isHttps ?
                            '✅ Supabase私有化部署URL格式正确（HTTPS）' :
                            '✅ Supabase私有化部署URL格式正确（HTTP）';
                    }

                    showStatus('config-status', message, 'success');
                    log('URL验证通过: ' + (isOfficialDomain ? '官方域名' : '私有化部署') +
                        ' (' + (isHttps ? 'HTTPS' : 'HTTP') + ')');
                } else {
                    showStatus('config-status', '❌ URL格式不正确或不是有效的Supabase地址', 'error');
                    log('URL验证失败', 'error');
                }
            }
        }

        function toggleKeyVisibility() {
            const keyInput = document.getElementById('test-supabase-key');
            keyInput.type = keyInput.type === 'password' ? 'text' : 'password';
        }

        // 管理工具测试函数
        function testMigration() {
            log('测试迁移功能...');
            showStatus('management-status', '🔄 迁移功能测试（模拟）', 'info');
        }

        function testMigrationStatus() {
            log('检查迁移状态...');
            showStatus('management-status', '✅ 迁移状态检查完成', 'success');
        }

        function testBackup() {
            log('测试备份功能...');
            showStatus('management-status', '💾 备份功能测试（模拟）', 'info');
        }

        function testBackupHistory() {
            log('查看备份历史...');
            showStatus('management-status', '📋 备份历史查看（模拟）', 'info');
        }

        function testRestore() {
            log('测试恢复功能...');
            showStatus('management-status', '⚠️ 恢复功能测试（模拟）', 'warning');
        }

        function testIntegrityCheck() {
            log('测试完整性检查...');
            showStatus('management-status', '🔍 完整性检查（模拟）', 'info');
        }

        function testDatabaseStats() {
            log('获取数据库统计...');
            showStatus('performance-status', '📊 数据库统计获取（模拟）', 'info');
        }

        function testConnectionPool() {
            log('检查连接池状态...');
            showStatus('performance-status', '🔗 连接池状态检查（模拟）', 'info');
        }

        function testPerformanceCheck() {
            log('执行性能检查...');
            showStatus('performance-status', '⚡ 性能检查执行（模拟）', 'info');
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            log('数据库管理功能测试页面加载完成');
            testLoadConfig();
        });
    </script>
</body>
</html>
