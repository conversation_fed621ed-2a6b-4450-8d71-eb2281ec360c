<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试用户 - 《大模型技术与油气应用概论》协作系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-users-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .page-title {
            font-size: 2.5rem;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .page-subtitle {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .user-template {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .user-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .user-info h3 {
            margin: 0;
            color: #1f2937;
        }
        
        .user-info p {
            margin: 5px 0 0 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .user-details {
            margin-bottom: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .detail-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .detail-value {
            color: #1f2937;
        }
        
        .role-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .role-owner { background: #fef3c7; color: #92400e; }
        .role-admin { background: #ddd6fe; color: #5b21b6; }
        .role-editor { background: #d1fae5; color: #065f46; }
        .role-author { background: #dbeafe; color: #1e40af; }
        .role-reviewer { background: #fee2e2; color: #991b1b; }
        
        .create-btn {
            width: 100%;
            padding: 10px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        
        .create-btn:hover {
            background: #4338ca;
        }
        
        .create-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .create-btn.success {
            background: #10b981;
        }
        
        .create-btn.error {
            background: #ef4444;
        }
        
        .batch-actions {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn-large {
            padding: 15px 30px;
            font-size: 1.1rem;
            margin: 0 10px;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        
        .btn-primary:hover {
            background: #4338ca;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #4f46e5;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-message {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .status-info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        .status-warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        
        .instructions {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="test-users-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-users-cog"></i> 创建测试用户
            </h1>
            <p class="page-subtitle">为多用户协作功能创建测试账户</p>
        </div>
        
        <div class="instructions">
            <h3><i class="fas fa-info-circle"></i> 使用说明</h3>
            <ul>
                <li>点击"批量创建所有用户"可以一次性创建所有测试用户</li>
                <li>也可以单独创建每个用户来测试不同场景</li>
                <li>创建的用户可以用于测试不同角色的权限和协作功能</li>
                <li>所有测试用户的密码都是：<strong>test123456</strong></li>
                <li>创建完成后可以使用这些账户登录测试系统功能</li>
            </ul>
        </div>
        
        <div class="batch-actions">
            <button class="btn-primary btn-large" onclick="createAllUsers()">
                <i class="fas fa-users"></i> 批量创建所有用户
            </button>
            <button class="btn-secondary btn-large" onclick="clearAllUsers()">
                <i class="fas fa-trash"></i> 清除所有测试用户
            </button>
        </div>
        
        <div class="progress-bar" id="progress-bar" style="display: none;">
            <div class="progress-fill" id="progress-fill"></div>
        </div>

        <div id="project-status" class="status-message status-info" style="display: none;">
            <i class="fas fa-project-diagram"></i> <span id="project-info">正在加载项目信息...</span>
        </div>

        <div id="status-message"></div>
        
        <div class="users-grid" id="users-grid">
            <!-- 用户模板将在这里动态生成 -->
        </div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script>
        // 测试用户模板
        const testUsers = [
            {
                id: 'owner',
                full_name: '张教授',
                username: 'zhang_prof',
                email: '<EMAIL>',
                institution: '中国石油大学（北京）',
                department: '人工智能学院',
                role: 'owner',
                avatar_color: '#f59e0b',
                description: '项目负责人，拥有所有权限'
            },
            {
                id: 'admin',
                full_name: '李副教授',
                username: 'li_admin',
                email: '<EMAIL>',
                institution: '中国石油大学（北京）',
                department: '人工智能学院',
                role: 'admin',
                avatar_color: '#8b5cf6',
                description: '项目管理员，负责日常管理'
            },
            {
                id: 'editor1',
                full_name: '王编辑',
                username: 'wang_editor',
                email: '<EMAIL>',
                institution: '中国石油大学（北京）',
                department: '计算机科学系',
                role: 'editor',
                avatar_color: '#10b981',
                description: '资深编辑，负责内容审核'
            },
            {
                id: 'editor2',
                full_name: '刘编辑',
                username: 'liu_editor',
                email: '<EMAIL>',
                institution: '清华大学',
                department: '计算机系',
                role: 'editor',
                avatar_color: '#10b981',
                description: '技术编辑，负责技术内容'
            },
            {
                id: 'author1',
                full_name: '陈博士',
                username: 'chen_author',
                email: '<EMAIL>',
                institution: '北京理工大学',
                department: '计算机学院',
                role: 'author',
                avatar_color: '#3b82f6',
                description: '专业作者，负责章节编写'
            },
            {
                id: 'author2',
                full_name: '赵研究员',
                username: 'zhao_author',
                email: '<EMAIL>',
                institution: '中科院计算所',
                department: '智能系统实验室',
                role: 'author',
                avatar_color: '#3b82f6',
                description: '研究员，负责理论章节'
            },
            {
                id: 'reviewer1',
                full_name: '孙专家',
                username: 'sun_reviewer',
                email: '<EMAIL>',
                institution: '华为技术有限公司',
                department: 'AI研究院',
                role: 'reviewer',
                avatar_color: '#ef4444',
                description: '行业专家，负责内容审阅'
            },
            {
                id: 'reviewer2',
                full_name: '周顾问',
                username: 'zhou_reviewer',
                email: '<EMAIL>',
                institution: '阿里巴巴集团',
                department: '达摩院',
                role: 'reviewer',
                avatar_color: '#ef4444',
                description: '技术顾问，负责技术审阅'
            }
        ];

        class TestUserCreator {
            constructor() {
                this.createdUsers = new Set();
                this.isCreating = false;
                this.currentProject = null;
            }

            async initialize() {
                // 获取当前项目
                await this.loadCurrentProject();
                this.renderUsers();
                await this.checkExistingUsers();
            }

            async loadCurrentProject() {
                const projectStatus = document.getElementById('project-status');
                const projectInfo = document.getElementById('project-info');

                projectStatus.style.display = 'block';
                projectInfo.textContent = '正在加载项目信息...';

                try {
                    // 获取当前项目（直接查询数据库）
                    const { data: projects, error } = await supabaseManager.supabase
                        .from('projects')
                        .select('*')
                        .limit(1);

                    if (error) {
                        console.error('获取项目失败:', error);
                        projectStatus.className = 'status-message status-error';
                        projectInfo.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 获取项目失败，用户将不会被添加到项目中';
                        return;
                    }

                    if (projects && projects.length > 0) {
                        this.currentProject = projects[0];
                        console.log('当前项目:', this.currentProject.title);
                        projectStatus.className = 'status-message status-success';
                        projectInfo.innerHTML = `<i class="fas fa-check-circle"></i> 当前项目：${this.currentProject.title}`;
                    } else {
                        console.log('未找到项目');
                        projectStatus.className = 'status-message status-error';
                        projectInfo.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 未找到项目，用户将不会被添加到项目中';
                    }
                } catch (error) {
                    console.error('加载项目失败:', error);
                    projectStatus.className = 'status-message status-error';
                    projectInfo.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 加载项目失败，用户将不会被添加到项目中';
                }
            }

            // 验证邮箱格式
            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            renderUsers() {
                const usersGrid = document.getElementById('users-grid');
                usersGrid.innerHTML = testUsers.map(user => this.createUserCard(user)).join('');
            }

            createUserCard(user) {
                const initials = user.full_name.substring(0, 2);
                const isCreated = this.createdUsers.has(user.email);
                
                return `
                    <div class="user-template" id="user-${user.id}">
                        <div class="user-header">
                            <div class="user-avatar" style="background: ${user.avatar_color};">
                                ${initials}
                            </div>
                            <div class="user-info">
                                <h3>${user.full_name}</h3>
                                <p>@${user.username}</p>
                            </div>
                        </div>
                        
                        <div class="user-details">
                            <div class="detail-item">
                                <span class="detail-label">邮箱:</span>
                                <span class="detail-value">${user.email}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">机构:</span>
                                <span class="detail-value">${user.institution}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">部门:</span>
                                <span class="detail-value">${user.department}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">角色:</span>
                                <span class="role-badge role-${user.role}">${this.getRoleDisplayName(user.role)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">描述:</span>
                                <span class="detail-value">${user.description}</span>
                            </div>
                        </div>
                        
                        <button class="create-btn" id="btn-${user.id}" onclick="testUserCreator.createUser('${user.id}')" ${isCreated ? 'disabled' : ''}>
                            ${isCreated ? '✓ 已创建' : '创建用户'}
                        </button>
                    </div>
                `;
            }

            getRoleDisplayName(role) {
                const roleNames = {
                    'owner': '项目所有者',
                    'admin': '管理员',
                    'editor': '编辑者',
                    'author': '作者',
                    'reviewer': '审阅者'
                };
                return roleNames[role] || role;
            }

            async checkExistingUsers() {
                try {
                    for (const user of testUsers) {
                        const { data } = await supabaseManager.supabase
                            .from('user_profiles')
                            .select('email')
                            .eq('email', user.email)
                            .single();

                        if (data) {
                            this.createdUsers.add(user.email);
                            this.updateUserButton(user.id, true);
                        }
                    }
                } catch (error) {
                    // 忽略查询错误，用户可能不存在
                }
            }

            async createUser(userId) {
                const user = testUsers.find(u => u.id === userId);
                if (!user) return;

                const btn = document.getElementById(`btn-${userId}`);
                btn.disabled = true;
                btn.textContent = '创建中...';

                try {
                    // 验证邮箱格式
                    if (!this.isValidEmail(user.email)) {
                        throw new Error('邮箱格式无效');
                    }

                    // 检查用户是否已存在（忽略RLS错误）
                    try {
                        const { data: existingUser } = await supabaseManager.supabase
                            .from('user_profiles')
                            .select('email')
                            .eq('email', user.email)
                            .single();

                        if (existingUser) {
                            this.createdUsers.add(user.email);
                            this.updateUserButton(userId, true);
                            this.showMessage(`用户 ${user.full_name} 已存在`, 'info');
                            return;
                        }
                    } catch (checkError) {
                        // 忽略查询错误，继续创建用户
                        console.log('用户检查被跳过:', checkError.message);
                    }

                    console.log('开始创建用户:', user.email);

                    // 使用直接插入方式创建用户（与 test-create-user.html 相同的方法）
                    const newUserId = crypto.randomUUID();
                    console.log('使用直接插入方式创建用户:', user.email);

                    // 1. 创建用户配置
                    const { data: profile, error: profileError } = await supabaseManager.supabase
                        .from('user_profiles')
                        .insert({
                            id: newUserId,
                            username: user.username,
                            full_name: user.full_name,
                            email: user.email,
                            institution: user.institution || '',
                            department: user.department || '',
                            bio: user.description || ''
                        })
                        .select()
                        .single();

                    if (profileError) {
                        console.error('创建用户配置失败:', profileError);
                        throw new Error(`创建用户配置失败: ${profileError.message}`);
                    }

                    // 2. 添加到项目成员（如果有当前项目）
                    if (this.currentProject) {
                        const currentUser = await supabaseManager.getCurrentUser();
                        const { error: memberError } = await supabaseManager.supabase
                            .from('project_members')
                            .insert({
                                project_id: this.currentProject.id,
                                user_id: newUserId,
                                role: user.role,
                                status: 'active',
                                invited_by: currentUser.id
                            });

                        if (memberError) {
                            console.error('添加项目成员失败:', memberError);
                            // 不抛出错误，因为用户已创建成功
                            console.log('用户创建成功，但添加到项目失败:', memberError.message);
                        }
                    }

                    this.createdUsers.add(user.email);
                    this.updateUserButton(userId, true);
                    this.showMessage(`用户 ${user.full_name} 创建成功！`, 'success');

                } catch (error) {
                    console.error('创建用户失败:', error);

                    let errorMessage = '创建用户失败';

                    if (error.message.includes('邮箱格式无效')) {
                        errorMessage = '邮箱格式无效';
                    } else if (error.message.includes('duplicate')) {
                        errorMessage = '用户信息重复';
                    } else if (error.message.includes('创建用户配置失败')) {
                        errorMessage = '创建用户配置失败';
                    } else if (error.message) {
                        errorMessage = error.message;
                    }

                    this.updateUserButton(userId, false, errorMessage);
                    this.showMessage(`创建用户 ${user.full_name} 失败: ${errorMessage}`, 'error');
                }
            }

            updateUserButton(userId, success, errorMessage = '') {
                const btn = document.getElementById(`btn-${userId}`);
                if (success) {
                    btn.textContent = '✓ 已创建';
                    btn.className = 'create-btn success';
                    btn.disabled = true;
                } else {
                    btn.textContent = errorMessage ? '创建失败' : '创建用户';
                    btn.className = errorMessage ? 'create-btn error' : 'create-btn';
                    btn.disabled = false;
                }
            }

            async createAllUsers() {
                if (this.isCreating) return;
                
                this.isCreating = true;
                const progressBar = document.getElementById('progress-bar');
                const progressFill = document.getElementById('progress-fill');
                
                progressBar.style.display = 'block';
                progressFill.style.width = '0%';

                let completed = 0;
                const total = testUsers.length;

                for (const user of testUsers) {
                    if (!this.createdUsers.has(user.email)) {
                        await this.createUser(user.id);
                    }
                    completed++;
                    progressFill.style.width = `${(completed / total) * 100}%`;
                    
                    // 添加延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                progressBar.style.display = 'none';
                this.isCreating = false;
                this.showMessage('所有测试用户创建完成！', 'success');
            }

            async clearAllUsers() {
                if (!confirm('确定要清除所有测试用户吗？此操作不可恢复！')) {
                    return;
                }

                this.showMessage('清除功能需要管理员权限，请手动删除测试用户', 'info');
            }

            showMessage(message, type) {
                const statusMessage = document.getElementById('status-message');
                statusMessage.innerHTML = `
                    <div class="status-message status-${type}">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                        ${message}
                    </div>
                `;

                setTimeout(() => {
                    statusMessage.innerHTML = '';
                }, 5000);
            }
        }

        // 全局函数
        async function createAllUsers() {
            await testUserCreator.createAllUsers();
        }

        async function clearAllUsers() {
            await testUserCreator.clearAllUsers();
        }

        // 初始化
        const testUserCreator = new TestUserCreator();
        window.addEventListener('load', () => {
            testUserCreator.initialize();
        });
    </script>
</body>
</html>
