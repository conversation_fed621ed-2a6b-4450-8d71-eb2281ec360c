<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase认证CORS测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="url"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            font-family: monospace;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success { background-color: #28a745; }
        .warning { background-color: #ffc107; color: #212529; }
        .danger { background-color: #dc3545; }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success-result {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error-result {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info-result {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .endpoint-list {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .endpoint-item {
            margin: 8px 0;
            padding: 8px;
            background-color: white;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Supabase认证CORS测试工具</h1>
        
        <div class="test-section">
            <h3>📋 配置信息</h3>
            <div class="input-group">
                <label for="supabase-url">Supabase URL:</label>
                <input type="url" id="supabase-url" value="http://superboss.ailer.ltd" placeholder="http://superboss.ailer.ltd">
            </div>
            <div class="input-group">
                <label for="api-key">API Key:</label>
                <input type="text" id="api-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE" placeholder="API Key">
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 API端点测试</h3>
            <div class="endpoint-list">
                <h4>需要测试的Supabase API端点：</h4>
                <div class="endpoint-item">🔍 <strong>数据库API:</strong> /rest/v1/</div>
                <div class="endpoint-item">🔐 <strong>认证API:</strong> /auth/v1/</div>
                <div class="endpoint-item">📁 <strong>存储API:</strong> /storage/v1/</div>
                <div class="endpoint-item">⚡ <strong>实时API:</strong> /realtime/v1/</div>
            </div>
            
            <button onclick="testAllEndpoints()">测试所有端点</button>
            <button onclick="testAuthEndpoint()" class="warning">测试认证端点</button>
            <button onclick="testDatabaseEndpoint()" class="success">测试数据库端点</button>
            <button onclick="generateCorsConfig()" class="danger">生成CORS配置</button>
            
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h3>🔧 CORS配置生成器</h3>
            <p>基于您的域名生成完整的CORS配置：</p>
            <div id="cors-config"></div>
        </div>

        <div class="test-section">
            <h3>📞 快速解决方案</h3>
            <div class="info-result">
                <h4>立即可用的解决方案：</h4>
                <ol>
                    <li><strong>联系系统管理员</strong>：请求在Supabase服务器上配置CORS策略</li>
                    <li><strong>临时解决方案</strong>：使用浏览器扩展禁用CORS检查（仅开发环境）</li>
                    <li><strong>代理服务器</strong>：设置本地代理服务器转发请求</li>
                    <li><strong>服务器端配置</strong>：在Nginx或Apache中添加CORS头部</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        async function testAllEndpoints() {
            const url = document.getElementById('supabase-url').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            const resultsDiv = document.getElementById('test-results');
            
            resultsDiv.innerHTML = '<div class="info-result">🔄 正在测试所有API端点...</div>';
            
            const endpoints = [
                { name: '数据库API', path: '/rest/v1/', method: 'GET' },
                { name: '认证API', path: '/auth/v1/user', method: 'GET' },
                { name: '存储API', path: '/storage/v1/buckets', method: 'GET' },
                { name: '实时API', path: '/realtime/v1/', method: 'GET' }
            ];
            
            let results = '<h4>📊 测试结果：</h4>';
            
            for (const endpoint of endpoints) {
                try {
                    const testUrl = url + endpoint.path;
                    const response = await fetch(testUrl, {
                        method: endpoint.method,
                        headers: {
                            'Content-Type': 'application/json',
                            'apikey': apiKey,
                            'Authorization': `Bearer ${apiKey}`
                        }
                    });
                    
                    results += `<div class="success-result">✅ ${endpoint.name}: 连接成功 (${response.status})</div>`;
                } catch (error) {
                    const isCorsError = error.message.includes('CORS') || error.message.includes('Failed to fetch');
                    const errorType = isCorsError ? 'CORS问题' : '其他错误';
                    results += `<div class="error-result">❌ ${endpoint.name}: ${errorType} - ${error.message}</div>`;
                }
            }
            
            resultsDiv.innerHTML = results;
        }

        async function testAuthEndpoint() {
            const url = document.getElementById('supabase-url').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            const resultsDiv = document.getElementById('test-results');
            
            resultsDiv.innerHTML = '<div class="info-result">🔄 正在测试认证端点...</div>';
            
            try {
                // 测试获取用户信息
                const response = await fetch(`${url}/auth/v1/user`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });
                
                resultsDiv.innerHTML = `<div class="success-result">✅ 认证端点测试成功！状态码: ${response.status}</div>`;
            } catch (error) {
                const isCorsError = error.message.includes('CORS') || error.message.includes('Failed to fetch');
                if (isCorsError) {
                    resultsDiv.innerHTML = `
                        <div class="error-result">
                            ❌ 认证端点CORS错误<br>
                            <strong>错误:</strong> ${error.message}<br>
                            <strong>解决方案:</strong> 需要在服务器配置中添加 /auth/v1/* 路径的CORS支持
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="error-result">❌ 认证端点错误: ${error.message}</div>`;
                }
            }
        }

        async function testDatabaseEndpoint() {
            const url = document.getElementById('supabase-url').value.trim();
            const apiKey = document.getElementById('api-key').value.trim();
            const resultsDiv = document.getElementById('test-results');
            
            resultsDiv.innerHTML = '<div class="info-result">🔄 正在测试数据库端点...</div>';
            
            try {
                const response = await fetch(`${url}/rest/v1/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': apiKey,
                        'Authorization': `Bearer ${apiKey}`
                    }
                });
                
                resultsDiv.innerHTML = `<div class="success-result">✅ 数据库端点测试成功！状态码: ${response.status}</div>`;
            } catch (error) {
                const isCorsError = error.message.includes('CORS') || error.message.includes('Failed to fetch');
                if (isCorsError) {
                    resultsDiv.innerHTML = `
                        <div class="error-result">
                            ❌ 数据库端点CORS错误<br>
                            <strong>错误:</strong> ${error.message}<br>
                            <strong>解决方案:</strong> 需要在服务器配置中添加 /rest/v1/* 路径的CORS支持
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="error-result">❌ 数据库端点错误: ${error.message}</div>`;
                }
            }
        }

        function generateCorsConfig() {
            const url = document.getElementById('supabase-url').value.trim();
            const domain = new URL(url).hostname;
            const configDiv = document.getElementById('cors-config');
            
            const nginxConfig = `
# Nginx配置 (${domain})
server {
    listen 80;
    server_name ${domain};
    
    # 全局CORS配置
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, apikey, X-Client-Info, X-Supabase-Auth' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    
    # 处理所有API路径
    location ~ ^/(rest|auth|storage|realtime)/ {
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, apikey, X-Client-Info, X-Supabase-Auth';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        proxy_pass http://your-supabase-backend;
    }
}`;

            const dockerConfig = `
# Docker Compose配置
version: '3.8'
services:
  kong:
    environment:
      KONG_CORS_ORIGINS: "*"
      KONG_CORS_METHODS: "GET,POST,PUT,DELETE,OPTIONS,PATCH"
      KONG_CORS_HEADERS: "Content-Type,Authorization,apikey,X-Client-Info,X-Supabase-Auth"
      KONG_CORS_CREDENTIALS: "true"`;

            configDiv.innerHTML = `
                <h4>🔧 为您的域名生成的CORS配置：</h4>
                <div class="code-block">${nginxConfig}</div>
                <div class="code-block">${dockerConfig}</div>
                <div class="info-result">
                    <strong>配置说明：</strong><br>
                    1. 将Nginx配置保存到服务器配置文件<br>
                    2. 重启Nginx服务: <code>sudo systemctl restart nginx</code><br>
                    3. 如果使用Docker，更新docker-compose.yml并重启容器
                </div>
            `;
        }

        // 页面加载时自动生成配置
        window.addEventListener('load', function() {
            generateCorsConfig();
        });
    </script>
</body>
</html>
