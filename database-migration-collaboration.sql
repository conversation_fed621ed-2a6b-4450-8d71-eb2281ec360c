-- 《大模型技术与油气应用概论》多用户协作系统数据库迁移脚本
-- 此脚本用于将现有数据库升级为支持多用户协作功能

-- 1. 更新用户配置表结构
ALTER TABLE public.user_profiles 
DROP COLUMN IF EXISTS role,
ADD COLUMN IF NOT EXISTS global_role VARCHAR(20) DEFAULT 'user' CHECK (global_role IN ('system_admin', 'user')),
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE;

-- 2. 更新项目成员表结构
ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS invited_by UUID REFERENCES public.user_profiles(id),
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'invited')),
ADD COLUMN IF NOT EXISTS invitation_token VARCHAR(255),
ADD COLUMN IF NOT EXISTS invitation_expires_at TIMESTAMP WITH TIME ZONE;

-- 更新项目成员角色约束
ALTER TABLE public.project_members 
DROP CONSTRAINT IF EXISTS project_members_role_check,
ADD CONSTRAINT project_members_role_check CHECK (role IN ('owner', 'admin', 'editor', 'author', 'reviewer'));

-- 3. 创建章节分配表
CREATE TABLE IF NOT EXISTS public.chapter_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('author', 'co_author', 'reviewer', 'editor')),
    assigned_by UUID REFERENCES public.user_profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deadline TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'assigned' CHECK (status IN ('assigned', 'accepted', 'in_progress', 'completed', 'reviewed')),
    notes TEXT,
    UNIQUE(chapter_id, user_id, role)
);

-- 4. 创建审核流程表
CREATE TABLE IF NOT EXISTS public.review_processes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'revision_required')),
    review_notes TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 更新评论系统表
DROP TABLE IF EXISTS public.comments;
CREATE TABLE public.comments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE, -- 支持回复
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position_start INTEGER, -- 评论位置（字符开始位置）
    position_end INTEGER,   -- 评论位置（字符结束位置）
    type VARCHAR(20) DEFAULT 'general' CHECK (type IN ('general', 'suggestion', 'correction', 'question')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 创建用户邀请表
CREATE TABLE IF NOT EXISTS public.user_invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    invited_by UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'editor', 'author', 'reviewer')),
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 更新通知表
ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS data JSONB;

-- 8. 创建新的索引
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_chapter ON public.chapter_assignments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_user ON public.chapter_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_review_processes_chapter ON public.review_processes(chapter_id);
CREATE INDEX IF NOT EXISTS idx_review_processes_reviewer ON public.review_processes(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_email ON public.user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_user_invitations_token ON public.user_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_comments_chapter ON public.comments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_comments_user ON public.comments(user_id);

-- 9. 启用新表的行级安全
ALTER TABLE public.chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_processes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- 10. 创建行级安全策略

-- 章节分配表的RLS策略
CREATE POLICY "Users can view assignments in their projects" ON public.chapter_assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = chapter_assignments.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
            AND pm.status = 'active'
        )
    );

CREATE POLICY "Project managers can manage assignments" ON public.chapter_assignments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = chapter_assignments.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
            AND pm.role IN ('owner', 'admin', 'editor')
            AND pm.status = 'active'
        )
    );

-- 审核流程表的RLS策略
CREATE POLICY "Users can view reviews in their projects" ON public.review_processes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = review_processes.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
            AND pm.status = 'active'
        )
    );

CREATE POLICY "Reviewers can manage their reviews" ON public.review_processes
    FOR ALL USING (
        reviewer_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = review_processes.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
            AND pm.role IN ('owner', 'admin', 'editor')
            AND pm.status = 'active'
        )
    );

-- 评论表的RLS策略
CREATE POLICY "Users can view comments in their projects" ON public.comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = comments.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
            AND pm.status = 'active'
        )
    );

CREATE POLICY "Users can manage their own comments" ON public.comments
    FOR ALL USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = comments.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
            AND pm.role IN ('owner', 'admin', 'editor')
            AND pm.status = 'active'
        )
    );

-- 用户邀请表的RLS策略
CREATE POLICY "Project managers can manage invitations" ON public.user_invitations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            WHERE pm.project_id = user_invitations.project_id 
            AND pm.user_id = auth.uid()
            AND pm.role IN ('owner', 'admin')
            AND pm.status = 'active'
        )
    );

-- 11. 创建触发器函数用于自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间戳的触发器
CREATE TRIGGER update_review_processes_updated_at 
    BEFORE UPDATE ON public.review_processes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_comments_updated_at 
    BEFORE UPDATE ON public.comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 12. 插入示例数据（可选）
-- 这里可以插入一些示例的角色权限配置等

-- 13. 更新现有数据（如果需要）
-- 将现有的单用户项目转换为多用户项目
-- 为现有项目的创建者分配owner角色
INSERT INTO public.project_members (project_id, user_id, role, status, joined_at)
SELECT 
    p.id as project_id,
    p.owner_id as user_id,
    'owner' as role,
    'active' as status,
    p.created_at as joined_at
FROM public.projects p
WHERE NOT EXISTS (
    SELECT 1 FROM public.project_members pm 
    WHERE pm.project_id = p.id AND pm.user_id = p.owner_id
)
ON CONFLICT (project_id, user_id) DO NOTHING;

-- 完成迁移
COMMENT ON SCHEMA public IS '多用户协作系统数据库 - 迁移完成于 ' || NOW();
