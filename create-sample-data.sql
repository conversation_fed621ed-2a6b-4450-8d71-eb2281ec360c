-- 创建示例数据用于测试章节分配功能
-- 在数据库结构修复后执行

-- 1. 首先检查是否已有项目数据
DO $$
DECLARE
    project_count INTEGER;
    user_count INTEGER;
    demo_project_id UUID;
    demo_user_id UUID;
BEGIN
    -- 检查项目数量
    SELECT COUNT(*) INTO project_count FROM public.projects;
    
    -- 检查用户数量  
    SELECT COUNT(*) INTO user_count FROM public.user_profiles;
    
    RAISE NOTICE '当前项目数量: %, 用户数量: %', project_count, user_count;
    
    -- 如果没有项目，创建一个演示项目
    IF project_count = 0 THEN
        -- 创建演示用户（如果不存在）
        INSERT INTO public.user_profiles (id, username, full_name, email, institution, department)
        VALUES 
            ('00000000-0000-0000-0000-000000000001', 'demo_user', '演示用户', '<EMAIL>', '演示大学', '计算机学院'),
            ('00000000-0000-0000-0000-000000000002', 'zhang_prof', '张教授', '<EMAIL>', '石油大学', '地质学院'),
            ('00000000-0000-0000-0000-000000000003', 'li_doctor', '李博士', '<EMAIL>', '石油大学', '计算机学院'),
            ('00000000-0000-0000-0000-000000000004', 'wang_researcher', '王研究员', '<EMAIL>', '石油大学', '地质学院'),
            ('00000000-0000-0000-0000-000000000005', 'liu_dean', '刘院长', '<EMAIL>', '石油大学', '地质学院'),
            ('00000000-0000-0000-0000-000000000006', 'chen_assistant', '陈助教', '<EMAIL>', '石油大学', '计算机学院'),
            ('00000000-0000-0000-0000-000000000007', 'zhao_prof', '赵副教授', '<EMAIL>', '石油大学', '地质学院')
        ON CONFLICT (id) DO NOTHING;
        
        -- 创建演示项目
        demo_project_id := '10000000-0000-0000-0000-000000000001';
        INSERT INTO public.projects (id, title, description, owner_id, status)
        VALUES (
            demo_project_id,
            '《大模型技术与油气应用概论》',
            '这是一个关于大模型技术在油气勘探开发中应用的专业著作项目',
            '00000000-0000-0000-0000-000000000001',
            'active'
        ) ON CONFLICT (id) DO NOTHING;
        
        -- 添加项目成员
        INSERT INTO public.project_members (project_id, user_id, role, status)
        VALUES 
            (demo_project_id, '00000000-0000-0000-0000-000000000001', 'owner', 'active'),
            (demo_project_id, '00000000-0000-0000-0000-000000000002', 'editor', 'active'),
            (demo_project_id, '00000000-0000-0000-0000-000000000003', 'author', 'active'),
            (demo_project_id, '00000000-0000-0000-0000-000000000004', 'author', 'active'),
            (demo_project_id, '00000000-0000-0000-0000-000000000005', 'reviewer', 'active'),
            (demo_project_id, '00000000-0000-0000-0000-000000000006', 'author', 'active'),
            (demo_project_id, '00000000-0000-0000-0000-000000000007', 'author', 'active')
        ON CONFLICT (project_id, user_id) DO NOTHING;
        
        RAISE NOTICE '演示项目和用户创建完成';
    ELSE
        -- 使用现有的第一个项目
        SELECT id INTO demo_project_id FROM public.projects LIMIT 1;
        RAISE NOTICE '使用现有项目: %', demo_project_id;
    END IF;
    
    -- 创建章节数据
    INSERT INTO public.chapters (id, project_id, title, summary, status, order_index, created_by)
    VALUES
        ('20000000-0000-0000-0000-000000000001', demo_project_id, '第0章：前言', '介绍本书的写作背景、目标读者、主要内容和结构安排', 'writing', 0, '00000000-0000-0000-0000-000000000001'),
        ('20000000-0000-0000-0000-000000000002', demo_project_id, '第1章：大模型基本概念与内涵', '阐述大模型的定义、特点、分类和发展历程', 'review', 1, '00000000-0000-0000-0000-000000000001'),
        ('20000000-0000-0000-0000-000000000003', demo_project_id, '第2章：大模型技术原理', '深入讲解Transformer架构、注意力机制等核心技术原理', 'draft', 2, '00000000-0000-0000-0000-000000000001'),
        ('20000000-0000-0000-0000-000000000004', demo_project_id, '第3章：大模型训练与优化', '介绍大模型的训练方法、优化技术和性能评估', 'writing', 3, '00000000-0000-0000-0000-000000000001'),
        ('20000000-0000-0000-0000-000000000005', demo_project_id, '第4章：油气勘探中的大模型应用', '大模型在地震数据解释、储层预测等勘探环节的应用', 'approved', 4, '00000000-0000-0000-0000-000000000001'),
        ('20000000-0000-0000-0000-000000000006', demo_project_id, '第5章：油气开发中的大模型应用', '大模型在钻井优化、生产预测等开发环节的应用', 'review', 5, '00000000-0000-0000-0000-000000000001')
    ON CONFLICT (id) DO NOTHING;
    
    -- 创建章节分配
    INSERT INTO public.chapter_assignments (
        id, chapter_id, project_id, lead_author_id, reviewer_id, assigned_by,
        title, description, status, due_date, priority, word_count_target
    )
    VALUES 
        (
            '30000000-0000-0000-0000-000000000001',
            '20000000-0000-0000-0000-000000000001',
            demo_project_id,
            '00000000-0000-0000-0000-000000000002', -- 张教授
            '00000000-0000-0000-0000-000000000005', -- 刘院长
            '00000000-0000-0000-0000-000000000001', -- 演示用户
            '第0章：前言',
            '介绍本书的写作背景、目标读者、主要内容和结构安排',
            'writing',
            NOW() + INTERVAL '7 days',
            'high',
            5000
        ),
        (
            '30000000-0000-0000-0000-000000000002',
            '20000000-0000-0000-0000-000000000002',
            demo_project_id,
            '00000000-0000-0000-0000-000000000003', -- 李博士
            '00000000-0000-0000-0000-000000000002', -- 张教授
            '00000000-0000-0000-0000-000000000001',
            '第1章：大模型基本概念与内涵',
            '阐述大模型的定义、特点、分类和发展历程',
            'reviewing',
            NOW() + INTERVAL '14 days',
            'medium',
            8000
        ),
        (
            '30000000-0000-0000-0000-000000000003',
            '20000000-0000-0000-0000-000000000003',
            demo_project_id,
            '00000000-0000-0000-0000-000000000004', -- 王研究员
            '00000000-0000-0000-0000-000000000005', -- 刘院长
            '00000000-0000-0000-0000-000000000001',
            '第2章：大模型技术原理',
            '深入讲解Transformer架构、注意力机制等核心技术原理',
            'pending',
            NOW() + INTERVAL '21 days',
            'medium',
            6000
        ),
        (
            '30000000-0000-0000-0000-000000000004',
            '20000000-0000-0000-0000-000000000004',
            demo_project_id,
            '00000000-0000-0000-0000-000000000007', -- 赵副教授
            '00000000-0000-0000-0000-000000000002', -- 张教授
            '00000000-0000-0000-0000-000000000001',
            '第3章：大模型训练与优化',
            '介绍大模型的训练方法、优化技术和性能评估',
            'writing',
            NOW() + INTERVAL '28 days',
            'high',
            7000
        ),
        (
            '30000000-0000-0000-0000-000000000005',
            '20000000-0000-0000-0000-000000000005',
            demo_project_id,
            '00000000-0000-0000-0000-000000000003', -- 李博士
            '00000000-0000-0000-0000-000000000005', -- 刘院长
            '00000000-0000-0000-0000-000000000001',
            '第4章：油气勘探中的大模型应用',
            '大模型在地震数据解释、储层预测等勘探环节的应用',
            'completed',
            NOW() - INTERVAL '7 days',
            'high',
            8500
        ),
        (
            '30000000-0000-0000-0000-000000000006',
            '20000000-0000-0000-0000-000000000006',
            demo_project_id,
            '00000000-0000-0000-0000-000000000006', -- 陈助教
            '00000000-0000-0000-0000-000000000002', -- 张教授
            '00000000-0000-0000-0000-000000000001',
            '第5章：油气开发中的大模型应用',
            '大模型在钻井优化、生产预测等开发环节的应用',
            'reviewing',
            NOW() + INTERVAL '35 days',
            'medium',
            7500
        )
    ON CONFLICT (id) DO NOTHING;
    
    -- 创建协作者关系
    INSERT INTO public.chapter_collaborators (assignment_id, user_id, role, status)
    VALUES 
        ('30000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000004', 'collaborator', 'active'), -- 第一章：王研究员协作
        ('30000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000006', 'collaborator', 'active'), -- 第二章：陈助教协作
        ('30000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000006', 'collaborator', 'active'), -- 第四章：陈助教协作
        ('30000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000004', 'collaborator', 'active'), -- 第五章：王研究员协作
        ('30000000-0000-0000-0000-000000000006', '00000000-0000-0000-0000-000000000007', 'collaborator', 'active')  -- 第六章：赵副教授协作
    ON CONFLICT (assignment_id, user_id) DO NOTHING;
    
    RAISE NOTICE '示例数据创建完成！';
    RAISE NOTICE '项目ID: %', demo_project_id;
    RAISE NOTICE '创建了6个章节和6个章节分配';
    
END $$;
