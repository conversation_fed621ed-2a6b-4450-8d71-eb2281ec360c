// 简化的Supabase管理器 - 用于演示和测试
// 当原始Supabase配置不可用时的后备方案

class SimpleSupabaseManager {
    constructor() {
        this.supabaseUrl = 'https://bigzfjlaypptochqpxzu.supabase.co';
        this.supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpZ3pmamxheXBwdG9jaHFweHp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MDc1MjUsImV4cCI6MjA2ODI4MzUyNX0.02clbd9p8TCBqsYEkKJlHnbnU60cRSVxgJo0bfNtq9M';
        
        this.currentUser = null;
        this.currentProject = null;
        this.isInitialized = false;
        
        this.initializeSimpleClient();
    }
    
    // 初始化简化客户端
    initializeSimpleClient() {
        try {
            // 模拟Supabase客户端
            this.supabase = {
                auth: {
                    getUser: () => this.getMockUser(),
                    signInWithPassword: (credentials) => this.mockSignIn(credentials),
                    signOut: () => this.mockSignOut(),
                    onAuthStateChange: (callback) => this.mockAuthStateChange(callback)
                },
                from: (table) => this.mockTable(table)
            };
            
            this.isInitialized = true;
            console.log('简化Supabase管理器初始化完成');
            
            // 模拟用户登录状态
            this.checkAuthState();
            
        } catch (error) {
            console.error('简化Supabase管理器初始化失败:', error);
        }
    }
    
    // 检查认证状态
    async checkAuthState() {
        try {
            // 模拟已登录用户
            this.currentUser = {
                id: 'demo-user-1',
                email: '<EMAIL>',
                user_metadata: {
                    full_name: '演示用户'
                },
                created_at: new Date().toISOString()
            };
            
            console.log('用户已登录:', this.currentUser.email);
            
        } catch (error) {
            console.error('检查认证状态失败:', error);
        }
    }
    
    // 获取当前用户
    async getCurrentUser() {
        // 确保返回用户对象
        if (!this.currentUser) {
            this.currentUser = {
                id: 'demo-user-1',
                email: '<EMAIL>',
                user_metadata: {
                    full_name: '演示用户'
                },
                created_at: new Date().toISOString()
            };
        }
        return this.currentUser;
    }
    
    // 模拟用户获取
    async getMockUser() {
        return {
            data: {
                user: this.currentUser
            },
            error: null
        };
    }
    
    // 模拟登录
    async mockSignIn(credentials) {
        console.log('模拟登录:', credentials.email);
        
        this.currentUser = {
            id: 'demo-user-1',
            email: credentials.email,
            user_metadata: {
                full_name: '演示用户'
            },
            created_at: new Date().toISOString()
        };
        
        return {
            data: {
                user: this.currentUser,
                session: {
                    access_token: 'mock-token',
                    refresh_token: 'mock-refresh-token'
                }
            },
            error: null
        };
    }
    
    // 模拟登出
    async mockSignOut() {
        this.currentUser = null;
        console.log('用户已登出');
        
        return {
            error: null
        };
    }
    
    // 模拟认证状态变化监听
    mockAuthStateChange(callback) {
        // 立即调用回调函数，模拟已登录状态
        setTimeout(() => {
            callback('SIGNED_IN', {
                user: this.currentUser,
                session: {
                    access_token: 'mock-token'
                }
            });
        }, 100);
        
        return {
            data: {
                subscription: {
                    unsubscribe: () => console.log('取消认证状态监听')
                }
            }
        };
    }
    
    // 模拟数据表操作
    mockTable(tableName) {
        return {
            select: (columns = '*') => ({
                eq: (column, value) => ({
                    single: () => this.mockSingleResult(tableName, column, value),
                    order: (column, options) => this.mockOrderedResults(tableName),
                    limit: (count) => this.mockLimitedResults(tableName, count)
                }),
                order: (column, options) => this.mockOrderedResults(tableName),
                limit: (count) => this.mockLimitedResults(tableName, count)
            }),
            insert: (data) => ({
                select: () => ({
                    single: () => this.mockInsertResult(tableName, data)
                })
            }),
            update: (data) => ({
                eq: (column, value) => this.mockUpdateResult(tableName, data, column, value)
            }),
            delete: () => ({
                eq: (column, value) => this.mockDeleteResult(tableName, column, value)
            })
        };
    }
    
    // 模拟单个结果
    async mockSingleResult(tableName, column, value) {
        console.log(`模拟查询 ${tableName} where ${column} = ${value}`);
        
        const mockData = {
            projects: {
                id: value,
                title: '演示项目',
                description: '这是一个演示项目',
                status: 'active',
                created_at: new Date().toISOString()
            },
            project_members: {
                id: value,
                role: 'chief_editor',
                user_id: this.currentUser?.id,
                project_id: value
            }
        };
        
        return {
            data: mockData[tableName] || null,
            error: null
        };
    }
    
    // 模拟排序结果
    async mockOrderedResults(tableName) {
        console.log(`模拟查询 ${tableName} 排序结果`);
        
        return {
            data: [],
            error: null
        };
    }
    
    // 模拟限制结果
    async mockLimitedResults(tableName, count) {
        console.log(`模拟查询 ${tableName} 限制 ${count} 条`);
        
        return {
            data: [],
            error: null
        };
    }
    
    // 模拟插入结果
    async mockInsertResult(tableName, data) {
        console.log(`模拟插入 ${tableName}:`, data);
        
        return {
            data: {
                id: 'mock-id-' + Date.now(),
                ...data,
                created_at: new Date().toISOString()
            },
            error: null
        };
    }
    
    // 模拟更新结果
    async mockUpdateResult(tableName, data, column, value) {
        console.log(`模拟更新 ${tableName} set`, data, `where ${column} = ${value}`);
        
        return {
            data: null,
            error: null
        };
    }
    
    // 模拟删除结果
    async mockDeleteResult(tableName, column, value) {
        console.log(`模拟删除 ${tableName} where ${column} = ${value}`);
        
        return {
            data: null,
            error: null
        };
    }
    
    // 获取当前项目
    getCurrentProject() {
        return this.currentProject;
    }
    
    // 设置当前项目
    setCurrentProject(project) {
        this.currentProject = project;
        console.log('当前项目已设置:', project?.title);
    }
    
    // 检查是否已初始化
    isReady() {
        return this.isInitialized;
    }
    
    // 获取配置信息
    getConfig() {
        return {
            url: this.supabaseUrl,
            isSimpleMode: true,
            initialized: this.isInitialized
        };
    }
}

// 如果原始supabaseManager不存在，创建简化版本
if (typeof supabaseManager === 'undefined') {
    console.log('创建简化Supabase管理器');
    window.supabaseManager = new SimpleSupabaseManager();
} else {
    console.log('使用现有Supabase管理器');
}

// 导出类供其他地方使用
window.SimpleSupabaseManager = SimpleSupabaseManager;
