<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>私有化Supabase CORS问题解决指南</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d73527;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
        }
        .error-box {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .solution-box {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning-box {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info-box {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .step {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .step h4 {
            margin-top: 0;
            color: #0056b3;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .config-example {
            background-color: #f1f3f4;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
        .test-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success { background-color: #28a745; }
        .warning { background-color: #ffc107; color: #212529; }
        .danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 私有化Supabase CORS问题解决指南</h1>
        
        <div class="error-box">
            <strong>常见错误信息：</strong><br>
            <strong>1. 数据库API错误：</strong><br>
            Access to fetch at 'http://superboss.ailer.ltd/rest/v1/user_profiles?select=count' from origin 'null' has been blocked by CORS policy<br><br>

            <strong>2. 认证API错误：</strong><br>
            Access to fetch at 'http://superboss.ailer.ltd/auth/v1/token?grant_type=password' from origin 'null' has been blocked by CORS policy<br><br>

            <strong>3. 通用错误：</strong><br>
            Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
        </div>

        <h2>🔍 问题分析</h2>
        <div class="info-box">
            <p><strong>CORS（跨域资源共享）问题</strong>是由于浏览器的安全策略导致的。当网页尝试访问不同域名的API时，浏览器会检查目标服务器是否允许这种跨域访问。</p>
            <p>您的私有化Supabase部署 <code>http://superboss.ailer.ltd</code> 没有正确配置CORS策略，影响了以下API端点：</p>
            <ul>
                <li><strong>数据库API：</strong> <code>/rest/v1/*</code> - 用于数据查询和操作</li>
                <li><strong>认证API：</strong> <code>/auth/v1/*</code> - 用于用户登录、注册等</li>
                <li><strong>存储API：</strong> <code>/storage/v1/*</code> - 用于文件上传下载</li>
                <li><strong>实时API：</strong> <code>/realtime/v1/*</code> - 用于实时数据同步</li>
            </ul>
        </div>

        <h2>🛠️ 解决方案</h2>

        <div class="step">
            <h4>方案1：配置Supabase服务器的CORS设置（推荐）</h4>
            <p>在您的私有化Supabase部署中添加以下CORS配置：</p>
            
            <div class="code-block">
# 在Supabase配置文件中添加
CORS_ALLOWED_ORIGINS=*
# 或者指定具体域名
CORS_ALLOWED_ORIGINS=http://localhost:3000,file://,null

# 允许的HTTP方法
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH

# 允许的请求头
CORS_ALLOWED_HEADERS=Content-Type,Authorization,apikey,X-Client-Info
            </div>

            <div class="warning-box">
                <strong>注意：</strong>在生产环境中，建议指定具体的域名而不是使用 <code>*</code>
            </div>
        </div>

        <div class="step">
            <h4>方案2：在反向代理中配置CORS</h4>
            <p>如果您使用Nginx作为反向代理，可以添加以下配置：</p>
            
            <div class="code-block">
server {
    listen 80;
    server_name superboss.ailer.ltd;

    # 全局CORS配置
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, apikey, X-Client-Info, X-Supabase-Auth' always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;

    # 数据库API
    location /rest/ {
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS, PATCH';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, apikey, X-Client-Info';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        proxy_pass http://your-supabase-backend;
    }

    # 认证API
    location /auth/ {
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, apikey, X-Supabase-Auth';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        proxy_pass http://your-supabase-backend;
    }

    # 存储API
    location /storage/ {
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, apikey';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        proxy_pass http://your-supabase-backend;
    }

    # 实时API
    location /realtime/ {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_pass http://your-supabase-backend;
    }
}
            </div>
        </div>

        <div class="step">
            <h4>方案3：使用代理服务器</h4>
            <p>创建一个简单的代理服务器来转发请求：</p>
            
            <div class="code-block">
// proxy-server.js (Node.js)
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();

// 启用CORS
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'apikey']
}));

// 代理到Supabase
app.use('/api', createProxyMiddleware({
    target: 'http://superboss.ailer.ltd',
    changeOrigin: true,
    pathRewrite: {
        '^/api': ''
    }
}));

app.listen(3001, () => {
    console.log('代理服务器运行在 http://localhost:3001');
});
            </div>
        </div>

        <div class="step">
            <h4>方案4：修改应用配置使用代理</h4>
            <p>临时解决方案：在应用中使用代理URL</p>
            
            <div class="config-example">
原始URL: http://superboss.ailer.ltd/rest/v1/
代理URL: http://localhost:3001/rest/v1/
            </div>
        </div>

        <h2>🧪 测试CORS配置</h2>
        <div class="test-section">
            <h3>快速测试工具</h3>
            <p>使用以下工具测试您的CORS配置是否正确：</p>
            
            <button onclick="testCorsWithFetch()">测试Fetch请求</button>
            <button onclick="testCorsWithCurl()" class="warning">生成cURL命令</button>
            <button onclick="checkCorsHeaders()" class="success">检查CORS头</button>
            
            <div id="test-results" style="margin-top: 15px;"></div>
        </div>

        <h2>📋 检查清单</h2>
        <div class="solution-box">
            <h4>配置完成后，请检查以下项目：</h4>
            <ul>
                <li>✅ 服务器返回 <code>Access-Control-Allow-Origin</code> 头</li>
                <li>✅ 允许的方法包含 <code>GET, POST, PUT, DELETE, OPTIONS</code></li>
                <li>✅ 允许的头部包含 <code>Content-Type, Authorization, apikey</code></li>
                <li>✅ 正确处理 <code>OPTIONS</code> 预检请求</li>
                <li>✅ 重启Supabase服务使配置生效</li>
            </ul>
        </div>

        <h2>🔧 Docker部署的特殊配置</h2>
        <div class="info-box">
            <p>如果您使用Docker部署Supabase，可能需要在docker-compose.yml中添加环境变量：</p>
            
            <div class="code-block">
version: '3.8'
services:
  kong:
    environment:
      KONG_CORS_ORIGINS: "*"
      KONG_CORS_METHODS: "GET,POST,PUT,DELETE,OPTIONS"
      KONG_CORS_HEADERS: "Content-Type,Authorization,apikey"
            </div>
        </div>

        <h2>📞 需要帮助？</h2>
        <div class="warning-box">
            <p><strong>如果问题仍然存在：</strong></p>
            <ul>
                <li>检查Supabase服务器日志</li>
                <li>确认API端点是否正确</li>
                <li>验证API密钥是否有效</li>
                <li>尝试使用Postman等工具直接测试API</li>
                <li>联系您的系统管理员配置CORS策略</li>
            </ul>
        </div>
    </div>

    <script>
        function testCorsWithFetch() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML = '<div style="color: #007bff;">🔄 正在测试CORS配置...</div>';
            
            fetch('http://superboss.ailer.ltd/rest/v1/', {
                method: 'OPTIONS',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                resultDiv.innerHTML = `
                    <div style="color: #28a745;">✅ CORS测试完成</div>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
CORS Headers:
${JSON.stringify(corsHeaders, null, 2)}
                    </pre>
                `;
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div style="color: #dc3545;">❌ CORS测试失败</div>
                    <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin-top: 10px;">
                        错误: ${error.message}
                    </div>
                `;
            });
        }

        function testCorsWithCurl() {
            const curlCommand = `curl -X OPTIONS \\
  -H "Origin: http://localhost:3000" \\
  -H "Access-Control-Request-Method: GET" \\
  -H "Access-Control-Request-Headers: Content-Type,Authorization,apikey" \\
  -v \\
  http://superboss.ailer.ltd/rest/v1/`;
            
            document.getElementById('test-results').innerHTML = `
                <div style="color: #ffc107;">📋 cURL测试命令：</div>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px; overflow-x: auto;">${curlCommand}</pre>
                <div style="margin-top: 10px; color: #6c757d;">
                    <small>在终端中运行此命令来测试CORS配置</small>
                </div>
            `;
        }

        function checkCorsHeaders() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML = `
                <div style="color: #17a2b8;">🔍 CORS配置检查指南：</div>
                <div style="background: #d1ecf1; padding: 15px; border-radius: 4px; margin-top: 10px;">
                    <h4 style="margin-top: 0;">必需的CORS头部：</h4>
                    <ul>
                        <li><code>Access-Control-Allow-Origin: *</code> 或指定域名</li>
                        <li><code>Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS</code></li>
                        <li><code>Access-Control-Allow-Headers: Content-Type, Authorization, apikey</code></li>
                    </ul>
                    <p><strong>检查方法：</strong>打开浏览器开发者工具的Network标签，查看响应头部</p>
                </div>
            `;
        }
    </script>
</body>
</html>
