// 测试项目重复问题的脚本
// 在浏览器控制台中运行此脚本来检查项目重复情况

async function testProjectDuplicates() {
    console.log('🔍 开始测试项目重复问题...');
    
    try {
        // 1. 测试原始数据库查询
        console.log('\n1. 测试原始数据库查询（可能有重复）:');
        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            console.error('用户未登录');
            return;
        }
        
        const { data: rawData, error } = await supabaseManager.supabase
            .from('projects')
            .select(`
                *,
                project_members!inner(role)
            `)
            .eq('project_members.user_id', user.id);
            
        if (error) {
            console.error('查询失败:', error);
            return;
        }
        
        console.log(`原始查询结果: ${rawData?.length || 0} 条记录`);
        if (rawData && rawData.length > 0) {
            const projectTitles = rawData.map(p => p.title);
            console.log('项目标题:', projectTitles);
            
            // 检查重复
            const duplicates = projectTitles.filter((title, index) => 
                projectTitles.indexOf(title) !== index
            );
            if (duplicates.length > 0) {
                console.warn('⚠️ 发现重复项目:', [...new Set(duplicates)]);
            } else {
                console.log('✅ 原始数据无重复');
            }
        }
        
        // 2. 测试修复后的getUserProjects方法
        console.log('\n2. 测试修复后的getUserProjects方法:');
        const projects = await supabaseManager.getUserProjects();
        console.log(`去重后结果: ${projects?.length || 0} 个项目`);
        
        if (projects && projects.length > 0) {
            const projectTitles = projects.map(p => p.title);
            console.log('项目标题:', projectTitles);
            
            // 检查重复
            const duplicates = projectTitles.filter((title, index) => 
                projectTitles.indexOf(title) !== index
            );
            if (duplicates.length > 0) {
                console.error('❌ 仍有重复项目:', [...new Set(duplicates)]);
            } else {
                console.log('✅ 去重成功，无重复项目');
            }
            
            // 显示用户角色信息
            projects.forEach(project => {
                if (project.user_roles && project.user_roles.length > 1) {
                    console.log(`📋 项目 "${project.title}" 中用户角色:`, project.user_roles);
                }
            });
        }
        
        // 3. 测试项目成员表数据
        console.log('\n3. 检查项目成员表数据:');
        const { data: memberData, error: memberError } = await supabaseManager.supabase
            .from('project_members')
            .select('project_id, user_id, role, status')
            .eq('user_id', user.id);
            
        if (memberError) {
            console.error('查询成员数据失败:', memberError);
        } else {
            console.log(`用户参与的项目成员记录: ${memberData?.length || 0} 条`);
            if (memberData && memberData.length > 0) {
                // 按项目分组
                const projectGroups = {};
                memberData.forEach(member => {
                    if (!projectGroups[member.project_id]) {
                        projectGroups[member.project_id] = [];
                    }
                    projectGroups[member.project_id].push(member.role);
                });
                
                console.log('按项目分组的角色:');
                Object.entries(projectGroups).forEach(([projectId, roles]) => {
                    if (roles.length > 1) {
                        console.log(`  项目 ${projectId}: ${roles.join(', ')} (多角色)`);
                    } else {
                        console.log(`  项目 ${projectId}: ${roles[0]}`);
                    }
                });
            }
        }
        
        console.log('\n✅ 项目重复测试完成');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 测试下拉框渲染
function testDropdownRendering() {
    console.log('\n🎨 测试下拉框渲染...');
    
    const projectList = document.getElementById('project-list-inline');
    if (!projectList) {
        console.error('项目下拉列表元素未找到');
        return;
    }
    
    const projectItems = projectList.querySelectorAll('.project-item:not(.create-new)');
    console.log(`下拉框中显示的项目数量: ${projectItems.length}`);
    
    const titles = Array.from(projectItems).map(item => {
        const textElement = item.querySelector('.project-item-text');
        return textElement ? textElement.textContent : '未知';
    });
    
    console.log('下拉框中的项目标题:', titles);
    
    // 检查重复
    const duplicates = titles.filter((title, index) => 
        titles.indexOf(title) !== index
    );
    
    if (duplicates.length > 0) {
        console.error('❌ 下拉框中发现重复项目:', [...new Set(duplicates)]);
    } else {
        console.log('✅ 下拉框无重复项目');
    }
}

// 运行测试
console.log('🚀 运行项目重复测试...');
testProjectDuplicates().then(() => {
    setTimeout(testDropdownRendering, 1000);
});
