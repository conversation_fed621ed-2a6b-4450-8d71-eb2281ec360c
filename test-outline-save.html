<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大纲保存测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
            background: #f8fafc;
        }
        .test-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
            font-size: 18px;
        }
        .test-steps {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            margin: 10px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-info { background: #06b6d4; color: white; }
        .log-area {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: 500;
        }
        .status-success { background: #d1fae5; color: #065f46; }
        .status-error { background: #fee2e2; color: #991b1b; }
        .status-warning { background: #fef3c7; color: #92400e; }
        .status-info { background: #dbeafe; color: #1e40af; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 大纲保存功能测试</h1>
        <p>此页面用于测试AI生成大纲和导入大纲的数据库保存功能</p>
    </div>

    <div class="test-section">
        <div class="test-title">📋 测试步骤</div>
        <div class="test-steps">
            <ol>
                <li>点击"初始化系统"按钮加载必要的组件</li>
                <li>点击"模拟登录"按钮模拟用户登录状态</li>
                <li>点击"创建测试项目"按钮创建一个测试项目</li>
                <li>点击"生成测试大纲"按钮创建测试大纲数据</li>
                <li>点击"保存大纲到数据库"按钮测试保存功能</li>
                <li>点击"从数据库加载大纲"按钮验证数据是否正确保存</li>
                <li>检查日志输出确认所有步骤是否成功</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🎮 测试控制</div>
        <button class="btn btn-primary" onclick="initializeSystem()">初始化系统</button>
        <button class="btn btn-success" onclick="simulateLogin()">模拟登录</button>
        <button class="btn btn-info" onclick="goToRealLogin()">真实登录</button>
        <button class="btn btn-warning" onclick="createTestProject()">创建测试项目</button>
        <button class="btn btn-primary" onclick="generateTestOutline()">生成测试大纲</button>
        <button class="btn btn-success" onclick="saveOutlineTest()">保存大纲到数据库</button>
        <button class="btn btn-warning" onclick="loadOutlineTest()">从数据库加载大纲</button>
        <button class="btn btn-danger" onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <div class="test-title">📊 测试状态</div>
        <div id="test-status">
            <div class="status status-info">等待开始测试...</div>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">📝 测试日志</div>
        <div class="log-area" id="log-area">
            等待测试开始...
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-config.js"></script>
    <script src="ai-service.js"></script>
    <script src="collaboration.js"></script>
    <script src="app.js"></script>

    <script>
        let testProject = null;
        let testOutline = null;

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(message);
        }

        // 更新状态
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.innerHTML = `<div class="status status-${type}">${message}</div>`;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('log-area').textContent = '';
            updateStatus('日志已清空', 'info');
        }

        // 初始化系统
        async function initializeSystem() {
            try {
                log('🚀 开始初始化系统...');
                updateStatus('正在初始化系统...', 'warning');
                
                // 等待必要的组件加载
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (typeof supabaseManager !== 'undefined') {
                    log('✅ Supabase管理器已加载');
                } else {
                    throw new Error('Supabase管理器未加载');
                }
                
                if (typeof collaborationManager !== 'undefined') {
                    log('✅ 协作管理器已加载');
                } else {
                    throw new Error('协作管理器未加载');
                }
                
                log('✅ 系统初始化完成');
                updateStatus('系统初始化完成', 'success');
                
            } catch (error) {
                log(`❌ 系统初始化失败: ${error.message}`);
                updateStatus('系统初始化失败', 'error');
            }
        }

        // 模拟登录
        async function simulateLogin() {
            try {
                log('👤 开始模拟登录...');
                updateStatus('正在模拟登录...', 'warning');

                // 检查是否已有真实用户登录
                if (typeof supabaseManager !== 'undefined') {
                    const realUser = await supabaseManager.getCurrentUser();
                    if (realUser) {
                        log('✅ 检测到真实用户已登录: ' + realUser.email);
                        updateStatus('真实用户已登录', 'success');
                        return;
                    }
                }

                // 模拟用户对象
                const mockUser = {
                    id: 'test-user-' + Date.now(),
                    email: '<EMAIL>',
                    user_metadata: {
                        full_name: '测试用户'
                    }
                };

                // 设置模拟用户
                if (typeof supabaseManager !== 'undefined') {
                    supabaseManager.currentUser = mockUser;
                    log('✅ 模拟用户已设置');
                } else {
                    throw new Error('Supabase管理器不可用');
                }

                log('✅ 模拟登录完成');
                updateStatus('模拟登录完成', 'success');

            } catch (error) {
                log(`❌ 模拟登录失败: ${error.message}`);
                updateStatus('模拟登录失败', 'error');
            }
        }

        // 创建测试项目
        async function createTestProject() {
            try {
                log('📁 开始创建测试项目...');
                updateStatus('正在创建测试项目...', 'warning');
                
                testProject = {
                    id: 'test-project-' + Date.now(),
                    title: '测试项目 - 大模型技术与油气应用',
                    description: '用于测试大纲保存功能的项目'
                };
                
                // 设置当前项目
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.currentProjectId = testProject.id;
                    log('✅ 测试项目ID已设置');
                } else {
                    throw new Error('协作管理器不可用');
                }
                
                log(`✅ 测试项目创建完成: ${testProject.title}`);
                updateStatus('测试项目创建完成', 'success');
                
            } catch (error) {
                log(`❌ 创建测试项目失败: ${error.message}`);
                updateStatus('创建测试项目失败', 'error');
            }
        }

        // 生成测试大纲
        function generateTestOutline() {
            try {
                log('📝 开始生成测试大纲...');
                updateStatus('正在生成测试大纲...', 'warning');
                
                testOutline = [
                    {
                        id: 'outline_' + Date.now() + '_1',
                        title: '第一篇 理论基础篇',
                        level: 0,
                        description: '大模型技术的理论基础',
                        children: [
                            {
                                id: 'outline_' + Date.now() + '_2',
                                title: '第1章 大模型概述',
                                level: 1,
                                description: '大模型的基本概念和发展历程',
                                children: []
                            },
                            {
                                id: 'outline_' + Date.now() + '_3',
                                title: '第2章 技术架构',
                                level: 1,
                                description: 'Transformer架构和关键技术',
                                children: []
                            }
                        ]
                    },
                    {
                        id: 'outline_' + Date.now() + '_4',
                        title: '第二篇 应用实践篇',
                        level: 0,
                        description: '大模型在油气行业的应用',
                        children: [
                            {
                                id: 'outline_' + Date.now() + '_5',
                                title: '第3章 油气勘探应用',
                                level: 1,
                                description: '大模型在油气勘探中的应用案例',
                                children: []
                            }
                        ]
                    }
                ];
                
                log('✅ 测试大纲生成完成');
                log(`📊 大纲包含 ${testOutline.length} 个主要部分`);
                updateStatus('测试大纲生成完成', 'success');
                
            } catch (error) {
                log(`❌ 生成测试大纲失败: ${error.message}`);
                updateStatus('生成测试大纲失败', 'error');
            }
        }

        // 测试保存大纲
        async function saveOutlineTest() {
            try {
                log('💾 开始测试保存大纲到数据库...');
                updateStatus('正在保存大纲到数据库...', 'warning');

                if (!testOutline) {
                    throw new Error('请先生成测试大纲');
                }

                // 检查前置条件
                log('🔍 检查保存前置条件...');

                if (typeof collaborationManager === 'undefined') {
                    throw new Error('协作管理器不可用');
                }

                if (!collaborationManager.currentProjectId) {
                    log('⚠️ 没有当前项目ID，这是正常的，因为我们在测试环境');
                }

                if (typeof supabaseManager === 'undefined') {
                    throw new Error('Supabase管理器不可用');
                }

                const user = await supabaseManager.getCurrentUser();
                if (!user) {
                    log('⚠️ 用户未登录，这可能导致保存失败');
                }

                if (typeof safelySaveOutlineToServer === 'function') {
                    log('📤 调用保存函数...');
                    const success = await safelySaveOutlineToServer(testOutline);

                    if (success) {
                        log('✅ 大纲保存到数据库成功');
                        updateStatus('大纲保存成功', 'success');
                    } else {
                        log('⚠️ 大纲保存失败或仅保存到本地');
                        log('💡 这可能是因为：');
                        log('   - 用户未真实登录到Supabase');
                        log('   - 没有有效的项目ID');
                        log('   - 数据库连接问题');
                        updateStatus('大纲保存失败（预期结果）', 'warning');
                    }
                } else {
                    throw new Error('safelySaveOutlineToServer函数不可用');
                }

            } catch (error) {
                log(`❌ 保存大纲测试失败: ${error.message}`);
                updateStatus('保存大纲测试失败', 'error');
            }
        }

        // 测试加载大纲
        async function loadOutlineTest() {
            try {
                log('📥 开始测试从数据库加载大纲...');
                updateStatus('正在从数据库加载大纲...', 'warning');
                
                if (typeof loadOutlineFromServer === 'function') {
                    await loadOutlineFromServer();
                    log('✅ 大纲加载测试完成');
                    
                    // 检查加载的数据
                    if (currentProject && currentProject.outline && currentProject.outline.length > 0) {
                        log(`📊 成功加载 ${currentProject.outline.length} 个大纲项目`);
                        updateStatus('大纲加载成功', 'success');
                    } else {
                        log('⚠️ 未加载到大纲数据');
                        updateStatus('未加载到大纲数据', 'warning');
                    }
                } else {
                    throw new Error('loadOutlineFromServer函数不可用');
                }
                
            } catch (error) {
                log(`❌ 加载大纲测试失败: ${error.message}`);
                updateStatus('加载大纲测试失败', 'error');
            }
        }

        // 跳转到真实登录页面
        function goToRealLogin() {
            log('🔗 跳转到真实登录页面...');
            updateStatus('跳转到登录页面', 'info');
            window.location.href = 'auth.html';
        }

        // 检查真实登录状态
        async function checkRealLoginStatus() {
            try {
                if (typeof supabaseManager !== 'undefined') {
                    const user = await supabaseManager.getCurrentUser();
                    if (user) {
                        log('✅ 检测到真实用户已登录: ' + user.email);
                        updateStatus('真实用户已登录', 'success');
                        return true;
                    } else {
                        log('ℹ️ 未检测到真实用户登录');
                        return false;
                    }
                }
                return false;
            } catch (error) {
                log('❌ 检查登录状态失败: ' + error.message);
                return false;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 测试页面已加载');
            updateStatus('测试页面已准备就绪', 'info');

            // 检查是否有真实用户登录
            setTimeout(async () => {
                await checkRealLoginStatus();
            }, 1000);
        });
    </script>
</body>
</html>
