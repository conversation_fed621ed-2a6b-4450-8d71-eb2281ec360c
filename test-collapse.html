<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右侧工具栏收缩测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
        }
        .test-container {
            display: flex;
            height: 100vh;
        }
        .test-content {
            flex: 1;
            padding: 2rem;
            background: white;
            margin: 1rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            margin: 0.5rem 0;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .status {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 6px;
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0c4a6e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- 测试内容区域 -->
        <div class="test-content">
            <h1>右侧工具栏收缩功能测试</h1>
            
            <div class="status" id="status">
                当前状态：展开状态 (宽度: 350px)
            </div>
            
            <button class="test-button" onclick="toggleToolbar()">
                切换收缩状态
            </button>

            <button class="test-button" onclick="runStyleTest()" style="background: #10b981;">
                运行样式测试 (查看控制台)
            </button>
            
            <h2>测试说明</h2>
            <ul>
                <li>点击"切换收缩状态"按钮测试收缩功能</li>
                <li>收缩后工具栏宽度应为60px</li>
                <li>只显示工具图标，隐藏文字和其他内容</li>
                <li>图标应该居中显示</li>
                <li>切换按钮应该变为向左箭头</li>
            </ul>
            
            <h2>预期效果</h2>
            <ul>
                <li>✅ 收缩状态宽度为60px</li>
                <li>✅ 工具图标正确显示</li>
                <li>✅ 文字内容隐藏</li>
                <li>✅ 布局不溢出</li>
            </ul>
        </div>

        <!-- 右侧工具栏 -->
        <div class="right-toolbar" id="rightToolbar">
            <!-- 工具栏头部 -->
            <div class="right-toolbar-header">
                <div class="toolbar-title">
                    <i class="fas fa-tools toolbar-icon"></i>
                    <span class="toolbar-title-text">工具栏</span>
                </div>
                <button class="right-toggle-btn" id="toggleBtn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- 工具选择器 -->
            <div class="tool-selector">
                <div class="tool-select-wrapper">
                    <select class="tool-select">
                        <option value="">选择工具</option>
                        <option value="pdf">PDF预览</option>
                        <option value="outline">大纲管理</option>
                        <option value="reference">参考资料</option>
                    </select>
                </div>
            </div>

            <!-- 工具信息 -->
            <div class="tool-info">
                选择一个工具开始使用
            </div>

            <!-- iframe容器 -->
            <div class="tool-iframe-container">
                <div style="padding: 2rem; text-align: center; color: #64748b;">
                    <i class="fas fa-tools" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                    <p>工具内容区域</p>
                    <p>这里会显示选中工具的内容</p>
                </div>
            </div>

            <!-- 收缩状态的工具图标 -->
            <div class="collapsed-tools">
                <div class="collapsed-tool-item active" title="PDF预览">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="collapsed-tool-item" title="大纲管理">
                    <i class="fas fa-list-ul"></i>
                </div>
                <div class="collapsed-tool-item" title="参考资料">
                    <i class="fas fa-bookmark"></i>
                </div>
                <div class="collapsed-tool-item" title="AI助手">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="collapsed-tool-item" title="设置">
                    <i class="fas fa-cog"></i>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleToolbar() {
            const toolbar = document.getElementById('rightToolbar');
            const toggleBtn = document.getElementById('toggleBtn');
            const status = document.getElementById('status');
            
            toolbar.classList.toggle('collapsed');
            
            if (toolbar.classList.contains('collapsed')) {
                toggleBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
                status.textContent = '当前状态：收缩状态 (宽度: 60px)';
                status.style.background = '#fef3c7';
                status.style.borderColor = '#f59e0b';
                status.style.color = '#92400e';
            } else {
                toggleBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
                status.textContent = '当前状态：展开状态 (宽度: 350px)';
                status.style.background = '#f0f9ff';
                status.style.borderColor = '#0ea5e9';
                status.style.color = '#0c4a6e';
            }
        }

        // 工具图标点击事件
        document.querySelectorAll('.collapsed-tool-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除其他活动状态
                document.querySelectorAll('.collapsed-tool-item').forEach(i => i.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');
                
                // 显示工具名称
                const toolName = this.getAttribute('title');
                console.log('选中工具:', toolName);
            });
        });

        // 右侧工具栏切换按钮点击事件
        document.getElementById('toggleBtn').addEventListener('click', toggleToolbar);

        // 运行样式测试
        function runStyleTest() {
            console.log('=== 右侧工具栏样式测试 ===');

            const toolbar = document.getElementById('rightToolbar');

            // 测试展开状态
            console.log('\n1. 测试展开状态:');
            toolbar.classList.remove('collapsed');
            const expandedStyle = window.getComputedStyle(toolbar);
            console.log('展开宽度:', expandedStyle.width);

            // 测试收缩状态
            console.log('\n2. 测试收缩状态:');
            toolbar.classList.add('collapsed');
            const collapsedStyle = window.getComputedStyle(toolbar);
            console.log('收缩宽度:', collapsedStyle.width);
            console.log('overflow:', collapsedStyle.overflow);

            // 测试工具图标
            const toolItems = toolbar.querySelectorAll('.collapsed-tool-item');
            if (toolItems.length > 0) {
                const iconStyle = window.getComputedStyle(toolItems[0]);
                console.log('工具图标尺寸:', iconStyle.width, 'x', iconStyle.height);
            }

            // 恢复原状态
            toolbar.classList.remove('collapsed');

            console.log('\n=== 测试完成 ===');
        }

        // 页面加载完成后运行测试
        setTimeout(runStyleTest, 1000);
    </script>
</body>
</html>
