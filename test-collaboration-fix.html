<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协作功能修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #374151;
            margin-bottom: 15px;
        }
        
        .test-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        
        .test-button:hover {
            background: #4338ca;
        }
        
        .test-button.danger {
            background: #dc2626;
        }
        
        .test-button.danger:hover {
            background: #b91c1c;
        }
        
        .status-message {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .status-info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .empty-state h3 {
            margin: 0 0 8px 0;
            color: #374151;
        }
        
        .empty-state p {
            margin: 0;
            color: #6b7280;
        }
        
        .mock-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        
        .mock-tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            color: #6b7280;
            font-weight: 500;
        }
        
        .mock-tab.active {
            color: #4f46e5;
            border-bottom: 2px solid #4f46e5;
        }
        
        .mock-content {
            min-height: 200px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        #team-members-list,
        #chapter-assignments-list,
        #permissions-matrix {
            min-height: 150px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 协作功能修复测试</h1>
            <p>测试修复后的协作功能是否正常工作</p>
        </div>
        
        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <ol>
                <li>点击下面的标签页测试各个功能</li>
                <li>在没有选择项目时，应该显示友好的提示信息</li>
                <li>不应该出现 JavaScript 错误</li>
                <li>所有功能都应该有适当的错误处理</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🎯 模拟协作标签页</h3>
            
            <div class="mock-tabs">
                <button class="mock-tab active" data-tab="members">团队成员</button>
                <button class="mock-tab" data-tab="assignments">章节分配</button>
                <button class="mock-tab" data-tab="permissions">权限设置</button>
            </div>
            
            <div class="mock-content">
                <div id="team-members-list">
                    <div class="empty-state">
                        <div class="empty-icon">👥</div>
                        <h3>请先选择项目</h3>
                        <p>选择一个项目后即可查看团队成员</p>
                    </div>
                </div>
                
                <div id="chapter-assignments-list" style="display: none;">
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <h3>请先选择项目</h3>
                        <p>选择一个项目后即可查看章节分配</p>
                    </div>
                </div>
                
                <div id="permissions-matrix" style="display: none;">
                    <div class="empty-state">
                        <div class="empty-icon">🔐</div>
                        <h3>请先选择项目</h3>
                        <p>选择一个项目后即可查看权限设置</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 功能测试</h3>
            <button class="test-button" onclick="testLoadTeamMembers()">测试加载团队成员</button>
            <button class="test-button" onclick="testLoadChapterAssignments()">测试加载章节分配</button>
            <button class="test-button" onclick="testLoadPermissionsMatrix()">测试加载权限矩阵</button>
            <button class="test-button danger" onclick="testWithoutProject()">测试无项目状态</button>
        </div>
        
        <div id="test-results"></div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script src="collaboration.js"></script>
    <script>
        // 模拟标签页切换
        document.querySelectorAll('.mock-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                // 移除所有活动状态
                document.querySelectorAll('.mock-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('[id$="-list"], #permissions-matrix').forEach(content => {
                    content.style.display = 'none';
                });
                
                // 激活当前标签
                e.target.classList.add('active');
                const tabName = e.target.dataset.tab;
                
                // 显示对应内容
                if (tabName === 'members') {
                    document.getElementById('team-members-list').style.display = 'block';
                } else if (tabName === 'assignments') {
                    document.getElementById('chapter-assignments-list').style.display = 'block';
                } else if (tabName === 'permissions') {
                    document.getElementById('permissions-matrix').style.display = 'block';
                }
                
                // 测试协作管理器功能
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.switchCollabTab(tabName);
                }
            });
        });
        
        function showTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-message status-${type}`;
            messageDiv.textContent = message;
            resultsDiv.appendChild(messageDiv);
            
            // 自动清除旧消息
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }
        
        function testLoadTeamMembers() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.loadTeamMembers();
                    showTestResult('✅ 团队成员加载测试完成 - 无错误', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 团队成员加载测试失败: ${error.message}`, 'error');
            }
        }
        
        function testLoadChapterAssignments() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.loadChapterAssignments();
                    showTestResult('✅ 章节分配加载测试完成 - 无错误', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 章节分配加载测试失败: ${error.message}`, 'error');
            }
        }
        
        function testLoadPermissionsMatrix() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.loadPermissionsMatrix();
                    showTestResult('✅ 权限矩阵加载测试完成 - 无错误', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 权限矩阵加载测试失败: ${error.message}`, 'error');
            }
        }
        
        function testWithoutProject() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    // 确保没有项目
                    collaborationManager.currentProject = null;
                    
                    // 测试所有功能
                    collaborationManager.loadTeamMembers();
                    collaborationManager.loadChapterAssignments();
                    collaborationManager.loadPermissionsMatrix();
                    
                    showTestResult('✅ 无项目状态测试完成 - 所有功能都有适当的错误处理', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 无项目状态测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            showTestResult('🚀 协作功能修复测试页面已加载', 'info');
            
            // 等待协作管理器初始化
            setTimeout(() => {
                if (typeof collaborationManager !== 'undefined') {
                    showTestResult('✅ 协作管理器已初始化', 'success');
                } else {
                    showTestResult('⚠️ 协作管理器未找到，某些测试可能无法进行', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>
