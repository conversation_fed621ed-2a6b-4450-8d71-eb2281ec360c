<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文导出功能测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4f46e5;
            padding-bottom: 15px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #4f46e5;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            font-family: inherit;
        }
        .btn:hover {
            background: #4338ca;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .chinese-sample {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-size: 14px;
        }
        .chinese-sample h4 {
            margin-top: 0;
            color: #0369a1;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #10b981;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇨🇳 中文导出功能测试</h1>
        
        <div class="test-section">
            <h2>🔧 修复说明</h2>
            <div class="chinese-sample">
                <h4>PDF导出修复：</h4>
                <ul class="feature-list">
                    <li>使用Canvas渲染中文文本</li>
                    <li>支持Microsoft YaHei、SimHei等中文字体</li>
                    <li>自动换行和分页处理</li>
                    <li>图像化输出确保中文正确显示</li>
                </ul>
                
                <h4>DOCX导出修复：</h4>
                <ul class="feature-list">
                    <li>使用HTML格式作为备用方案</li>
                    <li>UTF-8编码确保中文兼容</li>
                    <li>可被浏览器和Word正常打开</li>
                    <li>保持文档结构和格式</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 中文测试数据</h2>
            <div class="chinese-sample">
                <h4>测试项目：大模型技术与油气应用概论</h4>
                <p><strong>项目描述：</strong>本书系统性地探讨了大模型技术在油气行业的应用前景、技术挑战和发展趋势，涵盖了从基础理论到实际应用的全方位内容。</p>
                <p><strong>章节示例：</strong></p>
                <ul>
                    <li>第一章：人工智能与大模型技术基础</li>
                    <li>第二章：油气勘探中的智能化应用</li>
                    <li>第三章：生产优化与智能决策系统</li>
                    <li>第四章：安全监控与风险预警机制</li>
                </ul>
                <p><strong>特殊字符测试：</strong>①②③④⑤、甲乙丙丁戊、αβγδε、℃℉°、±×÷≈≠</p>
            </div>
            <button class="btn" onclick="generateChineseTestData()">生成中文测试数据</button>
            <div id="data-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📄 PDF中文导出测试</h2>
            <p>使用Canvas渲染技术，确保中文字符正确显示</p>
            <button class="btn" onclick="testChinesePDFExport()" id="pdf-btn">测试PDF中文导出</button>
            <div id="pdf-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📝 DOCX中文导出测试</h2>
            <p>使用HTML格式备用方案，支持中文字符和格式</p>
            <button class="btn" onclick="testChineseDOCXExport()" id="docx-btn">测试DOCX中文导出</button>
            <div id="docx-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📋 测试日志</h2>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script src="export-service.js"></script>
    <script>
        let chineseTestData = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logEntry);
        }

        function showStatus(elementId, message, type) {
            const statusDiv = document.getElementById(elementId);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        function generateChineseTestData() {
            log('生成中文测试数据...');
            
            chineseTestData = {
                project: {
                    id: 'chinese-test-001',
                    title: '大模型技术与油气应用概论',
                    description: '本书系统性地探讨了大模型技术在油气行业的应用前景、技术挑战和发展趋势，涵盖了从基础理论到实际应用的全方位内容，为相关领域的研究人员和工程技术人员提供了宝贵的参考资料。',
                    created_at: new Date().toISOString(),
                    status: 'active'
                },
                outlines: [
                    {
                        id: 'outline-1',
                        title: '第一章 人工智能与大模型技术基础',
                        level: 1,
                        sort_order: 1,
                        children: [
                            {
                                id: 'outline-1-1',
                                title: '1.1 人工智能发展历程回顾',
                                level: 2,
                                sort_order: 1,
                                children: []
                            },
                            {
                                id: 'outline-1-2',
                                title: '1.2 大模型技术原理与架构',
                                level: 2,
                                sort_order: 2,
                                children: []
                            }
                        ]
                    },
                    {
                        id: 'outline-2',
                        title: '第二章 油气勘探中的智能化应用',
                        level: 1,
                        sort_order: 2,
                        children: [
                            {
                                id: 'outline-2-1',
                                title: '2.1 地震数据智能解释技术',
                                level: 2,
                                sort_order: 1,
                                children: []
                            },
                            {
                                id: 'outline-2-2',
                                title: '2.2 储层预测与评价方法',
                                level: 2,
                                sort_order: 2,
                                children: []
                            }
                        ]
                    }
                ],
                chapters: [
                    {
                        id: 'chapter-1',
                        title: '第一章 人工智能与大模型技术基础',
                        summary: '本章详细介绍了人工智能的发展历程，从早期的专家系统到现代的深度学习和大模型技术。重点阐述了Transformer架构、注意力机制、预训练模型等核心概念，为后续章节的应用讨论奠定了坚实的理论基础。',
                        outline_id: 'outline-1',
                        project_id: 'chinese-test-001'
                    },
                    {
                        id: 'chapter-2',
                        title: '第二章 油气勘探中的智能化应用',
                        summary: '本章深入分析了大模型技术在油气勘探各个环节的具体应用场景，包括地震数据处理、地质建模、储层预测等关键技术。通过实际案例展示了智能化技术如何提高勘探效率、降低成本并减少风险。',
                        outline_id: 'outline-2',
                        project_id: 'chinese-test-001'
                    }
                ],
                references: [
                    {
                        id: 'ref-1',
                        title: 'Attention Is All You Need',
                        authors: 'Vaswani等',
                        year: 2017,
                        type: 'paper'
                    },
                    {
                        id: 'ref-2',
                        title: '深度学习在石油勘探中的应用研究',
                        authors: '张三，李四',
                        year: 2023,
                        type: 'journal'
                    }
                ],
                members: [],
                exportDate: new Date().toISOString(),
                exportVersion: '2.0'
            };

            log('✅ 中文测试数据生成完成');
            showStatus('data-status', '中文测试数据已生成，包含复杂中文内容', 'success');
        }

        async function testChinesePDFExport() {
            if (!chineseTestData) {
                showStatus('pdf-status', '请先生成中文测试数据', 'error');
                return;
            }

            const btn = document.getElementById('pdf-btn');
            btn.disabled = true;
            btn.textContent = '正在生成PDF...';

            log('开始测试PDF中文导出...');
            showStatus('pdf-status', '正在使用Canvas技术生成PDF...', 'info');

            try {
                const result = await exportService.exportToPDF(chineseTestData);
                log(`✅ PDF中文导出成功: ${JSON.stringify(result)}`);
                showStatus('pdf-status', 'PDF中文导出成功！请检查下载的文件中文显示是否正常', 'success');
            } catch (error) {
                log(`❌ PDF中文导出失败: ${error.message}`, 'error');
                showStatus('pdf-status', `PDF导出失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试PDF中文导出';
            }
        }

        async function testChineseDOCXExport() {
            if (!chineseTestData) {
                showStatus('docx-status', '请先生成中文测试数据', 'error');
                return;
            }

            const btn = document.getElementById('docx-btn');
            btn.disabled = true;
            btn.textContent = '正在生成DOCX...';

            log('开始测试DOCX中文导出...');
            showStatus('docx-status', '正在生成支持中文的文档...', 'info');

            try {
                const result = await exportService.exportToDOCX(chineseTestData);
                log(`✅ DOCX中文导出成功: ${JSON.stringify(result)}`);
                const message = result.note ? 
                    `导出成功！${result.note}` : 
                    'DOCX中文导出成功！请检查下载的文件中文显示是否正常';
                showStatus('docx-status', message, 'success');
            } catch (error) {
                log(`❌ DOCX中文导出失败: ${error.message}`, 'error');
                showStatus('docx-status', `DOCX导出失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试DOCX中文导出';
            }
        }

        // 页面加载完成后自动生成测试数据
        window.addEventListener('load', () => {
            log('中文导出测试页面加载完成');
            setTimeout(() => {
                if (typeof exportService !== 'undefined') {
                    log('✅ 导出服务已加载');
                    generateChineseTestData();
                } else {
                    log('❌ 导出服务未加载');
                }
            }, 500);
        });
    </script>
</body>
</html>
