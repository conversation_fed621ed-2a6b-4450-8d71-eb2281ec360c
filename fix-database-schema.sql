-- 修复数据库结构问题
-- 解决章节分配表与章节表之间的关系问题

-- 1. 首先检查现有表结构
-- 查看 chapters 表结构
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'chapters' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 查看 chapter_assignments 表结构
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'chapter_assignments' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 查看外键约束
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name IN ('chapter_assignments', 'chapters')
  AND tc.table_schema = 'public';

-- 2. 删除可能存在的错误外键约束
DO $$ 
DECLARE
    constraint_name text;
BEGIN
    -- 查找并删除 chapter_assignments 表中可能存在的错误外键
    FOR constraint_name IN 
        SELECT tc.constraint_name
        FROM information_schema.table_constraints AS tc
        WHERE tc.table_name = 'chapter_assignments' 
          AND tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_schema = 'public'
    LOOP
        EXECUTE 'ALTER TABLE public.chapter_assignments DROP CONSTRAINT IF EXISTS ' || constraint_name;
    END LOOP;
END $$;

-- 3. 确保 chapters 表存在且结构正确
CREATE TABLE IF NOT EXISTS public.chapters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    outline_id UUID,
    title VARCHAR(300) NOT NULL,
    summary TEXT,
    content JSONB DEFAULT '{"ops":[]}',
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'writing', 'review', 'approved', 'published')),
    word_count INTEGER DEFAULT 0,
    order_index INTEGER DEFAULT 0,
    created_by UUID,
    updated_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 重新创建 chapter_assignments 表，确保结构正确
DROP TABLE IF EXISTS public.chapter_assignments CASCADE;

CREATE TABLE public.chapter_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chapter_id UUID NOT NULL,
    project_id UUID NOT NULL,
    
    -- 角色分配
    lead_author_id UUID,
    reviewer_id UUID,
    assigned_by UUID,
    
    -- 任务信息
    title TEXT,
    description TEXT,
    requirements TEXT,
    word_count_target INTEGER DEFAULT 0,
    
    -- 状态管理
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN (
        'pending',     -- 待分配
        'assigned',    -- 已分配
        'accepted',    -- 已接受
        'writing',     -- 编制中
        'reviewing',   -- 审核中
        'completed',   -- 已完成
        'rejected'     -- 已拒绝
    )),
    
    -- 时间管理
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    due_date TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    submitted_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- 优先级
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建协作者关联表
CREATE TABLE IF NOT EXISTS public.chapter_collaborators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL,
    user_id UUID NOT NULL,
    role VARCHAR(50) DEFAULT 'collaborator' CHECK (role IN ('collaborator', 'reviewer', 'assistant')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(assignment_id, user_id)
);

-- 6. 添加外键约束
ALTER TABLE public.chapter_assignments 
ADD CONSTRAINT fk_chapter_assignments_chapter 
FOREIGN KEY (chapter_id) REFERENCES public.chapters(id) ON DELETE CASCADE;

ALTER TABLE public.chapter_assignments 
ADD CONSTRAINT fk_chapter_assignments_project 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

ALTER TABLE public.chapter_collaborators 
ADD CONSTRAINT fk_chapter_collaborators_assignment 
FOREIGN KEY (assignment_id) REFERENCES public.chapter_assignments(id) ON DELETE CASCADE;

-- 7. 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_chapter_id ON public.chapter_assignments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_project_id ON public.chapter_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_lead_author ON public.chapter_assignments(lead_author_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_status ON public.chapter_assignments(status);
CREATE INDEX IF NOT EXISTS idx_chapter_collaborators_assignment ON public.chapter_collaborators(assignment_id);
CREATE INDEX IF NOT EXISTS idx_chapter_collaborators_user ON public.chapter_collaborators(user_id);

-- 8. 启用行级安全策略
ALTER TABLE public.chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_collaborators ENABLE ROW LEVEL SECURITY;

-- 9. 创建基本的RLS策略
-- 用户可以查看自己参与的章节分配
CREATE POLICY "Users can view their assignments" ON public.chapter_assignments
    FOR SELECT USING (
        lead_author_id = auth.uid() OR
        reviewer_id = auth.uid() OR
        assigned_by = auth.uid() OR
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid()
        )
    );

-- 用户可以更新自己的分配
CREATE POLICY "Users can update their assignments" ON public.chapter_assignments
    FOR UPDATE USING (
        lead_author_id = auth.uid() OR
        assigned_by = auth.uid() OR
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'editor')
        )
    );

-- 项目管理员可以创建分配
CREATE POLICY "Project admins can create assignments" ON public.chapter_assignments
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'editor')
        )
    );

-- 协作者策略
CREATE POLICY "Users can view collaborations" ON public.chapter_collaborators
    FOR SELECT USING (
        user_id = auth.uid() OR
        assignment_id IN (
            SELECT id FROM public.chapter_assignments 
            WHERE lead_author_id = auth.uid() OR assigned_by = auth.uid()
        )
    );

-- 10. 创建更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_chapter_assignments_updated_at 
    BEFORE UPDATE ON public.chapter_assignments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 完成提示
SELECT 'Database schema fix completed successfully!' as status;
