# 数据库修复指南

## 🚨 问题描述

用户管理页面出现错误：`column user_profiles_1.last_login_at does not exist`

这是因为数据库中缺少多用户协作功能所需的字段。

## 🔧 解决方案

### 方案1：使用数据库检查工具（推荐）

1. **打开数据库检查页面**
   ```
   访问：database-check.html
   ```

2. **运行自动检查**
   - 页面会自动检查数据库结构
   - 显示缺失的表和字段
   - 提供修复建议

### 方案2：手动执行SQL脚本

1. **登录Supabase控制台**
   - 访问：https://supabase.com/dashboard
   - 选择您的项目

2. **打开SQL编辑器**
   - 在左侧菜单选择 "SQL Editor"
   - 点击 "New query"

3. **执行修复脚本**
   - 复制 `database-fix.sql` 文件的内容
   - 粘贴到SQL编辑器中
   - 点击 "Run" 执行

### 方案3：最小化修复（临时解决）

如果您只想快速修复用户管理页面，可以只执行以下SQL：

```sql
-- 添加缺失的字段
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' 
CHECK (status IN ('active', 'inactive', 'pending', 'invited'));
```

## 📋 验证修复

修复完成后：

1. **刷新用户管理页面**
   - 访问：user-management.html
   - 检查是否正常加载

2. **运行数据库检查**
   - 访问：database-check.html
   - 确认所有检查项都通过

3. **测试功能**
   - 尝试邀请用户
   - 测试角色分配
   - 验证权限控制

## 🔍 常见问题

### Q: 为什么会出现这个问题？
A: 这是因为多用户协作功能需要额外的数据库字段，而现有数据库可能没有这些字段。

### Q: 修复会影响现有数据吗？
A: 不会。修复脚本使用 `ADD COLUMN IF NOT EXISTS`，只会添加缺失的字段，不会影响现有数据。

### Q: 修复后还是有问题怎么办？
A: 
1. 检查浏览器控制台的错误信息
2. 运行 `database-check.html` 进行全面检查
3. 确认Supabase项目的权限设置

## 📞 技术支持

如果问题仍然存在：

1. **检查错误日志**
   - 打开浏览器开发者工具（F12）
   - 查看Console标签的错误信息

2. **验证数据库连接**
   - 确认Supabase配置正确
   - 检查网络连接

3. **联系支持**
   - 提供具体的错误信息
   - 说明执行的修复步骤

## 🎯 预防措施

为避免类似问题：

1. **定期备份数据库**
2. **在测试环境先验证修改**
3. **保持数据库结构文档更新**
4. **使用版本控制管理数据库变更**

---

**修复完成后，您就可以正常使用多用户协作功能了！** 🎉
