<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #4f46e5;
            margin-top: 0;
        }
        .btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #4338ca;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .sample-data {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 导出功能测试</h1>
        
        <div class="test-section">
            <h2>🔧 测试环境检查</h2>
            <button class="btn" onclick="checkEnvironment()">检查环境</button>
            <div id="env-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📊 测试数据</h2>
            <div class="sample-data">
                <strong>模拟项目数据：</strong>
                <ul>
                    <li>项目标题：大模型技术与油气应用概论</li>
                    <li>项目描述：探讨大模型在油气行业的应用前景</li>
                    <li>章节数量：3个</li>
                    <li>大纲层级：2级</li>
                </ul>
            </div>
            <button class="btn" onclick="generateSampleData()">生成测试数据</button>
            <div id="data-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📄 PDF导出测试（中文支持）</h2>
            <p>测试PDF导出功能，验证中文字符是否正确显示</p>
            <button class="btn" onclick="testPDFExport()" id="pdf-btn">测试PDF导出</button>
            <div id="pdf-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📝 DOCX导出测试（备用方案）</h2>
            <p>测试DOCX导出功能，包含库加载失败时的备用RTF方案</p>
            <button class="btn" onclick="testDOCXExport()" id="docx-btn">测试DOCX导出</button>
            <div id="docx-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>💾 JSON导出测试</h2>
            <button class="btn" onclick="testJSONExport()" id="json-btn">测试JSON导出</button>
            <div id="json-status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📋 测试日志</h2>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script src="export-service.js"></script>
    <script>
        let sampleProjectData = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logEntry);
        }

        function showStatus(elementId, message, type) {
            const statusDiv = document.getElementById(elementId);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        async function checkEnvironment() {
            log('开始检查测试环境...');
            
            try {
                // 检查导出服务
                if (typeof exportService === 'undefined') {
                    throw new Error('导出服务未加载');
                }
                log('✅ 导出服务已加载');

                // 检查支持的格式
                const formats = exportService.supportedFormats;
                log(`✅ 支持的导出格式: ${formats.join(', ')}`);

                showStatus('env-status', '环境检查通过', 'success');
                log('环境检查完成');
            } catch (error) {
                log(`❌ 环境检查失败: ${error.message}`, 'error');
                showStatus('env-status', `环境检查失败: ${error.message}`, 'error');
            }
        }

        function generateSampleData() {
            log('生成测试数据...');
            
            sampleProjectData = {
                project: {
                    id: 'test-project-001',
                    title: '大模型技术与油气应用概论',
                    description: '本书系统性地探讨了大模型技术在油气行业的应用前景、技术挑战和发展趋势。',
                    created_at: new Date().toISOString(),
                    status: 'active'
                },
                outlines: [
                    {
                        id: 'outline-1',
                        title: '第一章 大模型技术基础',
                        level: 1,
                        sort_order: 1,
                        children: [
                            {
                                id: 'outline-1-1',
                                title: '1.1 人工智能发展历程',
                                level: 2,
                                sort_order: 1,
                                children: []
                            },
                            {
                                id: 'outline-1-2',
                                title: '1.2 大模型技术原理',
                                level: 2,
                                sort_order: 2,
                                children: []
                            }
                        ]
                    },
                    {
                        id: 'outline-2',
                        title: '第二章 油气行业应用场景',
                        level: 1,
                        sort_order: 2,
                        children: [
                            {
                                id: 'outline-2-1',
                                title: '2.1 勘探开发应用',
                                level: 2,
                                sort_order: 1,
                                children: []
                            }
                        ]
                    }
                ],
                chapters: [
                    {
                        id: 'chapter-1',
                        title: '第一章 大模型技术基础',
                        summary: '本章介绍了大模型技术的基本概念、发展历程和核心原理，为后续章节的应用讨论奠定理论基础。',
                        outline_id: 'outline-1',
                        project_id: 'test-project-001'
                    },
                    {
                        id: 'chapter-2',
                        title: '第二章 油气行业应用场景',
                        summary: '本章详细分析了大模型技术在油气勘探、开发、生产等各个环节的具体应用场景和技术方案。',
                        outline_id: 'outline-2',
                        project_id: 'test-project-001'
                    }
                ],
                references: [
                    {
                        id: 'ref-1',
                        title: 'Attention Is All You Need',
                        authors: 'Vaswani et al.',
                        year: 2017,
                        type: 'paper'
                    }
                ],
                members: [],
                exportDate: new Date().toISOString(),
                exportVersion: '1.0'
            };

            log('✅ 测试数据生成完成');
            showStatus('data-status', '测试数据已生成', 'success');
        }

        async function testPDFExport() {
            if (!sampleProjectData) {
                showStatus('pdf-status', '请先生成测试数据', 'error');
                return;
            }

            const btn = document.getElementById('pdf-btn');
            btn.disabled = true;
            btn.textContent = '正在测试...';

            log('开始测试PDF导出...');
            showStatus('pdf-status', '正在测试PDF导出...', 'info');

            try {
                const result = await exportService.exportToPDF(sampleProjectData);
                log(`✅ PDF导出成功: ${JSON.stringify(result)}`);
                showStatus('pdf-status', 'PDF导出测试通过', 'success');
            } catch (error) {
                log(`❌ PDF导出失败: ${error.message}`, 'error');
                showStatus('pdf-status', `PDF导出失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试PDF导出';
            }
        }

        async function testDOCXExport() {
            if (!sampleProjectData) {
                showStatus('docx-status', '请先生成测试数据', 'error');
                return;
            }

            const btn = document.getElementById('docx-btn');
            btn.disabled = true;
            btn.textContent = '正在测试...';

            log('开始测试DOCX导出...');
            showStatus('docx-status', '正在测试DOCX导出...', 'info');

            try {
                const result = await exportService.exportToDOCX(sampleProjectData);
                log(`✅ DOCX导出成功: ${JSON.stringify(result)}`);
                showStatus('docx-status', 'DOCX导出测试通过', 'success');
            } catch (error) {
                log(`❌ DOCX导出失败: ${error.message}`, 'error');
                showStatus('docx-status', `DOCX导出失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试DOCX导出';
            }
        }

        async function testJSONExport() {
            if (!sampleProjectData) {
                showStatus('json-status', '请先生成测试数据', 'error');
                return;
            }

            const btn = document.getElementById('json-btn');
            btn.disabled = true;
            btn.textContent = '正在测试...';

            log('开始测试JSON导出...');
            showStatus('json-status', '正在测试JSON导出...', 'info');

            try {
                const result = await exportService.exportToJSON(sampleProjectData);
                log(`✅ JSON导出成功: ${JSON.stringify(result)}`);
                showStatus('json-status', 'JSON导出测试通过', 'success');
            } catch (error) {
                log(`❌ JSON导出失败: ${error.message}`, 'error');
                showStatus('json-status', `JSON导出失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '测试JSON导出';
            }
        }

        // 页面加载完成后自动检查环境
        window.addEventListener('load', () => {
            log('测试页面加载完成');
            setTimeout(checkEnvironment, 500);
        });
    </script>
</body>
</html>
