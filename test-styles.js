// 测试右侧工具栏收缩功能的脚本

function testCollapsedStyles() {
    console.log('开始测试右侧工具栏收缩样式...');
    
    // 检查收缩状态的CSS规则
    const testResults = {
        collapsedWidth: false,
        collapsedPadding: false,
        toolIconSize: false,
        toolIconAlignment: false,
        overflow: false
    };
    
    // 创建测试元素
    const testToolbar = document.createElement('div');
    testToolbar.className = 'right-toolbar collapsed';
    testToolbar.style.position = 'absolute';
    testToolbar.style.top = '-9999px';
    document.body.appendChild(testToolbar);
    
    // 测试收缩宽度
    const computedStyle = window.getComputedStyle(testToolbar);
    if (computedStyle.width === '60px') {
        testResults.collapsedWidth = true;
        console.log('✅ 收缩宽度正确: 60px');
    } else {
        console.log('❌ 收缩宽度错误:', computedStyle.width);
    }
    
    // 测试overflow
    if (computedStyle.overflow === 'hidden') {
        testResults.overflow = true;
        console.log('✅ overflow设置正确: hidden');
    } else {
        console.log('❌ overflow设置错误:', computedStyle.overflow);
    }
    
    // 创建工具图标测试
    const testTools = document.createElement('div');
    testTools.className = 'collapsed-tools';
    testToolbar.appendChild(testTools);
    
    const testIcon = document.createElement('div');
    testIcon.className = 'collapsed-tool-item';
    testTools.appendChild(testIcon);
    
    const iconStyle = window.getComputedStyle(testIcon);
    if (iconStyle.width === '48px' && iconStyle.height === '48px') {
        testResults.toolIconSize = true;
        console.log('✅ 工具图标尺寸正确: 48px x 48px');
    } else {
        console.log('❌ 工具图标尺寸错误:', iconStyle.width, 'x', iconStyle.height);
    }
    
    // 清理测试元素
    document.body.removeChild(testToolbar);
    
    // 输出测试结果
    const passedTests = Object.values(testResults).filter(result => result).length;
    const totalTests = Object.keys(testResults).length;
    
    console.log(`\n测试完成: ${passedTests}/${totalTests} 项通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！右侧工具栏收缩功能正常。');
    } else {
        console.log('⚠️ 部分测试失败，需要进一步调整。');
    }
    
    return testResults;
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', testCollapsedStyles);
    } else {
        testCollapsedStyles();
    }
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testCollapsedStyles };
}
