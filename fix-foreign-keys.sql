-- 修复外键关系脚本
-- 为 user_invitations 表添加缺失的外键约束

-- 1. 首先检查表结构
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'user_invitations' 
AND table_schema = 'public';

-- 2. 添加外键约束（如果不存在）
DO $$ 
BEGIN
    -- 检查并添加 invited_by 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_invitations_invited_by_fkey'
        AND table_name = 'user_invitations'
    ) THEN
        ALTER TABLE public.user_invitations 
        ADD CONSTRAINT user_invitations_invited_by_fkey 
        FOREIGN KEY (invited_by) REFERENCES public.user_profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key constraint: user_invitations_invited_by_fkey';
    ELSE
        RAISE NOTICE 'Foreign key constraint user_invitations_invited_by_fkey already exists';
    END IF;

    -- 检查并添加 project_id 外键
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'user_invitations_project_id_fkey'
        AND table_name = 'user_invitations'
    ) THEN
        ALTER TABLE public.user_invitations 
        ADD CONSTRAINT user_invitations_project_id_fkey 
        FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added foreign key constraint: user_invitations_project_id_fkey';
    ELSE
        RAISE NOTICE 'Foreign key constraint user_invitations_project_id_fkey already exists';
    END IF;
END $$;

-- 3. 验证外键关系
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = 'user_invitations'
    AND tc.table_schema = 'public';

-- 完成提示
SELECT 'Foreign key constraints fixed successfully!' as result;
