<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节保存加载实时监控</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #fff;
        }
        .monitor-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .monitor-panel {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #444;
        }
        .monitor-panel h3 {
            margin: 0 0 15px 0;
            color: #4CAF50;
            border-bottom: 1px solid #444;
            padding-bottom: 10px;
        }
        .log-container {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 3px 0;
            border-bottom: 1px solid #333;
        }
        .log-entry.info { color: #81C784; }
        .log-entry.warn { color: #FFB74D; }
        .log-entry.error { color: #E57373; }
        .log-entry.success { color: #4CAF50; }
        .log-entry.debug { color: #64B5F6; }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn.secondary {
            background: #2196F3;
        }
        .btn.secondary:hover {
            background: #1976D2;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.danger:hover {
            background: #d32f2f;
        }
        .status-bar {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #666;
        }
        .status-indicator.active {
            background: #4CAF50;
            animation: pulse 2s infinite;
        }
        .status-indicator.error {
            background: #f44336;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .data-preview {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 章节保存加载实时监控</h1>
            <p>实时监控章节内容的保存和加载过程</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="connection-status"></div>
                <span>连接状态: <span id="connection-text">未连接</span></span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="save-status"></div>
                <span>保存状态: <span id="save-text">待机</span></span>
            </div>
            <div class="status-item">
                <div class="status-indicator" id="load-status"></div>
                <span>加载状态: <span id="load-text">待机</span></span>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="connectToMainApp()">连接主应用</button>
            <button class="btn secondary" onclick="triggerSave()">触发保存</button>
            <button class="btn secondary" onclick="triggerLoad()">触发加载</button>
            <button class="btn danger" onclick="clearLogs()">清除日志</button>
        </div>

        <div class="monitor-grid">
            <div class="monitor-panel">
                <h3>💾 保存过程监控</h3>
                <div id="save-log" class="log-container"></div>
                <div class="data-preview" id="save-data-preview">
                    等待保存数据...
                </div>
            </div>

            <div class="monitor-panel">
                <h3>📥 加载过程监控</h3>
                <div id="load-log" class="log-container"></div>
                <div class="data-preview" id="load-data-preview">
                    等待加载数据...
                </div>
            </div>
        </div>

        <div class="monitor-panel">
            <h3>🔍 综合日志</h3>
            <div id="general-log" class="log-container"></div>
        </div>
    </div>

    <script>
        let mainAppWindow = null;
        let originalConsole = {};
        let logs = {
            save: [],
            load: [],
            general: []
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('general', 'info', '监控系统已启动');
            updateStatus('connection', 'error', '未连接');
            updateStatus('save', 'inactive', '待机');
            updateStatus('load', 'inactive', '待机');
        });

        // 添加日志
        function addLog(type, level, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                level,
                message,
                data
            };

            logs[type].push(logEntry);
            updateLogDisplay(type);

            // 同时添加到综合日志
            if (type !== 'general') {
                logs.general.push({ ...logEntry, source: type });
                updateLogDisplay('general');
            }
        }

        // 更新日志显示
        function updateLogDisplay(type) {
            const container = document.getElementById(`${type}-log`);
            const logList = logs[type];
            
            container.innerHTML = logList.slice(-50).map(entry => {
                const sourceText = entry.source ? `[${entry.source.toUpperCase()}] ` : '';
                return `<div class="log-entry ${entry.level}">
                    [${entry.timestamp}] ${sourceText}${entry.message}
                    ${entry.data ? `\n${JSON.stringify(entry.data, null, 2)}` : ''}
                </div>`;
            }).join('');
            
            container.scrollTop = container.scrollHeight;
        }

        // 更新状态
        function updateStatus(type, status, text) {
            const indicator = document.getElementById(`${type}-status`);
            const textElement = document.getElementById(`${type}-text`);
            
            indicator.className = 'status-indicator';
            if (status === 'active') {
                indicator.classList.add('active');
            } else if (status === 'error') {
                indicator.classList.add('error');
            }
            
            textElement.textContent = text;
        }

        // 连接到主应用
        function connectToMainApp() {
            try {
                // 尝试打开主应用
                mainAppWindow = window.open('./index.html', 'mainApp');
                
                if (mainAppWindow) {
                    addLog('general', 'info', '正在连接主应用...');
                    updateStatus('connection', 'active', '连接中');
                    
                    // 等待主应用加载完成
                    setTimeout(() => {
                        if (mainAppWindow && !mainAppWindow.closed) {
                            setupMonitoring();
                            updateStatus('connection', 'active', '已连接');
                            addLog('general', 'success', '主应用连接成功');
                        } else {
                            updateStatus('connection', 'error', '连接失败');
                            addLog('general', 'error', '主应用连接失败');
                        }
                    }, 3000);
                } else {
                    throw new Error('无法打开主应用窗口');
                }
            } catch (error) {
                addLog('general', 'error', '连接主应用失败', { error: error.message });
                updateStatus('connection', 'error', '连接失败');
            }
        }

        // 设置监控
        function setupMonitoring() {
            if (!mainAppWindow || mainAppWindow.closed) {
                addLog('general', 'error', '主应用窗口不可用');
                return;
            }

            try {
                // 劫持主应用的console方法
                const mainConsole = mainAppWindow.console;
                originalConsole = {
                    log: mainConsole.log,
                    warn: mainConsole.warn,
                    error: mainConsole.error,
                    info: mainConsole.info
                };

                // 重写console方法
                mainConsole.log = function(...args) {
                    originalConsole.log.apply(this, args);
                    interceptConsoleMessage('info', args);
                };

                mainConsole.warn = function(...args) {
                    originalConsole.warn.apply(this, args);
                    interceptConsoleMessage('warn', args);
                };

                mainConsole.error = function(...args) {
                    originalConsole.error.apply(this, args);
                    interceptConsoleMessage('error', args);
                };

                mainConsole.info = function(...args) {
                    originalConsole.info.apply(this, args);
                    interceptConsoleMessage('info', args);
                };

                addLog('general', 'success', '控制台监控已设置');
            } catch (error) {
                addLog('general', 'error', '设置监控失败', { error: error.message });
            }
        }

        // 拦截控制台消息
        function interceptConsoleMessage(level, args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');

            // 根据消息内容分类
            if (message.includes('保存') || message.includes('💾') || message.includes('saveChapter')) {
                addLog('save', level, message);
                updateStatus('save', 'active', '保存中');
                
                if (message.includes('成功') || message.includes('✅')) {
                    setTimeout(() => updateStatus('save', 'inactive', '完成'), 2000);
                }
            } else if (message.includes('加载') || message.includes('📥') || message.includes('loadChapter')) {
                addLog('load', level, message);
                updateStatus('load', 'active', '加载中');
                
                if (message.includes('成功') || message.includes('✅')) {
                    setTimeout(() => updateStatus('load', 'inactive', '完成'), 2000);
                }
            } else {
                addLog('general', level, message);
            }

            // 更新数据预览
            if (message.includes('Delta') || message.includes('content')) {
                updateDataPreview(message);
            }
        }

        // 更新数据预览
        function updateDataPreview(message) {
            try {
                if (message.includes('保存')) {
                    document.getElementById('save-data-preview').textContent = message.substring(0, 500) + '...';
                } else if (message.includes('加载')) {
                    document.getElementById('load-data-preview').textContent = message.substring(0, 500) + '...';
                }
            } catch (error) {
                // 忽略预览更新错误
            }
        }

        // 触发保存
        function triggerSave() {
            if (!mainAppWindow || mainAppWindow.closed) {
                addLog('general', 'error', '主应用未连接');
                return;
            }

            try {
                if (mainAppWindow.saveCurrentChapterContent) {
                    addLog('save', 'info', '手动触发保存');
                    mainAppWindow.saveCurrentChapterContent();
                } else {
                    addLog('save', 'error', '保存函数不存在');
                }
            } catch (error) {
                addLog('save', 'error', '触发保存失败', { error: error.message });
            }
        }

        // 触发加载
        function triggerLoad() {
            if (!mainAppWindow || mainAppWindow.closed) {
                addLog('general', 'error', '主应用未连接');
                return;
            }

            try {
                if (mainAppWindow.currentChapter && mainAppWindow.loadChapterContent) {
                    addLog('load', 'info', '手动触发加载');
                    mainAppWindow.loadChapterContent(mainAppWindow.currentChapter);
                } else {
                    addLog('load', 'error', '加载函数不存在或没有当前章节');
                }
            } catch (error) {
                addLog('load', 'error', '触发加载失败', { error: error.message });
            }
        }

        // 清除日志
        function clearLogs() {
            logs = {
                save: [],
                load: [],
                general: []
            };
            
            updateLogDisplay('save');
            updateLogDisplay('load');
            updateLogDisplay('general');
            
            document.getElementById('save-data-preview').textContent = '等待保存数据...';
            document.getElementById('load-data-preview').textContent = '等待加载数据...';
            
            addLog('general', 'info', '日志已清除');
        }

        // 监听窗口关闭
        window.addEventListener('beforeunload', function() {
            if (mainAppWindow && !mainAppWindow.closed) {
                // 恢复原始console方法
                try {
                    const mainConsole = mainAppWindow.console;
                    Object.assign(mainConsole, originalConsole);
                } catch (error) {
                    // 忽略恢复错误
                }
            }
        });
    </script>
</body>
</html>
