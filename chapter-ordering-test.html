<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节排序测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .chapter-list {
            list-style: none;
            padding: 0;
        }
        
        .chapter-item {
            padding: 8px 12px;
            margin: 5px 0;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chapter-item.correct {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .chapter-item.incorrect {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .order-value {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
        }
        
        .test-controls {
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 书籍目录排序测试</h1>
        
        <div class="test-controls">
            <button class="btn btn-primary" onclick="runSortingTest()">🧪 运行排序测试</button>
            <button class="btn btn-success" onclick="testDatabaseConnection()">🔗 测试数据库连接</button>
            <button class="btn btn-warning" onclick="generateTestData()">📝 生成测试数据</button>
        </div>
        
        <div id="status-display"></div>
        
        <div class="test-section">
            <h3>🔍 排序算法测试</h3>
            <p>测试智能章节排序算法是否能正确识别和排序各种章节标题格式。</p>
            <div id="algorithm-test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 排序前后对比</h3>
            <p>显示排序前后的章节顺序对比，验证排序效果。</p>
            <div id="comparison-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 预期排序结果（篇-章-节结构）</h3>
            <p>根据书籍逻辑结构，章节应该按以下层级顺序排列：</p>
            <ul class="chapter-list" id="expected-order">
                <li class="chapter-item">前言 <span class="order-value">0</span></li>
                <li class="chapter-item">第0章：概述 <span class="order-value">10</span></li>

                <li class="chapter-item" style="background: #e3f2fd;">📖 第一篇：理论基础 <span class="order-value">1000</span></li>
                <li class="chapter-item" style="margin-left: 20px;">　第一章：大模型基本概念与内涵 <span class="order-value">1100</span></li>
                <li class="chapter-item" style="margin-left: 40px;">　　1.1 基本概念 <span class="order-value">1110</span></li>
                <li class="chapter-item" style="margin-left: 40px;">　　1.2 技术特点 <span class="order-value">1120</span></li>
                <li class="chapter-item" style="margin-left: 20px;">　第二章：大模型技术原理 <span class="order-value">1200</span></li>
                <li class="chapter-item" style="margin-left: 40px;">　　2.1 Transformer架构 <span class="order-value">1210</span></li>

                <li class="chapter-item" style="background: #e8f5e8;">📖 第二篇：应用实践 <span class="order-value">2000</span></li>
                <li class="chapter-item" style="margin-left: 20px;">　第三章：油气勘探中的大模型应用 <span class="order-value">2300</span></li>
                <li class="chapter-item" style="margin-left: 20px;">　第四章：实际应用案例 <span class="order-value">2400</span></li>

                <li class="chapter-item" style="background: #fff3e0;">📖 第三篇：发展趋势 <span class="order-value">3000</span></li>
                <li class="chapter-item" style="margin-left: 20px;">　第五章：未来发展方向 <span class="order-value">3500</span></li>
                <li class="chapter-item" style="margin-left: 20px;">　第六章：技术挑战与机遇 <span class="order-value">3600</span></li>

                <li class="chapter-item" style="background: #f3e5f5;">📎 附录A：术语表 <span class="order-value">9000</span></li>
                <li class="chapter-item" style="background: #f3e5f5;">📎 附录B：技术规范 <span class="order-value">9010</span></li>
                <li class="chapter-item" style="background: #f3e5f5;">📚 参考文献 <span class="order-value">9100</span></li>
                <li class="chapter-item" style="background: #f3e5f5;">📇 索引 <span class="order-value">9200</span></li>
            </ul>
        </div>
    </div>

    <script>
        // 智能章节排序函数 - 支持篇-章-节层级结构（从app.js复制）
        function calculateChapterOrder(title) {
            if (!title) return 9999;

            const lowerTitle = title.toLowerCase().trim();

            // 前言、序言 (0-9)
            if (lowerTitle.includes('前言') || lowerTitle.includes('序') || lowerTitle.includes('preface')) {
                return 0;
            }

            // 目录 (5)
            if (lowerTitle.includes('目录') || lowerTitle.includes('contents')) {
                return 5;
            }

            // 第0章或引言 (10-19)
            if (lowerTitle.includes('第0章') || lowerTitle.includes('第零章') ||
                lowerTitle.includes('引言') || lowerTitle.includes('概述') || lowerTitle.includes('导论')) {
                return 10;
            }

            // 篇级别 (1000, 2000, 3000, 4000, 5000)
            const partMatches = [
                { pattern: /第一篇|第1篇/, value: 1000 },
                { pattern: /第二篇|第2篇/, value: 2000 },
                { pattern: /第三篇|第3篇/, value: 3000 },
                { pattern: /第四篇|第4篇/, value: 4000 },
                { pattern: /第五篇|第5篇/, value: 5000 },
                { pattern: /第(\d+)篇/, multiplier: 1000 }
            ];

            for (const match of partMatches) {
                if (match.pattern.test(lowerTitle)) {
                    if (match.value) {
                        return match.value;
                    } else {
                        const partNum = parseInt(lowerTitle.match(match.pattern)[1]);
                        return partNum * 1000;
                    }
                }
            }

            // 章级别 - 需要根据上下文判断属于哪一篇
            const chapterMatches = [
                { pattern: /第一章|第1章/, baseValue: 100 },
                { pattern: /第二章|第2章/, baseValue: 200 },
                { pattern: /第三章|第3章/, baseValue: 300 },
                { pattern: /第四章|第4章/, baseValue: 400 },
                { pattern: /第五章|第5章/, baseValue: 500 },
                { pattern: /第六章|第6章/, baseValue: 600 },
                { pattern: /第(\d+)章/, multiplier: 100 }
            ];

            for (const match of chapterMatches) {
                if (match.pattern.test(lowerTitle)) {
                    if (match.baseValue) {
                        return 1000 + match.baseValue; // 默认放在第一篇
                    } else {
                        const chapterNum = parseInt(lowerTitle.match(match.pattern)[1]);
                        return 1000 + (chapterNum * 100);
                    }
                }
            }

            // 小节级别 (X.Y 格式)
            const sectionMatch = lowerTitle.match(/(\d+)\.(\d+)/);
            if (sectionMatch) {
                const chapter = parseInt(sectionMatch[1]);
                const section = parseInt(sectionMatch[2]);
                return 1000 + (chapter * 100) + (section * 10);
            }

            // 附录 (9000+)
            if (lowerTitle.includes('附录') || lowerTitle.includes('appendix')) {
                if (lowerTitle.includes('附录a') || lowerTitle.includes('附录A')) return 9000;
                if (lowerTitle.includes('附录b') || lowerTitle.includes('附录B')) return 9010;
                return 9000;
            }

            // 参考文献 (9100)
            if (lowerTitle.includes('参考文献') || lowerTitle.includes('references')) {
                return 9100;
            }

            // 索引 (9200)
            if (lowerTitle.includes('索引') || lowerTitle.includes('index')) {
                return 9200;
            }

            // 默认值
            return 6000;
        }

        // 根据上下文智能计算章节排序值
        function calculateChapterOrderWithContext(item, allItems) {
            const baseOrder = calculateChapterOrder(item.title);

            // 如果是章节，需要找到它属于哪一篇
            if (item.title && (item.title.includes('章') || /\d+\.\d+/.test(item.title))) {
                const itemIndex = allItems.findIndex(x => x.title === item.title);
                if (itemIndex >= 0) {
                    for (let i = itemIndex - 1; i >= 0; i--) {
                        const prevItem = allItems[i];
                        if (prevItem.title && prevItem.title.includes('篇')) {
                            const partOrder = calculateChapterOrder(prevItem.title);
                            if (baseOrder < 2000 && partOrder >= 1000) {
                                const chapterOffset = baseOrder % 1000;
                                return partOrder + chapterOffset;
                            }
                        }
                    }
                }
            }

            return baseOrder;
        }

        // 测试数据 - 包含篇-章-节完整结构
        const testChapters = [
            { title: '前言', order_index: null, level: 0 },
            { title: '第0章：概述', order_index: null, level: 1 },

            // 第一篇
            { title: '第一篇：理论基础', order_index: null, level: 0 },
            { title: '第一章：大模型基本概念与内涵', order_index: null, level: 1 },
            { title: '1.1 基本概念', order_index: null, level: 2 },
            { title: '1.2 技术特点', order_index: null, level: 2 },
            { title: '第二章：大模型技术原理', order_index: null, level: 1 },
            { title: '2.1 Transformer架构', order_index: null, level: 2 },
            { title: '2.2 注意力机制', order_index: null, level: 2 },

            // 第二篇
            { title: '第二篇：应用实践', order_index: null, level: 0 },
            { title: '第三章：油气勘探中的大模型应用', order_index: null, level: 1 },
            { title: '3.1 地震数据处理', order_index: null, level: 2 },
            { title: '第四章：实际应用案例', order_index: null, level: 1 },

            // 第三篇
            { title: '第三篇：发展趋势', order_index: null, level: 0 },
            { title: '第五章：未来发展方向', order_index: null, level: 1 },
            { title: '第六章：技术挑战与机遇', order_index: null, level: 1 },

            // 附录部分
            { title: '附录A：术语表', order_index: null, level: 0 },
            { title: '附录B：技术规范', order_index: null, level: 0 },
            { title: '参考文献', order_index: null, level: 0 },
            { title: '索引', order_index: null, level: 0 }
        ];

        // 运行排序测试
        function runSortingTest() {
            showStatus('🧪 开始运行排序测试...', 'info');

            // 测试排序算法（考虑上下文）
            const results = testChapters.map(chapter => ({
                ...chapter,
                calculatedOrder: calculateChapterOrderWithContext(chapter, testChapters),
                baseOrder: calculateChapterOrder(chapter.title)
            }));

            // 排序
            const sortedResults = results.sort((a, b) => {
                if (a.calculatedOrder !== b.calculatedOrder) {
                    return a.calculatedOrder - b.calculatedOrder;
                }
                // 如果排序值相同，按层级排序
                if (a.level !== b.level) {
                    return a.level - b.level;
                }
                return 0;
            });

            // 显示结果
            displayAlgorithmResults(sortedResults);
            displayComparisonResults(testChapters, sortedResults);

            showStatus('✅ 排序测试完成！检查结果中的篇-章-节层级结构', 'success');
        }

        // 显示算法测试结果
        function displayAlgorithmResults(results) {
            const container = document.getElementById('algorithm-test-results');

            // 分析内容类型
            function getContentType(order, title) {
                if (order < 100) return { type: '前言', color: '#6c757d' };
                if (order >= 1000 && order < 2000) return { type: '第一篇', color: '#007bff' };
                if (order >= 2000 && order < 3000) return { type: '第二篇', color: '#28a745' };
                if (order >= 3000 && order < 4000) return { type: '第三篇', color: '#ffc107' };
                if (order >= 4000 && order < 5000) return { type: '第四篇', color: '#fd7e14' };
                if (order >= 9000) return { type: '附录', color: '#6f42c1' };
                return { type: '其他', color: '#dc3545' };
            }

            // 获取层级缩进
            function getIndent(level) {
                return '　'.repeat(level || 0);
            }

            const html = `
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>章节标题</th>
                            <th>层级</th>
                            <th>基础排序值</th>
                            <th>上下文排序值</th>
                            <th>排序位置</th>
                            <th>所属部分</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.map((chapter, index) => {
                            const contentType = getContentType(chapter.calculatedOrder, chapter.title);
                            const indent = getIndent(chapter.level);
                            return `
                                <tr>
                                    <td>${indent}${chapter.title}</td>
                                    <td>L${chapter.level || 0}</td>
                                    <td>${chapter.baseOrder || 'N/A'}</td>
                                    <td><strong>${chapter.calculatedOrder}</strong></td>
                                    <td>${index + 1}</td>
                                    <td>
                                        <span style="color: ${contentType.color}; font-weight: bold;">
                                            ${contentType.type}
                                        </span>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = html;
        }

        // 显示排序前后对比
        function displayComparisonResults(original, sorted) {
            const container = document.getElementById('comparison-results');
            
            const html = `
                <div style="display: flex; gap: 20px;">
                    <div style="flex: 1;">
                        <h4>排序前（原始顺序）</h4>
                        <ul class="chapter-list">
                            ${original.map((chapter, index) => `
                                <li class="chapter-item">
                                    ${chapter.title}
                                    <span class="order-value">#${index + 1}</span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                    <div style="flex: 1;">
                        <h4>排序后（智能排序）</h4>
                        <ul class="chapter-list">
                            ${sorted.map((chapter, index) => `
                                <li class="chapter-item correct">
                                    ${chapter.title}
                                    <span class="order-value">${chapter.calculatedOrder}</span>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                </div>
            `;
            
            container.innerHTML = html;
        }

        // 显示状态信息
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-display');
            container.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            // 3秒后自动清除
            setTimeout(() => {
                container.innerHTML = '';
            }, 3000);
        }

        // 测试数据库连接
        function testDatabaseConnection() {
            showStatus('🔗 测试数据库连接功能需要在实际项目环境中运行', 'info');
        }

        // 生成测试数据
        function generateTestData() {
            showStatus('📝 测试数据已生成，请查看上方的测试结果', 'success');
            runSortingTest();
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('📚 章节排序测试页面已加载，点击按钮开始测试', 'info');
        });
    </script>
</body>
</html>
