# Supabase 配置指南

## 概述

本指南将帮助您配置 Supabase 数据库，以支持《大模型技术与油气应用概论》多用户协作编写系统。

## 第一步：创建 Supabase 项目

### 1. 注册 Supabase 账户
1. 访问 [https://supabase.com](https://supabase.com)
2. 点击 "Start your project" 注册账户
3. 使用 GitHub、Google 或邮箱注册

### 2. 创建新项目
1. 登录后点击 "New Project"
2. 选择组织（个人或团队）
3. 填写项目信息：
   - **Name**: `llm-book-collaboration`
   - **Database Password**: 设置一个强密码（请记住）
   - **Region**: 选择离您最近的区域（建议选择 Singapore 或 Tokyo）
4. 点击 "Create new project"

### 3. 等待项目初始化
- 项目创建需要 1-2 分钟
- 创建完成后会自动跳转到项目仪表板

## 第二步：配置数据库

### 1. 执行数据库脚本
1. 在 Supabase 仪表板中，点击左侧菜单的 "SQL Editor"
2. 点击 "New query"
3. 将 `database-schema.sql` 文件的内容复制粘贴到编辑器中
4. 点击 "Run" 执行脚本

### 2. 验证表创建
1. 点击左侧菜单的 "Table Editor"
2. 确认以下表已创建：
   - `user_profiles`
   - `projects`
   - `project_members`
   - `outlines`
   - `chapters`
   - `chapter_permissions`
   - `references`
   - `diagrams`
   - `comments`
   - `chapter_versions`
   - `activity_logs`
   - `notifications`

## 第三步：配置认证

### 1. 启用邮箱认证
1. 点击左侧菜单的 "Authentication"
2. 点击 "Settings" 标签
3. 在 "Auth Providers" 部分，确保 "Email" 已启用
4. 配置邮箱设置（可选）：
   - 如需自定义邮箱模板，可在 "Email Templates" 中修改

### 2. 配置行级安全策略
1. 点击左侧菜单的 "Authentication"
2. 点击 "Policies" 标签
3. 确认各表的 RLS 策略已正确创建

## 第四步：获取项目配置信息

### 1. 获取项目 URL 和 API 密钥
1. 点击左侧菜单的 "Settings"
2. 点击 "API" 标签
3. 复制以下信息：
   - **Project URL**: `https://your-project-ref.supabase.co`
   https://bigzfjlaypptochqpxzu.supabase.co
   - **anon public key**: `eyJ...` (很长的字符串)
   eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.02clbd9p8TCBqsYEkKJlHnbnU60cRSVxgJo0bfNtq9M

### 2. 配置前端应用
1. 打开 `supabase-config.js` 文件
2. 替换以下配置：
```javascript
this.supabaseUrl = 'https://your-project-ref.supabase.co'; // 替换为您的项目URL
this.supabaseKey = 'your-anon-key'; // 替换为您的匿名密钥
```

## 第五步：测试配置

### 1. 启动应用
1. 打开 `auth.html` 进行用户注册和登录测试
2. 注册一个测试账户
3. 检查邮箱验证邮件

### 2. 验证数据库连接
1. 注册成功后，检查 Supabase 仪表板中的 "Table Editor"
2. 在 `user_profiles` 表中应该能看到新注册的用户
3. 在 "Authentication" > "Users" 中也应该能看到用户

## 第六步：配置实时功能

### 1. 启用实时订阅
1. 在 Supabase 仪表板中，点击 "Database"
2. 点击 "Replication" 标签
3. 为以下表启用实时功能：
   - `chapters`
   - `outlines`
   - `comments`
   - `activity_logs`

### 2. 配置发布规则
```sql
-- 在 SQL Editor 中执行以下命令
ALTER PUBLICATION supabase_realtime ADD TABLE chapters;
ALTER PUBLICATION supabase_realtime ADD TABLE outlines;
ALTER PUBLICATION supabase_realtime ADD TABLE comments;
ALTER PUBLICATION supabase_realtime ADD TABLE activity_logs;
```

## 第七步：安全配置

### 1. 配置域名白名单（生产环境）
1. 在 "Settings" > "API" 中
2. 在 "Site URL" 中添加您的域名
3. 在 "Redirect URLs" 中添加允许的重定向地址

### 2. 配置 CORS（如需要）
1. 在 "Settings" > "API" 中
2. 配置 "CORS origins" 允许的域名

## 第八步：备份和监控

### 1. 设置自动备份
1. 在 "Settings" > "Database" 中
2. 配置自动备份策略
3. 建议启用每日备份

### 2. 监控设置
1. 在 "Settings" > "Billing" 中查看使用情况
2. 设置使用量警报
3. 监控 API 请求数量

## 常见问题

### Q: 数据库脚本执行失败
**A**: 检查以下几点：
- 确保项目已完全初始化
- 检查 SQL 语法是否正确
- 分段执行脚本，逐步排查问题

### Q: 用户注册后无法登录
**A**: 检查以下设置：
- 确认邮箱验证是否完成
- 检查 RLS 策略是否正确配置
- 查看浏览器控制台错误信息

### Q: 实时功能不工作
**A**: 确认以下配置：
- 实时订阅是否已启用
- 发布规则是否正确配置
- 网络连接是否稳定

### Q: API 请求失败
**A**: 检查以下项目：
- API 密钥是否正确
- 项目 URL 是否正确
- 网络连接是否正常
- 是否超出使用限制

## 生产环境部署

### 1. 升级到付费计划
- 免费计划有一定限制
- 生产环境建议使用 Pro 计划或以上

### 2. 配置自定义域名
- 在 "Settings" > "Custom Domains" 中配置
- 设置 SSL 证书

### 3. 性能优化
- 创建适当的数据库索引
- 优化查询语句
- 配置连接池

## 支持和帮助

- **Supabase 官方文档**: [https://supabase.com/docs](https://supabase.com/docs)
- **社区支持**: [https://github.com/supabase/supabase/discussions](https://github.com/supabase/supabase/discussions)
- **API 参考**: [https://supabase.com/docs/reference](https://supabase.com/docs/reference)

## 配置检查清单

- [ ] Supabase 项目已创建
- [ ] 数据库表已创建
- [ ] 认证功能已配置
- [ ] RLS 策略已启用
- [ ] 项目 URL 和 API 密钥已配置
- [ ] 实时功能已启用
- [ ] 用户注册和登录测试通过
- [ ] 数据库连接测试通过
- [ ] 安全设置已配置
- [ ] 备份策略已设置

完成以上配置后，您的多用户协作系统就可以正常运行了！
