<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节保存修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1e293b;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin: 5px;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-success {
            background: #059669;
            color: white;
        }
        
        .btn-warning {
            background: #d97706;
            color: white;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .status-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #1e293b;
        }
        
        .log-container {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 3px;
        }
        
        .log-entry.success {
            color: #10b981;
        }
        
        .log-entry.error {
            color: #ef4444;
        }
        
        .log-entry.warning {
            color: #f59e0b;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 章节保存修复测试</h1>
            <p>测试和验证章节保存时outline_id的修复效果</p>
        </div>
        
        <div class="alert alert-info">
            <strong>测试说明：</strong>此页面用于测试章节保存修复功能，验证新章节保存时是否正确设置outline_id。
        </div>
        
        <!-- 当前状态 -->
        <div class="test-section">
            <h3>📊 当前数据状态</h3>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-label">总章节数</div>
                    <div class="status-value" id="totalChapters">-</div>
                </div>
                <div class="status-card">
                    <div class="status-label">outline_id为null</div>
                    <div class="status-value" id="nullOutlineId">-</div>
                </div>
                <div class="status-card">
                    <div class="status-label">有效关联</div>
                    <div class="status-value" id="validOutlineId">-</div>
                </div>
                <div class="status-card">
                    <div class="status-label">成功率</div>
                    <div class="status-value" id="successRate">-</div>
                </div>
            </div>
            <button class="btn btn-primary" onclick="checkCurrentStatus()">检查状态</button>
        </div>
        
        <!-- 快速清理 -->
        <div class="test-section">
            <h3>🛠️ 快速数据库清理</h3>
            <p>使用简化的清理函数修复现有的outline_id问题</p>
            <button class="btn btn-success" onclick="runQuickCleanup()" id="quickCleanupBtn">快速清理</button>
            <button class="btn btn-primary" onclick="checkCurrentStatus()">重新检查</button>
        </div>
        
        <!-- 测试章节保存 -->
        <div class="test-section">
            <h3>📝 测试章节保存逻辑</h3>
            <p>模拟章节保存过程，验证outline_id是否正确设置</p>
            <div style="margin: 15px 0;">
                <label>测试章节标题：</label>
                <input type="text" id="testChapterTitle" value="测试章节 - 验证outline_id" style="width: 300px; padding: 8px; margin-left: 10px;">
            </div>
            <button class="btn btn-warning" onclick="simulateChapterSave()">模拟保存章节</button>
            <button class="btn btn-danger" onclick="deleteTestChapters()">删除测试章节</button>
        </div>
        
        <!-- 日志输出 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button class="btn btn-primary" onclick="clearLog()">清空日志</button>
            <div class="log-container" id="logContainer">
                <div class="log-entry">等待测试操作...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-config.js"></script>
    <script>
        // 日志系统
        const logs = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="log-entry ${type}">[${timestamp}] ${message}</div>`;
            logs.push(logEntry);
            
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = logs.join('');
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            logs.length = 0;
            document.getElementById('logContainer').innerHTML = '<div class="log-entry">日志已清空</div>';
        }
        
        // 检查当前状态
        async function checkCurrentStatus() {
            addLog('开始检查数据状态...', 'info');
            
            try {
                if (!supabaseManager || !supabaseManager.supabase) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                // 获取章节数据
                const { data: chapters, error: chaptersError } = await supabaseManager.supabase
                    .from('chapters')
                    .select('id, outline_id, title');
                
                if (chaptersError) throw chaptersError;
                
                const totalChapters = chapters?.length || 0;
                const nullOutlineId = chapters?.filter(c => !c.outline_id).length || 0;
                const validOutlineId = totalChapters - nullOutlineId;
                const successRate = totalChapters > 0 ? ((validOutlineId / totalChapters) * 100).toFixed(1) + '%' : '0%';
                
                // 更新显示
                document.getElementById('totalChapters').textContent = totalChapters;
                document.getElementById('nullOutlineId').textContent = nullOutlineId;
                document.getElementById('validOutlineId').textContent = validOutlineId;
                document.getElementById('successRate').textContent = successRate;
                
                addLog(`状态检查完成: 总章节${totalChapters}, 问题章节${nullOutlineId}, 成功率${successRate}`, 'success');
                
                if (nullOutlineId > 0) {
                    addLog(`⚠️ 发现${nullOutlineId}个章节的outline_id为null`, 'warning');
                    
                    // 显示问题章节
                    const problemChapters = chapters.filter(c => !c.outline_id);
                    problemChapters.slice(0, 5).forEach(chapter => {
                        addLog(`  - "${chapter.title}" (ID: ${chapter.id})`, 'warning');
                    });
                    if (problemChapters.length > 5) {
                        addLog(`  ... 还有 ${problemChapters.length - 5} 个问题章节`, 'warning');
                    }
                } else {
                    addLog('✅ 所有章节的outline_id都正常', 'success');
                }
                
            } catch (error) {
                addLog(`状态检查失败: ${error.message}`, 'error');
            }
        }
        
        // 运行快速清理
        async function runQuickCleanup() {
            const btn = document.getElementById('quickCleanupBtn');
            btn.disabled = true;
            btn.textContent = '清理中...';
            
            addLog('开始快速数据库清理...', 'info');
            
            try {
                // 调用全局的快速清理函数
                if (typeof quickDatabaseCleanup === 'function') {
                    const success = await quickDatabaseCleanup();
                    if (success) {
                        addLog('✅ 快速清理完成', 'success');
                        await checkCurrentStatus();
                    } else {
                        addLog('❌ 快速清理失败', 'error');
                    }
                } else {
                    addLog('❌ 快速清理函数未找到，请确保已加载app.js', 'error');
                }
                
            } catch (error) {
                addLog(`快速清理异常: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '快速清理';
            }
        }
        
        // 模拟章节保存
        async function simulateChapterSave() {
            const title = document.getElementById('testChapterTitle').value;
            if (!title.trim()) {
                addLog('请输入测试章节标题', 'warning');
                return;
            }
            
            addLog(`开始模拟保存章节: "${title}"`, 'info');
            
            try {
                // 获取第一个项目ID
                const { data: projects } = await supabaseManager.supabase
                    .from('projects')
                    .select('id')
                    .limit(1);
                
                if (!projects || projects.length === 0) {
                    throw new Error('没有找到项目');
                }
                
                const projectId = projects[0].id;
                
                // 获取第一个大纲项作为outline_id
                const { data: outlines } = await supabaseManager.supabase
                    .from('outlines')
                    .select('id, title')
                    .eq('project_id', projectId)
                    .limit(1);
                
                if (!outlines || outlines.length === 0) {
                    throw new Error('没有找到大纲项');
                }
                
                const outlineId = outlines[0].id;
                addLog(`使用大纲ID: ${outlineId} (${outlines[0].title})`, 'info');
                
                // 创建测试章节
                const chapterData = {
                    id: crypto.randomUUID(),
                    project_id: projectId,
                    outline_id: outlineId, // 关键：正确设置outline_id
                    title: title,
                    content: JSON.stringify({
                        ops: [
                            { insert: `这是测试章节"${title}"的内容。\n` },
                            { insert: '测试时间: ' + new Date().toLocaleString() + '\n' }
                        ]
                    }),
                    word_count: 50,
                    status: 'draft',
                    created_by: 'test-user',
                    last_edited_by: 'test-user'
                };
                
                const { data: savedChapter, error } = await supabaseManager.supabase
                    .from('chapters')
                    .insert(chapterData)
                    .select()
                    .single();
                
                if (error) {
                    throw error;
                }
                
                addLog(`✅ 测试章节保存成功`, 'success');
                addLog(`  - 章节ID: ${savedChapter.id}`, 'info');
                addLog(`  - outline_id: ${savedChapter.outline_id}`, 'success');
                addLog(`  - 标题: ${savedChapter.title}`, 'info');
                
                // 重新检查状态
                await checkCurrentStatus();
                
            } catch (error) {
                addLog(`模拟保存失败: ${error.message}`, 'error');
            }
        }
        
        // 删除测试章节
        async function deleteTestChapters() {
            if (!confirm('确定要删除所有测试章节吗？')) {
                return;
            }
            
            addLog('开始删除测试章节...', 'info');
            
            try {
                const { data: deletedChapters, error } = await supabaseManager.supabase
                    .from('chapters')
                    .delete()
                    .like('title', '%测试章节%')
                    .select();
                
                if (error) {
                    throw error;
                }
                
                const deletedCount = deletedChapters?.length || 0;
                addLog(`✅ 删除了 ${deletedCount} 个测试章节`, 'success');
                
                // 重新检查状态
                await checkCurrentStatus();
                
            } catch (error) {
                addLog(`删除测试章节失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', async () => {
            addLog('测试页面已加载', 'info');
            await checkCurrentStatus();
        });
    </script>
</body>
</html>
