# AI增强学术专著编写系统 - 快速启动指南

## 🚀 一键部署

### 1. 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **硬件配置**: 4核CPU, 8GB内存, 100GB存储空间
- **网络**: 稳定的互联网连接

### 2. 快速部署命令

```bash
# 下载部署脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/llm-book-system/main/deploy.sh -o deploy.sh

# 设置执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

### 3. 手动部署步骤

如果自动部署失败，可以按以下步骤手动部署：

#### 步骤1: 安装Docker
```bash
# 安装Docker
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录以使Docker权限生效
```

#### 步骤2: 下载项目文件
```bash
# 创建项目目录
mkdir -p /opt/llm-book-system
cd /opt/llm-book-system

# 下载必要文件（或从Git仓库克隆）
# 确保包含以下文件：
# - docker-compose.yml
# - database-schema.sql
# - seed-data.sql
# - .env
# - nginx/nginx.conf
```

#### 步骤3: 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env

# 必须配置的项目：
# - POSTGRES_PASSWORD (数据库密码)
# - JWT_SECRET (JWT密钥)
# - SITE_URL (您的域名)
# - OPENROUTER_API_KEY (AI服务密钥)
# - SMTP_* (邮件配置)
```

#### 步骤4: 启动服务
```bash
# 创建必要目录
mkdir -p volumes/{postgres_data,storage_data,redis_data,logs/nginx}
mkdir -p nginx/ssl

# 启动服务
docker-compose up -d

# 检查服务状态
docker-compose ps
```

## 🔧 配置说明

### 环境变量配置

#### 必须配置项
```bash
# 数据库配置
POSTGRES_PASSWORD=your_secure_password_here

# JWT密钥（32字符以上）
JWT_SECRET=your_jwt_secret_here_32_characters_minimum

# 站点URL
SITE_URL=https://your-domain.com

# AI服务配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

#### 可选配置项
```bash
# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_ADMIN_EMAIL=<EMAIL>

# 系统配置
ENVIRONMENT=production
LOG_LEVEL=info
```

### OpenRouter API配置

1. 访问 [OpenRouter](https://openrouter.ai/)
2. 注册账户并获取API密钥
3. 在`.env`文件中配置`OPENROUTER_API_KEY`
4. 确保账户有足够余额使用DeepSeek模型

### 邮件服务配置

#### Gmail配置示例
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password  # 使用应用专用密码
```

#### 其他邮件服务
- **腾讯企业邮箱**: smtp.exmail.qq.com:587
- **阿里云邮箱**: smtp.mxhichina.com:587
- **网易邮箱**: smtp.163.com:587

## 🌐 域名和SSL配置

### 1. 域名配置
```bash
# 更新.env文件中的域名
SITE_URL=https://your-domain.com
ADDITIONAL_REDIRECT_URLS=https://your-domain.com/**

# 重启服务使配置生效
docker-compose restart
```

### 2. SSL证书配置

#### 使用Let's Encrypt免费证书
```bash
# 安装Certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到nginx目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem

# 更新nginx配置启用HTTPS
# 编辑 nginx/nginx.conf，取消HTTPS配置的注释

# 重启nginx
docker-compose restart nginx
```

#### 证书自动续期
```bash
# 添加到crontab
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## 📊 系统管理

### 常用管理命令

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs [service_name]

# 重启服务
docker-compose restart [service_name]

# 停止所有服务
docker-compose down

# 更新服务
docker-compose pull
docker-compose up -d
```

### 数据备份
```bash
# 设置脚本权限
chmod +x scripts/*.sh

# 执行备份
./scripts/backup.sh

# 定时备份（添加到crontab）
echo "0 2 * * * /opt/llm-book-system/scripts/backup.sh" | crontab -
```

### 系统监控
```bash
# 快速状态检查
./scripts/monitor.sh -q

# 完整监控检查
./scripts/monitor.sh

# 持续监控模式
./scripts/monitor.sh -w

# 自动修复问题
./scripts/monitor.sh -f
```

### 数据恢复
```bash
# 恢复数据库
./scripts/restore.sh -t database /path/to/backup/database_backup.sql.gz

# 完整恢复
./scripts/restore.sh /path/to/backup/directory/
```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
docker-compose logs

# 检查端口占用
sudo netstat -tlnp | grep -E ":80|:443|:3001|:4000|:5000|:9999"

# 重新启动
docker-compose down
docker-compose up -d
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U supabase -d llm_book_system

# 查看数据库日志
docker-compose logs postgres

# 重启数据库
docker-compose restart postgres
```

#### 3. AI服务不可用
```bash
# 检查API密钥配置
grep OPENROUTER_API_KEY .env

# 测试API连接
curl -H "Authorization: Bearer YOUR_API_KEY" https://openrouter.ai/api/v1/models

# 查看AI任务日志
docker-compose exec postgres psql -U supabase -d llm_book_system -c "SELECT * FROM ai_tasks WHERE status = 'failed' ORDER BY created_at DESC LIMIT 10;"
```

#### 4. 邮件发送失败
```bash
# 检查邮件配置
grep SMTP .env

# 测试SMTP连接
telnet smtp.gmail.com 587
```

### 性能优化

#### 1. 数据库优化
```sql
-- 连接到数据库
docker-compose exec postgres psql -U supabase -d llm_book_system

-- 查看慢查询
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;

-- 分析表统计信息
ANALYZE;

-- 重建索引
REINDEX DATABASE llm_book_system;
```

#### 2. 系统资源优化
```bash
# 清理Docker资源
docker system prune -f

# 清理日志文件
sudo find /var/lib/docker/containers/ -name "*.log" -exec truncate -s 0 {} \;

# 优化内存使用
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
```

## 📞 技术支持

### 获取帮助
- **文档**: 查看项目README和相关文档
- **日志**: 使用`docker-compose logs`查看详细日志
- **监控**: 使用`./scripts/monitor.sh`检查系统状态
- **社区**: 在GitHub Issues中提问

### 联系方式
- **项目仓库**: [GitHub链接]
- **技术支持**: [支持邮箱]
- **用户手册**: [在线文档链接]

---

**祝您使用愉快！如有问题，请及时联系技术支持。**
