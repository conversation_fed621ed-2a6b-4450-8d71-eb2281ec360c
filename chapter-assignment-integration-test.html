<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节分配集成测试 - 专业学术协作编著系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .test-content {
            padding: 0;
        }
        
        .collaboration-tabs {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .collab-tab {
            flex: 1;
            padding: 16px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .collab-tab:hover {
            background: #e5e7eb;
            color: #374151;
        }
        
        .collab-tab.active {
            background: white;
            color: #6366f1;
            border-bottom: 2px solid #6366f1;
        }
        
        .collab-content {
            display: none;
            padding: 0;
        }
        
        .collab-content.active {
            display: block;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            background: #10b981;
            color: white;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            z-index: 1000;
        }
        
        .status-indicator i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="status-indicator">
        <i class="fas fa-check-circle"></i>
        章节分配系统已集成
    </div>

    <div class="test-container">
        <div class="test-header">
            <h1>章节分配系统集成测试</h1>
            <p>测试新的章节分配UI在协作编著系统中的集成效果</p>
        </div>
        
        <div class="test-content">
            <div class="collaboration-tabs">
                <button class="collab-tab" data-tab="members" onclick="switchTab('members')">
                    <i class="fas fa-users"></i> 团队成员
                </button>
                <button class="collab-tab active" data-tab="assignments" onclick="switchTab('assignments')">
                    <i class="fas fa-tasks"></i> 章节分配
                </button>
                <button class="collab-tab" data-tab="permissions" onclick="switchTab('permissions')">
                    <i class="fas fa-shield-alt"></i> 权限设置
                </button>
            </div>

            <!-- 团队成员 -->
            <div id="members-tab" class="collab-content">
                <div style="padding: 60px; text-align: center; color: #6b7280;">
                    <i class="fas fa-users" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <h3 style="margin: 0 0 8px 0; color: #374151;">团队成员</h3>
                    <p style="margin: 0;">团队成员管理功能</p>
                </div>
            </div>

            <!-- 章节分配 -->
            <div id="assignments-tab" class="collab-content active">
                <div class="chapter-assignment-container">
                    <!-- 章节分配头部 -->
                    <div class="assignment-header">
                        <div class="header-left">
                            <h3>章节分配</h3>
                            <div class="assignment-stats" id="assignment-stats">
                                <span class="stat-item">
                                    <i class="fas fa-book"></i>
                                    <span id="total-chapters">2</span> 总章节
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-users"></i>
                                    <span id="total-authors">2</span> 参与作者
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span id="completed-chapters">0</span> 已完成
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i>
                                    <span id="pending-reviews">1</span> 待审核
                                </span>
                            </div>
                        </div>
                        <div class="header-actions">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="assignment-search" placeholder="搜索章节或作者...">
                            </div>
                            <select id="status-filter" class="status-filter">
                                <option value="">所有状态</option>
                                <option value="pending">待确认</option>
                                <option value="in_progress">进行中</option>
                                <option value="reviewing">审核中</option>
                                <option value="completed">已完成</option>
                            </select>
                            <button class="btn btn-primary" onclick="showCreateModal()">
                                <i class="fas fa-plus"></i>
                                新建分配
                            </button>
                            <button class="btn btn-secondary" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                        </div>
                    </div>

                    <!-- 章节分配列表 -->
                    <div class="assignments-container">
                        <div class="assignments-header-row">
                            <div class="header-cell">章节</div>
                            <div class="header-cell">主笔作者</div>
                            <div class="header-cell">协作者</div>
                            <div class="header-cell">状态</div>
                            <div class="header-cell">进度</div>
                            <div class="header-cell">截止日期</div>
                            <div class="header-cell">操作</div>
                        </div>
                        <div class="assignments-list" id="chapter-assignments-list">
                            <!-- 动态内容将在这里加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权限设置 -->
            <div id="permissions-tab" class="collab-content">
                <div style="padding: 60px; text-align: center; color: #6b7280;">
                    <i class="fas fa-shield-alt" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <h3 style="margin: 0 0 8px 0; color: #374151;">权限设置</h3>
                    <p style="margin: 0;">项目权限管理功能</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟协作管理器
        const collaborationManager = {
            // 模拟数据
            mockAssignments: [
                {
                    id: '1',
                    title: '第0章：前言',
                    description: '介绍本书的写作背景、目标读者、主要内容和结构安排',
                    status: 'in_progress',
                    user_profiles: { full_name: '张教授', email: '<EMAIL>' },
                    collaborators: '李博士',
                    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    priority: 'high'
                },
                {
                    id: '2',
                    title: '第二章：文献综述',
                    description: '相关研究领域的文献综述和分析',
                    status: 'reviewing',
                    user_profiles: { full_name: '李博士', email: '<EMAIL>' },
                    collaborators: '王研究员',
                    due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
                    priority: 'medium'
                }
            ],

            // 计算进度
            calculateProgress(assignment) {
                if (assignment.status === 'completed') return 100;
                if (assignment.status === 'reviewing') return 90;
                if (assignment.status === 'in_progress') return 50;
                if (assignment.status === 'pending') return 10;
                return 0;
            },

            // 获取状态文本
            getStatusText(status) {
                const statusMap = {
                    'pending': '待确认',
                    'in_progress': '进行中',
                    'reviewing': '审核中',
                    'completed': '已完成',
                    'rejected': '已拒绝'
                };
                return statusMap[status] || status;
            },

            // 渲染章节分配
            renderChapterAssignments() {
                const assignmentsContainer = document.getElementById('chapter-assignments-list');
                if (!assignmentsContainer) return;

                const assignments = this.mockAssignments;

                assignmentsContainer.innerHTML = assignments.map(assignment => {
                    const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
                    const isOverdue = dueDate && dueDate < new Date();
                    const progress = this.calculateProgress(assignment);
                    
                    return `
                        <div class="assignment-item">
                            <div class="assignment-title">
                                ${assignment.title}
                                <div class="assignment-subtitle">
                                    ${assignment.description}
                                </div>
                            </div>
                            <div class="user-badge">
                                <i class="fas fa-user"></i>
                                ${assignment.user_profiles.full_name}
                            </div>
                            <div class="user-badge">
                                <i class="fas fa-users"></i>
                                ${assignment.collaborators}
                            </div>
                            <div>
                                <span class="status-badge status-${assignment.status}">
                                    ${this.getStatusText(assignment.status)}
                                </span>
                            </div>
                            <div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progress}%"></div>
                                </div>
                                <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                                    ${progress}%
                                </div>
                            </div>
                            <div class="due-date ${isOverdue ? 'overdue' : ''}">
                                ${dueDate ? dueDate.toLocaleDateString() : '无截止日期'}
                            </div>
                            <div class="actions">
                                <button class="action-btn" onclick="editAssignment('${assignment.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" onclick="viewAssignment('${assignment.id}')" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" onclick="deleteAssignment('${assignment.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');
            }
        };

        // 标签切换
        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.collab-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.collab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-tab`).classList.add('active');

            // 如果切换到章节分配，渲染数据
            if (tabName === 'assignments') {
                collaborationManager.renderChapterAssignments();
            }
        }

        // 功能按钮
        function showCreateModal() {
            alert('新建分配功能已集成！');
        }

        function refreshData() {
            collaborationManager.renderChapterAssignments();
            alert('数据已刷新！');
        }

        function editAssignment(id) {
            alert(`编辑分配: ${id}`);
        }

        function viewAssignment(id) {
            alert(`查看分配详情: ${id}`);
        }

        function deleteAssignment(id) {
            if (confirm('确定要删除这个分配吗？')) {
                alert(`删除分配: ${id}`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示章节分配
            collaborationManager.renderChapterAssignments();
            
            // 设置搜索功能
            const searchInput = document.getElementById('assignment-search');
            const statusFilter = document.getElementById('status-filter');
            
            if (searchInput) {
                searchInput.addEventListener('input', filterAssignments);
            }
            
            if (statusFilter) {
                statusFilter.addEventListener('change', filterAssignments);
            }
        });

        // 过滤功能
        function filterAssignments() {
            const searchTerm = document.getElementById('assignment-search').value.toLowerCase();
            const statusFilter = document.getElementById('status-filter').value;
            const items = document.querySelectorAll('.assignment-item');
            
            items.forEach(item => {
                const title = item.querySelector('.assignment-title').textContent.toLowerCase();
                const author = item.querySelector('.user-badge').textContent.toLowerCase();
                const status = item.querySelector('.status-badge').className;
                
                const matchesSearch = !searchTerm || title.includes(searchTerm) || author.includes(searchTerm);
                const matchesStatus = !statusFilter || status.includes(`status-${statusFilter}`);
                
                item.style.display = matchesSearch && matchesStatus ? 'grid' : 'none';
            });
        }
    </script>
</body>
</html>
