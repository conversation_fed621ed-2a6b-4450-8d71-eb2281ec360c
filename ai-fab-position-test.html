<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手按钮位置测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .editor-simulation {
            border: 1px solid #d1d5db;
            border-radius: 6px;
            min-height: 400px;
            background: white;
            position: relative;
        }
        
        .editor-toolbar {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            padding: 0.5rem 1rem;
            display: flex;
            gap: 0.5rem;
            align-items: center;
            border-radius: 6px 6px 0 0;
        }
        
        .toolbar-button {
            padding: 0.25rem 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .toolbar-button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .editor-content {
            padding: 1rem;
            min-height: 350px;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }
        
        .position-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .position-demo {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            background: #f9fafb;
            position: relative;
            min-height: 200px;
        }
        
        .demo-title {
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .old-position {
            color: #ef4444;
        }
        
        .new-position {
            color: #10b981;
        }
        
        .fab-demo-old {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 48px;
            height: 48px;
            background: #3b82f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            z-index: 1000;
        }
        
        .fab-demo-new {
            position: absolute;
            top: 80px;
            right: 20px;
            width: 48px;
            height: 48px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            z-index: 1000;
        }
        
        .toolbar-demo {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            margin: 1rem 0;
            border-radius: 4px;
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .toolbar-demo span {
            padding: 0.25rem 0.5rem;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 3px;
            font-size: 0.75rem;
        }
        
        .overlap-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 60px;
            height: 40px;
            background: rgba(239, 68, 68, 0.2);
            border: 2px dashed #ef4444;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            color: #ef4444;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-robot"></i> AI助手按钮位置调整测试</h1>
            <p>测试AI助手悬浮按钮位置调整，确保不遮挡编辑器工具栏</p>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-edit"></i> 编辑器界面模拟</h3>
            <p>模拟章节编辑页面，展示AI助手按钮的新位置</p>
            
            <div class="editor-simulation">
                <!-- 模拟编辑器工具栏 -->
                <div class="editor-toolbar">
                    <button class="toolbar-button"><i class="fas fa-bold"></i></button>
                    <button class="toolbar-button"><i class="fas fa-italic"></i></button>
                    <button class="toolbar-button"><i class="fas fa-underline"></i></button>
                    <button class="toolbar-button"><i class="fas fa-list-ul"></i></button>
                    <button class="toolbar-button"><i class="fas fa-list-ol"></i></button>
                    <button class="toolbar-button"><i class="fas fa-link"></i></button>
                    <button class="toolbar-button"><i class="fas fa-image"></i></button>
                </div>
                
                <!-- 模拟编辑器内容区域 -->
                <div class="editor-content">
                    <h4>2.3 解构器架构模型</h4>
                    <p>学习目标</p>
                    <br>
                    <p>主要内容</p>
                    <br>
                    <p>请在此处编写章节内容...</p>
                </div>
                
                <!-- AI助手按钮 - 新位置 -->
                <div class="ai-fab">
                    <button class="fab-button" title="AI助手">
                        <i class="fas fa-robot"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-compare"></i> 位置对比</h3>
            <p>对比调整前后的按钮位置，验证是否解决了遮挡问题</p>
            
            <div class="position-comparison">
                <div class="position-demo">
                    <div class="demo-title old-position">调整前 (top: 20px)</div>
                    <div class="toolbar-demo">
                        <span>B</span>
                        <span>I</span>
                        <span>U</span>
                        <span>•</span>
                        <span>1.</span>
                        <span>🔗</span>
                        <span>📷</span>
                    </div>
                    <div class="overlap-indicator">遮挡</div>
                    <div class="fab-demo-old">
                        <i class="fas fa-robot"></i>
                    </div>
                    <p style="margin-top: 2rem; font-size: 0.875rem; color: #6b7280;">
                        ❌ 按钮位置过高，遮挡了编辑器工具栏
                    </p>
                </div>
                
                <div class="position-demo">
                    <div class="demo-title new-position">调整后 (top: 80px)</div>
                    <div class="toolbar-demo">
                        <span>B</span>
                        <span>I</span>
                        <span>U</span>
                        <span>•</span>
                        <span>1.</span>
                        <span>🔗</span>
                        <span>📷</span>
                    </div>
                    <div class="fab-demo-new">
                        <i class="fas fa-robot"></i>
                    </div>
                    <p style="margin-top: 2rem; font-size: 0.875rem; color: #6b7280;">
                        ✅ 按钮位置适中，不遮挡工具栏，便于访问
                    </p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> 修复验证</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                <div style="padding: 1rem; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px;">
                    <h4 style="color: #166534; margin: 0 0 0.5rem 0;">
                        <i class="fas fa-check"></i> 问题解决
                    </h4>
                    <ul style="margin: 0; color: #166534; font-size: 0.875rem;">
                        <li>AI助手按钮不再遮挡工具栏</li>
                        <li>按钮位置仍然便于用户访问</li>
                        <li>保持了原有的悬浮效果</li>
                    </ul>
                </div>
                
                <div style="padding: 1rem; background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 8px;">
                    <h4 style="color: #1d4ed8; margin: 0 0 0.5rem 0;">
                        <i class="fas fa-info-circle"></i> 技术细节
                    </h4>
                    <ul style="margin: 0; color: #1d4ed8; font-size: 0.875rem;">
                        <li>调整 .ai-fab 的 top 值从 20px 到 80px</li>
                        <li>保持 z-index: 1000 确保层级正确</li>
                        <li>维持响应式设计兼容性</li>
                    </ul>
                </div>
                
                <div style="padding: 1rem; background: #fefce8; border: 1px solid #fde047; border-radius: 8px;">
                    <h4 style="color: #a16207; margin: 0 0 0.5rem 0;">
                        <i class="fas fa-lightbulb"></i> 用户体验
                    </h4>
                    <ul style="margin: 0; color: #a16207; font-size: 0.875rem;">
                        <li>编辑器工具栏完全可见</li>
                        <li>AI助手按钮易于点击</li>
                        <li>界面布局更加合理</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
