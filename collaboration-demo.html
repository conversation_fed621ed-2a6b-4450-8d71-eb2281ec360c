<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多用户协作功能演示 - 《大模型技术与油气应用概论》协作系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
        }
        
        .demo-title {
            font-size: 3rem;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .demo-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .demo-description {
            font-size: 1.1rem;
            opacity: 0.8;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-demo {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-demo:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: #4f46e5;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
        }
        
        .feature-title {
            font-size: 1.5rem;
            color: #1f2937;
            margin: 0;
            font-weight: 600;
        }
        
        .feature-content {
            margin-bottom: 25px;
        }
        
        .feature-description {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-highlights {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-highlights li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: #374151;
        }
        
        .feature-highlights i {
            color: #10b981;
            font-size: 0.9rem;
        }
        
        .feature-actions {
            display: flex;
            gap: 15px;
        }
        
        .demo-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }
        
        .demo-btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .demo-btn-primary:hover {
            background: #4338ca;
            color: white;
        }
        
        .demo-btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .demo-btn-secondary:hover {
            background: #e5e7eb;
            color: #1f2937;
        }
        
        .workflow-demo {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            padding: 40px;
            margin-bottom: 40px;
        }
        
        .workflow-title {
            text-align: center;
            font-size: 2rem;
            color: #1f2937;
            margin-bottom: 30px;
        }
        
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .workflow-step {
            text-align: center;
            padding: 20px;
            border-radius: 12px;
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .workflow-step:hover {
            border-color: #4f46e5;
            background: #f8faff;
        }
        
        .step-number {
            width: 50px;
            height: 50px;
            background: #4f46e5;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            margin: 0 auto 15px;
        }
        
        .step-title {
            font-size: 1.1rem;
            color: #1f2937;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .step-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .quick-start {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 16px;
            padding: 40px;
            text-align: center;
        }
        
        .quick-start h2 {
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .quick-start p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .quick-start-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .quick-start-btn {
            padding: 15px 30px;
            background: white;
            color: #059669;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .quick-start-btn:hover {
            background: #f0fdf4;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            color: #047857;
        }
        
        .status-indicators {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .status-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-size: 1.2rem;
        }
        
        .status-complete {
            background: #ecfdf5;
            color: #059669;
        }
        
        .status-progress {
            background: #fef3c7;
            color: #d97706;
        }
        
        .status-pending {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .status-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .status-description {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .demo-title {
                font-size: 2rem;
            }
            
            .features-showcase {
                grid-template-columns: 1fr;
            }
            
            .workflow-steps {
                grid-template-columns: 1fr;
            }
            
            .quick-start-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 演示头部 -->
        <div class="demo-header">
            <h1 class="demo-title">
                <i class="fas fa-users"></i> 多用户协作功能演示
            </h1>
            <p class="demo-subtitle">《大模型技术与油气应用概论》智能编纂系统</p>
            <p class="demo-description">
                体验专业级的团队协作编著功能，支持5-10人团队高效协作，
                包含完整的用户角色体系、权限控制、实时协作和审核流程。
            </p>
        </div>
        
        <!-- 功能状态指示器 -->
        <div class="status-indicators">
            <div class="status-card">
                <div class="status-icon status-complete">
                    <i class="fas fa-check"></i>
                </div>
                <div class="status-title">用户管理系统</div>
                <div class="status-description">已完成</div>
            </div>
            
            <div class="status-card">
                <div class="status-icon status-complete">
                    <i class="fas fa-check"></i>
                </div>
                <div class="status-title">权限控制</div>
                <div class="status-description">已完成</div>
            </div>
            
            <div class="status-card">
                <div class="status-icon status-complete">
                    <i class="fas fa-check"></i>
                </div>
                <div class="status-title">协作编辑</div>
                <div class="status-description">已完成</div>
            </div>
            
            <div class="status-card">
                <div class="status-icon status-progress">
                    <i class="fas fa-cog fa-spin"></i>
                </div>
                <div class="status-title">实时同步</div>
                <div class="status-description">优化中</div>
            </div>
        </div>
        
        <!-- 功能展示 -->
        <div class="features-showcase">
            <!-- 用户角色管理 -->
            <div class="feature-demo">
                <div class="feature-header">
                    <div class="feature-icon">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <h3 class="feature-title">用户角色管理</h3>
                </div>
                <div class="feature-content">
                    <p class="feature-description">
                        完整的5级用户角色体系，从项目所有者到审阅者，每个角色都有明确的权限边界和职责分工。
                    </p>
                    <ul class="feature-highlights">
                        <li><i class="fas fa-check"></i> 项目所有者：全权管理</li>
                        <li><i class="fas fa-check"></i> 管理员：成员和项目管理</li>
                        <li><i class="fas fa-check"></i> 编辑者：内容编辑和审核</li>
                        <li><i class="fas fa-check"></i> 作者：章节编写</li>
                        <li><i class="fas fa-check"></i> 审阅者：内容审阅</li>
                    </ul>
                </div>
                <div class="feature-actions">
                    <a href="user-management.html" class="demo-btn demo-btn-primary">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                    <a href="create-test-users.html" class="demo-btn demo-btn-secondary">
                        <i class="fas fa-plus"></i> 创建测试用户
                    </a>
                </div>
            </div>
            
            <!-- 邀请协作系统 -->
            <div class="feature-demo">
                <div class="feature-header">
                    <div class="feature-icon">
                        <i class="fas fa-envelope-open"></i>
                    </div>
                    <h3 class="feature-title">邀请协作系统</h3>
                </div>
                <div class="feature-content">
                    <p class="feature-description">
                        智能的用户邀请系统，支持邮箱邀请、角色预设、过期管理，让团队组建变得简单高效。
                    </p>
                    <ul class="feature-highlights">
                        <li><i class="fas fa-check"></i> 邮箱邀请链接</li>
                        <li><i class="fas fa-check"></i> 角色预分配</li>
                        <li><i class="fas fa-check"></i> 邀请状态跟踪</li>
                        <li><i class="fas fa-check"></i> 自动过期管理</li>
                    </ul>
                </div>
                <div class="feature-actions">
                    <a href="accept-invitation.html?token=demo" class="demo-btn demo-btn-primary">
                        <i class="fas fa-link"></i> 邀请演示
                    </a>
                    <button class="demo-btn demo-btn-secondary" onclick="showInviteDemo()">
                        <i class="fas fa-play"></i> 流程演示
                    </button>
                </div>
            </div>
            
            <!-- 权限控制矩阵 -->
            <div class="feature-demo">
                <div class="feature-header">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">权限控制矩阵</h3>
                </div>
                <div class="feature-content">
                    <p class="feature-description">
                        细粒度的权限控制系统，确保不同角色用户只能访问授权的功能和数据，保障项目安全。
                    </p>
                    <ul class="feature-highlights">
                        <li><i class="fas fa-check"></i> 功能级权限控制</li>
                        <li><i class="fas fa-check"></i> 数据访问限制</li>
                        <li><i class="fas fa-check"></i> 行级安全策略</li>
                        <li><i class="fas fa-check"></i> 操作审计日志</li>
                    </ul>
                </div>
                <div class="feature-actions">
                    <button class="demo-btn demo-btn-primary" onclick="showPermissionMatrix()">
                        <i class="fas fa-table"></i> 权限矩阵
                    </button>
                    <button class="demo-btn demo-btn-secondary" onclick="testPermissions()">
                        <i class="fas fa-vial"></i> 权限测试
                    </button>
                </div>
            </div>
            
            <!-- 实时协作编辑 -->
            <div class="feature-demo">
                <div class="feature-header">
                    <div class="feature-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h3 class="feature-title">实时协作编辑</h3>
                </div>
                <div class="feature-content">
                    <p class="feature-description">
                        支持多用户同时在线编辑，实时同步内容变更，智能冲突检测和版本控制，确保协作顺畅。
                    </p>
                    <ul class="feature-highlights">
                        <li><i class="fas fa-check"></i> 多用户同时编辑</li>
                        <li><i class="fas fa-check"></i> 实时内容同步</li>
                        <li><i class="fas fa-check"></i> 冲突检测解决</li>
                        <li><i class="fas fa-check"></i> 版本历史管理</li>
                    </ul>
                </div>
                <div class="feature-actions">
                    <a href="index.html" class="demo-btn demo-btn-primary">
                        <i class="fas fa-pen"></i> 开始编辑
                    </a>
                    <button class="demo-btn demo-btn-secondary" onclick="showCollabDemo()">
                        <i class="fas fa-users-cog"></i> 协作演示
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 工作流程演示 -->
        <div class="workflow-demo">
            <h2 class="workflow-title">协作工作流程</h2>
            <div class="workflow-steps">
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div class="step-title">项目创建</div>
                    <div class="step-description">项目所有者创建新的书籍项目，设置基本信息和大纲结构</div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div class="step-title">团队组建</div>
                    <div class="step-description">邀请团队成员加入项目，分配合适的角色和权限</div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div class="step-title">任务分配</div>
                    <div class="step-description">编辑者将章节分配给作者，设置截止时间和要求</div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div class="step-title">协作编写</div>
                    <div class="step-description">作者编写分配的章节，支持实时协作和版本控制</div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">5</div>
                    <div class="step-title">审核反馈</div>
                    <div class="step-description">审阅者和编辑者审核内容，提供修改建议和评论</div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">6</div>
                    <div class="step-title">发布完成</div>
                    <div class="step-description">内容审核通过后发布，完成协作编著流程</div>
                </div>
            </div>
        </div>
        
        <!-- 快速开始 -->
        <div class="quick-start">
            <h2><i class="fas fa-rocket"></i> 立即开始体验</h2>
            <p>选择下面的方式开始体验多用户协作功能</p>
            <div class="quick-start-actions">
                <a href="create-test-users.html" class="quick-start-btn">
                    <i class="fas fa-users-cog"></i> 创建测试用户
                </a>
                <a href="collaboration-test.html" class="quick-start-btn">
                    <i class="fas fa-vial"></i> 功能测试
                </a>
                <a href="project-management.html" class="quick-start-btn">
                    <i class="fas fa-project-diagram"></i> 项目管理
                </a>
                <a href="index.html" class="quick-start-btn">
                    <i class="fas fa-edit"></i> 开始编辑
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // 演示功能
        function showInviteDemo() {
            alert('邀请流程演示：\n1. 项目管理员发送邀请\n2. 被邀请用户收到邮件\n3. 点击链接接受邀请\n4. 自动加入项目团队');
        }
        
        function showPermissionMatrix() {
            window.open('user-management.html', '_blank');
        }
        
        function testPermissions() {
            alert('权限测试：\n1. 使用不同角色账户登录\n2. 尝试访问不同功能\n3. 验证权限边界\n4. 检查数据访问限制');
        }
        
        function showCollabDemo() {
            alert('协作演示：\n1. 多个用户同时登录\n2. 编辑同一章节内容\n3. 观察实时同步效果\n4. 测试冲突检测机制');
        }
    </script>
</body>
</html>
