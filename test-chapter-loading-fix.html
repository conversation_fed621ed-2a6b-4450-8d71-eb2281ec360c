<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节加载修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #1e293b;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin: 5px;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-success {
            background: #059669;
            color: white;
        }
        
        .btn-warning {
            background: #d97706;
            color: white;
        }
        
        .btn-info {
            background: #0891b2;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .chapter-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 10px;
            margin: 15px 0;
        }
        
        .chapter-item {
            padding: 10px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .chapter-item:hover {
            background-color: #f8fafc;
        }
        
        .chapter-item:last-child {
            border-bottom: none;
        }
        
        .chapter-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .chapter-info {
            font-size: 12px;
            color: #6b7280;
        }
        
        .log-container {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 3px;
        }
        
        .log-entry.success {
            color: #10b981;
        }
        
        .log-entry.error {
            color: #ef4444;
        }
        
        .log-entry.warning {
            color: #f59e0b;
        }
        
        .log-entry.info {
            color: #3b82f6;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .alert-info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .status-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #1e293b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 章节加载修复测试</h1>
            <p>测试和验证章节内容从数据库正确加载到编辑器的功能</p>
        </div>
        
        <div class="alert alert-info">
            <strong>测试说明：</strong>此页面用于测试章节加载修复功能，验证章节内容能否正确从数据库加载到编辑器中。
        </div>
        
        <!-- 连接状态 -->
        <div class="test-section">
            <h3>🔌 系统状态检查</h3>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-label">Supabase连接</div>
                    <div class="status-value" id="supabaseStatus">检查中...</div>
                </div>
                <div class="status-card">
                    <div class="status-label">用户登录</div>
                    <div class="status-value" id="userStatus">检查中...</div>
                </div>
                <div class="status-card">
                    <div class="status-label">项目ID</div>
                    <div class="status-value" id="projectStatus">检查中...</div>
                </div>
                <div class="status-card">
                    <div class="status-label">编辑器状态</div>
                    <div class="status-value" id="editorStatus">检查中...</div>
                </div>
            </div>
            <button class="btn btn-primary" onclick="checkSystemStatus()">重新检查</button>
        </div>
        
        <!-- 章节列表 -->
        <div class="test-section">
            <h3>📚 可用章节列表</h3>
            <p>点击章节进行加载测试</p>
            <button class="btn btn-info" onclick="loadChapterList()">刷新章节列表</button>
            <div class="chapter-list" id="chapterList">
                <div class="chapter-item">正在加载章节列表...</div>
            </div>
        </div>
        
        <!-- 调试工具 -->
        <div class="test-section">
            <h3>🔍 调试工具</h3>
            <p>深入分析章节加载过程</p>
            <div style="margin: 15px 0;">
                <label>大纲ID：</label>
                <input type="text" id="debugOutlineId" placeholder="输入大纲ID进行调试" style="width: 300px; padding: 8px; margin-left: 10px;">
                <button class="btn btn-warning" onclick="debugSpecificChapter()">调试指定章节</button>
            </div>
            <button class="btn btn-info" onclick="debugCurrentChapter()">调试当前章节</button>
            <button class="btn btn-success" onclick="testChapterSaveLoad()">测试保存加载循环</button>
        </div>
        
        <!-- 日志输出 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button class="btn btn-primary" onclick="clearLog()">清空日志</button>
            <div class="log-container" id="logContainer">
                <div class="log-entry">等待测试操作...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-config.js"></script>
    <script>
        // 日志系统
        const logs = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `<div class="log-entry ${type}">[${timestamp}] ${message}</div>`;
            logs.push(logEntry);
            
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = logs.join('');
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            logs.length = 0;
            document.getElementById('logContainer').innerHTML = '<div class="log-entry">日志已清空</div>';
        }
        
        // 检查系统状态
        async function checkSystemStatus() {
            addLog('开始检查系统状态...', 'info');
            
            try {
                // 检查Supabase连接
                if (supabaseManager && supabaseManager.supabase) {
                    document.getElementById('supabaseStatus').textContent = '✅ 正常';
                    addLog('Supabase连接正常', 'success');
                } else {
                    document.getElementById('supabaseStatus').textContent = '❌ 异常';
                    addLog('Supabase连接异常', 'error');
                    return;
                }
                
                // 检查用户登录
                const user = await supabaseManager.getCurrentUser();
                if (user) {
                    document.getElementById('userStatus').textContent = '✅ 已登录';
                    addLog(`用户已登录: ${user.email}`, 'success');
                } else {
                    document.getElementById('userStatus').textContent = '❌ 未登录';
                    addLog('用户未登录', 'warning');
                }
                
                // 检查项目ID
                if (typeof ensureProjectId === 'function') {
                    const projectId = await ensureProjectId();
                    if (projectId) {
                        document.getElementById('projectStatus').textContent = '✅ 已设置';
                        addLog(`项目ID: ${projectId}`, 'success');
                    } else {
                        document.getElementById('projectStatus').textContent = '❌ 未设置';
                        addLog('项目ID未设置', 'error');
                    }
                } else {
                    document.getElementById('projectStatus').textContent = '❌ 函数未找到';
                    addLog('ensureProjectId函数未找到', 'error');
                }
                
                // 检查编辑器状态
                if (typeof window.parent !== 'undefined' && window.parent.quillEditor) {
                    document.getElementById('editorStatus').textContent = '✅ 可用';
                    addLog('Quill编辑器可用', 'success');
                } else {
                    document.getElementById('editorStatus').textContent = '❌ 不可用';
                    addLog('Quill编辑器不可用', 'warning');
                }
                
            } catch (error) {
                addLog(`系统状态检查失败: ${error.message}`, 'error');
            }
        }
        
        // 加载章节列表
        async function loadChapterList() {
            addLog('开始加载章节列表...', 'info');
            
            try {
                const { data: chapters, error } = await supabaseManager.supabase
                    .from('chapters')
                    .select('id, title, outline_id, word_count, updated_at')
                    .order('updated_at', { ascending: false })
                    .limit(20);
                
                if (error) throw error;
                
                const chapterListDiv = document.getElementById('chapterList');
                
                if (!chapters || chapters.length === 0) {
                    chapterListDiv.innerHTML = '<div class="chapter-item">没有找到章节</div>';
                    addLog('没有找到章节', 'warning');
                    return;
                }
                
                const chapterItems = chapters.map(chapter => `
                    <div class="chapter-item" onclick="testChapterLoading('${chapter.outline_id}', '${chapter.id}')">
                        <div class="chapter-title">${chapter.title}</div>
                        <div class="chapter-info">
                            章节ID: ${chapter.id} | 大纲ID: ${chapter.outline_id || '未设置'} | 
                            字数: ${chapter.word_count || 0} | 更新: ${new Date(chapter.updated_at).toLocaleString()}
                        </div>
                    </div>
                `).join('');
                
                chapterListDiv.innerHTML = chapterItems;
                addLog(`加载了 ${chapters.length} 个章节`, 'success');
                
            } catch (error) {
                addLog(`加载章节列表失败: ${error.message}`, 'error');
            }
        }
        
        // 测试章节加载
        async function testChapterLoading(outlineId, chapterId) {
            addLog(`开始测试章节加载: ${outlineId}`, 'info');
            
            try {
                if (typeof debugChapterLoading === 'function') {
                    const result = await debugChapterLoading(outlineId);
                    if (result) {
                        addLog('章节加载测试完成', 'success');
                        addLog(`  - 项目ID: ${result.projectId}`, 'info');
                        addLog(`  - 章节ID: ${result.chapterId}`, 'info');
                        addLog(`  - 数据存在: ${result.chapterData ? '是' : '否'}`, result.chapterData ? 'success' : 'warning');
                        
                        if (result.chapterData && result.chapterData.ops) {
                            addLog(`  - Delta操作数: ${result.chapterData.ops.length}`, 'success');
                        }
                    } else {
                        addLog('章节加载测试失败', 'error');
                    }
                } else {
                    addLog('debugChapterLoading函数未找到，请确保已加载app.js', 'error');
                }
                
            } catch (error) {
                addLog(`章节加载测试异常: ${error.message}`, 'error');
            }
        }
        
        // 调试指定章节
        async function debugSpecificChapter() {
            const outlineId = document.getElementById('debugOutlineId').value.trim();
            if (!outlineId) {
                addLog('请输入大纲ID', 'warning');
                return;
            }
            
            await testChapterLoading(outlineId);
        }
        
        // 调试当前章节
        async function debugCurrentChapter() {
            try {
                if (typeof window.parent !== 'undefined' && window.parent.currentChapter) {
                    const currentChapter = window.parent.currentChapter;
                    addLog(`调试当前章节: ${currentChapter.title}`, 'info');
                    await testChapterLoading(currentChapter.outlineId, currentChapter.chapterId);
                } else {
                    addLog('没有当前章节，请先在主应用中选择一个章节', 'warning');
                }
            } catch (error) {
                addLog(`调试当前章节失败: ${error.message}`, 'error');
            }
        }
        
        // 测试保存加载循环
        async function testChapterSaveLoad() {
            addLog('开始测试保存加载循环...', 'info');
            
            try {
                if (typeof window.parent === 'undefined' || !window.parent.currentChapter) {
                    addLog('请先在主应用中选择一个章节', 'warning');
                    return;
                }
                
                const mainWindow = window.parent;
                const currentChapter = mainWindow.currentChapter;
                
                addLog(`测试章节: ${currentChapter.title}`, 'info');
                
                // 1. 保存当前内容
                if (typeof mainWindow.saveCurrentChapterContent === 'function') {
                    await mainWindow.saveCurrentChapterContent();
                    addLog('保存当前内容完成', 'success');
                } else {
                    addLog('saveCurrentChapterContent函数未找到', 'error');
                    return;
                }
                
                // 2. 重新加载内容
                if (typeof mainWindow.loadChapterContent === 'function') {
                    await mainWindow.loadChapterContent(currentChapter);
                    addLog('重新加载内容完成', 'success');
                } else {
                    addLog('loadChapterContent函数未找到', 'error');
                    return;
                }
                
                addLog('保存加载循环测试完成', 'success');
                
            } catch (error) {
                addLog(`保存加载循环测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', async () => {
            addLog('测试页面已加载', 'info');
            await checkSystemStatus();
            await loadChapterList();
        });
    </script>
</body>
</html>
