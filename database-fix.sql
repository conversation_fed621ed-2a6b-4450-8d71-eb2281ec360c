-- 数据库修复脚本
-- 修复用户管理页面的数据库字段问题

-- 1. 检查并添加缺失的字段到 user_profiles 表
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS global_role VARCHAR(20) DEFAULT 'user' CHECK (global_role IN ('system_admin', 'user'));

-- 2. 检查并添加缺失的字段到 project_members 表
ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS invited_by UUID REFERENCES public.user_profiles(id);

ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'invited'));

ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS invitation_token VARCHAR(255);

ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS invitation_expires_at TIMESTAMP WITH TIME ZONE;

-- 3. 更新项目成员角色约束（如果存在的话）
DO $$ 
BEGIN
    -- 删除旧的约束（如果存在）
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'project_members_role_check' 
               AND table_name = 'project_members') THEN
        ALTER TABLE public.project_members DROP CONSTRAINT project_members_role_check;
    END IF;
    
    -- 添加新的约束
    ALTER TABLE public.project_members 
    ADD CONSTRAINT project_members_role_check 
    CHECK (role IN ('owner', 'admin', 'editor', 'author', 'reviewer'));
END $$;

-- 4. 创建缺失的表（如果不存在）

-- 章节分配表
CREATE TABLE IF NOT EXISTS public.chapter_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('author', 'co_author', 'reviewer', 'editor')),
    assigned_by UUID REFERENCES public.user_profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deadline TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'assigned' CHECK (status IN ('assigned', 'accepted', 'in_progress', 'completed', 'reviewed')),
    notes TEXT,
    UNIQUE(chapter_id, user_id, role)
);

-- 审核流程表
CREATE TABLE IF NOT EXISTS public.review_processes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'revision_required')),
    review_notes TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户邀请表
CREATE TABLE IF NOT EXISTS public.user_invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    invited_by UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'editor', 'author', 'reviewer')),
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建缺失的索引
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_chapter ON public.chapter_assignments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_user ON public.chapter_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_review_processes_chapter ON public.review_processes(chapter_id);
CREATE INDEX IF NOT EXISTS idx_review_processes_reviewer ON public.review_processes(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_email ON public.user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_user_invitations_token ON public.user_invitations(invitation_token);

-- 6. 启用行级安全（如果尚未启用）
ALTER TABLE public.chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_processes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- 7. 更新现有数据（确保项目所有者在 project_members 表中）
INSERT INTO public.project_members (project_id, user_id, role, status, joined_at)
SELECT 
    p.id as project_id,
    p.owner_id as user_id,
    'owner' as role,
    'active' as status,
    p.created_at as joined_at
FROM public.projects p
WHERE NOT EXISTS (
    SELECT 1 FROM public.project_members pm 
    WHERE pm.project_id = p.id AND pm.user_id = p.owner_id
)
ON CONFLICT (project_id, user_id) DO NOTHING;

-- 8. 验证修复结果
DO $$ 
DECLARE
    table_count INTEGER;
    column_count INTEGER;
BEGIN
    -- 检查关键表是否存在
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN ('user_profiles', 'project_members', 'chapter_assignments', 'user_invitations', 'review_processes');
    
    -- 检查关键字段是否存在
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'user_profiles'
    AND column_name IN ('last_login_at', 'is_active', 'global_role');
    
    RAISE NOTICE '修复完成: 找到 % 个关键表, % 个关键字段', table_count, column_count;
END $$;

-- 完成
DO $$
BEGIN
    EXECUTE 'COMMENT ON SCHEMA public IS ''数据库修复完成于 ' || NOW() || '''';
END $$;
