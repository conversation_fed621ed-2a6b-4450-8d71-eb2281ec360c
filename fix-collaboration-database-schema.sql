-- 修复协作编著功能的数据库结构问题
-- 执行此脚本以确保协作编著功能能够正常使用真实数据

-- 1. 为 chapters 表添加缺失的字段
ALTER TABLE public.chapters 
ADD COLUMN IF NOT EXISTS order_index INTEGER DEFAULT 0;

-- 2. 更新现有章节的 order_index 值（按创建时间排序）
UPDATE public.chapters 
SET order_index = subquery.row_number 
FROM (
    SELECT id, ROW_NUMBER() OVER (PARTITION BY project_id ORDER BY created_at) as row_number
    FROM public.chapters
) AS subquery 
WHERE public.chapters.id = subquery.id;

-- 3. 确保 chapter_assignments 表结构正确
-- 检查并创建 chapter_assignments 表（如果不存在）
CREATE TABLE IF NOT EXISTS public.chapter_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    
    -- 角色分配
    lead_author_id UUID REFERENCES public.user_profiles(id),
    reviewer_id UUID REFERENCES public.user_profiles(id),
    assigned_by UUID NOT NULL REFERENCES public.user_profiles(id),
    
    -- 任务信息
    title TEXT NOT NULL,
    description TEXT,
    requirements TEXT,
    word_count_target INTEGER DEFAULT 0,
    
    -- 时间管理
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    due_date TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    submitted_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- 状态管理
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending',      -- 待确认
        'accepted',     -- 已接受
        'in_progress',  -- 进行中
        'submitted',    -- 已提交
        'reviewing',    -- 审核中
        'revising',     -- 修改中
        'approved',     -- 已批准
        'completed',    -- 已完成
        'rejected'      -- 已拒绝
    )),
    
    -- 优先级
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建 chapter_collaborators 表（如果不存在）
CREATE TABLE IF NOT EXISTS public.chapter_collaborators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL REFERENCES public.chapter_assignments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    
    -- 角色类型
    role TEXT NOT NULL CHECK (role IN (
        'co_author',           -- 协作作者
        'reviewer',            -- 审稿人
        'editorial_assistant'  -- 编辑助理
    )),
    
    -- 具体职责
    responsibilities TEXT,
    sections TEXT[], -- 负责的具体章节部分
    
    -- 状态
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending',    -- 待确认
        'accepted',   -- 已接受
        'declined',   -- 已拒绝
        'active',     -- 活跃中
        'completed'   -- 已完成
    )),
    
    -- 元数据
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(assignment_id, user_id, role)
);

-- 5. 修复 outlines 表的 sort_order 字段（如果不存在）
ALTER TABLE public.outlines 
ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0;

-- 更新现有大纲的 sort_order 值
UPDATE public.outlines 
SET sort_order = subquery.row_number 
FROM (
    SELECT id, ROW_NUMBER() OVER (PARTITION BY project_id ORDER BY created_at) as row_number
    FROM public.outlines
) AS subquery 
WHERE public.outlines.id = subquery.id;

-- 6. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_chapters_project_order ON public.chapters(project_id, order_index);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_project ON public.chapter_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_lead_author ON public.chapter_assignments(lead_author_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_reviewer ON public.chapter_assignments(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_chapter_collaborators_assignment ON public.chapter_collaborators(assignment_id);
CREATE INDEX IF NOT EXISTS idx_chapter_collaborators_user ON public.chapter_collaborators(user_id);
CREATE INDEX IF NOT EXISTS idx_outlines_project_sort ON public.outlines(project_id, sort_order);

-- 7. 启用行级安全策略 (RLS)
ALTER TABLE public.chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_collaborators ENABLE ROW LEVEL SECURITY;

-- 8. 创建 RLS 策略
-- 章节分配的 RLS 策略
DROP POLICY IF EXISTS "Users can view their chapter assignments" ON public.chapter_assignments;
CREATE POLICY "Users can view their chapter assignments" ON public.chapter_assignments
    FOR SELECT USING (
        lead_author_id = auth.uid() OR
        reviewer_id = auth.uid() OR
        assigned_by = auth.uid() OR
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

DROP POLICY IF EXISTS "Users can create chapter assignments" ON public.chapter_assignments;
CREATE POLICY "Users can create chapter assignments" ON public.chapter_assignments
    FOR INSERT WITH CHECK (
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid() 
            AND status = 'active'
            AND role IN ('owner', 'admin', 'editor')
        )
    );

DROP POLICY IF EXISTS "Users can update their chapter assignments" ON public.chapter_assignments;
CREATE POLICY "Users can update their chapter assignments" ON public.chapter_assignments
    FOR UPDATE USING (
        lead_author_id = auth.uid() OR
        assigned_by = auth.uid() OR
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid() 
            AND status = 'active'
            AND role IN ('owner', 'admin', 'editor')
        )
    );

-- 协作者的 RLS 策略
DROP POLICY IF EXISTS "Users can view chapter collaborators" ON public.chapter_collaborators;
CREATE POLICY "Users can view chapter collaborators" ON public.chapter_collaborators
    FOR SELECT USING (
        user_id = auth.uid() OR
        assignment_id IN (
            SELECT id FROM public.chapter_assignments 
            WHERE lead_author_id = auth.uid() 
            OR assigned_by = auth.uid()
            OR project_id IN (
                SELECT project_id FROM public.project_members 
                WHERE user_id = auth.uid() AND status = 'active'
            )
        )
    );

DROP POLICY IF EXISTS "Users can manage chapter collaborators" ON public.chapter_collaborators;
CREATE POLICY "Users can manage chapter collaborators" ON public.chapter_collaborators
    FOR ALL USING (
        assignment_id IN (
            SELECT id FROM public.chapter_assignments 
            WHERE assigned_by = auth.uid()
            OR project_id IN (
                SELECT project_id FROM public.project_members 
                WHERE user_id = auth.uid() 
                AND status = 'active'
                AND role IN ('owner', 'admin', 'editor')
            )
        )
    );

-- 9. 创建触发器函数来自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
DROP TRIGGER IF EXISTS update_chapter_assignments_updated_at ON public.chapter_assignments;
CREATE TRIGGER update_chapter_assignments_updated_at
    BEFORE UPDATE ON public.chapter_assignments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_chapter_collaborators_updated_at ON public.chapter_collaborators;
CREATE TRIGGER update_chapter_collaborators_updated_at
    BEFORE UPDATE ON public.chapter_collaborators
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 10. 插入一些示例数据（可选，用于测试）
-- 注意：这部分只在没有现有数据时执行

-- 检查是否需要创建示例章节分配
DO $$
DECLARE
    project_count INTEGER;
    assignment_count INTEGER;
    sample_project_id UUID;
    sample_user_id UUID;
BEGIN
    -- 检查是否有项目和用户数据
    SELECT COUNT(*) INTO project_count FROM public.projects;
    SELECT COUNT(*) INTO assignment_count FROM public.chapter_assignments;
    
    IF project_count > 0 AND assignment_count = 0 THEN
        -- 获取第一个项目和用户
        SELECT id INTO sample_project_id FROM public.projects LIMIT 1;
        SELECT id INTO sample_user_id FROM public.user_profiles LIMIT 1;
        
        IF sample_project_id IS NOT NULL AND sample_user_id IS NOT NULL THEN
            RAISE NOTICE '发现项目但没有章节分配，可以通过应用程序创建默认分配';
        END IF;
    END IF;
END $$;

-- 11. 修复可能存在的数据不一致问题

-- 确保所有章节都有有效的 project_id
UPDATE public.chapters
SET project_id = (
    SELECT project_id
    FROM public.outlines
    WHERE outlines.id = chapters.outline_id
    LIMIT 1
)
WHERE project_id IS NULL
AND outline_id IS NOT NULL;

-- 清理无效的章节分配（引用不存在的章节）
DELETE FROM public.chapter_assignments
WHERE chapter_id IS NOT NULL
AND chapter_id NOT IN (SELECT id FROM public.chapters);

-- 清理无效的协作者记录（引用不存在的分配）
DELETE FROM public.chapter_collaborators
WHERE assignment_id NOT IN (SELECT id FROM public.chapter_assignments);

-- 12. 创建视图以简化查询

-- 章节分配详细视图
CREATE OR REPLACE VIEW chapter_assignments_detailed AS
SELECT
    ca.*,
    c.title as chapter_title,
    c.status as chapter_status,
    c.word_count as chapter_word_count,
    la.full_name as lead_author_name,
    la.email as lead_author_email,
    r.full_name as reviewer_name,
    r.email as reviewer_email,
    ab.full_name as assigned_by_name,
    ab.email as assigned_by_email,
    p.title as project_title
FROM public.chapter_assignments ca
LEFT JOIN public.chapters c ON ca.chapter_id = c.id
LEFT JOIN public.user_profiles la ON ca.lead_author_id = la.id
LEFT JOIN public.user_profiles r ON ca.reviewer_id = r.id
LEFT JOIN public.user_profiles ab ON ca.assigned_by = ab.id
LEFT JOIN public.projects p ON ca.project_id = p.id;

-- 项目协作统计视图
CREATE OR REPLACE VIEW project_collaboration_stats AS
SELECT
    p.id as project_id,
    p.title as project_title,
    COUNT(DISTINCT ca.id) as total_assignments,
    COUNT(DISTINCT CASE WHEN ca.status = 'completed' THEN ca.id END) as completed_assignments,
    COUNT(DISTINCT CASE WHEN ca.status IN ('pending', 'accepted') THEN ca.id END) as pending_assignments,
    COUNT(DISTINCT CASE WHEN ca.status = 'in_progress' THEN ca.id END) as active_assignments,
    COUNT(DISTINCT ca.lead_author_id) as unique_authors,
    COUNT(DISTINCT cc.user_id) as unique_collaborators,
    AVG(CASE WHEN ca.status = 'completed' AND ca.completed_at IS NOT NULL
        THEN EXTRACT(EPOCH FROM (ca.completed_at - ca.assigned_at))/86400 END) as avg_completion_days
FROM public.projects p
LEFT JOIN public.chapter_assignments ca ON p.id = ca.project_id
LEFT JOIN public.chapter_collaborators cc ON ca.id = cc.assignment_id
GROUP BY p.id, p.title;

-- 13. 创建函数来自动分配章节

CREATE OR REPLACE FUNCTION create_default_chapter_assignments(
    target_project_id UUID,
    default_author_id UUID DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    assignment_count INTEGER := 0;
    chapter_record RECORD;
    outline_record RECORD;
    project_owner_id UUID;
BEGIN
    -- 获取项目所有者
    SELECT owner_id INTO project_owner_id
    FROM public.projects
    WHERE id = target_project_id;

    -- 如果没有指定默认作者，使用项目所有者
    IF default_author_id IS NULL THEN
        default_author_id := project_owner_id;
    END IF;

    -- 为没有分配的章节创建默认分配
    FOR chapter_record IN
        SELECT c.*
        FROM public.chapters c
        WHERE c.project_id = target_project_id
        AND c.id NOT IN (
            SELECT chapter_id
            FROM public.chapter_assignments
            WHERE chapter_id IS NOT NULL
        )
    LOOP
        INSERT INTO public.chapter_assignments (
            chapter_id,
            project_id,
            title,
            description,
            lead_author_id,
            reviewer_id,
            assigned_by,
            due_date,
            status,
            priority,
            word_count_target
        ) VALUES (
            chapter_record.id,
            target_project_id,
            chapter_record.title,
            COALESCE(chapter_record.summary, '请完成本章节的编写工作'),
            default_author_id,
            project_owner_id,
            project_owner_id,
            NOW() + INTERVAL '7 days',
            'pending',
            'medium',
            3000
        );

        assignment_count := assignment_count + 1;
    END LOOP;

    -- 为没有对应章节的大纲项创建分配（仅限一级大纲）
    FOR outline_record IN
        SELECT o.*
        FROM public.outlines o
        WHERE o.project_id = target_project_id
        AND o.level = 1
        AND o.id NOT IN (
            SELECT DISTINCT outline_id
            FROM public.chapters
            WHERE outline_id IS NOT NULL
        )
        AND o.title NOT IN (
            SELECT title
            FROM public.chapter_assignments
            WHERE project_id = target_project_id
        )
    LOOP
        INSERT INTO public.chapter_assignments (
            chapter_id,
            project_id,
            title,
            description,
            lead_author_id,
            reviewer_id,
            assigned_by,
            due_date,
            status,
            priority,
            word_count_target
        ) VALUES (
            NULL, -- 大纲项暂时没有对应的章节
            target_project_id,
            outline_record.title,
            COALESCE(outline_record.description, '请完成本章节的编写工作'),
            default_author_id,
            project_owner_id,
            project_owner_id,
            NOW() + INTERVAL '7 days',
            'pending',
            'medium',
            3000
        );

        assignment_count := assignment_count + 1;
    END LOOP;

    RETURN assignment_count;
END;
$$ LANGUAGE plpgsql;

-- 完成提示
SELECT 'Collaboration database schema fix completed successfully!' as status,
       'Execute: SELECT create_default_chapter_assignments(''your-project-id'');' as next_step;
