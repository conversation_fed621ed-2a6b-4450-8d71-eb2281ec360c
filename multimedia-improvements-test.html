<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多媒体功能改进测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8fafc;
        }

        .container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        h1 {
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .test-section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .test-section h3 {
            color: #374151;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .demo-area {
            position: relative;
            min-height: 200px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .generated-image {
            max-width: 100%;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .image-actions, .audio-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .audio-player-container {
            margin-bottom: 1rem;
        }

        .generated-audio {
            width: 100%;
            margin-bottom: 1rem;
        }

        /* 加载状态样式 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            z-index: 1000;
        }

        .loading-content {
            text-align: center;
            padding: 2rem;
        }

        .loading-spinner {
            margin-bottom: 1rem;
        }

        .spinner-ring {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-message {
            font-size: 1rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 1rem;
        }

        .loading-progress {
            width: 200px;
            margin: 0 auto;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
        }

        @keyframes progressAnimation {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .progress-text {
            font-size: 0.875rem;
            color: #6b7280;
            text-align: center;
        }

        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
        }

        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .info h3 {
            margin: 0 0 0.5rem 0;
            color: #0369a1;
        }

        .info p {
            margin: 0;
            color: #0c4a6e;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 多媒体功能改进测试</h1>

        <div class="info">
            <h3>改进内容</h3>
            <p>本页面用于测试多媒体功能的改进，包括：复制按钮、等待进度条、用户体验优化等。</p>
        </div>

        <!-- 图片生成测试 -->
        <div class="test-section">
            <h3>🖼️ 图片生成功能测试</h3>
            <button class="btn btn-primary" onclick="testImageGeneration()">模拟图片生成</button>
            <div class="demo-area" id="image-demo">
                <p>点击按钮测试图片生成功能...</p>
            </div>
        </div>

        <!-- 音频生成测试 -->
        <div class="test-section">
            <h3>🔊 音频生成功能测试</h3>
            <button class="btn btn-primary" onclick="testAudioGeneration()">模拟音频生成</button>
            <div class="demo-area" id="audio-demo">
                <p>点击按钮测试音频生成功能...</p>
            </div>
        </div>

        <!-- 文本转录测试 -->
        <div class="test-section">
            <h3>📝 文本转录功能测试</h3>
            <button class="btn btn-primary" onclick="testTranscription()">模拟文本转录</button>
            <div class="demo-area" id="transcription-demo">
                <p>点击按钮测试文本转录功能...</p>
            </div>
        </div>

        <!-- 加载状态测试 -->
        <div class="test-section">
            <h3>⏳ 加载状态测试</h3>
            <button class="btn btn-primary" onclick="testLoadingState()">测试加载状态</button>
            <div class="demo-area" id="loading-demo">
                <p>点击按钮测试加载状态...</p>
            </div>
        </div>

        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        // 模拟加载状态显示函数
        function showLoadingState(container, message, type = 'default') {
            const loadingHtml = `
                <div class="loading-overlay">
                    <div class="loading-content">
                        <div class="loading-spinner">
                            <div class="spinner-ring"></div>
                        </div>
                        <div class="loading-message">${message}</div>
                        <div class="loading-progress">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div class="progress-text">处理中...</div>
                        </div>
                    </div>
                </div>
            `;

            const existingLoading = container.querySelector('.loading-overlay');
            if (existingLoading) {
                existingLoading.remove();
            }

            container.insertAdjacentHTML('beforeend', loadingHtml);

            const progressFill = container.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.animation = 'progressAnimation 3s ease-in-out infinite';
            }
        }

        function hideLoadingState(container) {
            const loadingOverlay = container.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.remove();
            }
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';

            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }

        // 测试图片生成
        async function testImageGeneration() {
            const container = document.getElementById('image-demo');

            try {
                showLoadingState(container, '正在生成图片，请稍候...', 'image');
                showStatus('正在生成图片，请稍候...', 'info');

                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 3000));

                hideLoadingState(container);

                // 显示模拟结果
                container.innerHTML = `
                    <h4>生成的图片：</h4>
                    <img src="https://via.placeholder.com/400x300/3b82f6/ffffff?text=Generated+Image"
                         alt="生成的图片" class="generated-image">
                    <div class="image-actions">
                        <button class="btn btn-secondary download-image">
                            <i class="fas fa-download"></i> 下载图片
                        </button>
                        <button class="btn btn-secondary copy-image">
                            <i class="fas fa-copy"></i> 复制图片
                        </button>
                        <button class="btn btn-primary insert-image">
                            <i class="fas fa-plus"></i> 插入到编辑器
                        </button>
                    </div>
                `;

                setupImageActions();
                showStatus('图片生成成功！', 'success');

            } catch (error) {
                hideLoadingState(container);
                showStatus('图片生成失败', 'error');
            }
        }

        // 测试音频生成
        async function testAudioGeneration() {
            const container = document.getElementById('audio-demo');

            try {
                showLoadingState(container, '正在生成语音，请稍候...', 'audio');
                showStatus('正在生成语音，请稍候...', 'info');

                await new Promise(resolve => setTimeout(resolve, 2500));

                hideLoadingState(container);

                container.innerHTML = `
                    <h4>生成的语音：</h4>
                    <div class="audio-player-container">
                        <audio class="generated-audio" controls>
                            <source src="https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" type="audio/wav">
                        </audio>
                        <div class="audio-actions">
                            <button class="btn btn-secondary download-audio">
                                <i class="fas fa-download"></i> 下载音频
                            </button>
                            <button class="btn btn-secondary copy-audio">
                                <i class="fas fa-copy"></i> 复制音频
                            </button>
                            <button class="btn btn-primary play-audio">
                                <i class="fas fa-play"></i> 播放
                            </button>
                        </div>
                    </div>
                `;

                setupAudioActions();
                showStatus('语音生成成功！', 'success');

            } catch (error) {
                hideLoadingState(container);
                showStatus('语音生成失败', 'error');
            }
        }

        // 测试文本转录
        async function testTranscription() {
            const container = document.getElementById('transcription-demo');

            try {
                showLoadingState(container, '正在转录音频，请稍候...', 'transcription');
                showStatus('正在转录音频，请稍候...', 'info');

                await new Promise(resolve => setTimeout(resolve, 4000));

                hideLoadingState(container);

                container.innerHTML = `
                    <h4>转录结果：</h4>
                    <textarea readonly rows="5" style="width: 100%; margin-bottom: 1rem; padding: 0.5rem; border: 1px solid #e5e7eb; border-radius: 4px;">这是一段模拟的转录文本。在实际应用中，这里会显示从音频文件转录出来的文字内容。转录功能支持多种音频格式，包括WAV、MP3、M4A等。</textarea>
                    <div class="audio-actions">
                        <button class="btn btn-secondary copy-transcription">
                            <i class="fas fa-copy"></i> 复制文本
                        </button>
                        <button class="btn btn-primary insert-transcription">
                            <i class="fas fa-plus"></i> 插入到编辑器
                        </button>
                    </div>
                `;

                setupTranscriptionActions();
                showStatus('音频转录成功！', 'success');

            } catch (error) {
                hideLoadingState(container);
                showStatus('音频转录失败', 'error');
            }
        }

        // 测试加载状态
        function testLoadingState() {
            const container = document.getElementById('loading-demo');

            showLoadingState(container, '测试加载状态显示...', 'test');

            setTimeout(() => {
                hideLoadingState(container);
                container.innerHTML = '<p style="color: #059669; font-weight: 500;">✅ 加载状态测试完成！</p>';
                showStatus('加载状态测试完成', 'success');
            }, 3000);
        }

        // 设置图片操作按钮
        function setupImageActions() {
            const downloadBtn = document.querySelector('.download-image');
            const copyBtn = document.querySelector('.copy-image');
            const insertBtn = document.querySelector('.insert-image');

            if (downloadBtn) {
                downloadBtn.onclick = () => {
                    showStatus('图片下载功能已触发', 'success');
                };
            }

            if (copyBtn) {
                copyBtn.onclick = async () => {
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 复制中...';
                    copyBtn.disabled = true;

                    setTimeout(() => {
                        copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                        showStatus('图片已复制到剪贴板', 'success');

                        setTimeout(() => {
                            copyBtn.innerHTML = originalText;
                            copyBtn.disabled = false;
                        }, 2000);
                    }, 1000);
                };
            }

            if (insertBtn) {
                insertBtn.onclick = () => {
                    showStatus('图片插入功能已触发', 'success');
                };
            }
        }

        // 设置音频操作按钮
        function setupAudioActions() {
            const downloadBtn = document.querySelector('.download-audio');
            const copyBtn = document.querySelector('.copy-audio');
            const playBtn = document.querySelector('.play-audio');

            if (downloadBtn) {
                downloadBtn.onclick = () => {
                    showStatus('音频下载功能已触发', 'success');
                };
            }

            if (copyBtn) {
                copyBtn.onclick = async () => {
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 复制中...';
                    copyBtn.disabled = true;

                    setTimeout(() => {
                        copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                        showStatus('音频已复制到剪贴板', 'success');

                        setTimeout(() => {
                            copyBtn.innerHTML = originalText;
                            copyBtn.disabled = false;
                        }, 2000);
                    }, 1000);
                };
            }

            if (playBtn) {
                playBtn.onclick = () => {
                    const audio = document.querySelector('.generated-audio');
                    if (audio) {
                        audio.play();
                        showStatus('音频播放已开始', 'info');
                    }
                };
            }
        }

        // 设置转录操作按钮
        function setupTranscriptionActions() {
            const copyBtn = document.querySelector('.copy-transcription');
            const insertBtn = document.querySelector('.insert-transcription');

            if (copyBtn) {
                copyBtn.onclick = () => {
                    const textarea = document.querySelector('textarea');
                    if (textarea) {
                        navigator.clipboard.writeText(textarea.value).then(() => {
                            showStatus('文本已复制到剪贴板', 'success');
                        });
                    }
                };
            }

            if (insertBtn) {
                insertBtn.onclick = () => {
                    showStatus('文本插入功能已触发', 'success');
                };
            }
        }
    </script>
</body>
</html>