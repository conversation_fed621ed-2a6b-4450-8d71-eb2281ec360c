-- 书籍目录排序优化方案
-- 基于书籍逻辑结构优化章节和大纲的排序

-- ========================================
-- 第一步：备份当前数据（可选）
-- ========================================

-- 创建备份表（如果需要）
-- CREATE TABLE outlines_backup AS SELECT * FROM public.outlines;
-- CREATE TABLE chapters_backup AS SELECT * FROM public.chapters;

-- ========================================
-- 第二步：标准化大纲排序（outlines表）
-- ========================================

-- 1. 为大纲表添加临时的逻辑排序字段
ALTER TABLE public.outlines 
ADD COLUMN IF NOT EXISTS logical_order INTEGER;

-- 2. 根据标题内容计算逻辑排序值
UPDATE public.outlines 
SET logical_order = CASE 
    -- 前言和序言
    WHEN title ILIKE '%前言%' OR title ILIKE '%序%' OR title ILIKE '%preface%' THEN 0
    
    -- 第0章
    WHEN title ILIKE '%第0章%' OR title ILIKE '%第零章%' THEN 0
    
    -- 正文章节（第1-20章）
    WHEN title ILIKE '%第1章%' OR title ILIKE '%第一章%' THEN 10
    WHEN title ILIKE '%第2章%' OR title ILIKE '%第二章%' THEN 20
    WHEN title ILIKE '%第3章%' OR title ILIKE '%第三章%' THEN 30
    WHEN title ILIKE '%第4章%' OR title ILIKE '%第四章%' THEN 40
    WHEN title ILIKE '%第5章%' OR title ILIKE '%第五章%' THEN 50
    WHEN title ILIKE '%第6章%' OR title ILIKE '%第六章%' THEN 60
    WHEN title ILIKE '%第7章%' OR title ILIKE '%第七章%' THEN 70
    WHEN title ILIKE '%第8章%' OR title ILIKE '%第八章%' THEN 80
    WHEN title ILIKE '%第9章%' OR title ILIKE '%第九章%' THEN 90
    WHEN title ILIKE '%第10章%' OR title ILIKE '%第十章%' THEN 100
    WHEN title ILIKE '%第11章%' OR title ILIKE '%第十一章%' THEN 110
    WHEN title ILIKE '%第12章%' OR title ILIKE '%第十二章%' THEN 120
    WHEN title ILIKE '%第13章%' OR title ILIKE '%第十三章%' THEN 130
    WHEN title ILIKE '%第14章%' OR title ILIKE '%第十四章%' THEN 140
    WHEN title ILIKE '%第15章%' OR title ILIKE '%第十五章%' THEN 150
    
    -- 子章节（在主章节基础上加小数）
    WHEN title ILIKE '%1.1%' OR title ILIKE '%一.一%' THEN 11
    WHEN title ILIKE '%1.2%' OR title ILIKE '%一.二%' THEN 12
    WHEN title ILIKE '%1.3%' OR title ILIKE '%一.三%' THEN 13
    WHEN title ILIKE '%2.1%' OR title ILIKE '%二.一%' THEN 21
    WHEN title ILIKE '%2.2%' OR title ILIKE '%二.二%' THEN 22
    WHEN title ILIKE '%2.3%' OR title ILIKE '%二.三%' THEN 23
    WHEN title ILIKE '%3.1%' OR title ILIKE '%三.一%' THEN 31
    WHEN title ILIKE '%3.2%' OR title ILIKE '%三.二%' THEN 32
    WHEN title ILIKE '%3.3%' OR title ILIKE '%三.三%' THEN 33
    
    -- 附录和后记
    WHEN title ILIKE '%附录%' OR title ILIKE '%appendix%' THEN 900
    WHEN title ILIKE '%参考文献%' OR title ILIKE '%references%' THEN 910
    WHEN title ILIKE '%索引%' OR title ILIKE '%index%' THEN 920
    WHEN title ILIKE '%后记%' OR title ILIKE '%后序%' OR title ILIKE '%epilogue%' THEN 930
    WHEN title ILIKE '%致谢%' OR title ILIKE '%acknowledgments%' THEN 940
    
    -- 默认值（根据当前sort_order）
    ELSE COALESCE(sort_order * 10, 500)
END;

-- 3. 更新sort_order字段为逻辑排序值
UPDATE public.outlines 
SET sort_order = logical_order;

-- 4. 处理同一项目内的排序冲突
WITH ranked_outlines AS (
    SELECT
        id,
        project_id,
        logical_order,
        ROW_NUMBER() OVER (PARTITION BY project_id, logical_order ORDER BY created_at) as rn
    FROM public.outlines
)
UPDATE public.outlines
SET sort_order = ranked_outlines.logical_order + (ranked_outlines.rn - 1)
FROM ranked_outlines
WHERE public.outlines.id = ranked_outlines.id
AND ranked_outlines.rn > 1;

-- ========================================
-- 第三步：标准化章节排序（chapters表）
-- ========================================

-- 1. 为章节表添加临时的逻辑排序字段
ALTER TABLE public.chapters 
ADD COLUMN IF NOT EXISTS logical_order INTEGER;

-- 2. 根据标题内容计算逻辑排序值
UPDATE public.chapters 
SET logical_order = CASE 
    -- 前言和序言
    WHEN title ILIKE '%前言%' OR title ILIKE '%序%' OR title ILIKE '%preface%' THEN 0
    
    -- 第0章
    WHEN title ILIKE '%第0章%' OR title ILIKE '%第零章%' THEN 0
    
    -- 正文章节（第1-20章）
    WHEN title ILIKE '%第1章%' OR title ILIKE '%第一章%' THEN 10
    WHEN title ILIKE '%第2章%' OR title ILIKE '%第二章%' THEN 20
    WHEN title ILIKE '%第3章%' OR title ILIKE '%第三章%' THEN 30
    WHEN title ILIKE '%第4章%' OR title ILIKE '%第四章%' THEN 40
    WHEN title ILIKE '%第5章%' OR title ILIKE '%第五章%' THEN 50
    WHEN title ILIKE '%第6章%' OR title ILIKE '%第七章%' THEN 60
    WHEN title ILIKE '%第7章%' OR title ILIKE '%第七章%' THEN 70
    WHEN title ILIKE '%第8章%' OR title ILIKE '%第八章%' THEN 80
    WHEN title ILIKE '%第9章%' OR title ILIKE '%第九章%' THEN 90
    WHEN title ILIKE '%第10章%' OR title ILIKE '%第十章%' THEN 100
    WHEN title ILIKE '%第11章%' OR title ILIKE '%第十一章%' THEN 110
    WHEN title ILIKE '%第12章%' OR title ILIKE '%第十二章%' THEN 120
    WHEN title ILIKE '%第13章%' OR title ILIKE '%第十三章%' THEN 130
    WHEN title ILIKE '%第14章%' OR title ILIKE '%第十四章%' THEN 140
    WHEN title ILIKE '%第15章%' OR title ILIKE '%第十五章%' THEN 150
    
    -- 子章节（在主章节基础上加小数）
    WHEN title ILIKE '%1.1%' OR title ILIKE '%一.一%' THEN 11
    WHEN title ILIKE '%1.2%' OR title ILIKE '%一.二%' THEN 12
    WHEN title ILIKE '%1.3%' OR title ILIKE '%一.三%' THEN 13
    WHEN title ILIKE '%2.1%' OR title ILIKE '%二.一%' THEN 21
    WHEN title ILIKE '%2.2%' OR title ILIKE '%二.二%' THEN 22
    WHEN title ILIKE '%2.3%' OR title ILIKE '%二.三%' THEN 23
    WHEN title ILIKE '%3.1%' OR title ILIKE '%三.一%' THEN 31
    WHEN title ILIKE '%3.2%' OR title ILIKE '%三.二%' THEN 32
    WHEN title ILIKE '%3.3%' OR title ILIKE '%三.三%' THEN 33
    
    -- 附录和后记
    WHEN title ILIKE '%附录%' OR title ILIKE '%appendix%' THEN 900
    WHEN title ILIKE '%参考文献%' OR title ILIKE '%references%' THEN 910
    WHEN title ILIKE '%索引%' OR title ILIKE '%index%' THEN 920
    WHEN title ILIKE '%后记%' OR title ILIKE '%后序%' OR title ILIKE '%epilogue%' THEN 930
    WHEN title ILIKE '%致谢%' OR title ILIKE '%acknowledgments%' THEN 940
    
    -- 默认值（根据当前order_index）
    ELSE COALESCE(order_index * 10, 500)
END;

-- 3. 更新order_index字段为逻辑排序值
UPDATE public.chapters 
SET order_index = logical_order;

-- 4. 处理同一项目内的排序冲突
WITH ranked_chapters AS (
    SELECT
        id,
        project_id,
        logical_order,
        ROW_NUMBER() OVER (PARTITION BY project_id, logical_order ORDER BY created_at) as rn
    FROM public.chapters
)
UPDATE public.chapters
SET order_index = ranked_chapters.logical_order + (ranked_chapters.rn - 1)
FROM ranked_chapters
WHERE public.chapters.id = ranked_chapters.id
AND ranked_chapters.rn > 1;

-- ========================================
-- 第四步：同步大纲和章节的排序
-- ========================================

-- 确保关联的大纲和章节具有相同的排序值
UPDATE public.chapters 
SET order_index = o.sort_order
FROM public.outlines o
WHERE public.chapters.outline_id = o.id
AND public.chapters.order_index != o.sort_order;

-- ========================================
-- 第五步：清理临时字段
-- ========================================

-- 删除临时的逻辑排序字段
ALTER TABLE public.outlines DROP COLUMN IF EXISTS logical_order;
ALTER TABLE public.chapters DROP COLUMN IF EXISTS logical_order;

-- ========================================
-- 第六步：创建排序索引（提高查询性能）
-- ========================================

-- 为大纲表创建复合索引
CREATE INDEX IF NOT EXISTS idx_outlines_project_sort 
ON public.outlines(project_id, sort_order);

-- 为章节表创建复合索引
CREATE INDEX IF NOT EXISTS idx_chapters_project_order 
ON public.chapters(project_id, order_index);

-- 为大纲层级创建索引
CREATE INDEX IF NOT EXISTS idx_outlines_level_sort 
ON public.outlines(level, sort_order);

-- ========================================
-- 第七步：验证排序结果
-- ========================================

-- 查看优化后的大纲排序
SELECT 
    project_id,
    title,
    level,
    sort_order,
    created_at
FROM public.outlines 
ORDER BY project_id, sort_order;

-- 查看优化后的章节排序
SELECT 
    project_id,
    title,
    order_index,
    status,
    created_at
FROM public.chapters 
ORDER BY project_id, order_index;

-- 检查排序一致性
SELECT 
    o.project_id,
    o.title as outline_title,
    o.sort_order,
    c.title as chapter_title,
    c.order_index,
    CASE 
        WHEN o.sort_order = c.order_index THEN '✅ 一致'
        ELSE '❌ 不一致'
    END as consistency
FROM public.outlines o
LEFT JOIN public.chapters c ON o.id = c.outline_id
ORDER BY o.project_id, o.sort_order;
