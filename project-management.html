<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 专业著作智能编纂系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js"></script>
    <style>
        .project-management-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
        }

        .project-header {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .project-header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .brand {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .brand-icon {
            font-size: 2rem;
            color: #4f46e5;
        }

        .brand-title {
            font-size: 1.8rem;
            color: #000000 !important;
            margin: 0;
            font-weight: 600;
            background: none !important;
            -webkit-background-clip: initial !important;
            -webkit-text-fill-color: #000000 !important;
            background-clip: initial !important;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4f46e5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            background: #4338ca;
            transform: scale(1.05);
        }

        .user-details h3 {
            margin: 0;
            color: #1f2937;
            font-size: 1rem;
        }

        .user-details p {
            margin: 0;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-icon-only {
            padding: 10px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon-only:hover {
            background: #e5e7eb;
        }

        /* 个人资料模态框样式 */
        .profile-modal-content {
            padding: 20px;
        }

        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 500;
            color: #374151;
        }

        .form-group input {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .avatar-section {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: #f9fafb;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .avatar-preview {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #4f46e5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 2rem;
        }

        .avatar-info {
            flex: 1;
        }

        .avatar-info h4 {
            margin: 0 0 8px 0;
            color: #1f2937;
        }

        .avatar-info p {
            margin: 0;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .projects-section {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            color: #000000;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
        }

        .project-card {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 280px;
            display: flex;
            flex-direction: column;
        }

        .project-card:hover {
            border-color: #4f46e5;
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
            transform: translateY(-2px);
        }

        .project-card.create-new {
            border: 2px dashed #d1d5db;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            text-align: center;
        }

        .project-card.create-new:hover {
            border-color: #4f46e5;
            background: #f8faff;
        }

        .create-icon {
            font-size: 3rem;
            color: #9ca3af;
            margin-bottom: 16px;
        }

        .project-card.create-new:hover .create-icon {
            color: #4f46e5;
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.4;
        }

        .project-description {
            color: #6b7280;
            font-size: 0.875rem;
            margin: 0 0 16px 0;
            line-height: 1.5;
        }

        .project-meta {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 16px;
            gap: 12px;
        }

        .project-status {
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
            min-width: fit-content;
            flex-shrink: 0;
            display: inline-flex;
            align-items: center;
            line-height: 1;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-draft {
            background: #fef3c7;
            color: #92400e;
        }

        .project-date {
            color: #9ca3af;
            font-size: 0.75rem;
            white-space: nowrap;
            flex-shrink: 0;
            display: inline-flex;
            align-items: center;
            line-height: 1;
        }

        .project-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            padding: 12px 0;
            border-top: 1px solid #f3f4f6;
            border-bottom: 1px solid #f3f4f6;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .stat-item i {
            color: #9ca3af;
        }

        .project-progress {
            margin-bottom: 16px;
        }

        .progress-text {
            color: #374151;
            font-weight: 500;
        }

        .progress-percentage {
            color: #4f46e5;
            font-weight: 600;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4f46e5;
            transition: width 0.3s ease;
        }

        .project-actions {
            display: flex;
            flex-direction: row;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
            justify-content: flex-start;
            align-items: center;
        }

        .project-card:hover .project-actions {
            opacity: 1;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: all 0.3s ease;
        }

        .action-btn.edit {
            background: #f3f4f6;
            color: #374151;
        }

        .action-btn.edit:hover {
            background: #e5e7eb;
        }

        .action-btn.export {
            background: #ecfdf5;
            color: #065f46;
        }

        .action-btn.export:hover {
            background: #d1fae5;
        }

        .action-btn.delete {
            background: #fef2f2;
            color: #991b1b;
        }

        .action-btn.delete:hover {
            background: #fee2e2;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #6b7280;
            font-size: 1rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
        }

        .empty-icon {
            font-size: 4rem;
            color: #d1d5db;
            margin-bottom: 24px;
        }

        .empty-title {
            font-size: 1.5rem;
            color: #374151;
            margin: 0 0 12px 0;
        }

        .empty-description {
            color: #6b7280;
            margin: 0 0 24px 0;
        }

        /* 骨架屏样式 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .skeleton-card {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            min-height: 200px;
        }

        .skeleton-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .skeleton-status {
            width: 60px;
            height: 20px;
            border-radius: 10px;
        }

        .skeleton-date {
            width: 80px;
            height: 16px;
            border-radius: 8px;
        }

        .skeleton-title {
            width: 70%;
            height: 24px;
            border-radius: 6px;
            margin-bottom: 12px;
        }

        .skeleton-description {
            width: 100%;
            height: 16px;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .skeleton-description:last-of-type {
            width: 60%;
        }

        .skeleton-stats {
            display: flex;
            gap: 16px;
            margin: 16px 0;
            padding: 12px 0;
            border-top: 1px solid #f3f4f6;
            border-bottom: 1px solid #f3f4f6;
        }

        .skeleton-stat {
            width: 60px;
            height: 16px;
            border-radius: 8px;
        }

        .skeleton-progress {
            margin-bottom: 16px;
        }

        .skeleton-progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .skeleton-progress-text {
            width: 80px;
            height: 16px;
            border-radius: 8px;
        }

        .skeleton-progress-percent {
            width: 30px;
            height: 16px;
            border-radius: 8px;
        }

        .skeleton-progress-bar {
            width: 100%;
            height: 6px;
            border-radius: 3px;
        }

        .skeleton-actions {
            display: flex;
            gap: 8px;
        }

        .skeleton-action {
            width: 60px;
            height: 28px;
            border-radius: 6px;
        }

        /* 加载状态增强 */
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        .loading-text {
            color: #6b7280;
            font-size: 1rem;
            margin-bottom: 8px;
        }

        .loading-subtext {
            color: #9ca3af;
            font-size: 0.875rem;
            text-align: center;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .projects-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .project-card {
                min-height: auto;
                padding: 20px;
            }

            .project-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .project-status {
                align-self: flex-start;
            }
        }

        @media (max-width: 480px) {
            .projects-grid {
                grid-template-columns: 1fr;
            }

            .project-card {
                padding: 16px;
            }

            .project-stats {
                flex-wrap: wrap;
                gap: 12px;
            }

            .stat-item {
                min-width: 80px;
            }
        }

        /* 导出选项样式 */
        .export-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .export-options .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 15px 20px;
            border: 2px solid #e5e7eb;
            background: #f9fafb;
            color: #374151;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 500;
        }

        .export-options .btn:hover {
            border-color: #4f46e5;
            background: #f8faff;
            color: #4f46e5;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
        }

        .export-options .btn i {
            font-size: 1.2em;
        }

        .export-tips {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .export-tips p {
            margin: 0 0 10px 0;
            color: #0369a1;
            font-weight: 500;
        }

        .export-tips ul {
            margin: 0;
            padding-left: 20px;
        }

        .export-tips li {
            margin: 5px 0;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .export-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="project-management-container">
        <!-- 页面头部 -->
        <div class="project-header">
            <div class="project-header-left">
                <div class="brand">
                    <i class="fas fa-feather-alt brand-icon"></i>
                    <h1 class="brand-title">项目管理</h1>
                </div>
            </div>
            <div class="header-actions">
                <div class="user-info" id="user-info" style="display: none;">
                    <div class="user-avatar" id="user-avatar" onclick="showUserProfile()" title="点击编辑个人资料">U</div>
                    <div class="user-details">
                        <h3 id="user-name">用户</h3>
                        <p id="user-email"><EMAIL></p>
                    </div>
                </div>
                <button class="btn btn-secondary btn-icon-only" onclick="signOut()" title="退出登录">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>

        <!-- 项目列表区域 -->
        <div class="projects-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-folder-open"></i>
                    我的项目
                </h2>
                <button class="btn btn-primary" onclick="showCreateProjectModal()">
                    <i class="fas fa-plus"></i> 创建新项目
                </button>
            </div>

            <!-- 项目网格 -->
            <div id="projects-container">
                <!-- 加载状态 -->
                <div id="loading-state" class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在加载项目...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div id="modal-container" class="modal-container">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div id="modal-content" class="modal-content">
                <!-- 模态对话框内容 -->
            </div>
            <div class="modal-footer">
                <div class="modal-actions" id="modal-actions">
                    <!-- 按钮将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <script src="supabase-config.js"></script>
    <script src="export-service.js"></script>
    <script src="project-management.js"></script>
</body>
</html>
