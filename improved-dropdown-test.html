<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改进的下拉框测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 2rem;
            margin: 0;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .demo-area {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            margin-top: 1rem;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .comparison-item {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .comparison-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .old-style .project-btn-inline {
            min-width: 200px;
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }
        
        .old-style .project-list {
            min-width: 200px;
            max-height: 300px;
        }
        
        .old-style .project-item {
            padding: 0.75rem;
            min-height: auto;
        }
        
        .old-style .project-title {
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .old-style .project-role {
            font-size: 0.75rem;
        }
        
        .old-style .project-status {
            width: 8px;
            height: 8px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .feature-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .feature-item h4 {
            margin: 0 0 0.5rem 0;
            color: #1f2937;
            font-size: 0.9rem;
        }
        
        .feature-item p {
            margin: 0;
            color: #6b7280;
            font-size: 0.8rem;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-magic"></i> 改进的项目选择下拉框</h1>
            <p style="color: #6b7280; margin: 0.5rem 0 0 0;">更宽的布局，更清晰的视觉层次，移除了奇怪的元素</p>
        </div>
        
        <!-- 新版本展示 -->
        <div class="test-section">
            <h2 class="section-title">
                <i class="fas fa-star"></i> 改进后的下拉框
            </h2>
            <p style="color: #6b7280; margin-bottom: 1rem;">
                宽度增加到320-450px，更大的内边距，更清晰的文字层次，移除了不必要的视觉元素
            </p>
            <div class="demo-area">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <h3><i class="fas fa-tachometer-alt"></i> 项目概览</h3>
                    <div class="project-selector-inline">
                        <div class="project-dropdown">
                            <button class="project-btn project-btn-inline" onclick="toggleDropdown('new')">
                                <i class="fas fa-folder"></i>
                                <span>选择项目</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="project-list" id="project-list-new">
                                <div class="project-item" onclick="selectProject('new', 'new')">
                                    <i class="fas fa-plus"></i>
                                    <span>创建新项目</span>
                                </div>
                                <div class="project-divider"></div>
                                <div class="project-item" onclick="selectProject('book1', 'new')">
                                    <div class="project-info">
                                        <div class="project-title">大模型技术与油气应用概论</div>
                                        <div class="project-role">项目所有者</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book2', 'new')">
                                    <div class="project-info">
                                        <div class="project-title">人工智能在石油勘探中的应用研究</div>
                                        <div class="project-role">编辑者</div>
                                    </div>
                                    <div class="project-status suspended"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book3', 'new')">
                                    <div class="project-info">
                                        <div class="project-title">深度学习与地质建模技术发展</div>
                                        <div class="project-role">审阅者</div>
                                    </div>
                                    <div class="project-status archived"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book4', 'new')">
                                    <div class="project-info">
                                        <div class="project-title">机器学习在油藏工程中的实践应用</div>
                                        <div class="project-role">作者</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 对比展示 -->
        <div class="test-section">
            <h2 class="section-title">
                <i class="fas fa-balance-scale"></i> 前后对比
            </h2>
            <div class="comparison-grid">
                <div class="comparison-item old-style">
                    <div class="comparison-title">❌ 优化前</div>
                    <div class="project-selector-inline">
                        <div class="project-dropdown">
                            <button class="project-btn project-btn-inline" onclick="toggleDropdown('old')">
                                <i class="fas fa-folder"></i>
                                <span>选择项目</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="project-list" id="project-list-old">
                                <div class="project-item" onclick="selectProject('new', 'old')">
                                    <i class="fas fa-plus"></i>
                                    <span>创建新项目</span>
                                </div>
                                <div class="project-divider"></div>
                                <div class="project-item" onclick="selectProject('book1', 'old')">
                                    <div class="project-info">
                                        <div class="project-title">大模型技术与油气应用概论</div>
                                        <div class="project-role">项目所有者</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book2', 'old')">
                                    <div class="project-info">
                                        <div class="project-title">人工智能在石油勘探中的应用</div>
                                        <div class="project-role">编辑者</div>
                                    </div>
                                    <div class="project-status suspended"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <div class="comparison-title">✅ 优化后</div>
                    <div class="project-selector-inline">
                        <div class="project-dropdown">
                            <button class="project-btn project-btn-inline" onclick="toggleDropdown('new2')">
                                <i class="fas fa-folder"></i>
                                <span>选择项目</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="project-list" id="project-list-new2">
                                <div class="project-item" onclick="selectProject('new', 'new2')">
                                    <i class="fas fa-plus"></i>
                                    <span>创建新项目</span>
                                </div>
                                <div class="project-divider"></div>
                                <div class="project-item" onclick="selectProject('book1', 'new2')">
                                    <div class="project-info">
                                        <div class="project-title">大模型技术与油气应用概论</div>
                                        <div class="project-role">项目所有者</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book2', 'new2')">
                                    <div class="project-info">
                                        <div class="project-title">人工智能在石油勘探中的应用</div>
                                        <div class="project-role">编辑者</div>
                                    </div>
                                    <div class="project-status suspended"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 改进特性 -->
        <div class="test-section">
            <h2 class="section-title">
                <i class="fas fa-rocket"></i> 主要改进
            </h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h4><i class="fas fa-expand-arrows-alt"></i> 增加宽度</h4>
                    <p>从200px增加到320-450px，提供更多空间显示项目名称</p>
                </div>
                <div class="feature-item">
                    <h4><i class="fas fa-text-height"></i> 优化间距</h4>
                    <p>增加内边距到1.25rem x 1.5rem，最小高度60px，避免拥挤</p>
                </div>
                <div class="feature-item">
                    <h4><i class="fas fa-font"></i> 改进字体</h4>
                    <p>项目标题字体加粗，大小调整为0.95rem，角色信息0.8rem</p>
                </div>
                <div class="feature-item">
                    <h4><i class="fas fa-circle"></i> 状态指示器</h4>
                    <p>状态圆点增大到12px，添加光晕效果，更加醒目</p>
                </div>
                <div class="feature-item">
                    <h4><i class="fas fa-mouse-pointer"></i> 交互优化</h4>
                    <p>悬停时增加阴影效果，移动距离增加到3px</p>
                </div>
                <div class="feature-item">
                    <h4><i class="fas fa-ban"></i> 移除怪异元素</h4>
                    <p>移除了奇怪的绿色进度条和不必要的渐变效果</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown(type) {
            const dropdown = document.getElementById(`project-list-${type}`);
            const btn = dropdown.previousElementSibling;
            
            // 关闭其他下拉菜单
            document.querySelectorAll('.project-list').forEach(list => {
                if (list !== dropdown) {
                    list.classList.remove('show');
                    list.previousElementSibling.classList.remove('active');
                }
            });
            
            dropdown.classList.toggle('show');
            btn.classList.toggle('active');
        }
        
        function selectProject(projectId, type) {
            const projectNames = {
                'new': '创建新项目',
                'book1': '大模型技术与油气应用概论',
                'book2': '人工智能在石油勘探中的应用研究',
                'book3': '深度学习与地质建模技术发展',
                'book4': '机器学习在油藏工程中的实践应用'
            };
            
            // 更新按钮文本
            const btn = document.getElementById(`project-list-${type}`).previousElementSibling;
            const span = btn.querySelector('span');
            if (span && projectId !== 'new') {
                span.textContent = projectNames[projectId] || '选择项目';
            }
            
            // 关闭下拉菜单
            const dropdown = document.getElementById(`project-list-${type}`);
            dropdown.classList.remove('show');
            btn.classList.remove('active');
            
            if (projectId === 'new') {
                alert('创建新项目功能');
            }
        }
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.project-dropdown')) {
                document.querySelectorAll('.project-list').forEach(list => {
                    list.classList.remove('show');
                    list.previousElementSibling.classList.remove('active');
                });
            }
        });
    </script>
</body>
</html>
