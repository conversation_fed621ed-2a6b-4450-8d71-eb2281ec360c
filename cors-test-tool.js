// CORS测试工具
// 用于测试私有化Supabase部署的CORS配置

class CorsTestTool {
    constructor() {
        this.testResults = [];
    }

    // 测试基本的CORS配置
    async testBasicCors(url) {
        console.log(`🧪 开始测试CORS配置: ${url}`);
        
        try {
            // 测试OPTIONS预检请求
            const optionsResponse = await fetch(url, {
                method: 'OPTIONS',
                headers: {
                    'Origin': window.location.origin || 'null',
                    'Access-Control-Request-Method': 'GET',
                    'Access-Control-Request-Headers': 'Content-Type, Authorization, apikey'
                }
            });

            const corsHeaders = this.extractCorsHeaders(optionsResponse);
            
            return {
                success: true,
                method: 'OPTIONS',
                status: optionsResponse.status,
                corsHeaders: corsHeaders,
                analysis: this.analyzeCorsHeaders(corsHeaders)
            };

        } catch (error) {
            return {
                success: false,
                method: 'OPTIONS',
                error: error.message,
                analysis: this.analyzeError(error)
            };
        }
    }

    // 测试实际的API请求
    async testApiRequest(url, apiKey) {
        console.log(`🔍 测试API请求: ${url}`);
        
        try {
            const response = await fetch(`${url}/rest/v1/`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'apikey': apiKey,
                    'Authorization': `Bearer ${apiKey}`
                }
            });

            return {
                success: true,
                method: 'GET',
                status: response.status,
                statusText: response.statusText,
                headers: this.extractResponseHeaders(response)
            };

        } catch (error) {
            return {
                success: false,
                method: 'GET',
                error: error.message,
                analysis: this.analyzeError(error)
            };
        }
    }

    // 提取CORS相关头部
    extractCorsHeaders(response) {
        return {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
            'Access-Control-Max-Age': response.headers.get('Access-Control-Max-Age')
        };
    }

    // 提取响应头部
    extractResponseHeaders(response) {
        const headers = {};
        for (let [key, value] of response.headers.entries()) {
            headers[key] = value;
        }
        return headers;
    }

    // 分析CORS头部
    analyzeCorsHeaders(headers) {
        const analysis = {
            issues: [],
            recommendations: [],
            score: 0
        };

        // 检查Access-Control-Allow-Origin
        if (!headers['Access-Control-Allow-Origin']) {
            analysis.issues.push('缺少 Access-Control-Allow-Origin 头部');
            analysis.recommendations.push('添加 Access-Control-Allow-Origin: * 或指定具体域名');
        } else {
            analysis.score += 25;
        }

        // 检查Access-Control-Allow-Methods
        if (!headers['Access-Control-Allow-Methods']) {
            analysis.issues.push('缺少 Access-Control-Allow-Methods 头部');
            analysis.recommendations.push('添加 Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        } else {
            const methods = headers['Access-Control-Allow-Methods'].toLowerCase();
            if (!methods.includes('get') || !methods.includes('post') || !methods.includes('options')) {
                analysis.issues.push('允许的HTTP方法不完整');
                analysis.recommendations.push('确保包含 GET, POST, PUT, DELETE, OPTIONS 方法');
            } else {
                analysis.score += 25;
            }
        }

        // 检查Access-Control-Allow-Headers
        if (!headers['Access-Control-Allow-Headers']) {
            analysis.issues.push('缺少 Access-Control-Allow-Headers 头部');
            analysis.recommendations.push('添加 Access-Control-Allow-Headers: Content-Type, Authorization, apikey');
        } else {
            const allowedHeaders = headers['Access-Control-Allow-Headers'].toLowerCase();
            const requiredHeaders = ['content-type', 'authorization', 'apikey'];
            const missingHeaders = requiredHeaders.filter(header => !allowedHeaders.includes(header));
            
            if (missingHeaders.length > 0) {
                analysis.issues.push(`缺少必需的头部: ${missingHeaders.join(', ')}`);
                analysis.recommendations.push(`添加缺少的头部到 Access-Control-Allow-Headers`);
            } else {
                analysis.score += 25;
            }
        }

        // 检查预检请求缓存
        if (headers['Access-Control-Max-Age']) {
            analysis.score += 25;
        } else {
            analysis.recommendations.push('建议添加 Access-Control-Max-Age 头部以提高性能');
        }

        return analysis;
    }

    // 分析错误
    analyzeError(error) {
        const errorMessage = error.message.toLowerCase();
        
        if (errorMessage.includes('cors')) {
            return {
                type: 'CORS',
                description: 'CORS策略阻止了请求',
                solutions: [
                    '在服务器端配置正确的CORS头部',
                    '检查Access-Control-Allow-Origin设置',
                    '确保处理OPTIONS预检请求'
                ]
            };
        } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
            return {
                type: 'NETWORK',
                description: '网络连接问题',
                solutions: [
                    '检查URL是否正确',
                    '确认服务器是否运行',
                    '检查防火墙设置'
                ]
            };
        } else if (errorMessage.includes('timeout')) {
            return {
                type: 'TIMEOUT',
                description: '请求超时',
                solutions: [
                    '检查服务器响应时间',
                    '增加请求超时时间',
                    '检查网络连接质量'
                ]
            };
        } else {
            return {
                type: 'UNKNOWN',
                description: '未知错误',
                solutions: [
                    '检查浏览器控制台的详细错误信息',
                    '尝试使用其他工具测试API',
                    '联系系统管理员'
                ]
            };
        }
    }

    // 生成测试报告
    generateReport(testResults) {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests: testResults.length,
                passedTests: testResults.filter(r => r.success).length,
                failedTests: testResults.filter(r => !r.success).length
            },
            details: testResults,
            recommendations: []
        };

        // 收集所有建议
        testResults.forEach(result => {
            if (result.analysis && result.analysis.recommendations) {
                report.recommendations.push(...result.analysis.recommendations);
            }
        });

        // 去重建议
        report.recommendations = [...new Set(report.recommendations)];

        return report;
    }

    // 运行完整的CORS测试
    async runFullTest(url, apiKey) {
        console.log(`🚀 开始完整的CORS测试: ${url}`);
        
        const results = [];

        // 测试1: 基本CORS配置
        console.log('📋 测试1: 基本CORS配置');
        const corsTest = await this.testBasicCors(url);
        results.push({
            testName: '基本CORS配置',
            ...corsTest
        });

        // 测试2: API请求
        if (apiKey) {
            console.log('📋 测试2: API请求');
            const apiTest = await this.testApiRequest(url, apiKey);
            results.push({
                testName: 'API请求测试',
                ...apiTest
            });
        }

        // 生成报告
        const report = this.generateReport(results);
        console.log('📊 测试报告:', report);

        return report;
    }

    // 显示测试结果
    displayResults(report, containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('找不到结果容器:', containerId);
            return;
        }

        const html = this.generateResultsHtml(report);
        container.innerHTML = html;
    }

    // 生成结果HTML
    generateResultsHtml(report) {
        const successRate = (report.summary.passedTests / report.summary.totalTests * 100).toFixed(1);
        
        let html = `
            <div class="cors-test-results">
                <h3>🧪 CORS测试报告</h3>
                <div class="test-summary">
                    <p><strong>测试时间:</strong> ${new Date(report.timestamp).toLocaleString()}</p>
                    <p><strong>成功率:</strong> ${successRate}% (${report.summary.passedTests}/${report.summary.totalTests})</p>
                </div>
        `;

        // 显示每个测试的详细结果
        report.details.forEach((test, index) => {
            const statusClass = test.success ? 'success' : 'error';
            const statusIcon = test.success ? '✅' : '❌';
            
            html += `
                <div class="test-result ${statusClass}">
                    <h4>${statusIcon} ${test.testName}</h4>
                    <p><strong>状态:</strong> ${test.success ? '通过' : '失败'}</p>
            `;

            if (test.success && test.corsHeaders) {
                html += '<p><strong>CORS头部:</strong></p><pre>' + JSON.stringify(test.corsHeaders, null, 2) + '</pre>';
            }

            if (!test.success && test.error) {
                html += `<p><strong>错误:</strong> ${test.error}</p>`;
            }

            if (test.analysis) {
                if (test.analysis.issues && test.analysis.issues.length > 0) {
                    html += '<p><strong>问题:</strong></p><ul>';
                    test.analysis.issues.forEach(issue => {
                        html += `<li>${issue}</li>`;
                    });
                    html += '</ul>';
                }

                if (test.analysis.solutions && test.analysis.solutions.length > 0) {
                    html += '<p><strong>解决方案:</strong></p><ul>';
                    test.analysis.solutions.forEach(solution => {
                        html += `<li>${solution}</li>`;
                    });
                    html += '</ul>';
                }
            }

            html += '</div>';
        });

        // 显示总体建议
        if (report.recommendations.length > 0) {
            html += '<div class="recommendations"><h4>💡 建议:</h4><ul>';
            report.recommendations.forEach(rec => {
                html += `<li>${rec}</li>`;
            });
            html += '</ul></div>';
        }

        html += '</div>';

        return html;
    }
}

// 创建全局实例
window.corsTestTool = new CorsTestTool();

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CorsTestTool;
}
