<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协作功能最终测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #374151;
            margin-bottom: 15px;
        }
        
        .test-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #4338ca;
        }
        
        .test-button.success {
            background: #059669;
        }
        
        .test-button.danger {
            background: #dc2626;
        }
        
        .test-button.warning {
            background: #d97706;
        }
        
        .status-message {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .status-info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .project-info {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .project-info h4 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .project-info p {
            margin: 5px 0;
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 协作功能最终修复测试</h1>
            <p>测试修复后的协作功能是否彻底解决了 JavaScript 错误</p>
        </div>
        
        <div class="test-section">
            <h3>📊 当前状态检查</h3>
            <div class="project-info">
                <h4>项目信息</h4>
                <p id="current-project-info">检查中...</p>
                <p id="collaboration-manager-info">检查中...</p>
                <p id="global-state-info">检查中...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 功能测试</h3>
            <div class="test-grid">
                <button class="test-button" onclick="testLoadProjectData()">测试加载项目数据</button>
                <button class="test-button" onclick="testLoadTeamMembers()">测试加载团队成员</button>
                <button class="test-button" onclick="testLoadChapterAssignments()">测试加载章节分配</button>
                <button class="test-button" onclick="testLoadPermissionsMatrix()">测试加载权限矩阵</button>
                <button class="test-button warning" onclick="testWithoutProject()">测试无项目状态</button>
                <button class="test-button danger" onclick="clearProjectState()">清除项目状态</button>
                <button class="test-button success" onclick="restoreProjectState()">恢复项目状态</button>
                <button class="test-button" onclick="clearConsole()">清除控制台</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试结果</h3>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🖥️ 控制台输出</h3>
            <div id="console-output" class="console-output">等待测试...</div>
        </div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script src="collaboration.js"></script>
    <script>
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let consoleOutput = [];
        
        // 拦截控制台输出
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            consoleOutput.push({ type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString() });
            updateConsoleDisplay();
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            consoleOutput.push({ type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString() });
            updateConsoleDisplay();
        };
        
        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('console-output');
            consoleDiv.innerHTML = consoleOutput.slice(-20).map(entry => {
                const color = entry.type === 'error' ? '#ef4444' : '#10b981';
                return `<div style="color: ${color};">[${entry.time}] ${entry.message}</div>`;
            }).join('');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        function showTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-message status-${type}`;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(messageDiv);
            
            // 自动清除旧消息
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 10000);
        }
        
        function checkCurrentState() {
            const projectInfo = document.getElementById('current-project-info');
            const collaborationInfo = document.getElementById('collaboration-manager-info');
            const globalInfo = document.getElementById('global-state-info');
            
            // 检查全局项目状态
            if (typeof currentProject !== 'undefined' && currentProject.id) {
                globalInfo.textContent = `全局项目: ${currentProject.title} (ID: ${currentProject.id})`;
            } else {
                globalInfo.textContent = '全局项目: 未设置';
            }
            
            // 检查协作管理器状态
            if (typeof collaborationManager !== 'undefined') {
                if (collaborationManager.currentProject) {
                    collaborationInfo.textContent = `协作管理器项目: ${collaborationManager.currentProject.title} (ID: ${collaborationManager.currentProject.id})`;
                } else {
                    collaborationInfo.textContent = '协作管理器项目: 未设置';
                }
            } else {
                collaborationInfo.textContent = '协作管理器: 未初始化';
            }
            
            // 检查 localStorage
            const savedProjectId = localStorage.getItem('currentProjectId');
            if (savedProjectId) {
                projectInfo.textContent = `localStorage 项目ID: ${savedProjectId}`;
            } else {
                projectInfo.textContent = 'localStorage: 无项目ID';
            }
        }
        
        function testLoadProjectData() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.loadProjectData();
                    showTestResult('✅ 项目数据加载测试完成 - 无错误', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 项目数据加载测试失败: ${error.message}`, 'error');
            }
        }
        
        function testLoadTeamMembers() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.loadTeamMembers();
                    showTestResult('✅ 团队成员加载测试完成 - 无错误', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 团队成员加载测试失败: ${error.message}`, 'error');
            }
        }
        
        function testLoadChapterAssignments() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.loadChapterAssignments();
                    showTestResult('✅ 章节分配加载测试完成 - 无错误', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 章节分配加载测试失败: ${error.message}`, 'error');
            }
        }
        
        function testLoadPermissionsMatrix() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    collaborationManager.loadPermissionsMatrix();
                    showTestResult('✅ 权限矩阵加载测试完成 - 无错误', 'success');
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 权限矩阵加载测试失败: ${error.message}`, 'error');
            }
        }
        
        function testWithoutProject() {
            try {
                if (typeof collaborationManager !== 'undefined') {
                    // 备份当前项目
                    window.backupProject = collaborationManager.currentProject;
                    window.backupProjectId = collaborationManager.currentProjectId;
                    
                    // 清除项目
                    collaborationManager.currentProject = null;
                    collaborationManager.currentProjectId = null;
                    
                    // 测试所有功能
                    collaborationManager.loadProjectData();
                    collaborationManager.loadTeamMembers();
                    collaborationManager.loadChapterAssignments();
                    collaborationManager.loadPermissionsMatrix();
                    
                    showTestResult('✅ 无项目状态测试完成 - 所有功能都有适当的错误处理', 'success');
                    checkCurrentState();
                } else {
                    showTestResult('❌ 协作管理器未初始化', 'error');
                }
            } catch (error) {
                showTestResult(`❌ 无项目状态测试失败: ${error.message}`, 'error');
            }
        }
        
        function clearProjectState() {
            if (typeof collaborationManager !== 'undefined') {
                collaborationManager.currentProject = null;
                collaborationManager.currentProjectId = null;
            }
            if (typeof currentProject !== 'undefined') {
                currentProject.id = null;
            }
            localStorage.removeItem('currentProjectId');
            showTestResult('🗑️ 项目状态已清除', 'warning');
            checkCurrentState();
        }
        
        function restoreProjectState() {
            if (window.backupProject && typeof collaborationManager !== 'undefined') {
                collaborationManager.currentProject = window.backupProject;
                collaborationManager.currentProjectId = window.backupProjectId;
                showTestResult('🔄 项目状态已恢复', 'success');
                checkCurrentState();
            } else {
                showTestResult('❌ 没有备份的项目状态可恢复', 'error');
            }
        }
        
        function clearConsole() {
            consoleOutput = [];
            updateConsoleDisplay();
            showTestResult('🧹 控制台已清除', 'info');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            showTestResult('🚀 协作功能最终测试页面已加载', 'info');
            
            // 等待协作管理器初始化
            setTimeout(() => {
                checkCurrentState();
                if (typeof collaborationManager !== 'undefined') {
                    showTestResult('✅ 协作管理器已初始化', 'success');
                } else {
                    showTestResult('⚠️ 协作管理器未找到，某些测试可能无法进行', 'error');
                }
            }, 1000);
            
            // 定期更新状态
            setInterval(checkCurrentState, 5000);
        });
    </script>
</body>
</html>
