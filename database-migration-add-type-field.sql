-- 数据库迁移脚本：为projects表添加type字段
-- 修复项目创建时的"Could not find the 'type' column"错误
-- 执行日期：2025-07-20

-- ============================================================================
-- 添加type字段到projects表
-- ============================================================================

-- 检查type字段是否已存在，如果不存在则添加
DO $$
BEGIN
    -- 检查type列是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'projects' 
        AND column_name = 'type'
    ) THEN
        -- 添加type字段
        ALTER TABLE public.projects 
        ADD COLUMN type VARCHAR(50) DEFAULT 'book' 
        CHECK (type IN ('book', 'paper', 'report', 'other'));
        
        RAISE NOTICE 'Successfully added type column to projects table';
    ELSE
        RAISE NOTICE 'Type column already exists in projects table';
    END IF;
END $$;

-- ============================================================================
-- 更新现有项目的type字段（如果有数据的话）
-- ============================================================================

-- 为现有项目设置默认类型
UPDATE public.projects 
SET type = 'book' 
WHERE type IS NULL;

-- ============================================================================
-- 添加注释说明
-- ============================================================================

COMMENT ON COLUMN public.projects.type IS '项目类型：book(书籍), paper(论文), report(报告), other(其他)';

-- ============================================================================
-- 验证迁移结果
-- ============================================================================

-- 查看projects表结构，确认type字段已添加
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'projects'
ORDER BY ordinal_position;

-- 显示迁移完成信息
SELECT 'Database migration completed successfully: type field added to projects table' AS migration_status;
