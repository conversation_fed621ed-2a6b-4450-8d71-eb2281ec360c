-- 智能书籍目录排序系统
-- 基于书籍结构的智能排序算法，支持多层级目录

-- ========================================
-- 第一步：创建排序辅助函数
-- ========================================

-- 创建智能排序函数
CREATE OR REPLACE FUNCTION calculate_chapter_order(title TEXT, level INTEGER DEFAULT 1)
RETURNS INTEGER AS $$
DECLARE
    base_order INTEGER := 0;
    chapter_num INTEGER;
    section_num INTEGER;
    subsection_num INTEGER;
BEGIN
    -- 移除标题中的空格和特殊字符，转为小写
    title := LOWER(TRIM(title));
    
    -- 前言、序言等特殊章节
    IF title ~ '(前言|序言|preface|序|导言)' THEN
        RETURN 0;
    END IF;
    
    -- 目录
    IF title ~ '(目录|contents|table of contents)' THEN
        RETURN 5;
    END IF;
    
    -- 第0章或引言
    IF title ~ '(第0章|第零章|引言|introduction|概述|overview)' THEN
        RETURN 10;
    END IF;
    
    -- 提取章节号码（支持中文数字和阿拉伯数字）
    -- 第X章模式
    IF title ~ '第[0-9一二三四五六七八九十]+章' THEN
        -- 提取数字部分
        chapter_num := CASE 
            WHEN title ~ '第[0-9]+章' THEN 
                CAST(substring(title from '第([0-9]+)章') AS INTEGER)
            WHEN title ~ '第一章' THEN 1
            WHEN title ~ '第二章' THEN 2
            WHEN title ~ '第三章' THEN 3
            WHEN title ~ '第四章' THEN 4
            WHEN title ~ '第五章' THEN 5
            WHEN title ~ '第六章' THEN 6
            WHEN title ~ '第七章' THEN 7
            WHEN title ~ '第八章' THEN 8
            WHEN title ~ '第九章' THEN 9
            WHEN title ~ '第十章' THEN 10
            WHEN title ~ '第十一章' THEN 11
            WHEN title ~ '第十二章' THEN 12
            WHEN title ~ '第十三章' THEN 13
            WHEN title ~ '第十四章' THEN 14
            WHEN title ~ '第十五章' THEN 15
            ELSE 1
        END;
        
        base_order := 100 + (chapter_num * 100);
        
        -- 检查是否有小节号
        IF title ~ '[0-9]+\.[0-9]+' THEN
            section_num := CAST(substring(title from '([0-9]+)\.[0-9]+') AS INTEGER);
            subsection_num := CAST(substring(title from '[0-9]+\.([0-9]+)') AS INTEGER);
            base_order := base_order + (section_num * 10) + subsection_num;
        END IF;
        
        RETURN base_order;
    END IF;
    
    -- 章节模式（X.Y.Z）
    IF title ~ '^[0-9]+\.[0-9]+(\.[0-9]+)?' THEN
        chapter_num := CAST(substring(title from '^([0-9]+)\.') AS INTEGER);
        section_num := CAST(substring(title from '^[0-9]+\.([0-9]+)') AS INTEGER);
        
        base_order := 100 + (chapter_num * 100) + (section_num * 10);
        
        -- 检查子小节
        IF title ~ '^[0-9]+\.[0-9]+\.[0-9]+' THEN
            subsection_num := CAST(substring(title from '^[0-9]+\.[0-9]+\.([0-9]+)') AS INTEGER);
            base_order := base_order + subsection_num;
        END IF;
        
        RETURN base_order;
    END IF;
    
    -- 附录
    IF title ~ '(附录|appendix)' THEN
        -- 提取附录编号
        IF title ~ '附录[a-zA-Z]' THEN
            RETURN 9000 + ASCII(UPPER(substring(title from '附录([a-zA-Z])'))) - ASCII('A');
        ELSIF title ~ 'appendix [a-zA-Z]' THEN
            RETURN 9000 + ASCII(UPPER(substring(title from 'appendix ([a-zA-Z])'))) - ASCII('A');
        ELSE
            RETURN 9000;
        END IF;
    END IF;
    
    -- 参考文献
    IF title ~ '(参考文献|references|bibliography)' THEN
        RETURN 9100;
    END IF;
    
    -- 索引
    IF title ~ '(索引|index)' THEN
        RETURN 9200;
    END IF;
    
    -- 后记、致谢等
    IF title ~ '(后记|致谢|acknowledgments|epilogue|后序)' THEN
        RETURN 9300;
    END IF;
    
    -- 默认情况：根据层级分配
    RETURN 5000 + (level * 100);
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 第二步：创建排序更新存储过程
-- ========================================

CREATE OR REPLACE FUNCTION update_project_chapter_ordering(target_project_id UUID)
RETURNS TABLE(
    updated_outlines INTEGER,
    updated_chapters INTEGER,
    message TEXT
) AS $$
DECLARE
    outline_count INTEGER := 0;
    chapter_count INTEGER := 0;
    temp_record RECORD;
BEGIN
    -- 更新大纲排序
    FOR temp_record IN 
        SELECT id, title, level FROM public.outlines 
        WHERE project_id = target_project_id
    LOOP
        UPDATE public.outlines 
        SET sort_order = calculate_chapter_order(temp_record.title, temp_record.level)
        WHERE id = temp_record.id;
        outline_count := outline_count + 1;
    END LOOP;
    
    -- 更新章节排序
    FOR temp_record IN 
        SELECT id, title FROM public.chapters 
        WHERE project_id = target_project_id
    LOOP
        UPDATE public.chapters 
        SET order_index = calculate_chapter_order(temp_record.title, 1)
        WHERE id = temp_record.id;
        chapter_count := chapter_count + 1;
    END LOOP;
    
    -- 同步关联的大纲和章节排序
    UPDATE public.chapters 
    SET order_index = o.sort_order
    FROM public.outlines o
    WHERE public.chapters.outline_id = o.id
    AND public.chapters.project_id = target_project_id;
    
    RETURN QUERY SELECT 
        outline_count,
        chapter_count,
        format('已更新 %s 个大纲项和 %s 个章节的排序', outline_count, chapter_count);
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 第三步：批量更新所有项目的排序
-- ========================================

CREATE OR REPLACE FUNCTION update_all_projects_ordering()
RETURNS TABLE(
    proj_id UUID,
    proj_title TEXT,
    updated_outlines INTEGER,
    updated_chapters INTEGER,
    status TEXT
) AS $$
DECLARE
    project_record RECORD;
    result_record RECORD;
BEGIN
    FOR project_record IN
        SELECT id, title FROM public.projects
        WHERE status = 'active'
    LOOP
        BEGIN
            -- 调用单个项目的排序更新
            SELECT * INTO result_record
            FROM update_project_chapter_ordering(project_record.id);

            RETURN QUERY SELECT
                project_record.id,
                project_record.title,
                result_record.updated_outlines,
                result_record.updated_chapters,
                '✅ 成功'::TEXT;

        EXCEPTION WHEN OTHERS THEN
            RETURN QUERY SELECT
                project_record.id,
                project_record.title,
                0,
                0,
                format('❌ 失败: %s', SQLERRM);
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 第四步：创建排序验证函数
-- ========================================

CREATE OR REPLACE FUNCTION validate_chapter_ordering(target_project_id UUID)
RETURNS TABLE(
    table_name TEXT,
    total_items INTEGER,
    properly_ordered INTEGER,
    ordering_issues INTEGER,
    duplicate_orders INTEGER,
    status TEXT
) AS $$
DECLARE
    outline_total INTEGER;
    outline_ordered INTEGER;
    outline_duplicates INTEGER;
    chapter_total INTEGER;
    chapter_ordered INTEGER;
    chapter_duplicates INTEGER;
BEGIN
    -- 检查大纲排序
    SELECT COUNT(*) INTO outline_total
    FROM public.outlines 
    WHERE project_id = target_project_id;
    
    SELECT COUNT(*) INTO outline_ordered
    FROM public.outlines 
    WHERE project_id = target_project_id 
    AND sort_order IS NOT NULL 
    AND sort_order > 0;
    
    SELECT COUNT(*) - COUNT(DISTINCT sort_order) INTO outline_duplicates
    FROM public.outlines 
    WHERE project_id = target_project_id;
    
    -- 检查章节排序
    SELECT COUNT(*) INTO chapter_total
    FROM public.chapters 
    WHERE project_id = target_project_id;
    
    SELECT COUNT(*) INTO chapter_ordered
    FROM public.chapters 
    WHERE project_id = target_project_id 
    AND order_index IS NOT NULL 
    AND order_index >= 0;
    
    SELECT COUNT(*) - COUNT(DISTINCT order_index) INTO chapter_duplicates
    FROM public.chapters 
    WHERE project_id = target_project_id;
    
    -- 返回大纲检查结果
    RETURN QUERY SELECT 
        'outlines'::TEXT,
        outline_total,
        outline_ordered,
        outline_total - outline_ordered,
        outline_duplicates,
        CASE 
            WHEN outline_total = outline_ordered AND outline_duplicates = 0 THEN '✅ 正常'
            ELSE '⚠️ 需要修复'
        END;
    
    -- 返回章节检查结果
    RETURN QUERY SELECT 
        'chapters'::TEXT,
        chapter_total,
        chapter_ordered,
        chapter_total - chapter_ordered,
        chapter_duplicates,
        CASE 
            WHEN chapter_total = chapter_ordered AND chapter_duplicates = 0 THEN '✅ 正常'
            ELSE '⚠️ 需要修复'
        END;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 第五步：使用示例和测试
-- ========================================

-- 测试排序函数
SELECT 
    title,
    calculate_chapter_order(title, 1) as calculated_order
FROM (
    VALUES 
        ('前言'),
        ('第0章：概述'),
        ('第1章：大模型基本概念与内涵'),
        ('第2章：大模型技术原理'),
        ('第3章：油气勘探中的大模型应用'),
        ('1.1 基本概念'),
        ('1.2 技术特点'),
        ('2.1 Transformer架构'),
        ('附录A：术语表'),
        ('参考文献'),
        ('索引')
) AS test_titles(title)
ORDER BY calculated_order;
