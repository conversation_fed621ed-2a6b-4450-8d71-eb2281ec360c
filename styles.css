/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 70px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.brand-icon {
    font-size: 1.8rem;
    color: #ffd700;
}

.brand-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.project-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.project-name {
    font-size: 0.9rem;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-info {
    background: #06b6d4;
    color: white;
}

.btn-info:hover {
    background: #0891b2;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
    transform: translateY(-1px);
}

.btn-icon {
    padding: 0.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    min-width: 40px;
    min-height: 40px;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.btn-icon i {
    font-size: 1rem;
    color: inherit;
}

/* 确保顶部栏图标按钮为白色 */
.header .btn-icon {
    color: white !important;
}

.header .btn-icon i {
    color: white !important;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧边栏 - 高雅简洁设计 */
.sidebar {
    width: 180px;
    background: #ffffff;
    border-right: 1px solid #f1f5f9;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.sidebar.collapsed {
    width: 70px;
}

/* 侧边栏头部 */
.sidebar-header {
    padding: 2rem 1.5rem 1.5rem 1.5rem;
    border-bottom: 1px solid #f8fafc;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80px;
    flex-shrink: 0;
    background: #ffffff;
}

.sidebar.collapsed .sidebar-header {
    padding: 1rem 0.75rem;
    justify-content: center;
    flex-direction: column;
    gap: 0.75rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    transition: all 0.3s ease;
}

.sidebar.collapsed .logo {
    justify-content: center;
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.logo-icon {
    font-size: 1.75rem;
    color: #2563eb;
    flex-shrink: 0;
    font-weight: 600;
    transition: all 0.3s ease;
}

.sidebar.collapsed .logo-icon {
    font-size: 1.5rem;
    color: #2563eb;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    letter-spacing: -0.025em;
    transition: all 0.3s ease;
}

.toggle-btn {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.25s ease;
    color: #64748b;
    font-size: 0.875rem;
}

.toggle-btn:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    color: #475569;
    transform: scale(1.05);
}

.sidebar.collapsed .toggle-btn {
    margin: 0;
    width: 32px;
    height: 32px;
}

/* 导航菜单 */
.nav-menu {
    padding: 1rem 0;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 隐藏导航菜单滚动条 */
.nav-menu::-webkit-scrollbar {
    width: 3px;
}

.nav-menu::-webkit-scrollbar-track {
    background: transparent;
}

.nav-menu::-webkit-scrollbar-thumb {
    background: #e2e8f0;
    border-radius: 2px;
}

.nav-menu::-webkit-scrollbar-thumb:hover {
    background: #cbd5e1;
}

.nav-item {
    margin: 0.375rem 1.25rem;
    border-radius: 14px;
    transition: all 0.25s ease;
    position: relative;
}

.sidebar.collapsed .nav-item {
    margin: 0.25rem 0.75rem;
    display: flex;
    justify-content: center;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    color: #64748b;
    text-decoration: none;
    border-radius: 14px;
    font-weight: 500;
    transition: all 0.25s ease;
    position: relative;
    min-height: 48px;
}

.sidebar.collapsed .nav-link {
    padding: 0.375rem;
    justify-content: center;
    min-height: 24px;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin: 0 auto;
}

.nav-link:hover {
    background: #f8fafc;
    color: #374151;
    transform: translateX(3px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.sidebar.collapsed .nav-link:hover {
    transform: none;
    background: #f1f5f9;
}

.nav-link.active {
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    color: #1d4ed8;
    box-shadow: 0 2px 8px rgba(29, 78, 216, 0.15);
    border: 1px solid #bfdbfe;
}

.nav-icon {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.sidebar.collapsed .nav-icon {
    width: 20px;
    height: 20px;
    font-size: 1rem;
}

.nav-text {
    margin-left: 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    letter-spacing: 0.01em;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    margin: 0;
    overflow: hidden;
    transform: translateX(-10px);
}

/* 工具提示 */
.nav-item .tooltip {
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #111827;
    color: white;
    padding: 0.625rem 0.875rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s ease;
    margin-left: 0.75rem;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-item .tooltip::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: #111827;
}

.sidebar.collapsed .nav-item:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

/* 导航分隔线 */
.nav-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #f1f5f9, transparent);
    margin: 1rem 1.25rem;
}

.sidebar.collapsed .nav-divider {
    margin: 0.25rem 0.5rem;
}

/* 管理员导航项 */
.nav-admin {
    position: relative;
    color: #94a3b8;
    font-size: 0.875rem;
}

.nav-admin:hover {
    background: #f8fafc;
    color: #64748b;
}

.nav-external {
    margin-left: auto;
    font-size: 0.75rem;
    opacity: 0.6;
}

/* 中间内容区域 */
.content-area {
    flex: 1;
    background: white;
    overflow: auto;
}

.panel {
    display: none;
    height: 100%;
    flex-direction: column;
}

.panel.active {
    display: flex;
}

.panel-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fafbfc;
}

.panel-header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.panel-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.panel-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* 大纲管理样式 */
.outline-container {
    flex: 1;
    padding: 2rem;
    overflow: auto;
}

.outline-tree {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    min-height: 400px;
}

.outline-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.outline-item:hover {
    background: #f9fafb;
}

.outline-item.selected {
    background: #eff6ff;
    border-left: 3px solid #3b82f6;
}

.outline-level-0 { padding-left: 1rem; font-weight: 600; }
.outline-level-1 { padding-left: 2rem; }
.outline-level-2 { padding-left: 3rem; }
.outline-level-3 { padding-left: 4rem; }

/* 可编辑章节样式 */
.outline-item.editable-chapter {
    transition: all 0.2s ease;
}

.outline-item.editable-chapter:hover {
    background: #f0f9ff !important;
    border-left: 3px solid #3b82f6;
    transform: translateX(2px);
}

.outline-item.editable-chapter::after {
    content: "双击编辑";
    position: absolute;
    right: 4rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.75rem;
    color: #6b7280;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.outline-item.editable-chapter:hover::after {
    opacity: 1;
}

/* 章节选择器样式 */
#chapter-selector {
    min-width: 250px;
    max-width: 400px;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: white;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

#chapter-selector:hover {
    border-color: #9ca3af;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

#chapter-selector:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* 章节选择器选项样式 */
#chapter-selector option {
    padding: 0.5rem;
    color: #374151;
    background: white;
}

#chapter-selector option:hover {
    background: #f3f4f6;
}

#chapter-selector option[data-level="1"] {
    font-weight: 600;
    color: #1f2937;
}

#chapter-selector option[data-level="2"] {
    color: #4b5563;
    padding-left: 1rem;
}

#chapter-selector option[data-level="3"] {
    color: #6b7280;
    padding-left: 2rem;
    font-size: 0.8rem;
}

/* 空状态样式 */
#chapter-selector option[value=""] {
    color: #9ca3af;
    font-style: italic;
}

/* 章节编写样式 */
.chapter-editor {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chapter-meta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-input, .form-textarea {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus, .form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.editor-wrapper {
    position: relative;
    flex: 1;
    min-height: 400px;
}

.editor-container {
    flex: 1;
    min-height: 400px;
}

#chapter-content-editor {
    flex: 1;
    min-height: 400px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
}

/* AI助手悬浮按钮样式 */
.ai-fab {
    position: absolute;
    top: 80px;
    right: 20px;
    z-index: 1000;
}

.fab-main-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.fab-main-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.fab-main-btn:active {
    transform: scale(0.95);
}

.fab-menu {
    position: absolute;
    top: 70px;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.ai-fab:hover .fab-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    text-align: left;
}

.fab-menu-item:hover {
    background: #f3f4f6;
    color: #1f2937;
    transform: translateX(4px);
}

.fab-menu-item i {
    width: 16px;
    text-align: center;
    color: #6b7280;
}

.fab-menu-item:hover i {
    color: #3b82f6;
}

.fab-menu-item span {
    font-weight: 500;
}

/* 特定功能的颜色 */
.fab-menu-item[data-action="polish"]:hover {
    background: #fef3c7;
    color: #92400e;
}

.fab-menu-item[data-action="polish"]:hover i {
    color: #f59e0b;
}

.fab-menu-item[data-action="translate"]:hover {
    background: #dbeafe;
    color: #1e40af;
}

.fab-menu-item[data-action="translate"]:hover i {
    color: #3b82f6;
}

.fab-menu-item[data-action="explain"]:hover {
    background: #d1fae5;
    color: #065f46;
}

.fab-menu-item[data-action="explain"]:hover i {
    color: #10b981;
}

.fab-menu-item[data-action="rewrite"]:hover {
    background: #fce7f3;
    color: #be185d;
}

.fab-menu-item[data-action="rewrite"]:hover i {
    color: #ec4899;
}

/* 多媒体功能样式 */
.fab-menu-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

.fab-menu-item[data-action="generate-image"]:hover {
    background: #f0f9ff;
    color: #0c4a6e;
}

.fab-menu-item[data-action="generate-image"]:hover i {
    color: #0284c7;
}

.fab-menu-item[data-action="text-to-speech"]:hover {
    background: #f0fdf4;
    color: #14532d;
}

.fab-menu-item[data-action="text-to-speech"]:hover i {
    color: #16a34a;
}

.fab-menu-item[data-action="speech-to-text"]:hover {
    background: #fef7ff;
    color: #581c87;
}

.fab-menu-item[data-action="speech-to-text"]:hover i {
    color: #9333ea;
}

.fab-menu-item[data-action="analyze-image"]:hover {
    background: #fff7ed;
    color: #9a3412;
}

.fab-menu-item[data-action="analyze-image"]:hover i {
    color: #ea580c;
}

/* 多媒体对话框样式 */
.multimedia-dialog {
    max-width: 600px;
}

.multimedia-input-section {
    margin-bottom: 1.5rem;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.option-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: center;
}

.option-group {
    flex: 1;
}

.option-group label {
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.option-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.image-options, .voice-options, .analysis-options {
    background: #f9fafb;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.generated-image-container, .audio-player-container, .transcription-result, .analysis-result {
    text-align: center;
    padding: 1rem;
}

.generated-image {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.image-actions, .audio-actions, .transcription-actions, .analysis-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

.recording-controls {
    text-align: center;
    margin-bottom: 1rem;
}

.recording-status {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.audio-upload, .image-upload-section {
    margin-top: 1rem;
}

.audio-upload label, .image-upload label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.audio-preview, .image-preview {
    margin-top: 0.5rem;
    text-align: center;
}

.transcription-text, .analysis-text {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.5;
    background: #f9fafb;
}

.quick-questions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.quick-question {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-question:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.image-url-section {
    margin-top: 1rem;
}

.image-url-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
}

.custom-text-section {
    margin-top: 1rem;
}

.custom-text-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.875rem;
    resize: vertical;
}

/* AI交互对话框样式 */
.ai-dialog {
    max-width: 800px;
    width: 90vw;
}

.ai-dialog-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.ai-dialog-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.ai-dialog-title {
    flex: 1;
}

.ai-dialog-title h3 {
    margin: 0 0 0.25rem 0;
    color: #1f2937;
    font-size: 1.25rem;
}

.ai-dialog-title p {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
}

.ai-dialog-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.ai-input-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ai-selected-text {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.ai-selected-text h4 {
    margin: 0 0 0.5rem 0;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 600;
}

.ai-selected-text-content {
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.5;
    max-height: 150px;
    overflow-y: auto;
    background: white;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.ai-prompt-input {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    resize: vertical;
    font-family: inherit;
}

.ai-prompt-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ai-file-upload {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ai-file-upload label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.ai-file-input {
    padding: 0.5rem;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background: #f9fafb;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ai-file-input:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}

.ai-file-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.ai-file-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #e5e7eb;
    border-radius: 6px;
    font-size: 0.75rem;
}

.ai-file-item button {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    font-size: 0.875rem;
}

.ai-file-item button:hover {
    color: #ef4444;
}

.ai-response-section {
    position: relative;
    border-top: 1px solid #e5e7eb;
    padding-top: 1.5rem;
}

.ai-response-content {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    font-size: 0.875rem;
    line-height: 1.6;
    white-space: pre-wrap;
}

.ai-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #6b7280;
    font-style: italic;
}

.ai-loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 结构图样式 */
.diagram-container {
    flex: 1;
    padding: 2rem;
    display: flex;
    gap: 2rem;
}

.diagram-editor {
    flex: 1;
}

.diagram-editor textarea {
    width: 100%;
    height: 400px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: none;
}

.diagram-preview {
    flex: 1;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 1rem;
    background: white;
    overflow: auto;
}

/* 右侧工具面板 */
.tools-panel {
    width: 280px;
    background: #fafbfc;
    border-left: 1px solid #e5e7eb;
    padding: 1.5rem;
    overflow: auto;
}

.tool-section {
    margin-bottom: 2rem;
}

.tool-section h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.tool-btn {
    padding: 0.75rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
}

.tool-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    transform: translateY(-1px);
}

.tool-btn i {
    font-size: 1.2rem;
    color: #6b7280;
}

.tool-btn span {
    font-size: 0.8rem;
    color: #374151;
}

/* 进度管理样式 */
.progress-container {
    padding: 2rem;
}

.progress-overview {
    margin-bottom: 2rem;
}

.progress-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    text-align: center;
}

.progress-card h3 {
    margin-bottom: 1rem;
    color: #374151;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* 模态对话框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal, .modal-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 85vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* AI助手对话框样式 */
.modal-container.ai-dialog {
    max-width: 800px;
    width: 95%;
    max-height: 90vh;
}

.modal-content {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.modal-buttons {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
    flex-shrink: 0;
    min-height: 60px;
    align-items: center;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
}

.modal-body {
    padding: 1.5rem;
    max-height: 400px;
    overflow: auto;
}

.modal-footer {
    padding: 0;
    border-top: none;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.modal-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

/* 大纲项目操作按钮 */
.outline-item {
    position: relative;
}

.outline-actions {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    gap: 0.25rem;
}

.outline-item:hover .outline-actions {
    display: flex;
}

.btn-icon {
    background: none;
    border: none;
    padding: 0.25rem;
    cursor: pointer;
    border-radius: 4px;
    color: #6b7280;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: #f3f4f6;
    color: #374151;
}

/* 添加子章节按钮特殊样式 */
.btn-icon[title="添加子章节"] {
    color: #059669;
}

.btn-icon[title="添加子章节"]:hover {
    background: #ecfdf5;
    color: #047857;
}

/* 参考文献样式 */
.references-container {
    flex: 1;
    padding: 2rem;
    overflow: auto;
}

.reference-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    transition: box-shadow 0.3s ease;
}

.reference-item:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.reference-content h4 {
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.reference-content p {
    margin-bottom: 0.25rem;
    color: #6b7280;
    font-size: 0.9rem;
}

.reference-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
}

/* 进度管理样式 */
.progress-part {
    margin: 1.5rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.progress-part h4 {
    color: #1f2937;
    font-weight: 600;
}

.progress-chapter {
    padding: 0.75rem 1rem;
    margin: 0.5rem 0;
    border-radius: 6px;
    border-left: 4px solid #d1d5db;
    background: #f9fafb;
    transition: all 0.3s ease;
}

.progress-chapter.completed {
    border-left-color: #10b981;
    background: #ecfdf5;
}

.progress-chapter.pending {
    border-left-color: #f59e0b;
    background: #fffbeb;
}

.chapter-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.chapter-status i {
    color: #10b981;
}

.progress-chapter.pending .chapter-status i {
    color: #f59e0b;
}

.chapter-meta small {
    color: #6b7280;
    font-size: 0.8rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.notification-success {
    background: #10b981;
}

.notification-warning {
    background: #f59e0b;
}

.notification-error {
    background: #ef4444;
}

.notification-info {
    background: #3b82f6;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 帮助内容样式 */
.help-content h4 {
    margin: 1rem 0 0.5rem 0;
    color: #1f2937;
}

.help-content ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.help-content li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* 文本样式工具类 */
.text-muted {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

.text-error {
    color: #ef4444;
    padding: 1rem;
    text-align: center;
}

/* 用户界面样式 */
.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.project-info {
    margin-left: 1rem;
    font-size: 0.9rem;
}

.project-name {
    font-weight: 600;
    color: #ffd700;
}

.user-role {
    color: #e5e7eb;
    margin-left: 0.5rem;
}

.collaboration-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.online-users {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.online-user {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4f46e5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.online-user:hover {
    transform: scale(1.1);
}

.online-user.typing {
    animation: pulse 1.5s infinite;
}

.online-user-tooltip {
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.online-user:hover .online-user-tooltip {
    opacity: 1;
}

.sync-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #6b7280;
}

.sync-status.connected {
    color: #10b981;
}

.sync-status.disconnected {
    color: #ef4444;
}

.sync-status.syncing {
    color: #f59e0b;
}

.sync-status i {
    font-size: 0.9rem;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.online-user {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #10b981;
}

.online-user img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-menu {
    position: relative;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.user-avatar:hover {
    background: rgba(255, 255, 255, 0.1);
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}



.dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

/* ===== 全新项目选择器系统 ===== */

/* 隐藏旧的项目选择器 */
.project-selector {
    display: none !important;
}

/* 现代化内联项目选择器 */
.project-selector-inline {
    position: relative;
    z-index: 100;
}

/* 全新设计的选择按钮 */
.project-btn-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: auto;
    min-width: 380px;
    max-width: 520px;
    padding: 1rem 1.5rem;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    color: #1f2937;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.project-btn-inline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05));
    opacity: 0;
    transition: opacity 0.25s ease;
}

.project-btn-inline:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.project-btn-inline:hover::before {
    opacity: 1;
}

.project-btn-inline:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 6px rgba(0, 0, 0, 0.1);
}

.project-btn-inline.active {
    border-color: #3b82f6;
    background: #f8fafc;
}

.project-btn-inline.active::before {
    opacity: 1;
}

/* 按钮内容布局 */
.project-btn-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    min-width: 0;
    pointer-events: none; /* 确保点击事件传播到按钮 */
    position: relative;
    z-index: 1;
}

.project-btn-icon {
    color: #3b82f6;
    font-size: 1.1rem;
    flex-shrink: 0;
    pointer-events: none; /* 确保点击事件传播到按钮 */
}

.project-btn-text {
    color: #1f2937;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    pointer-events: none; /* 确保点击事件传播到按钮 */
}

.project-btn-arrow {
    color: #6b7280;
    font-size: 0.875rem;
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    position: relative;
    z-index: 1;
    pointer-events: none; /* 确保点击事件传播到按钮 */
}

.project-btn-inline.active .project-btn-arrow {
    transform: rotate(180deg);
    color: #3b82f6;
}

/* 现代化下拉菜单 */
.project-list {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    min-width: 380px;
    max-width: 520px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    max-height: 420px;
    overflow: hidden;
    display: none;
    backdrop-filter: blur(10px);
    animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-list.show {
    display: block;
}

/* 优化的动画效果 */
@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 下拉菜单内容区域 */
.project-list-content {
    max-height: 420px;
    overflow-y: auto;
    padding: 0.5rem 0;
}

/* 现代化项目选项 */
.project-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    margin: 0 0.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-height: 64px;
    border: 1px solid transparent;
}

.project-item:hover {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-color: #e2e8f0;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.project-item:active {
    transform: translateX(2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 创建新项目特殊样式 */
.project-item.create-new {
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    color: #1d4ed8;
    font-weight: 600;
    border: 1px solid #bfdbfe;
    margin-bottom: 0.5rem;
    justify-content: center;
    gap: 0.75rem;
}

.project-item.create-new:hover {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-color: #93c5fd;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.2);
}

.project-item.create-new .project-item-icon {
    color: #1d4ed8;
    font-size: 1.1rem;
}

.project-item.create-new .project-item-text {
    color: #1d4ed8;
    font-weight: 600;
}

/* 项目信息布局 */
.project-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 0;
    margin-left: 1rem;
}

.project-title {
    font-weight: 700;
    color: #111827;
    font-size: 1rem;
    line-height: 1.4;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.project-role {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.2;
    margin: 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-role::before {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #d1d5db;
}

/* 项目图标 */
.project-item-icon {
    color: #3b82f6;
    font-size: 1.25rem;
    flex-shrink: 0;
    width: 24px;
    text-align: center;
}

/* 项目内容布局 */
.project-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 0;
    margin-left: 0.75rem;
}

.project-item-text {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.95rem;
    line-height: 1.4;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.project-item-desc {
    font-size: 0.8rem;
    color: #6b7280;
    line-height: 1.2;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 400;
}

/* 选中的项目样式 */
.project-item.selected {
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.project-item.selected .project-item-icon {
    color: #1d4ed8;
}

.project-item.selected .project-item-text {
    color: #1d4ed8;
    font-weight: 700;
}

.project-item.selected .project-item-desc {
    color: #3b82f6;
}

/* 简洁的圆形状态指示器 */
.project-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #3b82f6;
    flex-shrink: 0;
    margin-left: auto;
    border: 2px solid #ffffff;
    box-shadow: 0 0 0 1px #e5e7eb;
    position: relative;
}

.project-status.suspended {
    background: #f59e0b;
}

.project-status.archived {
    background: #6b7280;
}

.project-status.active {
    background: #10b981;
}

/* 分割线 */
.project-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
    margin: 0.5rem 1rem;
}

/* 优化的滚动条 */
.project-list-content::-webkit-scrollbar {
    width: 8px;
}

.project-list-content::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
}

.project-list-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #cbd5e1, #94a3b8);
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

.project-list-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #94a3b8, #64748b);
}

/* 空状态样式 */
.project-list-empty {
    padding: 2rem 1.5rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
}

.project-list-empty i {
    font-size: 2rem;
    color: #d1d5db;
    margin-bottom: 0.5rem;
    display: block;
}

/* 仪表板样式 */
.dashboard-container {
    padding: 2rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #eff6ff;
    color: #3b82f6;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #6b7280;
    font-size: 0.9rem;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.dashboard-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.dashboard-section h3 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-weight: 600;
}

/* 全新项目概览样式 - 高对比度设计 */
.project-overview {
    background: #ffffff;
    border: 2px solid #e5e7eb;
    padding: 2.5rem;
    border-radius: 20px;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.project-overview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(16, 185, 129, 0.02));
    pointer-events: none;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.project-title {
    flex: 1;
    min-width: 0; /* 防止flex项目溢出 */
}

.project-title h3 {
    margin: 0 0 0.75rem 0;
    font-size: 1.75rem;
    font-weight: 800;
    color: #111827;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.project-title p {
    margin: 0;
    font-size: 1.1rem;
    color: #4b5563;
    line-height: 1.6;
    font-weight: 500;
}

.project-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 1rem;
    flex-shrink: 0;
    min-width: 200px;
}

.project-status-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}

.project-tools {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    justify-content: flex-end;
    width: 100%;
}

.project-tools .btn-icon {
    background: #f8fafc;
    color: #374151;
    border: 2px solid #e5e7eb;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.project-tools .btn-icon:hover {
    background: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.project-status .status-badge {
    display: none; /* 隐藏长条状徽章，使用简洁的圆形指示器 */
}

/* 移除所有长条状徽章相关样式 */

/* 状态指示器脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 项目概览响应式设计 */
@media (max-width: 768px) {
    .project-overview {
        padding: 1.5rem;
    }

    .project-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1.5rem;
    }

    .project-actions {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        min-width: auto;
    }

    .project-status-container {
        justify-content: flex-start;
        width: auto;
    }

    .project-tools {
        justify-content: flex-end;
        width: auto;
        gap: 0.5rem;
    }

    .project-tools .btn-icon {
        width: 36px;
        height: 36px;
    }

    .project-title h3 {
        font-size: 1.25rem;
    }

    .project-title p {
        font-size: 0.9rem;
    }
}

.status-completed {
    background: #dbeafe;
    color: #1e40af;
}

/* 总体进度样式 */
.progress-overview {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-header h4 {
    margin: 0;
    font-size: 1.125rem;
    color: #1f2937;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-percentage {
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
}

.progress-bar-container {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #f3f4f6;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1e40af);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.progress-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.progress-item:last-child {
    border-bottom: none;
}

.progress-label {
    color: #6b7280;
    font-size: 0.875rem;
}

.progress-value {
    font-weight: 600;
    color: #1f2937;
}

/* 章节进度样式 */
.chapter-progress-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h4 {
    margin: 0;
    font-size: 1.125rem;
    color: #1f2937;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-all-link {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.view-all-link:hover {
    text-decoration: underline;
}

.chapter-progress-list {
    max-height: 300px;
    overflow-y: auto;
}

.chapter-progress-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.chapter-progress-item:last-child {
    border-bottom: none;
}

.chapter-info {
    flex: 1;
}

.chapter-title {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.chapter-meta {
    font-size: 0.75rem;
    color: #6b7280;
}

.chapter-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-draft {
    background: #f3f4f6;
    color: #374151;
}

.status-writing {
    background: #fef3c7;
    color: #92400e;
}

.status-review {
    background: #dbeafe;
    color: #1e40af;
}

.status-approved {
    background: #dcfce7;
    color: #166534;
}

/* 权限管理样式 */
.permissions-management {
    max-width: 600px;
}

.permissions-management .section {
    margin-bottom: 2rem;
}

.permissions-management h3 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.permission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.permission-item {
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.permission-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    color: #374151;
}

.permission-item input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: #3b82f6;
}

.role-permissions {
    display: grid;
    gap: 1rem;
}

.role-item {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border-left: 4px solid #3b82f6;
}

.role-item strong {
    display: block;
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-size: 1rem;
}

.role-item ul {
    margin: 0;
    padding-left: 1.25rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.role-item li {
    margin: 0.25rem 0;
}

/* 系统设置样式 - 响应式流式布局 */
.settings-container {
    padding: 1.5rem;
    width: 100%;
    max-width: none;
    margin: 0;
}

/* 设置卡片容器 - 流式布局 */
.settings-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    width: 100%;
}

/* 响应式断点调整 */
@media (max-width: 1200px) {
    .settings-cards-container {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.25rem;
    }
}

@media (max-width: 768px) {
    .settings-cards-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .settings-container {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .settings-container {
        padding: 0.75rem;
    }

    .settings-cards-container {
        gap: 0.75rem;
    }
}

/* 设置卡片样式 */
.settings-section {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    transition: all 0.3s ease;
    height: fit-content;
}

.settings-section:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
}

.settings-section h3 {
    margin: 0 0 1.25rem 0;
    color: #111827;
    font-size: 1.125rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
}

.settings-section h3 i {
    color: #3b82f6;
    font-size: 1rem;
}

.settings-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.setting-item label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.setting-item .form-input,
.setting-item .form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #ffffff;
}

.setting-item .form-input:focus,
.setting-item .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-item .setting-help {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* 设置操作按钮 */
.setting-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #f3f4f6;
    flex-wrap: wrap;
}

.setting-actions .btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

@media (max-width: 480px) {
    .setting-actions {
        flex-direction: column;
    }

    .setting-actions .btn {
        width: 100%;
    }
}

/* 复选框样式优化 */
.setting-item input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
    transform: scale(1.1);
}

.setting-item label:has(input[type="checkbox"]) {
    flex-direction: row;
    align-items: center;
    font-weight: 400;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.setting-item label:has(input[type="checkbox"]):hover {
    background-color: #f9fafb;
}

/* 特殊卡片样式 */
.settings-section.ai-config {
    border-left: 4px solid #3b82f6;
}

.settings-section.ai-config h3 i {
    color: #3b82f6;
}

.settings-section.multimedia-config {
    border-left: 4px solid #10b981;
}

.settings-section.multimedia-config h3 i {
    color: #10b981;
}

.settings-section.editor-config {
    border-left: 4px solid #f59e0b;
}

.settings-section.editor-config h3 i {
    color: #f59e0b;
}

.settings-section.database-config {
    border-left: 4px solid #dc2626;
}

.settings-section.database-config h3 i {
    color: #dc2626;
}

.settings-section.database-management {
    border-left: 4px solid #059669;
}

.settings-section.database-management h3 i {
    color: #059669;
}

.settings-section.system-config {
    border-left: 4px solid #8b5cf6;
}

.settings-section.system-config h3 i {
    color: #8b5cf6;
}

/* 管理按钮组 */
.management-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.management-buttons .btn {
    flex: 1;
    min-width: 120px;
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
}

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.input-group .btn-icon {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border: 1px solid #d1d5db;
    background: #f9fafb;
    color: #6b7280;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.input-group .btn-icon:hover {
    background: #f3f4f6;
    color: #374151;
}

/* 数据库管理样式 */
.database-info {
    padding: 1rem 0;
}

.info-grid, .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.info-item, .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.info-item label, .stat-item label {
    font-weight: 500;
    color: #374151;
}

.status-connected {
    color: #059669;
    font-weight: 500;
}

.status-error {
    color: #dc2626;
    font-weight: 500;
}

.migration-dialog, .restore-dialog {
    padding: 1rem 0;
}

.warning-box {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.warning-box i {
    color: #f59e0b;
    font-size: 1.25rem;
}

.migration-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 1rem 0;
}

.migration-item {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.migration-item:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.migration-item input[type="radio"] {
    margin-right: 0.75rem;
}

.migration-item label {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.migration-item strong {
    color: #111827;
}

.migration-item small {
    color: #6b7280;
}

.status-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin: 1rem 0;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.check-actions {
    margin-top: 1.5rem;
    text-align: center;
}

.backup-tips {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f0f9ff;
    border-radius: 6px;
    border: 1px solid #0ea5e9;
}

.backup-tips h4 {
    margin: 0 0 0.75rem 0;
    color: #0c4a6e;
}

.backup-tips ul {
    margin: 0;
    padding-left: 1.25rem;
    color: #0c4a6e;
}

.pool-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin: 1rem 0;
}

/* 测试结果区域 */
.test-results {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
    .settings-cards-container {
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    }
}

/* 超宽屏优化 */
@media (min-width: 1800px) {
    .settings-cards-container {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1600px;
        margin: 0 auto;
    }
}

.setting-item label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.setting-item input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}

.setting-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #6b7280;
    font-style: italic;
}

.setting-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 0.5rem;
}

.feature-notice {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.notice-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.notice-content i {
    color: #0369a1;
    font-size: 1.25rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.notice-text {
    color: #0c4a6e;
    font-size: 0.875rem;
    line-height: 1.5;
}

.notice-text strong {
    display: block;
    margin-bottom: 0.5rem;
    color: #0369a1;
}

.notice-text ul {
    margin: 0;
    padding-left: 1.25rem;
}

.notice-text li {
    margin-bottom: 0.25rem;
}

/* 加载状态样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    padding: 2rem;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.spinner-ring {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 1rem;
}

.loading-progress {
    width: 200px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.progress-text {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-group .form-input {
    flex: 1;
}

.input-group .btn-icon {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.input-group .btn-icon:hover {
    background: #e5e7eb;
}

.setting-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.test-results {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.test-success {
    background: #f0fdf4;
    border-color: #16a34a;
    color: #15803d;
}

.test-error {
    background: #fef2f2;
    border-color: #dc2626;
    color: #dc2626;
}

.test-success h4,
.test-error h4 {
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* AI生成大纲样式 */
.ai-outline-form {
    max-width: 500px;
}

.ai-outline-form .form-group {
    margin-bottom: 1rem;
}

.ai-outline-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.ai-tips {
    margin-top: 1rem;
    padding: 1rem;
    background: #eff6ff;
    border-radius: 0.5rem;
    border-left: 4px solid #3b82f6;
}

.ai-tips h4 {
    margin: 0 0 0.5rem 0;
    color: #1e40af;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ai-tips ul {
    margin: 0;
    padding-left: 1.25rem;
    color: #1e40af;
    font-size: 0.8rem;
}

.ai-tips li {
    margin: 0.25rem 0;
}

/* AI生成进度样式 */
.ai-generation-progress {
    text-align: center;
    padding: 2rem;
    max-width: 500px;
    margin: 0 auto;
}

.progress-indicator {
    margin-bottom: 2.5rem;
}

.progress-indicator i {
    font-size: 3.5rem;
    color: #3b82f6;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.progress-indicator h3 {
    margin: 0 0 0.75rem 0;
    color: #1f2937;
    font-size: 1.25rem;
    font-weight: 600;
}

.progress-indicator p {
    margin: 0 0 1.5rem 0;
    color: #6b7280;
    font-size: 0.875rem;
}

/* 整体进度条 */
.overall-progress-bar {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.overall-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1e40af);
    border-radius: 3px;
    transition: width 0.8s ease;
    width: 0%;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 10%;
    right: 10%;
    height: 2px;
    background: #e5e7eb;
    z-index: 1;
}

.progress-steps .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    opacity: 0.4;
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
    flex: 1;
    max-width: 100px;
}

.progress-steps .step.active {
    opacity: 1;
    transform: scale(1.05);
}

.progress-steps .step.completed {
    opacity: 1;
}

.progress-steps .step.completed .step-icon {
    background: #10b981;
    color: white;
    transform: scale(1.1);
}

.progress-steps .step.active .step-icon {
    background: #3b82f6;
    color: white;
    animation: bounce 1s infinite;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-steps .step span {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

.progress-steps .step.active span {
    color: #3b82f6;
    font-weight: 600;
}

.progress-steps .step.completed span {
    color: #10b981;
    font-weight: 600;
}

/* 时间估计显示 */
.time-estimate {
    margin-top: 1rem;
    padding: 0.75rem 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.time-estimate .estimate-text {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0;
}

.time-estimate .estimate-time {
    font-weight: 600;
    color: #3b82f6;
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* AI结果显示样式 */
.ai-result-container {
    max-width: 600px;
}

.result-message {
    margin-bottom: 1rem;
    padding: 1rem;
    background: #fffbeb;
    border-radius: 0.5rem;
    border-left: 4px solid #f59e0b;
}

.result-message h4 {
    margin: 0 0 0.5rem 0;
    color: #92400e;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.result-message p {
    margin: 0;
    color: #92400e;
    font-size: 0.875rem;
}

.result-content {
    margin-bottom: 1rem;
}

.result-content label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* 导入大纲样式 */
.import-outline-form {
    max-width: 600px;
}

.import-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.2s;
}

.tab-btn:hover {
    color: #374151;
    background: #f9fafb;
}

.tab-btn.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: #eff6ff;
}

.import-tab-content {
    display: none;
}

.import-tab-content.active {
    display: block;
}

.import-tips {
    margin-top: 1rem;
    padding: 1rem;
    background: #f0f9ff;
    border-radius: 0.5rem;
    border-left: 4px solid #0ea5e9;
}

.import-tips h4 {
    margin: 0 0 0.5rem 0;
    color: #0c4a6e;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.import-tips p {
    margin: 0;
    color: #0c4a6e;
    font-size: 0.8rem;
}

/* 协作管理样式 */
.collaboration-container {
    padding: 1.5rem;
    height: 100%;
    overflow-y: auto;
}

.collaboration-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
    background: #f9fafb;
    border-radius: 8px 8px 0 0;
}

.collab-tab {
    padding: 1rem 1.5rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: 8px 8px 0 0;
}

.collab-tab:hover {
    color: #4f46e5;
    background: #f3f4f6;
}

.collab-tab.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
    background: white;
}

.collab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.collab-content.active {
    display: block;
}

/* 成员列表样式 */
.members-header,
.assignments-header,
.permissions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.members-header h3,
.assignments-header h3,
.permissions-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 1.2rem;
}

.members-stats,
.assignments-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.stat-item i {
    font-size: 1rem;
}

.text-success {
    color: #10b981 !important;
}

.members-list {
    display: grid;
    gap: 1rem;
}

.member-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
}

.member-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.member-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4f46e5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.member-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.member-info {
    flex: 1;
}

.member-info h4 {
    margin: 0 0 0.25rem 0;
    color: #1f2937;
    font-size: 1rem;
}

.member-email {
    color: #6b7280;
    font-size: 0.8rem;
    margin: 0 0 0.25rem 0;
}

.member-institution {
    color: #9ca3af;
    font-size: 0.75rem;
    margin: 0;
}

.member-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.btn-danger:hover {
    background: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

.loading-state {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.loading-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #4f46e5;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #d1d5db;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.collab-content {
    display: none;
}

.collab-content.active {
    display: block;
}

.members-list {
    display: grid;
    gap: 1rem;
}

.member-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
}

.member-avatar {
    position: relative;
}

.member-avatar img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.member-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.member-status.online {
    background: #10b981;
}

.member-status.offline {
    background: #6b7280;
}

.member-info {
    flex: 1;
}

.member-name {
    font-weight: 600;
    color: #1f2937;
}

.member-role {
    color: #3b82f6;
    font-size: 0.9rem;
}

.member-institution {
    color: #6b7280;
    font-size: 0.8rem;
}

.member-actions {
    display: flex;
    gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .tools-panel {
        display: none;
    }

    .diagram-container {
        flex-direction: column;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 180px;
    }

    .header {
        padding: 1rem;
    }

    .header h1 {
        font-size: 1.2rem;
    }

    .panel-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .online-users {
        display: none;
    }

    .header-center {
        display: none;
    }
}

/* 项目管理页面样式增强 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    animation: slideIn 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-success {
    background: #10b981;
}

.notification-error {
    background: #ef4444;
}

.notification-warning {
    background: #f59e0b;
}

.notification-info {
    background: #3b82f6;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 模态框样式增强 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 24px 24px 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.modal-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 1.25rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-content {
    padding: 0 24px;
}

.modal-footer {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
    margin-top: 24px;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    color: #374151;
    font-weight: 500;
    font-size: 0.875rem;
}

.form-input, .form-textarea, .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* 导出选项样式 */
.export-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
}

.export-options .btn {
    justify-content: flex-start;
    text-align: left;
}

.btn-outline {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background: #f9fafb;
    border-color: #4f46e5;
    color: #4f46e5;
}

/* 当前项目信息显示样式 */
.current-project-info {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1rem;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.project-details {
    flex: 1;
    min-width: 0;
}

.project-details h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
    word-wrap: break-word;
}

.project-details p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
    word-wrap: break-word;
}

.project-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.project-actions .btn {
    padding: 0.5rem;
    min-width: auto;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .project-header {
        flex-direction: column;
        align-items: stretch;
    }

    .project-actions {
        justify-content: flex-end;
        margin-top: 0.75rem;
    }
}

/* ============================================================================
   Drawer 侧边栏样式 (从 drawer-sidebar-test.html 移植)
   ============================================================================ */

/* 重新定义主容器布局 */
.main-content {
    display: flex;
    height: calc(100vh - 70px);
    position: relative;
}

/* 移动端遮罩 */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}



/* 内容区域重新定义 */
.content-area {
    flex: 1;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding: 1.5rem;
    overflow-y: auto;
    overflow-x: hidden;
}

/* 隐藏内容区域滚动条样式 */
.content-area::-webkit-scrollbar {
    width: 8px;
}

.content-area::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.content-area::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.content-area::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 右侧工具栏 */
.right-toolbar {
    width: 350px;
    /* min-width: 280px; */
    max-width: 600px;
    background: white;
    border-left: 1px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 999;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.right-toolbar.resizing {
    transition: none;
}

.right-toolbar.collapsed {
    width: 60px;
    overflow: hidden;
}

/* 右侧工具栏头部 */
.right-toolbar-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
    background: #f8fafc;
}

.right-toolbar.collapsed .right-toolbar-header {
    padding: 0.5rem 0;
    justify-content: center;
    flex-direction: column;
    gap: 0.5rem;
    min-height: 80px;
    align-items: center;
}

.toolbar-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    transition: opacity 0.3s ease;
}

.right-toolbar.collapsed .toolbar-title {
    justify-content: center;
    gap: 0;
    width: 100%;
}

.right-toolbar.collapsed .toolbar-title-text {
    display: none;
}

.toolbar-icon {
    font-size: 1.25rem;
    color: #3b82f6;
    flex-shrink: 0;
}

.right-toolbar.collapsed .toolbar-icon {
    font-size: 1.5rem;
    display: block;
    width: 100%;
    text-align: center;
    margin: 0;
    padding: 0;
}

/* 右侧切换按钮 */
.right-toggle-btn {
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    color: #64748b;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.right-toggle-btn:hover {
    background: #e2e8f0;
    color: #475569;
    transform: scale(1.05);
}

.right-toolbar.collapsed .right-toggle-btn {
    margin: 0 auto;
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
    flex-shrink: 0;
    align-self: center;
}

/* 工具选择器 */
.tool-selector {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: white;
}

.right-toolbar.collapsed .tool-selector {
    display: none;
}

.tool-select-wrapper {
    position: relative;
}

.tool-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tool-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tool-select:hover {
    border-color: #9ca3af;
}

/* 工具信息显示 */
.tool-info {
    padding: 0.75rem 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.75rem;
    color: #64748b;
    line-height: 1.4;
}

.right-toolbar.collapsed .tool-info {
    display: none;
}

/* iframe容器 */
.tool-iframe-container {
    flex: 1;
    padding: 1rem;
    background: white;
    overflow: hidden;
}

.right-toolbar.collapsed .tool-iframe-container {
    display: none;
}

.tool-iframe {
    width: 100%;
    height: 100%;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
}

/* 收缩状态的工具图标 */
.collapsed-tools {
    display: none;
    flex-direction: column;
    gap: 0.4rem;
    padding: 0.5rem 0.4rem;
    flex: 1;
    align-items: center;
    overflow-y: auto;
    margin-top: 0.5rem;
}

.right-toolbar.collapsed .collapsed-tools {
    display: flex;
}

.collapsed-tool-item {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    color: #64748b;
    position: relative;
    flex-shrink: 0;
    margin: 1px;
}

.collapsed-tool-item:hover {
    background: #e2e8f0;
    color: #475569;
    transform: scale(1.05);
}

.collapsed-tool-item.active {
    background: #dbeafe;
    border-color: #3b82f6;
    color: #3b82f6;
}

/* 拖拽调整宽度手柄 */
.resize-handle {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: transparent;
    cursor: col-resize;
    z-index: 1001;
    transition: background-color 0.2s ease;
}

.resize-handle:hover {
    background: #3b82f6;
}

.resize-handle.resizing {
    background: #2563eb;
}

.resize-handle::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    bottom: 0;
    width: 8px;
    background: transparent;
}

/* 工具管理模态框 */
.tool-management-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.tool-management-modal.show {
    display: flex;
}

.tool-management-modal .modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.tool-management-modal .modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tool-management-modal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.tool-management-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.tool-management-modal .modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.tool-management-modal .modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* 工具表格 */
.tools-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
}

.tools-table th,
.tools-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.tools-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
}

.tools-table td {
    color: #6b7280;
}

.tool-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-edit,
.btn-delete {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-edit {
    background: #dbeafe;
    color: #1d4ed8;
}

.btn-edit:hover {
    background: #bfdbfe;
}

.btn-delete {
    background: #fee2e2;
    color: #dc2626;
}

.btn-delete:hover {
    background: #fecaca;
}

/* 工具表单 */
.tool-form {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.form-input,
.form-textarea {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

/* 章节分配专用样式 */
.chapter-assignment-container {
    background: white;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.assignment-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
    border-radius: 12px 12px 0 0;
}

.header-left h3 {
    margin: 0 0 16px 0;
    color: #1f2937;
    font-size: 20px;
    font-weight: 600;
}

.assignment-stats {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.assignment-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    font-size: 14px;
    color: #374151;
}

.assignment-stats .stat-item i {
    color: #6366f1;
    font-size: 16px;
}

.assignment-stats .stat-item span {
    font-weight: 600;
    color: #1f2937;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 12px;
    color: #9ca3af;
    font-size: 14px;
}

.search-box input {
    padding: 8px 12px 8px 36px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
    background: white;
}

.search-box input:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.status-filter {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    color: #374151;
    min-width: 120px;
}

.status-filter:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.assignments-container {
    padding: 0;
}

.assignments-header-row {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1fr 1fr 1fr 1fr 1.2fr 1fr;
    gap: 16px;
    padding: 16px 24px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    font-size: 13px;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-cell {
    display: flex;
    align-items: center;
}

.assignments-list {
    min-height: 200px;
}

.assignment-item {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1fr 1fr 1fr 1fr 1.2fr 1fr;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid #f3f4f6;
    align-items: center;
    transition: background-color 0.2s ease;
}

.assignment-item:hover {
    background: #f9fafb;
}

.assignment-item:last-child {
    border-bottom: none;
}

.assignment-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 15px;
    margin-bottom: 4px;
}

.assignment-subtitle {
    font-size: 13px;
    color: #6b7280;
}

.user-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #374151;
}

.user-badge i {
    color: #9ca3af;
    font-size: 12px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-writing {
    background: #dbeafe;
    color: #1e40af;
}

.status-reviewing {
    background: #e0e7ff;
    color: #5b21b6;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.status-rejected {
    background: #fee2e2;
    color: #991b1b;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #6366f1, #8b5cf6);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.due-date {
    font-size: 13px;
    color: #6b7280;
}

.due-date.overdue {
    color: #dc2626;
    font-weight: 500;
}

.actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: #f3f4f6;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.action-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

.action-btn:active {
    transform: scale(0.95);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 18px;
    font-weight: 600;
}

.empty-state p {
    margin: 0 0 24px 0;
    font-size: 14px;
}

.loading-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.loading-state i {
    font-size: 24px;
    margin-bottom: 16px;
    color: #6366f1;
}

.loading-state p {
    margin: 0;
    font-size: 14px;
}

/* 编辑分配模态对话框样式 */
.assignment-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.assignment-modal.active {
    opacity: 1;
    visibility: visible;
}

.assignment-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.assignment-modal.active .assignment-modal-content {
    transform: translateY(0);
}

.assignment-modal-header {
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.assignment-modal-title {
    margin: 0 0 16px 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 12px;
}

.assignment-modal-title i {
    color: #6366f1;
}

.assignment-modal-body {
    padding: 0 24px 24px 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.user-selector {
    position: relative;
}

.user-selector-input {
    cursor: pointer;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.user-dropdown.active {
    display: block;
}

.user-option {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-option:hover {
    background: #f3f4f6;
}

.user-option.selected {
    background: #eff6ff;
    color: #2563eb;
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6b7280;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 500;
    color: #1f2937;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: #6b7280;
}

.assignment-modal-footer {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 查看分配详情样式 */
.assignment-details {
    max-height: 60vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #6366f1;
}

.detail-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-section h4 i {
    color: #6366f1;
    font-size: 14px;
}

.detail-item {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item label {
    font-weight: 500;
    color: #374151;
    min-width: 100px;
    flex-shrink: 0;
}

.detail-item span {
    color: #6b7280;
    flex: 1;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 6px;
}

.user-info i {
    color: #6366f1;
    font-size: 12px;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.progress-container .progress-bar {
    flex: 1;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-container .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #34d399);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    min-width: 35px;
}

.priority-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.priority-high {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.priority-medium {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.priority-low {
    background: #f0f9ff;
    color: #0284c7;
    border: 1px solid #bae6fd;
}

.overdue {
    color: #dc2626 !important;
    font-weight: 500;
}

.modal-close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    font-size: 18px;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

/* 通知动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    transition: all 0.3s ease;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.notification-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #6366f1;
    color: white;
}

.btn-primary:hover {
    background: #5b21b6;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
    /* 章节分配响应式 */
    .assignment-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .header-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .search-box input {
        width: 100%;
    }

    .assignments-header-row {
        display: none;
    }

    .assignment-item {
        display: block;
        padding: 16px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 12px;
        background: white;
    }

    .assignment-item:hover {
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .assignment-title {
        font-size: 16px;
        margin-bottom: 8px;
    }

    .assignment-subtitle {
        margin-bottom: 12px;
    }

    .assignment-meta {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 12px;
    }

    .assignment-meta-item {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .assignment-meta-label {
        font-size: 11px;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    .progress-section {
        margin-bottom: 12px;
    }

    .actions {
        justify-content: flex-end;
        padding-top: 12px;
        border-top: 1px solid #f3f4f6;
    }

    .sidebar {
        position: fixed;
        left: -320px;
        z-index: 1001;
        height: 100vh;
        top: 0;
    }

    .sidebar.open {
        left: 0;
    }

    .sidebar.collapsed {
        left: -70px;
    }

    .sidebar.collapsed.open {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .content-area {
        width: 100%;
    }

    /* 移动端右侧工具栏 */
    .right-toolbar {
        position: fixed;
        right: -350px;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 1002;
    }

    .right-toolbar.open {
        right: 0;
    }

    .right-toolbar.collapsed {
        right: -52px;
    }

    .right-toolbar.collapsed.open {
        right: 0;
    }
}

/* 动画效果 */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}




