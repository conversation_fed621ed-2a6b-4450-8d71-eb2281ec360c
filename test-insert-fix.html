<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插入功能修复测试</title>
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8fafc;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .editor-container {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            min-height: 300px;
            margin-bottom: 2rem;
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .status.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }
        
        .info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .info h3 {
            margin: 0 0 0.5rem 0;
            color: #0369a1;
        }
        
        .info p {
            margin: 0;
            color: #0c4a6e;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 插入功能修复测试</h1>
        
        <div class="info">
            <h3>测试说明</h3>
            <p>这个页面用于测试修复后的插入功能。请先在编辑器中输入一些文本，然后测试各种插入功能。</p>
        </div>
        
        <div class="editor-container" id="editor"></div>
        
        <div class="test-buttons">
            <button class="btn btn-primary" onclick="testInsertText()">测试插入文本</button>
            <button class="btn btn-primary" onclick="testInsertImage()">测试插入图片</button>
            <button class="btn btn-secondary" onclick="clearEditor()">清空编辑器</button>
            <button class="btn btn-secondary" onclick="checkEditorStatus()">检查编辑器状态</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="pollinations-service.js"></script>
    <script src="multimedia-handlers.js"></script>
    <script>
        // 初始化编辑器
        let quillEditor = null;
        
        function initEditor() {
            const editorContainer = document.getElementById('editor');
            
            quillEditor = new Quill(editorContainer, {
                theme: 'snow',
                placeholder: '在这里输入一些文本，然后测试插入功能...',
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline'],
                        ['link', 'image'],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['clean']
                    ]
                }
            });
            
            // 设置为全局变量
            window.quillEditor = quillEditor;
            
            showStatus('编辑器初始化成功', 'success');
        }
        
        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }
        
        function testInsertText() {
            const testText = '这是一段测试文本，用于验证文本插入功能是否正常工作。插入时间：' + new Date().toLocaleTimeString();
            
            if (typeof insertTextToEditor === 'function') {
                const success = insertTextToEditor(testText);
                if (success) {
                    showStatus('文本插入测试成功！', 'success');
                } else {
                    showStatus('文本插入测试失败！', 'error');
                }
            } else {
                showStatus('insertTextToEditor函数未找到', 'error');
            }
        }
        
        function testInsertImage() {
            // 使用一个测试图片URL
            const testImageUrl = 'https://via.placeholder.com/300x200/3b82f6/ffffff?text=Test+Image';
            
            if (typeof insertImageToEditor === 'function') {
                const success = insertImageToEditor(testImageUrl, '测试图片');
                if (success) {
                    showStatus('图片插入测试成功！', 'success');
                } else {
                    showStatus('图片插入测试失败！', 'error');
                }
            } else {
                showStatus('insertImageToEditor函数未找到', 'error');
            }
        }
        
        function clearEditor() {
            if (window.quillEditor) {
                window.quillEditor.setContents('');
                showStatus('编辑器已清空', 'success');
            } else {
                showStatus('编辑器未初始化', 'error');
            }
        }
        
        function checkEditorStatus() {
            const status = {
                quillEditorExists: !!window.quillEditor,
                localQuillExists: !!quillEditor,
                editorContent: window.quillEditor ? window.quillEditor.getText().length : 0,
                insertTextFunction: typeof insertTextToEditor === 'function',
                insertImageFunction: typeof insertImageToEditor === 'function'
            };
            
            let message = '编辑器状态检查:\n';
            message += `全局编辑器: ${status.quillEditorExists ? '✓' : '✗'}\n`;
            message += `本地编辑器: ${status.localQuillExists ? '✓' : '✗'}\n`;
            message += `内容长度: ${status.editorContent} 字符\n`;
            message += `插入文本函数: ${status.insertTextFunction ? '✓' : '✗'}\n`;
            message += `插入图片函数: ${status.insertImageFunction ? '✓' : '✗'}`;
            
            alert(message);
        }
        
        // 模拟showNotification函数
        function showNotification(message, type) {
            showStatus(message, type);
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEditor();
        });
    </script>
</body>
</html>
