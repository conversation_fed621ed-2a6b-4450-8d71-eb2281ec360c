#!/bin/bash

# AI增强学术专著编写系统 - 数据恢复脚本
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用帮助
show_help() {
    cat << EOF
AI增强学术专著编写系统恢复脚本

用法: $0 [选项] <备份文件路径>

选项:
  -h, --help              显示此帮助信息
  -t, --type TYPE         恢复类型 (database|storage|config|full)
  -f, --force             强制恢复，不进行确认
  --no-backup             恢复前不创建当前数据备份

恢复类型:
  database                仅恢复数据库
  storage                 仅恢复存储文件
  config                  仅恢复配置文件
  full                    完整恢复 (默认)

示例:
  $0 /path/to/backup/database_20240115_120000.sql.gz
  $0 -t storage /path/to/backup/storage_20240115_120000.tar.gz
  $0 -t full /path/to/backup/directory/
  $0 --force --no-backup /path/to/backup/

注意: 恢复操作会覆盖现有数据，请谨慎操作！
EOF
}

# 检查备份文件
check_backup_file() {
    local backup_path=$1
    
    if [ ! -e "$backup_path" ]; then
        log_error "备份文件或目录不存在: $backup_path"
        exit 1
    fi
    
    log_success "备份文件检查通过: $backup_path"
}

# 确认恢复操作
confirm_restore() {
    if [ "$FORCE_RESTORE" = "true" ]; then
        return 0
    fi
    
    log_warning "恢复操作将覆盖现有数据！"
    echo -n "确定要继续吗？(yes/no): "
    read -r response
    
    if [ "$response" != "yes" ]; then
        log_info "恢复操作已取消"
        exit 0
    fi
}

# 创建当前数据备份
backup_current_data() {
    if [ "$NO_BACKUP" = "true" ]; then
        log_info "跳过当前数据备份"
        return 0
    fi
    
    log_info "创建当前数据备份..."
    
    local backup_script="./scripts/backup.sh"
    if [ -f "$backup_script" ]; then
        $backup_script -d "/tmp/restore_backup_$(date +%Y%m%d_%H%M%S)"
        log_success "当前数据备份完成"
    else
        log_warning "备份脚本不存在，跳过当前数据备份"
    fi
}

# 停止服务
stop_services() {
    log_info "停止Docker服务..."
    docker-compose down
    log_success "服务已停止"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 30
    
    if docker-compose ps | grep -q "Up"; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    local backup_file=$1
    
    log_info "恢复数据库: $backup_file"
    
    # 检查文件格式
    if [[ "$backup_file" == *.gz ]]; then
        log_info "解压数据库备份文件..."
        gunzip -c "$backup_file" > /tmp/restore_db.sql
        backup_file="/tmp/restore_db.sql"
    fi
    
    # 获取数据库连接信息
    source .env
    
    # 启动数据库服务
    docker-compose up -d postgres
    sleep 10
    
    # 恢复数据库
    docker-compose exec -T postgres psql \
        -U $POSTGRES_USER \
        -d $POSTGRES_DB \
        -f /dev/stdin < "$backup_file"
    
    # 清理临时文件
    if [ -f "/tmp/restore_db.sql" ]; then
        rm /tmp/restore_db.sql
    fi
    
    log_success "数据库恢复完成"
}

# 恢复存储文件
restore_storage() {
    local backup_file=$1
    
    log_info "恢复存储文件: $backup_file"
    
    # 停止存储服务
    docker-compose stop storage
    
    # 备份当前存储数据
    if [ -d "volumes/storage_data" ] && [ "$NO_BACKUP" != "true" ]; then
        mv volumes/storage_data volumes/storage_data.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 创建存储目录
    mkdir -p volumes
    
    # 解压存储备份
    tar -xzf "$backup_file" -C volumes/
    
    log_success "存储文件恢复完成"
}

# 恢复配置文件
restore_config() {
    local backup_file=$1
    
    log_info "恢复配置文件: $backup_file"
    
    # 备份当前配置
    if [ "$NO_BACKUP" != "true" ]; then
        local config_backup_dir="config_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$config_backup_dir"
        
        [ -f ".env" ] && cp .env "$config_backup_dir/"
        [ -f "docker-compose.yml" ] && cp docker-compose.yml "$config_backup_dir/"
        [ -d "nginx" ] && cp -r nginx "$config_backup_dir/"
        
        log_info "当前配置已备份到: $config_backup_dir"
    fi
    
    # 解压配置备份
    tar -xzf "$backup_file"
    
    log_success "配置文件恢复完成"
}

# 完整恢复
restore_full() {
    local backup_dir=$1
    
    log_info "执行完整恢复: $backup_dir"
    
    # 查找最新的备份文件
    local db_backup=$(find "$backup_dir" -name "database_*.sql.gz" | sort | tail -1)
    local storage_backup=$(find "$backup_dir" -name "storage_*.tar.gz" | sort | tail -1)
    local config_backup=$(find "$backup_dir" -name "config_*.tar.gz" | sort | tail -1)
    
    # 恢复配置文件
    if [ -n "$config_backup" ]; then
        restore_config "$config_backup"
    else
        log_warning "未找到配置备份文件"
    fi
    
    # 恢复存储文件
    if [ -n "$storage_backup" ]; then
        restore_storage "$storage_backup"
    else
        log_warning "未找到存储备份文件"
    fi
    
    # 恢复数据库
    if [ -n "$db_backup" ]; then
        restore_database "$db_backup"
    else
        log_error "未找到数据库备份文件"
        exit 1
    fi
    
    log_success "完整恢复完成"
}

# 验证恢复结果
verify_restore() {
    log_info "验证恢复结果..."
    
    # 检查数据库连接
    source .env
    if docker-compose exec -T postgres pg_isready -U $POSTGRES_USER -d $POSTGRES_DB; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        return 1
    fi
    
    # 检查表是否存在
    local table_count=$(docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema='public';" | tr -d ' ')
    
    if [ "$table_count" -gt 0 ]; then
        log_success "数据库表恢复正常 ($table_count 个表)"
    else
        log_error "数据库表未正确恢复"
        return 1
    fi
    
    # 检查存储目录
    if [ -d "volumes/storage_data" ]; then
        log_success "存储目录恢复正常"
    else
        log_warning "存储目录不存在"
    fi
    
    log_success "恢复验证通过"
}

# 主函数
main() {
    local backup_path=$1
    
    if [ -z "$backup_path" ]; then
        log_error "请指定备份文件或目录路径"
        show_help
        exit 1
    fi
    
    echo "=== AI增强学术专著编写系统恢复脚本 ==="
    echo "开始时间: $(date)"
    echo ""
    
    check_backup_file "$backup_path"
    confirm_restore
    backup_current_data
    stop_services
    
    case $RESTORE_TYPE in
        "database")
            restore_database "$backup_path"
            ;;
        "storage")
            restore_storage "$backup_path"
            ;;
        "config")
            restore_config "$backup_path"
            ;;
        "full"|*)
            if [ -d "$backup_path" ]; then
                restore_full "$backup_path"
            else
                log_error "完整恢复需要指定备份目录"
                exit 1
            fi
            ;;
    esac
    
    start_services
    verify_restore
    
    log_success "恢复操作完成！"
    echo "结束时间: $(date)"
}

# 默认参数
RESTORE_TYPE="full"
FORCE_RESTORE="false"
NO_BACKUP="false"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--type)
            RESTORE_TYPE="$2"
            shift 2
            ;;
        -f|--force)
            FORCE_RESTORE="true"
            shift
            ;;
        --no-backup)
            NO_BACKUP="true"
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            # 备份文件路径
            main "$1"
            exit 0
            ;;
    esac
done

# 如果没有提供备份路径
log_error "请指定备份文件或目录路径"
show_help
exit 1
