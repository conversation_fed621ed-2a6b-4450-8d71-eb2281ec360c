<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出格式测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4f46e5;
            padding-bottom: 15px;
        }
        .format-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .format-section h2 {
            color: #4f46e5;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            font-family: inherit;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn:hover {
            background: #4338ca;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .format-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-size: 14px;
        }
        .format-info h4 {
            margin-top: 0;
            color: #0369a1;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #10b981;
            font-weight: bold;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 导出格式功能测试</h1>
        
        <div class="format-section">
            <h2>📊 格式对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>格式</th>
                        <th>文件后缀</th>
                        <th>中文支持</th>
                        <th>打开方式</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><i class="fas fa-file-pdf"></i> PDF</td>
                        <td>新窗口打印</td>
                        <td>✅ 完美</td>
                        <td>浏览器打印功能</td>
                        <td>正式文档、打印</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-file-word"></i> DOCX</td>
                        <td>.docx</td>
                        <td>✅ 完美</td>
                        <td>Word、浏览器</td>
                        <td>编辑、协作</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-globe"></i> HTML</td>
                        <td>.html</td>
                        <td>✅ 完美</td>
                        <td>浏览器</td>
                        <td>网页展示、分享</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-file-code"></i> JSON</td>
                        <td>.json</td>
                        <td>✅ 支持</td>
                        <td>文本编辑器</td>
                        <td>数据处理、备份</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="format-section">
            <h2><i class="fas fa-file-pdf"></i> PDF格式测试</h2>
            <div class="format-info">
                <h4>技术特点：</h4>
                <ul class="feature-list">
                    <li>在新窗口中打开打印预览页面</li>
                    <li>使用浏览器原生打印功能生成PDF</li>
                    <li>完美支持中文显示和格式</li>
                    <li>专业的打印样式优化</li>
                </ul>
            </div>
            <button class="btn" onclick="testExport('pdf')" id="pdf-btn">
                <i class="fas fa-file-pdf"></i> 测试PDF导出
            </button>
            <div id="pdf-status" class="status" style="display: none;"></div>
        </div>

        <div class="format-section">
            <h2><i class="fas fa-file-word"></i> DOCX格式测试</h2>
            <div class="format-info">
                <h4>技术特点：</h4>
                <ul class="feature-list">
                    <li>HTML内容，保存为.docx后缀</li>
                    <li>可被Word和浏览器打开</li>
                    <li>支持中文和格式样式</li>
                    <li>便于进一步编辑</li>
                </ul>
            </div>
            <button class="btn" onclick="testExport('docx')" id="docx-btn">
                <i class="fas fa-file-word"></i> 测试DOCX导出
            </button>
            <div id="docx-status" class="status" style="display: none;"></div>
        </div>

        <div class="format-section">
            <h2><i class="fas fa-globe"></i> HTML格式测试</h2>
            <div class="format-info">
                <h4>技术特点：</h4>
                <ul class="feature-list">
                    <li>标准网页格式，保存为.html后缀</li>
                    <li>浏览器直接打开，无需其他软件</li>
                    <li>响应式设计，支持打印</li>
                    <li>便于网页分享和展示</li>
                </ul>
            </div>
            <button class="btn" onclick="testExport('html')" id="html-btn">
                <i class="fas fa-globe"></i> 测试HTML导出
            </button>
            <div id="html-status" class="status" style="display: none;"></div>
        </div>

        <div class="format-section">
            <h2><i class="fas fa-file-code"></i> JSON格式测试</h2>
            <div class="format-info">
                <h4>技术特点：</h4>
                <ul class="feature-list">
                    <li>原始数据格式，包含完整项目信息</li>
                    <li>便于程序处理和数据分析</li>
                    <li>支持数据备份和迁移</li>
                    <li>开发者友好格式</li>
                </ul>
            </div>
            <button class="btn" onclick="testExport('json')" id="json-btn">
                <i class="fas fa-file-code"></i> 测试JSON导出
            </button>
            <div id="json-status" class="status" style="display: none;"></div>
        </div>

        <div class="format-section">
            <h2>📋 测试说明</h2>
            <div class="format-info">
                <h4>测试步骤：</h4>
                <ol>
                    <li>点击对应格式的测试按钮</li>
                    <li>系统会自动生成测试数据并导出</li>
                    <li>检查下载的文件是否正确</li>
                    <li>验证中文显示和格式是否正常</li>
                </ol>
                <h4>注意事项：</h4>
                <ul>
                    <li>DOCX格式实际为HTML内容，但后缀为.docx</li>
                    <li>HTML格式为标准网页，后缀为.html</li>
                    <li>两者内容相同，但用途不同</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="export-service.js"></script>
    <script>
        // 测试数据
        const testData = {
            project: {
                id: 'format-test-001',
                title: '导出格式测试项目',
                description: '这是一个用于测试各种导出格式的示例项目，包含中文内容、特殊字符和格式测试。',
                created_at: new Date().toISOString(),
                status: 'active'
            },
            outlines: [
                {
                    id: 'outline-1',
                    title: '第一章 导出功能概述',
                    level: 1,
                    sort_order: 1,
                    children: [
                        {
                            id: 'outline-1-1',
                            title: '1.1 PDF导出技术',
                            level: 2,
                            sort_order: 1,
                            children: []
                        },
                        {
                            id: 'outline-1-2',
                            title: '1.2 HTML导出技术',
                            level: 2,
                            sort_order: 2,
                            children: []
                        }
                    ]
                }
            ],
            chapters: [
                {
                    id: 'chapter-1',
                    title: '第一章 导出功能概述',
                    summary: '本章详细介绍了系统支持的各种导出格式，包括PDF、DOCX、HTML和JSON格式的特点和适用场景。',
                    outline_id: 'outline-1',
                    project_id: 'format-test-001',
                    word_count: 1500
                }
            ],
            references: [
                {
                    id: 'ref-1',
                    title: 'HTML5 Canvas技术指南',
                    authors: '张三',
                    year: 2023,
                    type: 'book'
                }
            ],
            members: [],
            exportDate: new Date().toISOString(),
            exportVersion: '2.0'
        };

        function showStatus(format, message, type) {
            const statusDiv = document.getElementById(`${format}-status`);
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        async function testExport(format) {
            const btn = document.getElementById(`${format}-btn`);
            btn.disabled = true;
            btn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 导出中...`;

            showStatus(format, `正在生成${format.toUpperCase()}格式文件...`, 'info');

            try {
                // 检查导出服务是否可用
                if (typeof exportService === 'undefined') {
                    throw new Error('导出服务未加载，请检查export-service.js文件');
                }

                // 直接调用对应的导出方法
                let result;
                switch(format) {
                    case 'pdf':
                        result = await exportService.exportToPDF(testData);
                        break;
                    case 'docx':
                        result = await exportService.exportToDOCX(testData);
                        break;
                    case 'html':
                        result = await exportService.exportToHTML(testData);
                        break;
                    case 'json':
                        result = await exportService.exportToJSON(testData);
                        break;
                    default:
                        throw new Error(`不支持的格式: ${format}`);
                }

                console.log(`${format}导出结果:`, result);

                let message = `${format.toUpperCase()}导出成功！`;
                if (result.note) {
                    message += ` ${result.note}`;
                }

                showStatus(format, message, 'success');

            } catch (error) {
                console.error(`${format}导出失败:`, error);
                showStatus(format, `${format.toUpperCase()}导出失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                const icons = {
                    pdf: 'fa-file-pdf',
                    docx: 'fa-file-word',
                    html: 'fa-globe',
                    json: 'fa-file-code'
                };
                btn.innerHTML = `<i class="fas ${icons[format]}"></i> 测试${format.toUpperCase()}导出`;
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('导出格式测试页面加载完成');

            // 检查导出服务加载状态
            setTimeout(() => {
                if (typeof exportService !== 'undefined') {
                    console.log('✅ 导出服务已加载');
                    console.log('支持的格式:', exportService.supportedFormats);
                } else {
                    console.log('❌ 导出服务未加载');
                    console.log('请检查export-service.js文件是否正确加载');
                }
            }, 100);
        });
    </script>
</body>
</html>
