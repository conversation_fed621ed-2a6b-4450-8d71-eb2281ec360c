<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - 项目管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .test-header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 项目管理页面样式 */
        .project-management-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
        }

        .project-header {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .project-header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .brand {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .brand-icon {
            font-size: 2rem;
            color: #4f46e5;
        }

        .brand-title {
            font-size: 1.8rem;
            color: #000000 !important;
            margin: 0;
            font-weight: 600;
        }

        .projects-section {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            color: #000000;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
        }

        .project-card {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 280px;
            display: flex;
            flex-direction: column;
        }

        .project-card:hover {
            border-color: #4f46e5;
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
            transform: translateY(-2px);
        }

        .project-meta {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 16px;
            gap: 12px;
        }

        .project-status {
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
            min-width: fit-content;
            flex-shrink: 0;
            display: inline-flex;
            align-items: center;
            line-height: 1;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-completed {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-draft {
            background: #fef3c7;
            color: #92400e;
        }

        .project-date {
            color: #9ca3af;
            font-size: 0.75rem;
            white-space: nowrap;
            flex-shrink: 0;
            display: inline-flex;
            align-items: center;
            line-height: 1;
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
            line-height: 1.4;
        }

        .project-description {
            color: #6b7280;
            font-size: 0.875rem;
            margin: 0 0 16px 0;
            line-height: 1.5;
        }

        .project-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            padding: 12px 0;
            border-top: 1px solid #f3f4f6;
            border-bottom: 1px solid #f3f4f6;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .stat-item i {
            color: #9ca3af;
        }

        .project-progress {
            margin-bottom: 16px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .progress-text {
            color: #374151;
            font-weight: 500;
        }

        .progress-percentage {
            color: #4f46e5;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4f46e5;
            transition: width 0.3s ease;
        }

        .project-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
            margin-top: auto;
        }

        .project-card:hover .project-actions {
            opacity: 1;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: all 0.3s ease;
        }

        .action-btn.edit {
            background: #f3f4f6;
            color: #374151;
        }

        .action-btn.edit:hover {
            background: #e5e7eb;
        }

        .action-btn.export {
            background: #ecfdf5;
            color: #065f46;
        }

        .action-btn.export:hover {
            background: #d1fae5;
        }

        .action-btn.delete {
            background: #fef2f2;
            color: #991b1b;
        }

        .action-btn.delete:hover {
            background: #fee2e2;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .projects-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .project-card {
                min-height: auto;
                padding: 20px;
            }
            
            .project-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            
            .project-status {
                align-self: flex-start;
            }
        }

        @media (max-width: 480px) {
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .project-card {
                padding: 16px;
            }
            
            .project-stats {
                flex-wrap: wrap;
                gap: 12px;
            }
            
            .stat-item {
                min-width: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-layout"></i> 布局测试</h1>
            <p>测试项目管理页面的布局修复效果</p>
        </div>

        <div class="test-section">
            <h2>修复内容</h2>
            <ul>
                <li>✅ 项目状态改为横向单行显示</li>
                <li>✅ 项目状态和日期改为左对齐</li>
                <li>✅ "项目管理"标题改为黑色</li>
                <li>✅ 增加项目卡片最小宽度</li>
                <li>✅ 优化响应式布局</li>
            </ul>
        </div>

        <!-- 模拟项目管理页面 -->
        <div class="project-management-container">
            <div class="project-header">
                <div class="project-header-left">
                    <div class="brand">
                        <i class="fas fa-feather-alt brand-icon"></i>
                        <h1 class="brand-title">项目管理</h1>
                    </div>
                </div>
            </div>

            <div class="projects-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-folder-open"></i>
                        我的项目
                    </h2>
                </div>

                <div class="projects-grid">
                    <!-- 测试项目卡片 1 -->
                    <div class="project-card">
                        <div class="project-meta">
                            <div class="project-status status-active">进行中</div>
                            <div class="project-date">2024/7/17</div>
                        </div>
                        <h3 class="project-title">智能物联网</h3>
                        <p class="project-description">智能物联网系统开发项目，包含硬件设计、软件开发和系统集成等多个模块。</p>
                        <div class="project-stats">
                            <div class="stat-item">
                                <i class="fas fa-book"></i>
                                <span>8 章节</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-file-alt"></i>
                                <span>3.2k 字</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>3 成员</span>
                            </div>
                        </div>
                        <div class="project-progress">
                            <div class="progress-label">
                                <span class="progress-text">完成进度</span>
                                <span class="progress-percentage">65%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 65%"></div>
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="action-btn export">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="action-btn delete">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>

                    <!-- 测试项目卡片 2 -->
                    <div class="project-card">
                        <div class="project-meta">
                            <div class="project-status status-completed">已完成</div>
                            <div class="project-date">2024/7/17</div>
                        </div>
                        <h3 class="project-title">《大模型技术与油气应用概论》</h3>
                        <p class="project-description">面向大学生的大模型技术教材，涵盖理论基础、实践应用和行业案例。</p>
                        <div class="project-stats">
                            <div class="stat-item">
                                <i class="fas fa-book"></i>
                                <span>12 章节</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-file-alt"></i>
                                <span>8.5k 字</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>5 成员</span>
                            </div>
                        </div>
                        <div class="project-progress">
                            <div class="progress-label">
                                <span class="progress-text">完成进度</span>
                                <span class="progress-percentage">100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 100%"></div>
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="action-btn export">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="action-btn delete">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>

                    <!-- 测试项目卡片 3 -->
                    <div class="project-card">
                        <div class="project-meta">
                            <div class="project-status status-draft">草稿</div>
                            <div class="project-date">2024/7/16</div>
                        </div>
                        <h3 class="project-title">人工智能伦理研究</h3>
                        <p class="project-description">探讨人工智能发展中的伦理问题和社会影响。</p>
                        <div class="project-stats">
                            <div class="stat-item">
                                <i class="fas fa-book"></i>
                                <span>6 章节</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-file-alt"></i>
                                <span>1.8k 字</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>2 成员</span>
                            </div>
                        </div>
                        <div class="project-progress">
                            <div class="progress-label">
                                <span class="progress-text">完成进度</span>
                                <span class="progress-percentage">25%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 25%"></div>
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="action-btn edit">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="action-btn export">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="action-btn delete">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
