<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主应用AI助手功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .test-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .app-container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e5e7eb;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #6b7280;
        }
        
        .nav-item:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .nav-item.active {
            background: #eff6ff;
            color: #2563eb;
            border-right: 3px solid #2563eb;
        }
        
        .main-content {
            flex: 1;
            background: white;
            overflow: hidden;
        }
        
        .panel {
            height: 100%;
            display: none;
            flex-direction: column;
        }
        
        .panel.active {
            display: flex;
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        
        .panel-header h2 {
            margin: 0;
            color: #1f2937;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .panel-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .chapter-editor {
            flex: 1;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            overflow: auto;
        }
        
        .chapter-meta {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .editor-wrapper {
            position: relative;
            flex: 1;
            min-height: 400px;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
        }
        
        .success-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            z-index: 10000;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1><i class="fas fa-magic"></i> 主应用AI助手功能测试</h1>
        <p>在真实的主应用环境中测试AI助手的完整功能</p>
    </div>

    <div class="app-container">
        <div class="sidebar">
            <nav class="nav-menu">
                <div class="nav-item active" data-tab="editor">
                    <i class="fas fa-edit"></i>
                    <span>章节编写</span>
                </div>
                <div class="nav-item" data-tab="outline">
                    <i class="fas fa-list"></i>
                    <span>书籍目录</span>
                </div>
                <div class="nav-item" data-tab="references">
                    <i class="fas fa-bookmark"></i>
                    <span>文献管理</span>
                </div>
            </nav>
        </div>
        
        <div class="main-content">
            <div id="editor-panel" class="panel active">
                <div class="panel-header">
                    <h2><i class="fas fa-edit"></i> 章节编写</h2>
                    <div class="panel-actions">
                        <select id="chapter-selector" onchange="loadChapter()">
                            <option value="">选择章节</option>
                            <option value="chapter-1">第1章 大模型基本概念与内涵</option>
                            <option value="chapter-2">第2章 大模型技术发展历程</option>
                            <option value="chapter-3">第3章 勘探开发中的应用</option>
                        </select>
                        <button class="btn btn-sm btn-primary" onclick="saveChapter()">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                </div>
                <div class="chapter-editor">
                    <div class="chapter-meta">
                        <input type="text" id="chapter-title" placeholder="章节标题" class="form-input" value="第1章 大模型基本概念与内涵">
                        <textarea id="chapter-summary" placeholder="章节摘要" class="form-textarea">介绍大模型的演进历程、定义、类型及其能力特点。</textarea>
                    </div>
                    <div class="editor-wrapper">
                        <div id="chapter-content-editor" class="editor-container">
                            <!-- Quill编辑器将在这里初始化 -->
                        </div>
                        <!-- AI助手悬浮按钮 -->
                        <div id="ai-assistant-fab" class="ai-fab">
                            <button class="fab-main-btn" title="AI助手">
                                <i class="fas fa-magic"></i>
                            </button>
                            <div class="fab-menu" id="ai-fab-menu">
                                <button class="fab-menu-item" data-action="polish" title="内容润色">
                                    <i class="fas fa-magic"></i>
                                    <span>润色</span>
                                </button>
                                <button class="fab-menu-item" data-action="translate" title="翻译">
                                    <i class="fas fa-language"></i>
                                    <span>翻译</span>
                                </button>
                                <button class="fab-menu-item" data-action="explain" title="解读">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>解读</span>
                                </button>
                                <button class="fab-menu-item" data-action="rewrite" title="重写">
                                    <i class="fas fa-edit"></i>
                                    <span>重写</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div id="modal-container" class="modal-container">
            <div class="modal-header">
                <h3 id="modal-title"></h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modal-content" class="modal-content"></div>
            <div id="modal-buttons" class="modal-buttons"></div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="ai-service.js"></script>
    <script>
        // 全局变量
        let quillEditor = null;
        let currentChapter = null;

        // 示例章节数据
        const sampleChapters = {
            'chapter-1': {
                title: '第1章 大模型基本概念与内涵',
                summary: '介绍大模型的演进历程、定义、类型及其能力特点。',
                content: `大模型（Large Language Model, LLM）是指参数量达到数十亿甚至数千亿级别的深度学习模型，通常基于Transformer架构构建。这些模型通过在大规模文本数据上进行预训练，能够理解和生成人类语言，展现出强大的语言理解和生成能力。

大模型的核心特点包括：规模庞大的参数量、强大的泛化能力、涌现性能力以及多任务处理能力。随着模型规模的不断扩大，大模型在各种自然语言处理任务中都表现出了前所未有的性能。

在石油天然气行业中，大模型技术正在逐步展现其巨大的应用潜力，为传统的勘探开发和生产管理带来了新的技术手段和解决方案。`
            }
        };

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initializeQuillEditor();
            setupAIAssistantListeners();
            loadDefaultChapter();
            
            // 显示成功指示器
            showSuccessIndicator();
        });

        // 初始化Quill编辑器
        function initializeQuillEditor() {
            const editorContainer = document.getElementById('chapter-content-editor');
            
            quillEditor = new Quill(editorContainer, {
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['clean']
                    ]
                },
                theme: 'snow',
                placeholder: '开始编写章节内容...'
            });
        }

        // 加载默认章节
        function loadDefaultChapter() {
            const selector = document.getElementById('chapter-selector');
            selector.value = 'chapter-1';
            loadChapter();
        }

        // 加载章节
        function loadChapter() {
            const selector = document.getElementById('chapter-selector');
            const chapterId = selector.value;
            
            if (!chapterId || !sampleChapters[chapterId]) return;
            
            currentChapter = chapterId;
            const chapterData = sampleChapters[chapterId];
            
            document.getElementById('chapter-title').value = chapterData.title;
            document.getElementById('chapter-summary').value = chapterData.summary;
            quillEditor.setText(chapterData.content);
        }

        // 保存章节
        function saveChapter() {
            if (!currentChapter) {
                showNotification('请先选择一个章节', 'warning');
                return;
            }
            
            const title = document.getElementById('chapter-title').value;
            const summary = document.getElementById('chapter-summary').value;
            const content = quillEditor.getText();
            
            // 模拟保存
            sampleChapters[currentChapter] = { title, summary, content };
            showNotification('章节保存成功', 'success');
        }

        // 显示成功指示器
        function showSuccessIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'success-indicator';
            indicator.innerHTML = `
                <i class="fas fa-check-circle"></i>
                AI助手功能已成功集成到主应用！
            `;
            document.body.appendChild(indicator);

            setTimeout(() => {
                indicator.remove();
            }, 5000);
        }

        // ==================== 通知系统 ====================
        function showNotification(message, type = 'info', duration = 3000) {
            const container = document.getElementById('notification-container');
            if (!container) return;

            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }

        // ==================== 模态框系统 ====================
        function showModal(options) {
            const overlay = document.getElementById('modal-overlay');
            const container = document.getElementById('modal-container');
            const title = document.getElementById('modal-title');
            const content = document.getElementById('modal-content');
            const buttons = document.getElementById('modal-buttons');

            if (!overlay || !container || !title || !content || !buttons) return;

            title.textContent = options.title || '';
            content.innerHTML = options.content || '';

            // 设置自定义类名
            if (options.className) {
                container.className = `modal-container ${options.className}`;
            } else {
                container.className = 'modal-container';
            }

            // 创建按钮
            buttons.innerHTML = '';
            if (options.buttons) {
                options.buttons.forEach(btn => {
                    const button = document.createElement('button');
                    button.textContent = btn.text;
                    button.className = `btn ${btn.className || ''}`;
                    if (btn.id) button.id = btn.id;
                    if (btn.style) button.style.cssText = btn.style;
                    if (btn.onclick) button.onclick = btn.onclick;
                    buttons.appendChild(button);
                });
            }

            overlay.style.display = 'flex';
        }

        function closeModal() {
            const overlay = document.getElementById('modal-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // ==================== AI助手功能 ====================

        // 设置AI助手事件监听器
        function setupAIAssistantListeners() {
            // 监听AI助手菜单项点击
            document.addEventListener('click', function(e) {
                if (e.target.closest('.fab-menu-item')) {
                    const action = e.target.closest('.fab-menu-item').getAttribute('data-action');
                    handleAIAction(action);
                }
            });
        }

        // 处理AI操作
        async function handleAIAction(action) {
            // 检查编辑器是否存在
            if (!quillEditor) {
                showNotification('编辑器未初始化', 'warning');
                return;
            }

            // 获取选中的文本
            const selection = quillEditor.getSelection();
            let selectedText = '';

            if (selection && selection.length > 0) {
                selectedText = quillEditor.getText(selection.index, selection.length);
            } else {
                // 如果没有选中文本，获取全部内容
                selectedText = quillEditor.getText();
            }

            if (!selectedText.trim()) {
                showNotification('请先输入或选择要处理的文本内容', 'warning');
                return;
            }

            // 显示AI交互对话框
            showAIDialog(action, selectedText);
        }

        // 显示AI交互对话框
        function showAIDialog(action, selectedText) {
            const actionConfig = {
                polish: {
                    title: '内容润色',
                    description: '优化文本表达，提升可读性和流畅性',
                    icon: 'fas fa-magic',
                    placeholder: '请描述您的润色要求（可选）...'
                },
                translate: {
                    title: '内容翻译',
                    description: '将内容翻译为指定语言',
                    icon: 'fas fa-language',
                    placeholder: '请指定目标语言和特殊要求...'
                },
                explain: {
                    title: '内容解读',
                    description: '深入解析内容的含义和背景',
                    icon: 'fas fa-lightbulb',
                    placeholder: '请描述您希望了解的方面（可选）...'
                },
                rewrite: {
                    title: '内容重写',
                    description: '用不同风格重新组织和表达内容',
                    icon: 'fas fa-edit',
                    placeholder: '请指定重写风格和要求...'
                }
            };

            const config = actionConfig[action];
            if (!config) return;

            const dialogHTML = `
                <div class="ai-dialog-header">
                    <div class="ai-dialog-icon">
                        <i class="${config.icon}"></i>
                    </div>
                    <div class="ai-dialog-title">
                        <h3>${config.title}</h3>
                        <p>${config.description}</p>
                    </div>
                </div>
                <div class="ai-dialog-content">
                    <div class="ai-selected-text">
                        <h4>选中的内容：</h4>
                        <div class="ai-selected-text-content">${selectedText}</div>
                    </div>

                    <div class="ai-input-section">
                        <textarea class="ai-prompt-input" placeholder="${config.placeholder}"></textarea>

                        <div class="ai-file-upload">
                            <label>上传相关文件（可选）：</label>
                            <input type="file" class="ai-file-input" multiple accept="image/*,.pdf,.doc,.docx,.txt">
                            <div class="ai-file-preview"></div>
                        </div>
                    </div>

                    <div class="ai-response-section" style="display: none;">
                        <h4>AI处理结果：</h4>
                        <div class="ai-response-content"></div>
                    </div>
                </div>
            `;

            showModal({
                title: `AI助手 - ${config.title}`,
                content: dialogHTML,
                className: 'ai-dialog',
                buttons: [
                    {
                        text: '取消',
                        className: 'btn-secondary',
                        onclick: () => closeModal()
                    },
                    {
                        text: '开始处理',
                        className: 'btn-primary',
                        onclick: () => processAIRequest(action, selectedText)
                    },
                    {
                        text: '应用结果',
                        className: 'btn-success',
                        id: 'apply-ai-result',
                        style: 'display: none;',
                        onclick: () => applyAIResult()
                    }
                ]
            });

            // 设置文件上传监听器
            setupFileUploadListener();
        }

        // 设置文件上传监听器
        function setupFileUploadListener() {
            const fileInput = document.querySelector('.ai-file-input');
            const filePreview = document.querySelector('.ai-file-preview');

            if (!fileInput || !filePreview) return;

            fileInput.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                updateFilePreview(files, filePreview);
            });
        }

        // 更新文件预览
        function updateFilePreview(files, container) {
            container.innerHTML = '';

            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'ai-file-item';
                fileItem.innerHTML = `
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                    <button type="button" onclick="removeFile(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(fileItem);
            });
        }

        // 移除文件
        function removeFile(index) {
            const fileInput = document.querySelector('.ai-file-input');
            const filePreview = document.querySelector('.ai-file-preview');

            if (!fileInput) return;

            const dt = new DataTransfer();
            const files = Array.from(fileInput.files);

            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });

            fileInput.files = dt.files;
            updateFilePreview(Array.from(dt.files), filePreview);
        }

        // 处理AI请求
        async function processAIRequest(action, selectedText) {
            const promptInput = document.querySelector('.ai-prompt-input');
            const fileInput = document.querySelector('.ai-file-input');
            const responseSection = document.querySelector('.ai-response-section');
            const responseContent = document.querySelector('.ai-response-content');
            const applyButton = document.getElementById('apply-ai-result');

            if (!promptInput || !responseSection || !responseContent) return;

            const requirements = promptInput.value.trim();
            const files = Array.from(fileInput?.files || []);

            // 显示加载状态
            responseSection.style.display = 'block';
            responseContent.innerHTML = `
                <div class="ai-loading">
                    <i class="fas fa-spinner"></i>
                    <span>AI正在处理中，请稍候...</span>
                </div>
            `;

            try {
                let result;

                // 根据操作类型调用相应的AI服务
                switch (action) {
                    case 'polish':
                        result = await aiServiceManager.polishContent(selectedText, requirements);
                        break;
                    case 'translate':
                        const targetLanguage = requirements || '英文';
                        result = await aiServiceManager.translateContent(selectedText, targetLanguage);
                        break;
                    case 'explain':
                        result = await aiServiceManager.explainContent(selectedText, requirements);
                        break;
                    case 'rewrite':
                        const style = requirements || '学术风格';
                        result = await aiServiceManager.rewriteContent(selectedText, style);
                        break;
                    default:
                        throw new Error('未知的操作类型');
                }

                if (result.success) {
                    responseContent.textContent = result.result;
                    applyButton.style.display = 'inline-block';

                    // 存储结果用于应用
                    window.currentAIResult = {
                        action,
                        originalText: selectedText,
                        processedText: result.result
                    };
                } else {
                    responseContent.innerHTML = `
                        <div style="color: #ef4444;">
                            <i class="fas fa-exclamation-triangle"></i>
                            处理失败: ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                responseContent.innerHTML = `
                    <div style="color: #ef4444;">
                        <i class="fas fa-exclamation-triangle"></i>
                        处理失败: ${error.message}
                    </div>
                `;
            }
        }

        // 应用AI处理结果
        function applyAIResult() {
            if (!window.currentAIResult || !quillEditor) {
                showNotification('没有可应用的结果', 'warning');
                return;
            }

            const { action, originalText, processedText } = window.currentAIResult;

            // 获取当前选择
            const selection = quillEditor.getSelection();

            if (selection && selection.length > 0) {
                // 替换选中的文本
                quillEditor.deleteText(selection.index, selection.length);
                quillEditor.insertText(selection.index, processedText);
                quillEditor.setSelection(selection.index, processedText.length);
            } else {
                // 如果没有选择，在光标位置插入
                const cursorPosition = selection ? selection.index : quillEditor.getLength();
                quillEditor.insertText(cursorPosition, processedText);
                quillEditor.setSelection(cursorPosition, processedText.length);
            }

            // 显示成功通知
            const actionNames = {
                polish: '润色',
                translate: '翻译',
                explain: '解读',
                rewrite: '重写'
            };

            showNotification(`${actionNames[action]}结果已应用到编辑器`, 'success');

            // 关闭对话框
            closeModal();

            // 清理临时数据
            delete window.currentAIResult;
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
