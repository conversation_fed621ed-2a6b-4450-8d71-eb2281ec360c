-- 修复项目成员问题：为现有项目添加项目所有者作为成员
-- 这个脚本会检查所有项目，如果项目所有者不在project_members表中，则添加他们

-- 1. 查看当前项目和成员状态
SELECT 
    p.id as project_id,
    p.title,
    p.owner_id,
    up.full_name as owner_name,
    pm.user_id as member_user_id,
    pm.role as member_role
FROM projects p
LEFT JOIN user_profiles up ON p.owner_id = up.id
LEFT JOIN project_members pm ON p.id = pm.project_id AND p.owner_id = pm.user_id
ORDER BY p.created_at;

-- 2. 为没有成员记录的项目所有者添加成员记录
INSERT INTO project_members (project_id, user_id, role, status, joined_at)
SELECT 
    p.id as project_id,
    p.owner_id as user_id,
    'owner' as role,
    'active' as status,
    NOW() as joined_at
FROM projects p
LEFT JOIN project_members pm ON p.id = pm.project_id AND p.owner_id = pm.user_id
WHERE pm.user_id IS NULL  -- 项目所有者不在成员表中
AND p.owner_id IS NOT NULL;  -- 确保项目有所有者

-- 3. 验证修复结果
SELECT 
    p.id as project_id,
    p.title,
    p.owner_id,
    up.full_name as owner_name,
    pm.user_id as member_user_id,
    pm.role as member_role,
    pm.status as member_status
FROM projects p
LEFT JOIN user_profiles up ON p.owner_id = up.id
LEFT JOIN project_members pm ON p.id = pm.project_id AND p.owner_id = pm.user_id
ORDER BY p.created_at;
