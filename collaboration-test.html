<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多用户协作功能测试 - 《大模型技术与油气应用概论》协作系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
        }
        
        .test-title {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .test-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .test-sections {
            display: grid;
            gap: 30px;
        }
        
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f3f4f6;
        }
        
        .section-icon {
            width: 50px;
            height: 50px;
            background: #4f46e5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        .section-title {
            font-size: 1.5rem;
            color: #1f2937;
            margin: 0;
        }
        
        .test-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .test-step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #4f46e5;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: #4f46e5;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .step-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .test-accounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .account-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
        }
        
        .account-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .account-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .account-name {
            font-weight: 600;
            color: #1f2937;
        }
        
        .account-details {
            font-size: 0.8rem;
            color: #6b7280;
        }
        
        .role-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 5px;
        }
        
        .role-owner { background: #fef3c7; color: #92400e; }
        .role-admin { background: #ddd6fe; color: #5b21b6; }
        .role-editor { background: #d1fae5; color: #065f46; }
        .role-author { background: #dbeafe; color: #1e40af; }
        .role-reviewer { background: #fee2e2; color: #991b1b; }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .quick-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #4f46e5;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        
        .quick-link:hover {
            background: #4338ca;
            color: white;
        }
        
        .quick-link i {
            font-size: 1.2rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
        }
        
        .feature-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-complete { background: #10b981; }
        .status-progress { background: #f59e0b; }
        .status-pending { background: #6b7280; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">
                <i class="fas fa-users"></i> 多用户协作功能测试
            </h1>
            <p class="test-subtitle">《大模型技术与油气应用概论》智能编纂系统</p>
        </div>
        
        <div class="test-sections">
            <!-- 测试准备 -->
            <div class="test-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h2 class="section-title">测试准备</h2>
                </div>
                
                <ol class="test-steps">
                    <li class="test-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">创建测试用户账户</div>
                            <div class="step-description">
                                使用测试用户创建工具创建多个不同角色的用户账户，模拟真实的协作环境。
                                <br><strong>密码统一为：test123456</strong>
                            </div>
                        </div>
                    </li>
                    <li class="test-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">创建测试项目</div>
                            <div class="step-description">
                                使用项目所有者账户创建一个新的书籍项目，用于测试协作功能。
                            </div>
                        </div>
                    </li>
                    <li class="test-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-title">邀请团队成员</div>
                            <div class="step-description">
                                通过用户管理界面邀请其他测试用户加入项目，分配不同的角色权限。
                            </div>
                        </div>
                    </li>
                </ol>
                
                <div class="test-accounts">
                    <div class="account-card">
                        <div class="account-header">
                            <div class="account-avatar" style="background: #f59e0b;">张教</div>
                            <div>
                                <div class="account-name">张教授</div>
                                <div class="account-details"><EMAIL></div>
                            </div>
                        </div>
                        <div class="role-badge role-owner">项目所有者</div>
                    </div>
                    
                    <div class="account-card">
                        <div class="account-header">
                            <div class="account-avatar" style="background: #8b5cf6;">李副</div>
                            <div>
                                <div class="account-name">李副教授</div>
                                <div class="account-details"><EMAIL></div>
                            </div>
                        </div>
                        <div class="role-badge role-admin">管理员</div>
                    </div>
                    
                    <div class="account-card">
                        <div class="account-header">
                            <div class="account-avatar" style="background: #10b981;">王编</div>
                            <div>
                                <div class="account-name">王编辑</div>
                                <div class="account-details"><EMAIL></div>
                            </div>
                        </div>
                        <div class="role-badge role-editor">编辑者</div>
                    </div>
                    
                    <div class="account-card">
                        <div class="account-header">
                            <div class="account-avatar" style="background: #3b82f6;">陈博</div>
                            <div>
                                <div class="account-name">陈博士</div>
                                <div class="account-details"><EMAIL></div>
                            </div>
                        </div>
                        <div class="role-badge role-author">作者</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能测试 -->
            <div class="test-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h2 class="section-title">功能测试场景</h2>
                </div>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-title">
                            <span class="status-indicator status-complete"></span>
                            <i class="fas fa-user-plus"></i>
                            用户邀请与注册
                        </div>
                        <div class="feature-description">
                            测试邀请新用户加入项目，验证邀请链接、角色分配、权限设置等功能。
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">
                            <span class="status-indicator status-complete"></span>
                            <i class="fas fa-shield-alt"></i>
                            权限控制系统
                        </div>
                        <div class="feature-description">
                            验证不同角色用户的权限边界，确保用户只能访问授权的功能和数据。
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">
                            <span class="status-indicator status-progress"></span>
                            <i class="fas fa-edit"></i>
                            协作编辑功能
                        </div>
                        <div class="feature-description">
                            测试多用户同时编辑章节内容，验证冲突检测、版本控制、实时同步等功能。
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">
                            <span class="status-indicator status-progress"></span>
                            <i class="fas fa-comments"></i>
                            评论与审核
                        </div>
                        <div class="feature-description">
                            测试章节评论、审核流程、修改建议等协作沟通功能。
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">
                            <span class="status-indicator status-pending"></span>
                            <i class="fas fa-tasks"></i>
                            任务分配管理
                        </div>
                        <div class="feature-description">
                            测试章节分配、任务跟踪、进度管理等项目管理功能。
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-title">
                            <span class="status-indicator status-pending"></span>
                            <i class="fas fa-bell"></i>
                            通知与提醒
                        </div>
                        <div class="feature-description">
                            测试系统通知、邮件提醒、活动日志等信息同步功能。
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快速链接 -->
            <div class="test-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <h2 class="section-title">快速访问</h2>
                </div>
                
                <div class="quick-links">
                    <a href="create-test-users.html" class="quick-link">
                        <i class="fas fa-users-cog"></i>
                        <span>创建测试用户</span>
                    </a>
                    
                    <a href="auth.html" class="quick-link">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>用户登录</span>
                    </a>
                    
                    <a href="project-management.html" class="quick-link">
                        <i class="fas fa-project-diagram"></i>
                        <span>项目管理</span>
                    </a>
                    
                    <a href="user-management.html" class="quick-link">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                    
                    <a href="index.html" class="quick-link">
                        <i class="fas fa-home"></i>
                        <span>主编辑器</span>
                    </a>
                    
                    <a href="accept-invitation.html?token=demo" class="quick-link">
                        <i class="fas fa-envelope-open"></i>
                        <span>邀请接受页面</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
