-- 修复章节内容不匹配问题的数据库脚本
-- 问题：index.html协作编著页面的章节分配显示错误的章节标题
-- 根本原因：数据库chapters表中存储的title字段包含错误的章节标题
-- 解决方案：直接更新数据库中的错误章节标题

-- ============================================
-- 第一步：查看当前错误的章节数据
-- ============================================

-- 查看当前所有章节标题
SELECT id, title, summary, status, order_index, created_at
FROM chapters
ORDER BY order_index;

-- ============================================
-- 第二步：直接更新错误的章节标题
-- ============================================

-- 更新错误的章节标题为正确的标题
UPDATE chapters
SET title = '第0章：前言',
    summary = '介绍本书的写作背景、目标读者、主要内容和结构安排'
WHERE title IN ('第一章：绪论', '第一章 测试概述', '第1章 常见问题处理');

UPDATE chapters
SET title = '第1章：大模型基本概念与内涵',
    summary = '阐述大模型的定义、特点、分类和发展历程'
WHERE title IN ('第二章：文献综述', '第二章 文献综述');

UPDATE chapters
SET title = '第2章：大模型技术原理',
    summary = '深入讲解Transformer架构、注意力机制等核心技术原理'
WHERE title IN ('第三章：大模型工具简介', '第三章 大模型工具简介');

UPDATE chapters
SET title = '第3章：大模型训练与优化',
    summary = '介绍大模型的训练方法、优化技术和性能评估'
WHERE title IN ('第四章：修复地质问题', '第四章 修复地质问题');

UPDATE chapters
SET title = '第4章：油气勘探中的大模型应用',
    summary = '大模型在地震数据解释、储层预测等勘探环节的应用'
WHERE title IN ('第五章：地质环境建模', '第五章 地质环境建模');

UPDATE chapters
SET title = '第5章：油气开发中的大模型应用',
    summary = '大模型在钻井优化、生产预测等开发环节的应用'
WHERE title IN ('第六章：地质灾害预测', '第六章 地质灾害预测');

-- 同时更新章节分配表中的标题
UPDATE chapter_assignments
SET title = '第0章：前言',
    description = '介绍本书的写作背景、目标读者、主要内容和结构安排'
WHERE title IN ('第一章：绪论', '第一章 测试概述', '第1章 常见问题处理');

UPDATE chapter_assignments
SET title = '第1章：大模型基本概念与内涵',
    description = '阐述大模型的定义、特点、分类和发展历程'
WHERE title IN ('第二章：文献综述', '第二章 文献综述');

UPDATE chapter_assignments
SET title = '第2章：大模型技术原理',
    description = '深入讲解Transformer架构、注意力机制等核心技术原理'
WHERE title IN ('第三章：大模型工具简介', '第三章 大模型工具简介');

UPDATE chapter_assignments
SET title = '第3章：大模型训练与优化',
    description = '介绍大模型的训练方法、优化技术和性能评估'
WHERE title IN ('第四章：修复地质问题', '第四章 修复地质问题');

UPDATE chapter_assignments
SET title = '第4章：油气勘探中的大模型应用',
    description = '大模型在地震数据解释、储层预测等勘探环节的应用'
WHERE title IN ('第五章：地质环境建模', '第五章 地质环境建模');

UPDATE chapter_assignments
SET title = '第5章：油气开发中的大模型应用',
    description = '大模型在钻井优化、生产预测等开发环节的应用'
WHERE title IN ('第六章：地质灾害预测', '第六章 地质灾害预测');

-- ============================================
-- 第三步：验证修复结果
-- ============================================

-- 查看修复后的章节数据
SELECT id, title, summary, status, order_index, updated_at
FROM chapters
ORDER BY order_index;

-- 查看修复后的章节分配数据
SELECT id, title, description, status, due_date
FROM chapter_assignments
ORDER BY title;

-- 统计修复结果
SELECT
    COUNT(*) as total_chapters,
    COUNT(CASE WHEN title LIKE '%前言%' THEN 1 END) as preface_chapters,
    COUNT(CASE WHEN title LIKE '%大模型%' THEN 1 END) as correct_chapters,
    COUNT(CASE WHEN title LIKE '%地质%' OR title LIKE '%测试%' THEN 1 END) as remaining_errors
FROM chapters;

-- ============================================
-- 第四步：使用说明
-- ============================================

/*
🎯 修复目标：
解决index.html协作编著页面章节分配显示错误章节标题的问题

🔧 修复方法：
直接更新数据库chapters表和chapter_assignments表中的错误标题

📋 执行步骤：
1. 在Supabase控制台的SQL编辑器中执行此脚本
2. 或者在数据库管理工具中运行此脚本
3. 刷新index.html页面，进入协作编著 > 章节分配
4. 验证章节标题已更新为正确内容

✅ 预期结果：
- 第0章：前言
- 第1章：大模型基本概念与内涵
- 第2章：大模型技术原理
- 第3章：大模型训练与优化
- 第4章：油气勘探中的大模型应用
- 第5章：油气开发中的大模型应用

⚠️ 注意事项：
- 此脚本直接修改数据库数据
- 建议先在测试环境验证
- 如有重要数据请先备份
*/
