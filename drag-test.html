<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }

        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 1rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
            z-index: 1002;
            cursor: move;
            user-select: none;
            transition: box-shadow 0.2s ease;
        }

        .control-panel:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        .control-panel.dragging {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
            transform: scale(1.02);
        }

        .control-panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
            cursor: move;
        }

        .control-panel-title {
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
            margin: 0;
        }

        .drag-handle {
            color: #9ca3af;
            font-size: 0.75rem;
            cursor: move;
        }

        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .control-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.25rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .content {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>拖动功能测试</h1>
        <p>右上角的控制面板现在可以拖动了！</p>
        <p>尝试拖动控制面板到页面的任何位置。</p>
        <ul>
            <li>点击并拖动控制面板的标题栏或面板本身</li>
            <li>面板位置会自动保存到localStorage</li>
            <li>刷新页面后位置会被恢复</li>
            <li>面板会被限制在视窗范围内</li>
        </ul>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel" id="controlPanel">
        <div class="control-panel-header" id="controlPanelHeader">
            <div class="control-panel-title">测试控制</div>
            <div class="drag-handle">⋮⋮</div>
        </div>
        <div class="control-buttons">
            <button class="control-btn" onclick="alert('按钮1')">按钮1</button>
            <button class="control-btn" onclick="alert('按钮2')">按钮2</button>
            <button class="control-btn" onclick="alert('按钮3')">按钮3</button>
        </div>
    </div>

    <script>
        // DOM元素
        const controlPanel = document.getElementById('controlPanel');
        const controlPanelHeader = document.getElementById('controlPanelHeader');
        
        // 拖动状态
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let controlPanelPosition = { x: 20, y: 20 };

        // 初始化拖动功能
        function initDragAndDrop() {
            // 从localStorage恢复位置
            const savedPosition = localStorage.getItem('controlPanelPosition');
            if (savedPosition) {
                controlPanelPosition = JSON.parse(savedPosition);
                updateControlPanelPosition();
            }

            // 鼠标按下事件
            controlPanelHeader.addEventListener('mousedown', startDrag);
            controlPanel.addEventListener('mousedown', startDrag);
        }

        function startDrag(e) {
            // 只有在点击头部或面板本身时才开始拖动
            if (e.target.closest('.control-btn')) {
                return; // 不拖动按钮
            }

            isDragging = true;
            controlPanel.classList.add('dragging');
            
            const rect = controlPanel.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            // 添加全局事件监听器
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);
            
            // 防止文本选择
            e.preventDefault();
        }

        function drag(e) {
            if (!isDragging) return;

            const x = e.clientX - dragOffset.x;
            const y = e.clientY - dragOffset.y;

            // 限制在视窗范围内
            const maxX = window.innerWidth - controlPanel.offsetWidth;
            const maxY = window.innerHeight - controlPanel.offsetHeight;

            controlPanelPosition.x = Math.max(0, Math.min(x, maxX));
            controlPanelPosition.y = Math.max(0, Math.min(y, maxY));

            updateControlPanelPosition();
        }

        function stopDrag() {
            if (!isDragging) return;

            isDragging = false;
            controlPanel.classList.remove('dragging');

            // 移除全局事件监听器
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);

            // 保存位置到localStorage
            localStorage.setItem('controlPanelPosition', JSON.stringify(controlPanelPosition));
        }

        function updateControlPanelPosition() {
            controlPanel.style.left = controlPanelPosition.x + 'px';
            controlPanel.style.top = controlPanelPosition.y + 'px';
            controlPanel.style.right = 'auto';
            controlPanel.style.bottom = 'auto';
        }

        // 窗口大小改变时重新调整位置
        window.addEventListener('resize', () => {
            const maxX = window.innerWidth - controlPanel.offsetWidth;
            const maxY = window.innerHeight - controlPanel.offsetHeight;

            controlPanelPosition.x = Math.max(0, Math.min(controlPanelPosition.x, maxX));
            controlPanelPosition.y = Math.max(0, Math.min(controlPanelPosition.y, maxY));

            updateControlPanelPosition();
        });

        // 初始化
        initDragAndDrop();
    </script>
</body>
</html>
