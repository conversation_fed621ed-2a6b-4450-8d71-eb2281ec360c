<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大纲保存修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 大纲保存修复验证</h1>
        <p>验证AI生成大纲和导入大纲的ID格式修复是否成功</p>
        
        <div>
            <button class="btn btn-primary" onclick="testIDGeneration()">测试ID生成</button>
            <button class="btn btn-success" onclick="testOutlineConversion()">测试大纲转换</button>
            <button class="btn btn-warning" onclick="testFullFlow()">测试完整流程</button>
        </div>
        
        <div id="results"></div>
        
        <h3>测试日志</h3>
        <div class="log-area" id="log"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-config.js"></script>
    <script src="ai-service.js"></script>
    <script src="collaboration.js"></script>
    <script src="app.js"></script>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('log');
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function testIDGeneration() {
            log('🧪 开始测试ID生成功能');
            
            try {
                // 测试UUID生成
                const uuid = generateUUID();
                log(`生成的UUID: ${uuid}`);
                
                // 验证UUID格式
                const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                if (uuidRegex.test(uuid)) {
                    showResult('✅ UUID生成格式正确', 'success');
                    log('✅ UUID格式验证通过');
                } else {
                    showResult('❌ UUID生成格式错误', 'error');
                    log('❌ UUID格式验证失败');
                }
                
                // 测试字符串ID生成（用于对比）
                const stringId = generateUniqueId();
                log(`生成的字符串ID: ${stringId}`);
                
                showResult('ID生成测试完成', 'info');
                
            } catch (error) {
                showResult(`ID生成测试失败: ${error.message}`, 'error');
                log(`❌ ID生成测试失败: ${error.message}`);
            }
        }

        function testOutlineConversion() {
            log('🔄 开始测试大纲转换功能');
            
            try {
                // 模拟AI生成的大纲数据
                const aiOutline = [
                    {
                        title: '第一篇 理论基础',
                        level: 0,
                        description: '理论基础部分',
                        children: [
                            {
                                title: '第1章 概述',
                                level: 1,
                                description: '概述章节',
                                children: []
                            }
                        ]
                    }
                ];
                
                log('原始AI大纲数据:');
                log(JSON.stringify(aiOutline, null, 2));
                
                // 转换大纲格式
                const converted = convertAIOutlineFormat(aiOutline);
                log('转换后的大纲数据:');
                log(JSON.stringify(converted, null, 2));
                
                // 验证转换结果
                let allUUIDs = true;
                const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                
                function checkUUIDs(items) {
                    for (const item of items) {
                        if (!uuidRegex.test(item.id)) {
                            allUUIDs = false;
                            log(`❌ 发现非UUID格式的ID: ${item.id}`);
                        } else {
                            log(`✅ UUID格式正确: ${item.id}`);
                        }
                        
                        if (item.children && item.children.length > 0) {
                            checkUUIDs(item.children);
                        }
                    }
                }
                
                checkUUIDs(converted);
                
                if (allUUIDs) {
                    showResult('✅ 大纲转换成功，所有ID都是UUID格式', 'success');
                } else {
                    showResult('❌ 大纲转换失败，存在非UUID格式的ID', 'error');
                }
                
            } catch (error) {
                showResult(`大纲转换测试失败: ${error.message}`, 'error');
                log(`❌ 大纲转换测试失败: ${error.message}`);
            }
        }

        function testFullFlow() {
            log('🚀 开始测试完整流程');
            
            try {
                // 1. 模拟AI生成大纲
                const aiGeneratedOutline = {
                    title: '测试项目',
                    description: '测试项目描述',
                    outline: [
                        {
                            title: '第一篇 基础',
                            level: 0,
                            description: '基础部分',
                            children: [
                                {
                                    title: '第1章 概述',
                                    level: 1,
                                    description: '概述',
                                    children: []
                                }
                            ]
                        }
                    ]
                };
                
                log('1. 模拟AI生成大纲完成');
                
                // 2. 应用大纲（模拟applyGeneratedOutline的核心逻辑）
                if (aiGeneratedOutline.outline && Array.isArray(aiGeneratedOutline.outline)) {
                    const convertedOutline = convertAIOutlineFormat(aiGeneratedOutline.outline);
                    log('2. 大纲格式转换完成');
                    
                    // 3. 验证转换后的数据结构
                    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                    let validStructure = true;
                    
                    function validateStructure(items) {
                        for (const item of items) {
                            if (!item.id || !uuidRegex.test(item.id)) {
                                validStructure = false;
                                log(`❌ 无效的ID: ${item.id}`);
                            }
                            if (!item.title) {
                                validStructure = false;
                                log(`❌ 缺少标题: ${item.title}`);
                            }
                            if (typeof item.level !== 'number') {
                                validStructure = false;
                                log(`❌ 无效的级别: ${item.level}`);
                            }
                            
                            if (item.children && item.children.length > 0) {
                                validateStructure(item.children);
                            }
                        }
                    }
                    
                    validateStructure(convertedOutline);
                    
                    if (validStructure) {
                        showResult('✅ 完整流程测试成功！大纲数据结构正确', 'success');
                        log('3. ✅ 数据结构验证通过');
                        log('🎉 完整流程测试成功！');
                    } else {
                        showResult('❌ 完整流程测试失败：数据结构有问题', 'error');
                        log('3. ❌ 数据结构验证失败');
                    }
                } else {
                    showResult('❌ 完整流程测试失败：大纲数据格式错误', 'error');
                    log('❌ 大纲数据格式错误');
                }
                
            } catch (error) {
                showResult(`完整流程测试失败: ${error.message}`, 'error');
                log(`❌ 完整流程测试失败: ${error.message}`);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 大纲保存修复验证页面已加载');
            showResult('修复验证系统已准备就绪', 'info');
            
            // 自动运行基础测试
            setTimeout(() => {
                log('🔄 自动运行基础验证测试...');
                testIDGeneration();
                setTimeout(() => testOutlineConversion(), 1000);
            }, 1000);
        });
    </script>
</body>
</html>
