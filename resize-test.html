<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>右侧工具栏拖拽调整宽度测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            display: flex;
            height: 100vh;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            background: white;
            margin: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .right-toolbar {
            width: 350px;
            min-width: 280px;
            max-width: 600px;
            background: white;
            border-left: 1px solid #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 999;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .right-toolbar.resizing {
            transition: none;
        }

        .resize-handle {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: transparent;
            cursor: col-resize;
            z-index: 1001;
            transition: background-color 0.2s ease;
        }

        .resize-handle:hover {
            background: #3b82f6;
        }

        .resize-handle.resizing {
            background: #2563eb;
        }

        .resize-handle::before {
            content: '';
            position: absolute;
            left: -2px;
            top: 0;
            bottom: 0;
            width: 8px;
            background: transparent;
        }

        .toolbar-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .toolbar-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
        }

        .toolbar-icon {
            font-size: 1.25rem;
            cursor: pointer;
        }

        .toolbar-content {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
        }

        .width-display {
            background: #dbeafe;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }

        .instructions {
            background: #f0f9ff;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <h1>右侧工具栏拖拽调整宽度测试</h1>
        <p>右侧的工具栏现在支持拖拽调整宽度！</p>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ul>
                <li>将鼠标悬停在右侧工具栏的左边缘，会出现蓝色的拖拽手柄</li>
                <li>点击并拖拽左边缘来调整工具栏宽度</li>
                <li>宽度范围限制在 280px - 600px 之间</li>
                <li>调整后的宽度会自动保存到localStorage</li>
                <li>刷新页面后宽度会被恢复</li>
                <li>点击工具集图标（🛠️）会触发工具管理功能</li>
            </ul>
        </div>
    </div>

    <div class="right-toolbar" id="rightToolbar">
        <div class="resize-handle" id="resizeHandle"></div>
        
        <div class="toolbar-header">
            <div class="toolbar-title">
                <span class="toolbar-icon" onclick="alert('工具管理功能被触发！')" title="点击管理工具">🛠️</span>
                <span>工具集</span>
            </div>
        </div>
        
        <div class="toolbar-content">
            <div class="width-display" id="widthDisplay">
                当前宽度: 350px
            </div>
            
            <h3>工具栏内容</h3>
            <p>这里是工具栏的内容区域。</p>
            <p>您可以拖拽左边缘来调整宽度。</p>
            
            <div style="margin-top: 2rem;">
                <h4>测试内容</h4>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            </div>
        </div>
    </div>

    <script>
        // DOM元素
        const rightToolbar = document.getElementById('rightToolbar');
        const resizeHandle = document.getElementById('resizeHandle');
        const widthDisplay = document.getElementById('widthDisplay');
        
        // 状态变量
        let isResizing = false;
        let rightToolbarWidth = 350;
        const minToolbarWidth = 280;
        const maxToolbarWidth = 600;

        // 初始化
        function init() {
            // 从localStorage恢复宽度
            const savedWidth = localStorage.getItem('rightToolbarWidth');
            if (savedWidth) {
                rightToolbarWidth = parseInt(savedWidth);
                updateRightToolbarWidth();
            }

            // 添加拖拽事件监听器
            resizeHandle.addEventListener('mousedown', startResize);
        }

        function startResize(e) {
            isResizing = true;
            resizeHandle.classList.add('resizing');
            rightToolbar.classList.add('resizing');
            
            // 添加全局事件监听器
            document.addEventListener('mousemove', resize);
            document.addEventListener('mouseup', stopResize);
            
            // 防止文本选择
            e.preventDefault();
            document.body.style.userSelect = 'none';
        }

        function resize(e) {
            if (!isResizing) return;

            // 计算新宽度（从右边界向左拖拽）
            const rect = rightToolbar.getBoundingClientRect();
            const newWidth = rect.right - e.clientX;

            // 限制宽度范围
            rightToolbarWidth = Math.max(minToolbarWidth, Math.min(newWidth, maxToolbarWidth));
            
            updateRightToolbarWidth();
        }

        function stopResize() {
            if (!isResizing) return;

            isResizing = false;
            resizeHandle.classList.remove('resizing');
            rightToolbar.classList.remove('resizing');

            // 移除全局事件监听器
            document.removeEventListener('mousemove', resize);
            document.removeEventListener('mouseup', stopResize);
            
            // 恢复文本选择
            document.body.style.userSelect = '';

            // 保存宽度到localStorage
            localStorage.setItem('rightToolbarWidth', rightToolbarWidth.toString());
        }

        function updateRightToolbarWidth() {
            rightToolbar.style.width = rightToolbarWidth + 'px';
            widthDisplay.textContent = `当前宽度: ${rightToolbarWidth}px`;
        }

        // 初始化
        init();
    </script>
</body>
</html>
