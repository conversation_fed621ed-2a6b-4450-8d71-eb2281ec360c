<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大纲保存自动化测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-header h1 {
            margin: 0;
            font-size: 24px;
        }
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .test-section:last-child {
            border-bottom: none;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-info { background: #06b6d4; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        
        .test-results {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            line-height: 1.5;
        }
        .test-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .status-card.success {
            background: #f0fdf4;
            border-color: #10b981;
            color: #065f46;
        }
        .status-card.error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .status-card.warning {
            background: #fffbeb;
            border-color: #f59e0b;
            color: #92400e;
        }
        .status-card.info {
            background: #eff6ff;
            border-color: #3b82f6;
            color: #1e40af;
        }
        .status-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .status-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        .status-desc {
            font-size: 12px;
            opacity: 0.8;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            transition: width 0.3s ease;
            width: 0%;
        }
        .test-step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        .test-step:last-child {
            border-bottom: none;
        }
        .step-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .step-icon.pending {
            background: #e5e7eb;
            color: #6b7280;
        }
        .step-icon.running {
            background: #fbbf24;
            color: white;
        }
        .step-icon.success {
            background: #10b981;
            color: white;
        }
        .step-icon.error {
            background: #ef4444;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 大纲保存功能自动化测试</h1>
            <p>全面测试AI生成大纲和导入大纲的数据库保存功能</p>
        </div>

        <div class="test-section">
            <div class="test-title">
                <span>🎮</span>
                <span>测试控制面板</span>
            </div>
            <div class="test-controls">
                <button class="btn btn-primary" onclick="runFullTest()">🚀 运行完整测试</button>
                <button class="btn btn-info" onclick="testDatabaseConnection()">🔗 测试数据库连接</button>
                <button class="btn btn-success" onclick="testIDGeneration()">🆔 测试ID生成</button>
                <button class="btn btn-warning" onclick="testOutlineSave()">💾 测试大纲保存</button>
                <button class="btn btn-secondary" onclick="clearResults()">🗑️ 清空结果</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="test-progress"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                <span>📊</span>
                <span>测试状态</span>
            </div>
            <div class="test-status" id="test-status">
                <div class="status-card info">
                    <div class="status-icon">⏳</div>
                    <div class="status-title">等待测试</div>
                    <div class="status-desc">点击运行测试开始</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                <span>📋</span>
                <span>测试步骤</span>
            </div>
            <div id="test-steps">
                <div class="test-step">
                    <div class="step-icon pending">1</div>
                    <span>初始化测试环境</span>
                </div>
                <div class="test-step">
                    <div class="step-icon pending">2</div>
                    <span>检查数据库连接</span>
                </div>
                <div class="test-step">
                    <div class="step-icon pending">3</div>
                    <span>验证ID生成机制</span>
                </div>
                <div class="test-step">
                    <div class="step-icon pending">4</div>
                    <span>测试大纲数据结构</span>
                </div>
                <div class="test-step">
                    <div class="step-icon pending">5</div>
                    <span>模拟AI生成大纲</span>
                </div>
                <div class="test-step">
                    <div class="step-icon pending">6</div>
                    <span>测试数据库保存</span>
                </div>
                <div class="test-step">
                    <div class="step-icon pending">7</div>
                    <span>验证数据完整性</span>
                </div>
                <div class="test-step">
                    <div class="step-icon pending">8</div>
                    <span>测试数据加载</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                <span>📝</span>
                <span>测试日志</span>
            </div>
            <div class="test-results" id="test-results">
                等待测试开始...
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="supabase-config.js"></script>
    <script src="ai-service.js"></script>
    <script src="collaboration.js"></script>
    <script src="app.js"></script>

    <script>
        let testResults = [];
        let currentStep = 0;
        let totalSteps = 8;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.push({ timestamp, message, type });
            
            const resultsDiv = document.getElementById('test-results');
            const colorMap = {
                'info': '#60a5fa',
                'success': '#34d399', 
                'error': '#f87171',
                'warning': '#fbbf24'
            };
            
            resultsDiv.innerHTML += `<div style="color: ${colorMap[type] || '#f9fafb'}">${logEntry}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            
            console.log(logEntry);
        }

        // 更新进度
        function updateProgress(step) {
            currentStep = step;
            const progress = (step / totalSteps) * 100;
            document.getElementById('test-progress').style.width = progress + '%';
            
            // 更新步骤状态
            const steps = document.querySelectorAll('.test-step .step-icon');
            steps.forEach((icon, index) => {
                if (index < step) {
                    icon.className = 'step-icon success';
                    icon.textContent = '✓';
                } else if (index === step) {
                    icon.className = 'step-icon running';
                    icon.textContent = '⟳';
                } else {
                    icon.className = 'step-icon pending';
                    icon.textContent = index + 1;
                }
            });
        }

        // 更新状态卡片
        function updateStatus(title, desc, type = 'info', icon = '⏳') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.innerHTML = `
                <div class="status-card ${type}">
                    <div class="status-icon">${icon}</div>
                    <div class="status-title">${title}</div>
                    <div class="status-desc">${desc}</div>
                </div>
            `;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '等待测试开始...';
            testResults = [];
            currentStep = 0;
            updateProgress(0);
            updateStatus('等待测试', '点击运行测试开始', 'info', '⏳');
        }

        // 运行完整测试
        async function runFullTest() {
            log('🚀 开始运行完整自动化测试', 'info');
            updateStatus('测试进行中', '正在执行自动化测试流程', 'warning', '🔄');
            
            try {
                await testStep1_Initialize();
                await testStep2_DatabaseConnection();
                await testStep3_IDGeneration();
                await testStep4_OutlineStructure();
                await testStep5_AIGeneration();
                await testStep6_DatabaseSave();
                await testStep7_DataIntegrity();
                await testStep8_DataLoading();
                
                log('🎉 所有测试完成！', 'success');
                updateStatus('测试完成', '所有测试步骤已成功执行', 'success', '✅');
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
                updateStatus('测试失败', error.message, 'error', '❌');
            }
        }

        // 测试步骤1：初始化
        async function testStep1_Initialize() {
            updateProgress(1);
            log('📋 步骤1: 初始化测试环境', 'info');
            
            // 检查必要的全局对象
            const requiredObjects = [
                'supabaseManager',
                'collaborationManager', 
                'aiServiceManager',
                'generateUniqueId',
                'generateUUID',
                'safelySaveOutlineToServer'
            ];
            
            for (const obj of requiredObjects) {
                if (typeof window[obj] === 'undefined') {
                    throw new Error(`缺少必要对象: ${obj}`);
                }
                log(`✅ ${obj} 已加载`, 'success');
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        // 测试步骤2：数据库连接
        async function testStep2_DatabaseConnection() {
            updateProgress(2);
            log('🔗 步骤2: 检查数据库连接', 'info');
            
            if (!supabaseManager.supabase) {
                throw new Error('Supabase客户端未初始化');
            }
            
            // 测试简单查询
            try {
                const { data, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('id')
                    .limit(1);
                    
                if (error) throw error;
                log('✅ 数据库连接正常', 'success');
            } catch (error) {
                throw new Error(`数据库连接失败: ${error.message}`);
            }
        }

        // 继续添加其他测试步骤...
        async function testStep3_IDGeneration() {
            updateProgress(3);
            log('🆔 步骤3: 验证ID生成机制', 'info');
            
            // 测试字符串ID生成
            const stringId = generateUniqueId();
            log(`生成字符串ID: ${stringId}`, 'info');
            
            // 测试UUID生成
            const uuidId = generateUUID();
            log(`生成UUID: ${uuidId}`, 'info');
            
            // 验证UUID格式
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(uuidId)) {
                throw new Error('UUID格式不正确');
            }
            
            log('✅ ID生成机制正常', 'success');
        }

        async function testStep4_OutlineStructure() {
            updateProgress(4);
            log('📊 步骤4: 测试大纲数据结构', 'info');

            // 创建测试大纲数据
            const testOutline = [
                {
                    id: generateUUID(), // 使用UUID而不是字符串ID
                    title: '第一篇 理论基础篇',
                    level: 0,
                    description: '大模型技术的理论基础',
                    children: [
                        {
                            id: generateUUID(),
                            title: '第1章 大模型概述',
                            level: 1,
                            description: '大模型的基本概念和发展历程',
                            children: []
                        }
                    ]
                }
            ];

            log(`✅ 测试大纲结构创建完成，包含${testOutline.length}个主要部分`, 'success');
            window.testOutlineData = testOutline; // 保存供后续测试使用
        }

        async function testStep5_AIGeneration() {
            updateProgress(5);
            log('🤖 步骤5: 模拟AI生成大纲', 'info');

            // 模拟AI生成的大纲格式
            const aiGeneratedOutline = {
                title: '测试项目 - 大模型技术与油气应用',
                description: '自动化测试生成的项目描述',
                outline: [
                    {
                        id: generateUUID(),
                        title: '第一篇 基础理论',
                        level: 0,
                        description: '理论基础部分',
                        children: [
                            {
                                id: generateUUID(),
                                title: '第1章 概述',
                                level: 1,
                                description: '概述章节',
                                children: []
                            }
                        ]
                    }
                ]
            };

            log('✅ AI大纲生成模拟完成', 'success');
            window.aiTestOutline = aiGeneratedOutline;
        }

        async function testStep6_DatabaseSave() {
            updateProgress(6);
            log('💾 步骤6: 测试数据库保存', 'info');

            // 创建测试项目和用户
            const testUser = {
                id: generateUUID(),
                email: '<EMAIL>'
            };

            const testProject = {
                id: generateUUID(),
                title: '自动化测试项目'
            };

            // 模拟设置当前用户和项目
            if (supabaseManager) {
                supabaseManager.currentUser = testUser;
            }

            if (collaborationManager) {
                collaborationManager.currentProjectId = testProject.id;
            }

            log(`设置测试用户: ${testUser.email}`, 'info');
            log(`设置测试项目: ${testProject.id}`, 'info');

            // 测试保存函数是否能正确处理UUID格式的ID
            try {
                const result = await safelySaveOutlineToServer(window.testOutlineData);
                if (result) {
                    log('✅ 大纲保存测试成功', 'success');
                } else {
                    log('⚠️ 大纲保存返回false（可能是预期的）', 'warning');
                }
            } catch (error) {
                log(`❌ 大纲保存测试失败: ${error.message}`, 'error');
                // 不抛出错误，继续测试
            }
        }

        async function testStep7_DataIntegrity() {
            updateProgress(7);
            log('🔍 步骤7: 验证数据完整性', 'info');

            // 检查大纲数据的完整性
            const outline = window.testOutlineData;

            function validateOutlineItem(item, path = '') {
                const currentPath = path ? `${path}.${item.title}` : item.title;

                // 检查必需字段
                if (!item.id) throw new Error(`${currentPath}: 缺少ID`);
                if (!item.title) throw new Error(`${currentPath}: 缺少标题`);
                if (typeof item.level !== 'number') throw new Error(`${currentPath}: level必须是数字`);

                // 检查UUID格式
                const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                if (!uuidRegex.test(item.id)) {
                    throw new Error(`${currentPath}: ID不是有效的UUID格式`);
                }

                // 递归检查子项
                if (item.children && item.children.length > 0) {
                    item.children.forEach(child => validateOutlineItem(child, currentPath));
                }

                log(`✓ ${currentPath} 数据完整性验证通过`, 'success');
            }

            outline.forEach(item => validateOutlineItem(item));
            log('✅ 所有大纲数据完整性验证通过', 'success');
        }

        async function testStep8_DataLoading() {
            updateProgress(8);
            log('📥 步骤8: 测试数据加载', 'info');

            // 测试buildOutlineTree函数
            const flatData = [
                {
                    id: generateUUID(),
                    title: '根节点',
                    level: 0,
                    description: '根节点描述',
                    parent_id: null
                },
                {
                    id: generateUUID(),
                    title: '子节点',
                    level: 1,
                    description: '子节点描述',
                    parent_id: null // 将在下面设置
                }
            ];

            // 设置父子关系
            flatData[1].parent_id = flatData[0].id;

            try {
                const tree = buildOutlineTree(flatData);
                if (tree.length === 1 && tree[0].children.length === 1) {
                    log('✅ 大纲树构建测试成功', 'success');
                } else {
                    throw new Error('大纲树结构不正确');
                }
            } catch (error) {
                log(`❌ 大纲树构建测试失败: ${error.message}`, 'error');
            }
        }

        // 独立测试函数
        async function testDatabaseConnection() {
            log('🔗 开始测试数据库连接...', 'info');
            try {
                await testStep2_DatabaseConnection();
                updateStatus('数据库连接正常', '可以正常访问Supabase数据库', 'success', '✅');
            } catch (error) {
                log(`❌ 数据库连接测试失败: ${error.message}`, 'error');
                updateStatus('数据库连接失败', error.message, 'error', '❌');
            }
        }

        async function testIDGeneration() {
            log('🆔 开始测试ID生成...', 'info');
            try {
                await testStep3_IDGeneration();
                updateStatus('ID生成正常', 'UUID和字符串ID生成功能正常', 'success', '✅');
            } catch (error) {
                log(`❌ ID生成测试失败: ${error.message}`, 'error');
                updateStatus('ID生成失败', error.message, 'error', '❌');
            }
        }

        async function testOutlineSave() {
            log('💾 开始测试大纲保存...', 'info');
            try {
                await testStep4_OutlineStructure();
                await testStep6_DatabaseSave();
                updateStatus('大纲保存测试完成', '大纲保存功能测试已完成', 'success', '✅');
            } catch (error) {
                log(`❌ 大纲保存测试失败: ${error.message}`, 'error');
                updateStatus('大纲保存测试失败', error.message, 'error', '❌');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 自动化测试页面已加载', 'info');
            updateStatus('测试就绪', '自动化测试系统已准备完毕', 'info', '🎯');
        });
    </script>
</body>
</html>
