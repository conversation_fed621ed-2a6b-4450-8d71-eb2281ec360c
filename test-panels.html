<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面板测试</title>
    <style>
        .panel {
            display: none;
            height: 300px;
            border: 2px solid #ccc;
            padding: 20px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        
        .panel.active {
            display: block;
        }
        
        .nav-link {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .nav-link.active {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>面板切换测试</h1>
    
    <div>
        <a href="#" class="nav-link active" data-tab="overview">项目概览</a>
        <a href="#" class="nav-link" data-tab="outline">书籍目录</a>
        <a href="#" class="nav-link" data-tab="editor">章节编写</a>
        <a href="#" class="nav-link" data-tab="references">文献管理</a>
        <a href="#" class="nav-link" data-tab="collaboration">协作编著</a>
        <a href="#" class="nav-link" data-tab="settings">系统设置</a>
    </div>
    
    <div id="overview-panel" class="panel active">
        <h2>项目概览面板</h2>
        <p>这是项目概览的内容...</p>
    </div>
    
    <div id="outline-panel" class="panel">
        <h2>书籍目录面板</h2>
        <p>这是书籍目录的内容...</p>
    </div>
    
    <div id="editor-panel" class="panel">
        <h2>章节编写面板</h2>
        <p>这是章节编写的内容...</p>
    </div>
    
    <div id="references-panel" class="panel">
        <h2>文献管理面板</h2>
        <p>这是文献管理的内容...</p>
    </div>
    
    <div id="collaboration-panel" class="panel">
        <h2>协作编著面板</h2>
        <p>这是协作编著的内容...</p>
    </div>
    
    <div id="settings-panel" class="panel">
        <h2>系统设置面板</h2>
        <p>这是系统设置的内容...</p>
    </div>

    <script>
        // 显示指定面板
        function showPanel(panelName) {
            console.log('showPanel called with:', panelName);
            
            // 隐藏所有面板
            document.querySelectorAll('.panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // 显示指定面板
            const targetPanel = document.getElementById(panelName + '-panel');
            console.log('Target panel:', targetPanel);
            
            if (targetPanel) {
                targetPanel.classList.add('active');
                console.log('Panel activated:', panelName + '-panel');
            } else {
                console.error('Panel not found:', panelName + '-panel');
            }

            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.dataset.tab === panelName) {
                    link.classList.add('active');
                }
            });
        }

        // 绑定事件
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const tabName = link.dataset.tab;
                    if (tabName) {
                        showPanel(tabName);
                    }
                });
            });
        });
    </script>
</body>
</html>
