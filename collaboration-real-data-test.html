<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协作编著功能测试 - 真实数据</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="chapter-assignment.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .error {
            background: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        
        .success {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }
        
        #collaboration-container {
            min-height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-users"></i> 协作编著功能测试</h1>
            <p>测试真实数据集成和章节分配功能</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3><i class="fas fa-database"></i> 数据库连接测试</h3>
                <button class="btn" onclick="testDatabaseConnection()">
                    <i class="fas fa-plug"></i> 测试数据库连接
                </button>
                <button class="btn" onclick="testUserAuth()">
                    <i class="fas fa-user"></i> 测试用户认证
                </button>
                <div id="db-status" class="status" style="display: none;"></div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-project-diagram"></i> 项目数据测试</h3>
                <button class="btn" onclick="loadProjectData()">
                    <i class="fas fa-folder-open"></i> 加载项目数据
                </button>
                <button class="btn" onclick="loadTeamMembers()">
                    <i class="fas fa-users"></i> 加载团队成员
                </button>
                <button class="btn" onclick="loadChapters()">
                    <i class="fas fa-book"></i> 加载章节数据
                </button>
                <div id="project-status" class="status" style="display: none;"></div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-tasks"></i> 章节分配功能测试</h3>
                <button class="btn" onclick="testCreateAssignment()">
                    <i class="fas fa-plus"></i> 测试新建分配
                </button>
                <button class="btn" onclick="testLoadAssignments()">
                    <i class="fas fa-list"></i> 测试加载分配
                </button>
                <button class="btn" onclick="testDefaultAssignments()">
                    <i class="fas fa-magic"></i> 创建默认分配
                </button>
                <div id="assignment-status" class="status" style="display: none;"></div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-eye"></i> 协作管理界面</h3>
                <div id="collaboration-container"></div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-config.js"></script>
    <script src="collaboration.js"></script>
    
    <script>
        // 全局变量
        let testCollaborationManager = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            showStatus('db-status', '正在初始化...', 'info');
            
            try {
                // 等待 supabaseManager 初始化
                if (typeof supabaseManager !== 'undefined') {
                    await supabaseManager.initialize();
                    showStatus('db-status', '✅ 系统初始化成功', 'success');
                } else {
                    showStatus('db-status', '❌ supabaseManager 未定义', 'error');
                }
            } catch (error) {
                showStatus('db-status', '❌ 初始化失败: ' + error.message, 'error');
            }
        });
        
        // 显示状态信息
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = message;
            element.className = 'status ' + type;
        }
        
        // 测试数据库连接
        async function testDatabaseConnection() {
            showStatus('db-status', '正在测试数据库连接...', 'info');
            
            try {
                const { data, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('count')
                    .limit(1);
                
                if (error) throw error;
                showStatus('db-status', '✅ 数据库连接成功', 'success');
            } catch (error) {
                showStatus('db-status', '❌ 数据库连接失败: ' + error.message, 'error');
            }
        }
        
        // 测试用户认证
        async function testUserAuth() {
            showStatus('db-status', '正在测试用户认证...', 'info');
            
            try {
                const user = await supabaseManager.getCurrentUser();
                if (user) {
                    showStatus('db-status', `✅ 用户已登录: ${user.email}`, 'success');
                } else {
                    showStatus('db-status', '⚠️ 用户未登录', 'error');
                }
            } catch (error) {
                showStatus('db-status', '❌ 用户认证失败: ' + error.message, 'error');
            }
        }
        
        // 加载项目数据
        async function loadProjectData() {
            showStatus('project-status', '正在加载项目数据...', 'info');
            
            try {
                const { data: projects, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                showStatus('project-status', `✅ 找到 ${projects.length} 个项目`, 'success');
                
                // 如果有项目，设置第一个为当前项目
                if (projects.length > 0) {
                    if (!testCollaborationManager) {
                        testCollaborationManager = new CollaborationManager();
                    }
                    testCollaborationManager.currentProject = projects[0];
                    showStatus('project-status', `✅ 已设置当前项目: ${projects[0].title}`, 'success');
                }
            } catch (error) {
                showStatus('project-status', '❌ 加载项目数据失败: ' + error.message, 'error');
            }
        }
        
        // 加载团队成员
        async function loadTeamMembers() {
            if (!testCollaborationManager || !testCollaborationManager.currentProject) {
                showStatus('project-status', '❌ 请先加载项目数据', 'error');
                return;
            }
            
            showStatus('project-status', '正在加载团队成员...', 'info');
            
            try {
                await testCollaborationManager.loadTeamMembers();
                const members = testCollaborationManager.getProjectMembers();
                showStatus('project-status', `✅ 找到 ${members.length} 个团队成员`, 'success');
            } catch (error) {
                showStatus('project-status', '❌ 加载团队成员失败: ' + error.message, 'error');
            }
        }
        
        // 加载章节数据
        async function loadChapters() {
            if (!testCollaborationManager || !testCollaborationManager.currentProject) {
                showStatus('project-status', '❌ 请先加载项目数据', 'error');
                return;
            }
            
            showStatus('project-status', '正在加载章节数据...', 'info');
            
            try {
                const chapters = await testCollaborationManager.loadProjectChapters();
                showStatus('project-status', `✅ 找到 ${chapters.length} 个章节/大纲项`, 'success');
            } catch (error) {
                showStatus('project-status', '❌ 加载章节数据失败: ' + error.message, 'error');
            }
        }
        
        // 测试新建分配
        async function testCreateAssignment() {
            if (!testCollaborationManager || !testCollaborationManager.currentProject) {
                showStatus('assignment-status', '❌ 请先加载项目数据', 'error');
                return;
            }
            
            showStatus('assignment-status', '正在测试新建分配功能...', 'info');
            
            try {
                await testCollaborationManager.showCreateAssignmentModal();
                showStatus('assignment-status', '✅ 新建分配对话框已显示', 'success');
            } catch (error) {
                showStatus('assignment-status', '❌ 新建分配功能测试失败: ' + error.message, 'error');
            }
        }
        
        // 测试加载分配
        async function testLoadAssignments() {
            if (!testCollaborationManager || !testCollaborationManager.currentProject) {
                showStatus('assignment-status', '❌ 请先加载项目数据', 'error');
                return;
            }
            
            showStatus('assignment-status', '正在测试加载分配功能...', 'info');
            
            try {
                await testCollaborationManager.loadChapterAssignments();
                showStatus('assignment-status', '✅ 章节分配加载完成', 'success');
            } catch (error) {
                showStatus('assignment-status', '❌ 加载分配功能测试失败: ' + error.message, 'error');
            }
        }
        
        // 测试创建默认分配
        async function testDefaultAssignments() {
            if (!testCollaborationManager || !testCollaborationManager.currentProject) {
                showStatus('assignment-status', '❌ 请先加载项目数据', 'error');
                return;
            }
            
            showStatus('assignment-status', '正在创建默认分配...', 'info');
            
            try {
                const chapters = await testCollaborationManager.loadProjectChapters();
                if (chapters.length === 0) {
                    showStatus('assignment-status', '⚠️ 没有找到章节数据，无法创建默认分配', 'error');
                    return;
                }
                
                await testCollaborationManager.createDefaultAssignments(chapters);
                showStatus('assignment-status', '✅ 默认分配创建完成', 'success');
            } catch (error) {
                showStatus('assignment-status', '❌ 创建默认分配失败: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
