<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节分配系统优化测试 - 专业学术协作编著系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .test-content {
            padding: 0;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            background: #10b981;
            color: white;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            z-index: 1000;
        }
        
        .status-indicator i {
            margin-right: 8px;
        }
        
        .feature-highlights {
            background: #f8fafc;
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .feature-highlights h3 {
            margin: 0 0 12px 0;
            color: #1f2937;
            font-size: 16px;
            font-weight: 600;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #374151;
        }
        
        .feature-item i {
            color: #10b981;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="status-indicator">
        <i class="fas fa-check-circle"></i>
        章节分配系统已优化
    </div>

    <div class="test-container">
        <div class="test-header">
            <h1>章节分配系统优化测试</h1>
            <p>测试新增的审核者字段、正确的状态映射和编辑功能</p>
        </div>
        
        <div class="feature-highlights">
            <h3>✨ 新增功能特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-user-check"></i>
                    <span>新增审核者字段</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-edit"></i>
                    <span>完整的编辑功能</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-tags"></i>
                    <span>正确的状态映射</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-users"></i>
                    <span>项目成员选择</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>截止日期管理</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-search"></i>
                    <span>智能搜索过滤</span>
                </div>
            </div>
        </div>
        
        <div class="test-content">
            <div class="chapter-assignment-container">
                <!-- 章节分配头部 -->
                <div class="assignment-header">
                    <div class="header-left">
                        <h3>章节分配</h3>
                        <div class="assignment-stats" id="assignment-stats">
                            <span class="stat-item">
                                <i class="fas fa-book"></i>
                                <span id="total-chapters">2</span> 总章节
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-users"></i>
                                <span id="total-authors">6</span> 参与作者
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-check-circle"></i>
                                <span id="completed-chapters">0</span> 已完成
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span id="pending-reviews">1</span> 待审核
                            </span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="assignment-search" placeholder="搜索章节或作者...">
                        </div>
                        <select id="status-filter" class="status-filter">
                            <option value="">所有状态</option>
                            <option value="pending">待分配</option>
                            <option value="writing">编制中</option>
                            <option value="reviewing">审核中</option>
                            <option value="completed">已完成</option>
                        </select>
                        <button class="btn btn-primary" onclick="showCreateModal()">
                            <i class="fas fa-plus"></i>
                            新建分配
                        </button>
                        <button class="btn btn-secondary" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <!-- 章节分配列表 -->
                <div class="assignments-container">
                    <div class="assignments-header-row">
                        <div class="header-cell">章节</div>
                        <div class="header-cell">主笔作者</div>
                        <div class="header-cell">协作者</div>
                        <div class="header-cell">审核者</div>
                        <div class="header-cell">状态</div>
                        <div class="header-cell">进度</div>
                        <div class="header-cell">截止日期</div>
                        <div class="header-cell">操作</div>
                    </div>
                    <div class="assignments-list" id="chapter-assignments-list">
                        <!-- 动态内容将在这里加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟协作管理器
        const collaborationManager = {
            selectedCollaborators: [],
            
            // 模拟项目成员数据
            getProjectMembers() {
                return [
                    { id: 'user1', full_name: '张教授', email: '<EMAIL>', role: '教授', avatar: 'Z' },
                    { id: 'user2', full_name: '李博士', email: '<EMAIL>', role: '博士', avatar: 'L' },
                    { id: 'user3', full_name: '王研究员', email: '<EMAIL>', role: '研究员', avatar: 'W' },
                    { id: 'user4', full_name: '刘院长', email: '<EMAIL>', role: '院长', avatar: 'L' },
                    { id: 'user5', full_name: '陈助教', email: '<EMAIL>', role: '助教', avatar: 'C' },
                    { id: 'user6', full_name: '赵副教授', email: '<EMAIL>', role: '副教授', avatar: 'Z' }
                ];
            },

            // 模拟分配数据
            mockAssignments: [
                {
                    id: '1',
                    title: '第一章：绪论',
                    description: '介绍大模型技术发展背景和研究意义',
                    status: 'writing',
                    lead_author: { id: 'user1', full_name: '张教授', email: '<EMAIL>', role: '教授' },
                    collaborators: [
                        { id: 'user3', full_name: '王研究员', email: '<EMAIL>', role: '研究员' }
                    ],
                    reviewer: { id: 'user4', full_name: '刘院长', email: '<EMAIL>', role: '院长' },
                    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    priority: 'high'
                },
                {
                    id: '2',
                    title: '第二章：文献综述',
                    description: '相关研究领域的文献综述和分析',
                    status: 'reviewing',
                    lead_author: { id: 'user2', full_name: '李博士', email: '<EMAIL>', role: '博士' },
                    collaborators: [
                        { id: 'user5', full_name: '陈助教', email: '<EMAIL>', role: '助教' }
                    ],
                    reviewer: { id: 'user1', full_name: '张教授', email: '<EMAIL>', role: '教授' },
                    due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
                    priority: 'medium'
                }
            ],

            // 计算进度
            calculateProgress(assignment) {
                if (assignment.status === 'completed') return 100;
                if (assignment.status === 'reviewing') return 85;
                if (assignment.status === 'writing') return 50;
                if (assignment.status === 'pending') return 5;
                return 0;
            },

            // 获取状态文本
            getStatusText(status) {
                const statusMap = {
                    'pending': '待分配',
                    'writing': '编制中',
                    'reviewing': '审核中',
                    'completed': '已完成',
                    'rejected': '已拒绝'
                };
                return statusMap[status] || status;
            },

            // 渲染章节分配
            renderChapterAssignments() {
                const assignmentsContainer = document.getElementById('chapter-assignments-list');
                if (!assignmentsContainer) return;

                const assignments = this.mockAssignments;

                assignmentsContainer.innerHTML = assignments.map(assignment => {
                    const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
                    const isOverdue = dueDate && dueDate < new Date();
                    const progress = this.calculateProgress(assignment);
                    
                    // 处理协作者显示
                    const collaboratorsText = assignment.collaborators && assignment.collaborators.length > 0 
                        ? assignment.collaborators.map(c => c.full_name).join(', ')
                        : '无';
                    
                    return `
                        <div class="assignment-item">
                            <div class="assignment-title">
                                ${assignment.title}
                                <div class="assignment-subtitle">
                                    ${assignment.description}
                                </div>
                            </div>
                            <div class="user-badge">
                                <i class="fas fa-user"></i>
                                ${assignment.lead_author.full_name}
                            </div>
                            <div class="user-badge">
                                <i class="fas fa-users"></i>
                                ${collaboratorsText}
                            </div>
                            <div class="user-badge">
                                <i class="fas fa-user-check"></i>
                                ${assignment.reviewer.full_name}
                            </div>
                            <div>
                                <span class="status-badge status-${assignment.status}">
                                    ${this.getStatusText(assignment.status)}
                                </span>
                            </div>
                            <div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${progress}%"></div>
                                </div>
                                <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                                    ${progress}%
                                </div>
                            </div>
                            <div class="due-date ${isOverdue ? 'overdue' : ''}">
                                ${dueDate ? dueDate.toLocaleDateString() : '无截止日期'}
                            </div>
                            <div class="actions">
                                <button class="action-btn" onclick="collaborationManager.editAssignment('${assignment.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" onclick="viewAssignment('${assignment.id}')" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" onclick="deleteAssignment('${assignment.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');
            },

            // 编辑分配
            editAssignment(assignmentId) {
                const assignment = this.mockAssignments.find(a => a.id === assignmentId);
                if (!assignment) {
                    alert('未找到指定的分配记录');
                    return;
                }
                
                this.showEditAssignmentModal(assignment);
            }
        };

        // 包含编辑模态对话框的所有方法
        collaborationManager.showEditAssignmentModal = function(assignment) {
            const members = this.getProjectMembers();
            
            // 创建模态对话框HTML
            const modalHtml = `
                <div class="assignment-modal" id="editAssignmentModal">
                    <div class="assignment-modal-content">
                        <div class="assignment-modal-header">
                            <h3 class="assignment-modal-title">
                                <i class="fas fa-edit"></i>
                                编辑章节分配
                            </h3>
                        </div>
                        <div class="assignment-modal-body">
                            <form id="editAssignmentForm">
                                <input type="hidden" id="assignmentId" value="${assignment.id}">
                                
                                <div class="form-group">
                                    <label class="form-label">章节标题</label>
                                    <input type="text" class="form-input" id="assignmentTitle" value="${assignment.title}" readonly>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">章节描述</label>
                                    <textarea class="form-textarea" id="assignmentDescription" placeholder="请输入章节描述">${assignment.description}</textarea>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">主笔作者</label>
                                        <div class="user-selector">
                                            <input type="text" class="form-input user-selector-input" id="leadAuthorInput" 
                                                   value="${assignment.lead_author?.full_name || ''}" readonly 
                                                   placeholder="选择主笔作者" onclick="collaborationManager.toggleUserDropdown('leadAuthor')">
                                            <div class="user-dropdown" id="leadAuthorDropdown">
                                                ${members.map(member => `
                                                    <div class="user-option ${member.id === assignment.lead_author?.id ? 'selected' : ''}" 
                                                         onclick="collaborationManager.selectUser('leadAuthor', '${member.id}', '${member.full_name}')">
                                                        <div class="user-avatar-small">${member.avatar}</div>
                                                        <div class="user-info">
                                                            <div class="user-name">${member.full_name}</div>
                                                            <div class="user-role">${member.role}</div>
                                                        </div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">审核者</label>
                                        <div class="user-selector">
                                            <input type="text" class="form-input user-selector-input" id="reviewerInput" 
                                                   value="${assignment.reviewer?.full_name || ''}" readonly 
                                                   placeholder="选择审核者" onclick="collaborationManager.toggleUserDropdown('reviewer')">
                                            <div class="user-dropdown" id="reviewerDropdown">
                                                ${members.map(member => `
                                                    <div class="user-option ${member.id === assignment.reviewer?.id ? 'selected' : ''}" 
                                                         onclick="collaborationManager.selectUser('reviewer', '${member.id}', '${member.full_name}')">
                                                        <div class="user-avatar-small">${member.avatar}</div>
                                                        <div class="user-info">
                                                            <div class="user-name">${member.full_name}</div>
                                                            <div class="user-role">${member.role}</div>
                                                        </div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">协作者</label>
                                    <div class="user-selector">
                                        <input type="text" class="form-input user-selector-input" id="collaboratorsInput" 
                                               value="${assignment.collaborators?.map(c => c.full_name).join(', ') || ''}" readonly 
                                               placeholder="选择协作者（可多选）" onclick="collaborationManager.toggleUserDropdown('collaborators')">
                                        <div class="user-dropdown" id="collaboratorsDropdown">
                                            ${members.map(member => {
                                                const isSelected = assignment.collaborators?.some(c => c.id === member.id);
                                                return `
                                                    <div class="user-option ${isSelected ? 'selected' : ''}" 
                                                         onclick="collaborationManager.toggleCollaborator('${member.id}', '${member.full_name}')">
                                                        <div class="user-avatar-small">${member.avatar}</div>
                                                        <div class="user-info">
                                                            <div class="user-name">${member.full_name}</div>
                                                            <div class="user-role">${member.role}</div>
                                                        </div>
                                                        ${isSelected ? '<i class="fas fa-check" style="color: #10b981;"></i>' : ''}
                                                    </div>
                                                `;
                                            }).join('')}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" id="assignmentStatus">
                                            <option value="pending" ${assignment.status === 'pending' ? 'selected' : ''}>待分配</option>
                                            <option value="writing" ${assignment.status === 'writing' ? 'selected' : ''}>编制中</option>
                                            <option value="reviewing" ${assignment.status === 'reviewing' ? 'selected' : ''}>审核中</option>
                                            <option value="completed" ${assignment.status === 'completed' ? 'selected' : ''}>已完成</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">截止日期</label>
                                        <input type="date" class="form-input" id="assignmentDueDate" 
                                               value="${assignment.due_date ? new Date(assignment.due_date).toISOString().split('T')[0] : ''}">
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="assignment-modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="collaborationManager.closeEditModal()">
                                <i class="fas fa-times"></i>
                                取消
                            </button>
                            <button type="button" class="btn btn-primary" onclick="collaborationManager.saveAssignment()">
                                <i class="fas fa-save"></i>
                                保存
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 显示模态对话框
            setTimeout(() => {
                document.getElementById('editAssignmentModal').classList.add('active');
            }, 10);
            
            // 初始化选中的协作者
            this.selectedCollaborators = assignment.collaborators ? [...assignment.collaborators] : [];
        };

        // 添加其他必要的方法
        collaborationManager.toggleUserDropdown = function(type) {
            const dropdown = document.getElementById(`${type}Dropdown`);
            const isActive = dropdown.classList.contains('active');
            
            // 关闭所有下拉框
            document.querySelectorAll('.user-dropdown').forEach(d => d.classList.remove('active'));
            
            // 切换当前下拉框
            if (!isActive) {
                dropdown.classList.add('active');
            }
        };

        collaborationManager.selectUser = function(type, userId, userName) {
            const input = document.getElementById(`${type}Input`);
            const dropdown = document.getElementById(`${type}Dropdown`);
            
            input.value = userName;
            dropdown.classList.remove('active');
            
            // 更新选中状态
            dropdown.querySelectorAll('.user-option').forEach(option => {
                option.classList.remove('selected');
            });
            dropdown.querySelector(`[onclick*="${userId}"]`).classList.add('selected');
        };

        collaborationManager.toggleCollaborator = function(userId, userName) {
            const members = this.getProjectMembers();
            const member = members.find(m => m.id === userId);
            
            if (!member) return;
            
            const existingIndex = this.selectedCollaborators.findIndex(c => c.id === userId);
            
            if (existingIndex >= 0) {
                // 移除协作者
                this.selectedCollaborators.splice(existingIndex, 1);
            } else {
                // 添加协作者
                this.selectedCollaborators.push(member);
            }
            
            // 更新输入框显示
            const input = document.getElementById('collaboratorsInput');
            input.value = this.selectedCollaborators.map(c => c.full_name).join(', ');
            
            // 更新选中状态
            const option = document.querySelector(`[onclick*="toggleCollaborator('${userId}'"]`);
            if (option) {
                if (existingIndex >= 0) {
                    option.classList.remove('selected');
                    const checkIcon = option.querySelector('.fa-check');
                    if (checkIcon) checkIcon.remove();
                } else {
                    option.classList.add('selected');
                    option.insertAdjacentHTML('beforeend', '<i class="fas fa-check" style="color: #10b981;"></i>');
                }
            }
        };

        collaborationManager.closeEditModal = function() {
            const modal = document.getElementById('editAssignmentModal');
            if (modal) {
                modal.classList.remove('active');
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        };

        collaborationManager.saveAssignment = function() {
            const assignmentData = {
                id: document.getElementById('assignmentId').value,
                title: document.getElementById('assignmentTitle').value,
                description: document.getElementById('assignmentDescription').value,
                status: document.getElementById('assignmentStatus').value,
                due_date: document.getElementById('assignmentDueDate').value
            };
            
            console.log('保存分配数据:', assignmentData);
            alert('分配已保存！');
            
            this.closeEditModal();
            this.renderChapterAssignments();
        };

        // 其他功能按钮
        function showCreateModal() {
            alert('新建分配功能已集成！');
        }

        function refreshData() {
            collaborationManager.renderChapterAssignments();
            alert('数据已刷新！');
        }

        function viewAssignment(id) {
            alert(`查看分配详情: ${id}`);
        }

        function deleteAssignment(id) {
            if (confirm('确定要删除这个分配吗？')) {
                alert(`删除分配: ${id}`);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 渲染章节分配
            collaborationManager.renderChapterAssignments();
            
            // 设置搜索功能
            const searchInput = document.getElementById('assignment-search');
            const statusFilter = document.getElementById('status-filter');
            
            if (searchInput) {
                searchInput.addEventListener('input', filterAssignments);
            }
            
            if (statusFilter) {
                statusFilter.addEventListener('change', filterAssignments);
            }

            // 点击外部关闭下拉框
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.user-selector')) {
                    document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                        dropdown.classList.remove('active');
                    });
                }
            });
        });

        // 过滤功能
        function filterAssignments() {
            const searchTerm = document.getElementById('assignment-search').value.toLowerCase();
            const statusFilter = document.getElementById('status-filter').value;
            const items = document.querySelectorAll('.assignment-item');
            
            items.forEach(item => {
                const title = item.querySelector('.assignment-title').textContent.toLowerCase();
                const author = item.querySelector('.user-badge').textContent.toLowerCase();
                const status = item.querySelector('.status-badge').className;
                
                const matchesSearch = !searchTerm || title.includes(searchTerm) || author.includes(searchTerm);
                const matchesStatus = !statusFilter || status.includes(`status-${statusFilter}`);
                
                item.style.display = matchesSearch && matchesStatus ? 'grid' : 'none';
            });
        }
    </script>
</body>
</html>
