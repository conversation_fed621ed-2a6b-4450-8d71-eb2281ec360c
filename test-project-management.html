<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理测试页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .test-title {
            font-size: 1.8rem;
            color: #1f2937;
            margin: 0;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
        }

        .project-card {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            min-height: 280px;
            display: flex;
            flex-direction: column;
        }

        .project-card:hover {
            border-color: #4f46e5;
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
            transform: translateY(-2px);
        }

        .project-meta {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 16px;
            gap: 12px;
        }

        .project-status {
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            background: #dcfce7;
            color: #166534;
        }

        .project-date {
            color: #9ca3af;
            font-size: 0.75rem;
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
        }

        .project-description {
            color: #6b7280;
            font-size: 0.875rem;
            margin: 0 0 16px 0;
        }

        .project-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
            padding: 12px 0;
            border-top: 1px solid #f3f4f6;
            border-bottom: 1px solid #f3f4f6;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.75rem;
            color: #6b7280;
        }

        .project-progress {
            margin-bottom: 16px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.875rem;
        }

        .progress-text {
            color: #374151;
            font-weight: 500;
        }

        .progress-percentage {
            color: #4f46e5;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4f46e5;
            width: 75%;
        }

        .project-actions {
            display: flex;
            flex-direction: row;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
            justify-content: flex-start;
            align-items: center;
        }

        .project-card:hover .project-actions {
            opacity: 1;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: all 0.3s ease;
        }

        .action-btn.edit {
            background: #f3f4f6;
            color: #374151;
        }

        .action-btn.edit:hover {
            background: #e5e7eb;
        }

        .action-btn.export {
            background: #ecfdf5;
            color: #065f46;
        }

        .action-btn.export:hover {
            background: #d1fae5;
        }

        .action-btn.delete {
            background: #fef2f2;
            color: #991b1b;
        }

        .action-btn.delete:hover {
            background: #fee2e2;
        }

        .test-info {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4f46e5;
        }

        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }

        .test-info p {
            margin: 0;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">项目管理功能测试</h1>
            <button class="btn btn-secondary" onclick="testSignOut()">
                <i class="fas fa-sign-out-alt"></i> 测试退出登录
            </button>
        </div>

        <div class="test-info">
            <h3>测试说明</h3>
            <p>1. 鼠标悬停在项目卡片上查看按钮是否水平排列</p>
            <p>2. 点击右上角的"测试退出登录"按钮验证是否跳转到登录页面</p>
        </div>

        <div class="projects-grid">
            <div class="project-card">
                <div class="project-meta">
                    <div class="project-status">进行中</div>
                    <div class="project-date">2024-01-15</div>
                </div>
                <h3 class="project-title">大模型技术与油气应用概论</h3>
                <p class="project-description">这是一本关于大模型在油气行业应用的专业著作，涵盖了理论基础、技术实现和实际案例。</p>
                <div class="project-stats">
                    <div class="stat-item">
                        <i class="fas fa-book"></i>
                        <span>12 章节</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-file-alt"></i>
                        <span>8.5万 字</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <span>3 成员</span>
                    </div>
                </div>
                <div class="project-progress">
                    <div class="progress-label">
                        <span class="progress-text">完成进度</span>
                        <span class="progress-percentage">75%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="action-btn edit" onclick="alert('编辑功能')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn export" onclick="alert('导出功能')">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="action-btn delete" onclick="alert('删除功能')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>

            <div class="project-card">
                <div class="project-meta">
                    <div class="project-status">进行中</div>
                    <div class="project-date">2024-01-10</div>
                </div>
                <h3 class="project-title">AI在石油勘探中的应用研究</h3>
                <p class="project-description">探讨人工智能技术在石油勘探领域的创新应用和发展前景。</p>
                <div class="project-stats">
                    <div class="stat-item">
                        <i class="fas fa-book"></i>
                        <span>8 章节</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-file-alt"></i>
                        <span>5.2万 字</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <span>2 成员</span>
                    </div>
                </div>
                <div class="project-progress">
                    <div class="progress-label">
                        <span class="progress-text">完成进度</span>
                        <span class="progress-percentage">60%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%"></div>
                    </div>
                </div>
                <div class="project-actions">
                    <button class="action-btn edit" onclick="alert('编辑功能')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn export" onclick="alert('导出功能')">
                        <i class="fas fa-download"></i> 导出
                    </button>
                    <button class="action-btn delete" onclick="alert('删除功能')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testSignOut() {
            if (confirm('这将模拟退出登录并跳转到登录页面，确定继续吗？')) {
                // 清除本地存储（模拟登出）
                localStorage.clear();
                
                // 跳转到登录页面
                window.location.href = 'auth.html';
            }
        }
    </script>
</body>
</html>
