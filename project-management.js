// 项目管理页面逻辑
class ProjectManager {
    constructor() {
        this.projects = [];
        this.currentUser = null;
        this.isLoading = false;
    }

    // 初始化页面
    async init() {
        try {
            // 检查用户登录状态
            this.currentUser = await supabaseManager.getCurrentUser();
            if (!this.currentUser) {
                window.location.href = 'auth.html';
                return;
            }

            // 显示用户信息
            this.displayUserInfo();

            // 加载项目列表
            await this.loadProjects();

        } catch (error) {
            console.error('初始化失败:', error);
            this.showNotification('初始化失败: ' + error.message, 'error');
        }
    }

    // 显示用户信息
    displayUserInfo() {
        const userInfo = document.getElementById('user-info');
        const userAvatar = document.getElementById('user-avatar');
        const userName = document.getElementById('user-name');
        const userEmail = document.getElementById('user-email');

        if (this.currentUser) {
            const displayName = this.currentUser.user_metadata?.full_name || 
                               this.currentUser.email.split('@')[0];
            const email = this.currentUser.email;

            userAvatar.textContent = displayName.charAt(0).toUpperCase();
            userName.textContent = displayName;
            userEmail.textContent = email;
            userInfo.style.display = 'flex';
        }
    }

    // 加载项目列表
    async loadProjects() {
        this.isLoading = true;
        this.showLoadingState();

        try {
            // 获取用户项目
            this.projects = await supabaseManager.getUserProjects();
            
            // 渲染项目列表
            this.renderProjects();

        } catch (error) {
            console.error('加载项目失败:', error);
            this.showNotification('加载项目失败: ' + error.message, 'error');
            this.showEmptyState();
        } finally {
            this.isLoading = false;
        }
    }

    // 显示加载状态
    showLoadingState() {
        const container = document.getElementById('projects-container');

        // 显示骨架屏
        const skeletonGrid = document.createElement('div');
        skeletonGrid.className = 'projects-grid';

        // 添加创建新项目卡片
        skeletonGrid.appendChild(this.createNewProjectCard());

        // 添加骨架屏卡片
        for (let i = 0; i < 6; i++) {
            skeletonGrid.appendChild(this.createSkeletonCard());
        }

        container.innerHTML = '';
        container.appendChild(skeletonGrid);

        // 显示加载提示
        setTimeout(() => {
            if (this.isLoading) {
                this.showDetailedLoadingState();
            }
        }, 2000);
    }

    // 创建骨架屏卡片
    createSkeletonCard() {
        const card = document.createElement('div');
        card.className = 'project-card skeleton-card';

        card.innerHTML = `
            <div class="skeleton-header">
                <div class="skeleton skeleton-status"></div>
                <div class="skeleton skeleton-date"></div>
            </div>
            <div class="skeleton skeleton-title"></div>
            <div class="skeleton skeleton-description"></div>
            <div class="skeleton skeleton-description"></div>
            <div class="skeleton-stats">
                <div class="skeleton skeleton-stat"></div>
                <div class="skeleton skeleton-stat"></div>
                <div class="skeleton skeleton-stat"></div>
            </div>
            <div class="skeleton-progress">
                <div class="skeleton-progress-label">
                    <div class="skeleton skeleton-progress-text"></div>
                    <div class="skeleton skeleton-progress-percent"></div>
                </div>
                <div class="skeleton skeleton-progress-bar"></div>
            </div>
            <div class="skeleton-actions">
                <div class="skeleton skeleton-action"></div>
                <div class="skeleton skeleton-action"></div>
                <div class="skeleton skeleton-action"></div>
            </div>
        `;

        return card;
    }

    // 显示详细加载状态
    showDetailedLoadingState() {
        const container = document.getElementById('projects-container');
        container.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载项目数据...</div>
                <div class="loading-subtext">
                    正在从数据库获取项目信息、章节内容和统计数据<br>
                    首次加载可能需要较长时间，请耐心等待
                </div>
            </div>
        `;
    }

    // 渲染项目列表
    async renderProjects() {
        const container = document.getElementById('projects-container');

        if (this.projects.length === 0) {
            this.showEmptyState();
            return;
        }

        const projectsGrid = document.createElement('div');
        projectsGrid.className = 'projects-grid';

        // 添加创建新项目卡片
        projectsGrid.appendChild(this.createNewProjectCard());

        container.innerHTML = '';
        container.appendChild(projectsGrid);

        // 渐进式加载项目卡片
        await this.renderProjectsProgressively(projectsGrid);
    }

    // 渐进式渲染项目卡片
    async renderProjectsProgressively(container) {
        for (let i = 0; i < this.projects.length; i++) {
            const project = this.projects[i];

            // 显示加载占位符
            const placeholder = this.createLoadingPlaceholder();
            container.appendChild(placeholder);

            try {
                // 异步创建项目卡片
                const card = await this.createProjectCard(project);

                // 添加淡入动画
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                // 替换占位符
                container.replaceChild(card, placeholder);

                // 触发动画
                requestAnimationFrame(() => {
                    card.style.transition = 'all 0.3s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                });

                // 添加延迟以创建渐进效果
                if (i < this.projects.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            } catch (error) {
                console.error('渲染项目卡片失败:', error);
                // 移除占位符
                if (placeholder.parentNode) {
                    placeholder.parentNode.removeChild(placeholder);
                }
            }
        }
    }

    // 创建加载占位符
    createLoadingPlaceholder() {
        const placeholder = document.createElement('div');
        placeholder.className = 'project-card skeleton-card';
        placeholder.innerHTML = `
            <div class="skeleton-header">
                <div class="skeleton skeleton-status"></div>
                <div class="skeleton skeleton-date"></div>
            </div>
            <div class="skeleton skeleton-title"></div>
            <div class="skeleton skeleton-description"></div>
            <div class="skeleton skeleton-description"></div>
            <div class="skeleton-stats">
                <div class="skeleton skeleton-stat"></div>
                <div class="skeleton skeleton-stat"></div>
                <div class="skeleton skeleton-stat"></div>
            </div>
            <div class="skeleton-progress">
                <div class="skeleton-progress-label">
                    <div class="skeleton skeleton-progress-text"></div>
                    <div class="skeleton skeleton-progress-percent"></div>
                </div>
                <div class="skeleton skeleton-progress-bar"></div>
            </div>
            <div class="skeleton-actions">
                <div class="skeleton skeleton-action"></div>
                <div class="skeleton skeleton-action"></div>
                <div class="skeleton skeleton-action"></div>
            </div>
        `;
        return placeholder;
    }

    // 创建新项目卡片
    createNewProjectCard() {
        const card = document.createElement('div');
        card.className = 'project-card create-new';
        card.onclick = () => this.showCreateProjectModal();

        card.innerHTML = `
            <i class="fas fa-plus create-icon"></i>
            <h3 class="project-title">创建新项目</h3>
            <p class="project-description">开始一个新的写作项目</p>
        `;

        return card;
    }

    // 创建项目卡片
    async createProjectCard(project) {
        const card = document.createElement('div');
        card.className = 'project-card';
        card.onclick = () => this.openProject(project);

        // 计算进度
        const progress = await this.calculateProjectProgress(project);
        const statusClass = this.getStatusClass(project.status);
        const statusText = this.getStatusText(project.status);

        // 获取项目统计信息
        const stats = await this.getProjectStats(project.id);

        card.innerHTML = `
            <div class="project-meta">
                <div class="project-status ${statusClass}">${statusText}</div>
                <div class="project-date">${this.formatDate(project.updated_at)}</div>
            </div>
            <h3 class="project-title">${project.title}</h3>
            <p class="project-description">${project.description || '暂无描述'}</p>
            <div class="project-stats">
                <div class="stat-item">
                    <i class="fas fa-book"></i>
                    <span>${stats.chapterCount} 章节</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-file-alt"></i>
                    <span>${stats.wordCount} 字</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>${stats.memberCount} 成员</span>
                </div>
            </div>
            <div class="project-progress">
                <div class="progress-label">
                    <span class="progress-text">完成进度</span>
                    <span class="progress-percentage">${progress}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
            </div>
            <div class="project-actions">
                <button class="action-btn edit" onclick="event.stopPropagation(); projectManager.editProject('${project.id}')">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn users" onclick="event.stopPropagation(); projectManager.manageUsers('${project.id}')" title="用户管理">
                    <i class="fas fa-users"></i> 用户
                </button>
                <button class="action-btn export" onclick="event.stopPropagation(); projectManager.showExportModal('${project.id}')">
                    <i class="fas fa-download"></i> 导出
                </button>
                <button class="action-btn delete" onclick="event.stopPropagation(); projectManager.deleteProject('${project.id}')">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        `;

        return card;
    }

    // 计算项目进度
    async calculateProjectProgress(project) {
        try {
            // 获取项目的章节数据
            const { data: chapters, error } = await supabaseManager.supabase
                .from('chapters')
                .select('id, status, word_count')
                .eq('project_id', project.id);

            if (error) {
                console.error('获取章节数据失败:', error);
                return 0;
            }

            if (!chapters || chapters.length === 0) {
                return 0;
            }

            // 计算完成的章节数
            const completedChapters = chapters.filter(chapter =>
                chapter.status === 'completed' ||
                (chapter.word_count && chapter.word_count > 100)
            ).length;

            return Math.round((completedChapters / chapters.length) * 100);
        } catch (error) {
            console.error('计算项目进度失败:', error);
            return 0;
        }
    }

    // 获取项目统计信息
    async getProjectStats(projectId) {
        try {
            // 获取章节统计
            const { data: chapters, error: chapterError } = await supabaseManager.supabase
                .from('chapters')
                .select('word_count')
                .eq('project_id', projectId);

            if (chapterError) {
                console.error('获取章节统计失败:', chapterError);
            }

            // 获取成员统计
            const { data: members, error: memberError } = await supabaseManager.supabase
                .from('project_members')
                .select('user_id')
                .eq('project_id', projectId);

            if (memberError) {
                console.error('获取成员统计失败:', memberError);
            }

            const chapterCount = chapters ? chapters.length : 0;
            const wordCount = chapters ? chapters.reduce((total, chapter) =>
                total + (chapter.word_count || 0), 0) : 0;
            const memberCount = members ? members.length : 0;

            return {
                chapterCount,
                wordCount: this.formatNumber(wordCount),
                memberCount
            };
        } catch (error) {
            console.error('获取项目统计失败:', error);
            return {
                chapterCount: 0,
                wordCount: '0',
                memberCount: 0
            };
        }
    }

    // 格式化数字
    formatNumber(num) {
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        }
        return num.toString();
    }

    // 获取状态样式类
    getStatusClass(status) {
        const statusMap = {
            'active': 'status-active',
            'completed': 'status-completed',
            'draft': 'status-draft'
        };
        return statusMap[status] || 'status-draft';
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'active': '进行中',
            'completed': '已完成',
            'draft': '草稿'
        };
        return statusMap[status] || '草稿';
    }

    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    // 显示空状态
    showEmptyState() {
        const container = document.getElementById('projects-container');
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-folder-open empty-icon"></i>
                <h3 class="empty-title">还没有项目</h3>
                <p class="empty-description">创建您的第一个写作项目开始吧</p>
                <button class="btn btn-primary" onclick="projectManager.showCreateProjectModal()">
                    <i class="fas fa-plus"></i> 创建项目
                </button>
            </div>
        `;
    }

    // 打开项目
    openProject(project) {
        // 保存选中的项目ID到本地存储
        localStorage.setItem('selectedProjectId', project.id);

        // 跳转到主应用，并传递项目ID作为URL参数
        window.location.href = `index.html?project=${project.id}`;
    }

    // 显示创建项目模态框
    showCreateProjectModal() {
        this.showModal('创建新项目', `
            <div class="form-group">
                <label class="form-label">项目标题</label>
                <input type="text" id="project-title" class="form-input" placeholder="输入项目标题" required>
            </div>
            <div class="form-group">
                <label class="form-label">项目描述</label>
                <textarea id="project-description" class="form-textarea" placeholder="输入项目描述" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">项目类型</label>
                <select id="project-type" class="form-select">
                    <option value="book">书籍</option>
                    <option value="paper">论文</option>
                    <option value="report">报告</option>
                    <option value="other">其他</option>
                </select>
            </div>
        `, [
            {
                text: '取消',
                className: 'btn-secondary',
                onclick: () => this.closeModal()
            },
            {
                text: '创建',
                className: 'btn-primary',
                onclick: () => this.createProject()
            }
        ]);
    }

    // 创建项目
    async createProject() {
        const title = document.getElementById('project-title').value.trim();
        const description = document.getElementById('project-description').value.trim();
        const type = document.getElementById('project-type').value;

        if (!title) {
            this.showNotification('请输入项目标题', 'error');
            return;
        }

        try {
            const projectData = {
                title: title,
                description: description,
                type: type,
                status: 'active'
            };

            const project = await supabaseManager.createProject(projectData);
            
            this.showNotification('项目创建成功', 'success');
            this.closeModal();
            
            // 重新加载项目列表
            await this.loadProjects();

        } catch (error) {
            console.error('创建项目失败:', error);
            this.showNotification('创建项目失败: ' + error.message, 'error');
        }
    }

    // 编辑项目
    editProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (!project) return;

        this.showModal('编辑项目', `
            <div class="form-group">
                <label class="form-label">项目标题</label>
                <input type="text" id="edit-project-title" class="form-input" value="${project.title}" required>
            </div>
            <div class="form-group">
                <label class="form-label">项目描述</label>
                <textarea id="edit-project-description" class="form-textarea" rows="3">${project.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label class="form-label">项目状态</label>
                <select id="edit-project-status" class="form-select">
                    <option value="draft" ${project.status === 'draft' ? 'selected' : ''}>草稿</option>
                    <option value="active" ${project.status === 'active' ? 'selected' : ''}>进行中</option>
                    <option value="completed" ${project.status === 'completed' ? 'selected' : ''}>已完成</option>
                </select>
            </div>
        `, [
            {
                text: '取消',
                className: 'btn-secondary',
                onclick: () => this.closeModal()
            },
            {
                text: '保存',
                className: 'btn-primary',
                onclick: () => this.updateProject(projectId)
            }
        ]);
    }

    // 更新项目
    async updateProject(projectId) {
        const title = document.getElementById('edit-project-title').value.trim();
        const description = document.getElementById('edit-project-description').value.trim();
        const status = document.getElementById('edit-project-status').value;

        if (!title) {
            this.showNotification('请输入项目标题', 'error');
            return;
        }

        try {
            const { data, error } = await supabaseManager.supabase
                .from('projects')
                .update({
                    title: title,
                    description: description,
                    status: status,
                    updated_at: new Date().toISOString()
                })
                .eq('id', projectId)
                .select()
                .single();

            if (error) throw error;

            this.showNotification('项目更新成功', 'success');
            this.closeModal();
            
            // 重新加载项目列表
            await this.loadProjects();

        } catch (error) {
            console.error('更新项目失败:', error);
            this.showNotification('更新项目失败: ' + error.message, 'error');
        }
    }

    // 用户管理
    async manageUsers(projectId) {
        try {
            // 检查用户权限
            const { data: member, error } = await supabaseManager.supabase
                .from('project_members')
                .select('role')
                .eq('project_id', projectId)
                .eq('user_id', supabaseManager.currentUser.id)
                .single();

            if (error) throw error;

            // 只有项目所有者和管理员可以管理用户
            if (!['owner', 'admin'].includes(member.role)) {
                this.showNotification('您没有权限管理用户', 'error');
                return;
            }

            // 设置当前项目ID并跳转到用户管理页面
            localStorage.setItem('currentProjectId', projectId);
            window.location.href = 'user-management.html';

        } catch (error) {
            console.error('检查权限失败:', error);
            this.showNotification('无法访问用户管理: ' + error.message, 'error');
        }
    }

    // 删除项目
    async deleteProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (!project) return;

        if (!confirm(`确定要删除项目"${project.title}"吗？此操作不可撤销。`)) {
            return;
        }

        try {
            const { error } = await supabaseManager.supabase
                .from('projects')
                .delete()
                .eq('id', projectId);

            if (error) throw error;

            this.showNotification('项目删除成功', 'success');

            // 重新加载项目列表
            await this.loadProjects();

        } catch (error) {
            console.error('删除项目失败:', error);
            this.showNotification('删除项目失败: ' + error.message, 'error');
        }
    }

    // 显示导出模态框
    showExportModal(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (!project) return;

        this.showModal('导出项目', `
            <p>选择要导出的格式：</p>
            <div class="export-options">
                <button class="btn btn-outline" onclick="projectManager.exportProject('${projectId}', 'json')">
                    <i class="fas fa-file-code"></i> JSON格式
                </button>
                <button class="btn btn-outline" onclick="projectManager.exportProject('${projectId}', 'pdf')">
                    <i class="fas fa-file-pdf"></i> PDF格式
                </button>
                <button class="btn btn-outline" onclick="projectManager.exportProject('${projectId}', 'docx')">
                    <i class="fas fa-file-word"></i> DOCX格式
                </button>
                <button class="btn btn-outline" onclick="projectManager.exportProject('${projectId}', 'html')">
                    <i class="fas fa-globe"></i> HTML格式
                </button>
            </div>
            <div class="export-tips">
                <p><small><i class="fas fa-info-circle"></i> 提示：</small></p>
                <ul style="font-size: 12px; color: #666; margin: 10px 0; padding-left: 20px;">
                    <li><strong>PDF格式</strong>：在新窗口中打开，使用浏览器打印功能保存为PDF</li>
                    <li><strong>DOCX格式</strong>：HTML内容保存为.docx后缀，可用Word或浏览器打开</li>
                    <li><strong>HTML格式</strong>：标准网页格式，浏览器直接打开</li>
                    <li><strong>JSON格式</strong>：原始数据，便于程序处理</li>
                </ul>
            </div>
        `, [
            {
                text: '取消',
                className: 'btn-secondary',
                onclick: () => this.closeModal()
            }
        ]);
    }

    // 导出项目
    async exportProject(projectId, format) {
        try {
            this.showNotification('正在准备导出...', 'info');
            this.closeModal();

            // 使用导出服务进行导出
            const result = await exportService.exportProject(projectId, format);

            if (result.success) {
                this.showNotification(`${format.toUpperCase()}导出成功！`, 'success');
            } else {
                this.showNotification('导出失败', 'error');
            }

        } catch (error) {
            console.error('导出失败:', error);
            this.showNotification('导出失败: ' + error.message, 'error');
        }
    }

    // 显示模态框
    showModal(title, content, actions = []) {
        const overlay = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const modalContent = document.getElementById('modal-content');
        const modalActions = document.getElementById('modal-actions');

        modalTitle.textContent = title;
        modalContent.innerHTML = content;

        // 生成操作按钮
        modalActions.innerHTML = '';
        actions.forEach(action => {
            const button = document.createElement('button');
            button.className = `btn ${action.className}`;
            button.textContent = action.text;
            button.onclick = action.onclick;
            modalActions.appendChild(button);
        });

        overlay.style.display = 'flex';
    }

    // 关闭模态框
    closeModal() {
        const overlay = document.getElementById('modal-overlay');
        overlay.style.display = 'none';
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const container = document.getElementById('notification-container');
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${this.getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;

        container.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // 显示用户资料模态框
    showUserProfileModal() {
        if (!this.currentUser) {
            this.showNotification('用户信息不可用', 'error');
            return;
        }

        const displayName = this.currentUser.user_metadata?.full_name ||
                           this.currentUser.email.split('@')[0];
        const email = this.currentUser.email;
        const avatarLetter = displayName.charAt(0).toUpperCase();

        this.showModal('个人资料', `
            <div class="profile-modal-content">
                <div class="avatar-section">
                    <div class="avatar-preview">${avatarLetter}</div>
                    <div class="avatar-info">
                        <h4>头像</h4>
                        <p>点击头像字母可以更改显示的首字母</p>
                    </div>
                </div>
                <form class="profile-form">
                    <div class="form-group">
                        <label for="profile-name">姓名</label>
                        <input type="text" id="profile-name" value="${displayName}" placeholder="请输入您的姓名">
                    </div>
                    <div class="form-group">
                        <label for="profile-email">邮箱</label>
                        <input type="email" id="profile-email" value="${email}" readonly style="background-color: #f9fafb; cursor: not-allowed;">
                        <small style="color: #6b7280; font-size: 0.75rem; margin-top: 4px; display: block;">邮箱地址不可修改</small>
                    </div>
                    <div class="form-group">
                        <label for="profile-bio">个人简介</label>
                        <input type="text" id="profile-bio" value="${this.currentUser.user_metadata?.bio || ''}" placeholder="请输入个人简介（可选）">
                    </div>
                    <div class="form-group">
                        <label for="profile-organization">所属机构</label>
                        <input type="text" id="profile-organization" value="${this.currentUser.user_metadata?.organization || ''}" placeholder="请输入所属机构（可选）">
                    </div>
                </form>
            </div>
        `, [
            {
                text: '取消',
                className: 'btn-secondary',
                onclick: () => this.closeModal()
            },
            {
                text: '保存',
                className: 'btn-primary',
                onclick: () => this.updateUserProfile()
            }
        ]);
    }

    // 更新用户资料
    async updateUserProfile() {
        const name = document.getElementById('profile-name').value.trim();
        const bio = document.getElementById('profile-bio').value.trim();
        const organization = document.getElementById('profile-organization').value.trim();

        if (!name) {
            this.showNotification('请输入姓名', 'error');
            return;
        }

        try {
            // 更新用户元数据
            const { data, error } = await supabaseManager.supabase.auth.updateUser({
                data: {
                    full_name: name,
                    bio: bio,
                    organization: organization
                }
            });

            if (error) throw error;

            // 更新当前用户信息
            this.currentUser = data.user;

            // 重新显示用户信息
            this.displayUserInfo();

            this.showNotification('个人资料更新成功', 'success');
            this.closeModal();

        } catch (error) {
            console.error('更新个人资料失败:', error);
            this.showNotification('更新个人资料失败: ' + error.message, 'error');
        }
    }
}

// 全局函数
function showUserProfile() {
    projectManager.showUserProfileModal();
}

function signOut() {
    if (confirm('确定要退出登录吗？')) {
        supabaseManager.signOut();
    }
}

// 初始化项目管理器
const projectManager = new ProjectManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    projectManager.init();
});

// 点击模态框外部关闭
document.addEventListener('click', (e) => {
    const overlay = document.getElementById('modal-overlay');
    if (e.target === overlay) {
        projectManager.closeModal();
    }
});
