<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试 - 章节分配系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .test-header h1 {
            color: #1e40af;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #374151;
            margin-bottom: 15px;
        }
        
        .test-button {
            background: #1e40af;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #1e3a8a;
        }
        
        .test-button.success {
            background: #059669;
        }
        
        .test-button.danger {
            background: #dc2626;
        }
        
        .status-message {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .status-info {
            background: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }
        
        .demo-link {
            display: inline-block;
            background: #1e40af;
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin: 10px;
            transition: background-color 0.2s;
        }
        
        .demo-link:hover {
            background: #1e3a8a;
        }
        
        .code-block {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #374151;
            margin: 10px 0;
            white-space: pre-line;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 章节分配系统 - 快速测试</h1>
            <p>验证系统修复状态和基本功能</p>
        </div>
        
        <div class="test-section">
            <h3>🎯 修复状态检查</h3>
            <div id="fix-status">
                <div class="status-message status-info">
                    正在检查系统状态...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 基本功能测试</h3>
            <button class="test-button" onclick="testBasicFunctions()">测试基本功能</button>
            <button class="test-button" onclick="testModalSystem()">测试模态框系统</button>
            <button class="test-button" onclick="testPermissionSystem()">测试权限系统</button>
            <button class="test-button success" onclick="testNotificationSystem()">测试通知系统</button>
        </div>
        
        <div class="test-section">
            <h3>📊 系统信息</h3>
            <div id="system-info">
                <div class="code-block" id="system-details">
                    正在加载系统信息...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚀 访问完整系统</h3>
            <div style="text-align: center;">
                <a href="chapter-assignment-fixed.html" class="demo-link">
                    🎯 访问修复版本
                </a>
                <a href="comprehensive-test.html" class="demo-link">
                    🧪 综合测试平台
                </a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试结果</h3>
            <div id="test-results"></div>
        </div>
    </div>
    
    <!-- 引入必要的脚本 -->
    <script src="supabase-config.js" onerror="console.warn('原始Supabase配置加载失败，将使用简化版本')"></script>
    <script src="supabase-simple.js"></script>
    <script src="role-permission-manager.js"></script>
    
    <script>
        // 测试结果显示
        function showTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-message status-${type}`;
            messageDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(messageDiv);
            
            // 自动清除旧消息
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 10000);
        }
        
        // 检查修复状态
        function checkFixStatus() {
            const statusDiv = document.getElementById('fix-status');
            let status = [];

            // 检查Supabase配置
            if (typeof supabaseManager !== 'undefined') {
                status.push('✅ Supabase配置已加载');
                try {
                    if (supabaseManager.supabase) {
                        status.push('✅ Supabase客户端已初始化');
                    } else {
                        status.push('⚠️ Supabase客户端未初始化');
                    }
                } catch (error) {
                    status.push('❌ Supabase客户端错误: ' + error.message);
                }
            } else {
                status.push('❌ Supabase配置未找到');
                // 检查是否有加载错误
                if (typeof SupabaseManager !== 'undefined') {
                    status.push('⚠️ SupabaseManager类存在但实例未创建');
                }
            }

            // 检查权限管理器
            if (typeof rolePermissionManager !== 'undefined') {
                status.push('✅ 权限管理器已加载');
                try {
                    const testPermission = rolePermissionManager.hasPermission('test', 'chief_editor');
                    status.push('✅ 权限管理器功能正常');
                } catch (error) {
                    status.push('❌ 权限管理器错误: ' + error.message);
                }
            } else {
                status.push('❌ 权限管理器未找到');
            }

            // 检查Chart.js
            if (typeof Chart !== 'undefined') {
                status.push('✅ Chart.js已加载');
            } else {
                status.push('⚠️ Chart.js未加载（可选）');
            }

            // 检查全局对象
            const globalObjects = ['window', 'document', 'console'];
            globalObjects.forEach(obj => {
                if (typeof window[obj] !== 'undefined') {
                    status.push(`✅ ${obj}对象可用`);
                }
            });

            statusDiv.innerHTML = status.map(s => `
                <div class="status-message ${s.includes('✅') ? 'status-success' : s.includes('❌') ? 'status-error' : 'status-info'}">
                    ${s}
                </div>
            `).join('');
        }
        
        // 显示系统信息
        function showSystemInfo() {
            const infoDiv = document.getElementById('system-details');
            const info = {
                '浏览器': navigator.userAgent.split(' ').slice(-2).join(' '),
                '时间': new Date().toLocaleString('zh-CN'),
                '页面URL': window.location.href,
                'Supabase状态': typeof supabaseManager !== 'undefined' ? '已加载' : '未加载',
                '权限管理器': typeof rolePermissionManager !== 'undefined' ? '已加载' : '未加载'
            };
            
            infoDiv.innerHTML = Object.entries(info).map(([key, value]) => 
                `${key}: ${value}`
            ).join('\n');
        }
        
        // 测试基本功能
        function testBasicFunctions() {
            try {
                let testsPassed = 0;
                let totalTests = 0;

                // 测试权限管理器
                totalTests++;
                if (window.rolePermissionManager) {
                    try {
                        const hasPermission = window.rolePermissionManager.hasPermission('project.view', 'chief_editor');
                        showTestResult(`权限检查测试: ${hasPermission ? '通过' : '失败'}`, hasPermission ? 'success' : 'error');
                        if (hasPermission) testsPassed++;

                        // 测试更多权限
                        const permissions = ['chapter.create', 'member.invite', 'review.approve'];
                        permissions.forEach(permission => {
                            totalTests++;
                            const result = window.rolePermissionManager.hasPermission(permission, 'chief_editor');
                            if (result) testsPassed++;
                        });

                    } catch (permError) {
                        showTestResult(`权限管理器错误: ${permError.message}`, 'error');
                    }
                } else {
                    showTestResult('权限管理器未找到', 'error');
                }

                // 测试Supabase连接
                totalTests++;
                if (window.supabaseManager) {
                    showTestResult('Supabase管理器可用', 'success');
                    testsPassed++;

                    // 测试Supabase客户端
                    totalTests++;
                    if (window.supabaseManager.supabase) {
                        showTestResult('Supabase客户端已初始化', 'success');
                        testsPassed++;
                    } else {
                        showTestResult('Supabase客户端未初始化', 'error');
                    }
                } else {
                    showTestResult('Supabase管理器未找到 - 这是正常的，系统可以使用模拟数据运行', 'info');
                }

                // 测试DOM操作
                totalTests++;
                try {
                    const testElement = document.createElement('div');
                    testElement.innerHTML = '<span>测试</span>';
                    if (testElement.querySelector('span')) {
                        showTestResult('DOM操作测试通过', 'success');
                        testsPassed++;
                    }
                } catch (domError) {
                    showTestResult(`DOM操作测试失败: ${domError.message}`, 'error');
                }

                // 总结
                showTestResult(`基本功能测试完成: ${testsPassed}/${totalTests} 通过`, testsPassed === totalTests ? 'success' : 'info');

            } catch (error) {
                showTestResult(`基本功能测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试模态框系统
        function testModalSystem() {
            // 创建一个简单的模态框测试
            const modalHTML = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; padding: 30px; border-radius: 12px; max-width: 400px; text-align: center;" onclick="event.stopPropagation()">
                        <h3>模态框测试</h3>
                        <p>这是一个测试模态框，点击外部区域关闭。</p>
                        <button onclick="this.closest('[style*=fixed]').remove()" style="background: #1e40af; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">关闭</button>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            showTestResult('模态框系统测试完成', 'success');
        }
        
        // 测试权限系统
        function testPermissionSystem() {
            if (!window.rolePermissionManager) {
                showTestResult('权限管理器未找到', 'error');
                return;
            }
            
            try {
                const roles = ['chief_editor', 'associate_editor', 'lead_author'];
                const permissions = ['project.view', 'chapter.create', 'review.approve'];
                
                let passedTests = 0;
                let totalTests = roles.length * permissions.length;
                
                roles.forEach(role => {
                    permissions.forEach(permission => {
                        const hasPermission = window.rolePermissionManager.hasPermission(permission, role);
                        if (typeof hasPermission === 'boolean') {
                            passedTests++;
                        }
                    });
                });
                
                showTestResult(`权限系统测试: ${passedTests}/${totalTests} 通过`, passedTests === totalTests ? 'success' : 'error');
                
            } catch (error) {
                showTestResult(`权限系统测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试通知系统
        function testNotificationSystem() {
            // 创建一个简单的通知测试
            const notificationHTML = `
                <div style="position: fixed; top: 20px; right: 20px; background: #059669; color: white; padding: 15px 20px; border-radius: 8px; z-index: 1000; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <div style="font-weight: 600; margin-bottom: 5px;">测试通知</div>
                    <div style="font-size: 14px;">通知系统工作正常！</div>
                </div>
            `;
            
            const notification = document.createElement('div');
            notification.innerHTML = notificationHTML;
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.remove();
            }, 3000);
            
            showTestResult('通知系统测试完成', 'success');
        }
        
        // 页面加载时执行检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkFixStatus();
                showSystemInfo();
                showTestResult('快速测试页面已加载', 'info');
            }, 500);
        });
    </script>
</body>
</html>
