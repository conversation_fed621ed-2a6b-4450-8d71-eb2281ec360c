// AI服务管理器
class AIServiceManager {
    constructor() {
        this.settings = this.loadSettings();
        this.initializeDefaults();
    }

    // 初始化默认设置
    initializeDefaults() {
        const defaults = {
            provider: 'openrouter',
            apiUrl: 'https://openrouter.ai/api/v1/chat/completions',
            apiKey: '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',
            model: 'deepseek/deepseek-chat-v3-0324:free',
            autoSaveInterval: 2,
            editorTheme: 'snow',
            enableNotifications: true,
            enableAutoBackup: true,
            enableAISuggestions: false
        };

        // 合并默认设置和已保存的设置
        this.settings = { ...defaults, ...this.settings };
        this.saveSettings();
    }

    // 加载设置
    loadSettings() {
        try {
            const saved = localStorage.getItem('ai-service-settings');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.error('加载设置失败:', error);
            return {};
        }
    }

    // 保存设置
    saveSettings() {
        try {
            localStorage.setItem('ai-service-settings', JSON.stringify(this.settings));
            return true;
        } catch (error) {
            console.error('保存设置失败:', error);
            return false;
        }
    }

    // 更新设置
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        return this.saveSettings();
    }

    // 获取设置
    getSettings() {
        return { ...this.settings };
    }

    // 测试AI连接
    async testConnection() {
        console.log('开始测试AI连接...');
        console.log('当前设置:', this.settings);

        try {
            const response = await this.makeAIRequest([
                {
                    role: 'user',
                    content: '请回复"连接测试成功"'
                }
            ], {
                max_tokens: 50,
                temperature: 0.1
            });

            console.log('AI响应:', response);

            if (response && response.content) {
                return {
                    success: true,
                    message: '连接测试成功',
                    response: response.content
                };
            } else {
                return {
                    success: false,
                    message: '连接测试失败：无效响应'
                };
            }
        } catch (error) {
            console.error('AI连接测试错误:', error);
            return {
                success: false,
                message: `连接测试失败: ${error.message}`
            };
        }
    }

    // 发送AI请求
    async makeAIRequest(messages, options = {}) {
        const { apiUrl, apiKey, model } = this.settings;
        
        if (!apiKey) {
            throw new Error('请先配置API密钥');
        }

        const requestBody = {
            model: model,
            messages: messages,
            max_tokens: options.max_tokens || 2000,
            temperature: options.temperature || 0.7,
            stream: false
        };

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'HTTP-Referer': window.location.origin,
                'X-Title': 'Book Writing Assistant'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.choices && data.choices.length > 0) {
            return {
                content: data.choices[0].message.content,
                usage: data.usage
            };
        } else {
            throw new Error('AI响应格式无效');
        }
    }

    // 生成书籍大纲
    async generateOutline(prompt, bookInfo = {}) {
        const systemPrompt = `你是一个专业的书籍大纲生成助手。请根据用户的要求生成详细的书籍大纲。

要求：
1. 大纲应该结构清晰，层次分明
2. 包含篇、章、节的层级结构
3. 每个章节都要有简短的描述
4. 返回JSON格式，结构如下：
{
  "title": "书籍标题",
  "description": "书籍简介",
  "outline": [
    {
      "level": 0,
      "title": "第一篇 篇名",
      "description": "篇的描述",
      "children": [
        {
          "level": 1,
          "title": "第一章 章名",
          "description": "章的描述"
        }
      ]
    }
  ]
}

请确保返回的是有效的JSON格式。`;

        const userPrompt = `请为以下书籍生成详细大纲：

${bookInfo.title ? `书籍标题：${bookInfo.title}` : ''}
${bookInfo.description ? `书籍简介：${bookInfo.description}` : ''}

用户要求：${prompt}

请生成包含3-5篇，每篇3-6章的详细大纲。`;

        try {
            const response = await this.makeAIRequest([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ], {
                max_tokens: 3000,
                temperature: 0.8
            });

            // 尝试解析JSON响应
            const content = response.content.trim();
            
            // 提取JSON部分（可能包含在代码块中）
            let jsonStr = content;
            const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*\})\s*```/);
            if (jsonMatch) {
                jsonStr = jsonMatch[1];
            }

            try {
                const outline = JSON.parse(jsonStr);
                return {
                    success: true,
                    outline: outline,
                    rawResponse: content
                };
            } catch (parseError) {
                // 如果JSON解析失败，返回原始响应让用户手动处理
                return {
                    success: false,
                    message: 'AI生成的大纲格式需要手动调整',
                    rawResponse: content,
                    parseError: parseError.message
                };
            }
        } catch (error) {
            return {
                success: false,
                message: `生成大纲失败: ${error.message}`,
                error: error
            };
        }
    }

    // 生成章节内容建议
    async generateChapterSuggestions(chapterTitle, chapterDescription, context = '') {
        const systemPrompt = `你是一个专业的写作助手。请根据章节标题和描述，生成写作建议和内容要点。`;

        const userPrompt = `章节标题：${chapterTitle}
章节描述：${chapterDescription}
${context ? `上下文信息：${context}` : ''}

请提供：
1. 章节写作要点
2. 建议的内容结构
3. 关键概念和术语
4. 可能的案例或示例`;

        try {
            const response = await this.makeAIRequest([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ], {
                max_tokens: 1500,
                temperature: 0.7
            });

            return {
                success: true,
                suggestions: response.content
            };
        } catch (error) {
            return {
                success: false,
                message: `生成建议失败: ${error.message}`
            };
        }
    }

    // 内容润色
    async polishContent(content, requirements = '') {
        const systemPrompt = `你是一位专业的文本编辑专家，擅长润色和优化文本内容。请帮助用户改进文本的表达、逻辑和可读性。`;

        const userPrompt = `请对以下内容进行润色优化：

原文内容：
${content}

${requirements ? `特殊要求：${requirements}` : ''}

请提供润色后的内容，要求：
1. 保持原意不变
2. 提升表达的准确性和流畅性
3. 优化句式结构
4. 增强可读性
5. 保持专业性和学术性`;

        try {
            const response = await this.makeAIRequest([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ], {
                max_tokens: 2000,
                temperature: 0.7
            });

            return {
                success: true,
                result: response.content
            };
        } catch (error) {
            return {
                success: false,
                message: `润色失败: ${error.message}`
            };
        }
    }

    // 翻译内容
    async translateContent(content, targetLanguage = '英文', requirements = '') {
        const systemPrompt = `你是一位专业的翻译专家，精通多种语言的翻译工作。请提供准确、流畅、符合目标语言习惯的翻译。`;

        const userPrompt = `请将以下内容翻译为${targetLanguage}：

原文内容：
${content}

${requirements ? `特殊要求：${requirements}` : ''}

请提供高质量的翻译，要求：
1. 准确传达原文含义
2. 符合目标语言的表达习惯
3. 保持专业术语的准确性
4. 语言流畅自然
5. 保持原文的语气和风格`;

        try {
            const response = await this.makeAIRequest([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ], {
                max_tokens: 2000,
                temperature: 0.5
            });

            return {
                success: true,
                result: response.content
            };
        } catch (error) {
            return {
                success: false,
                message: `翻译失败: ${error.message}`
            };
        }
    }

    // 解读内容
    async explainContent(content, requirements = '') {
        const systemPrompt = `你是一位知识渊博的专家，擅长解释复杂概念和内容。请用通俗易懂的语言为用户解读内容。`;

        const userPrompt = `请对以下内容进行详细解读：

内容：
${content}

${requirements ? `特殊要求：${requirements}` : ''}

请提供深入的解读，包括：
1. 核心概念和要点
2. 背景知识和上下文
3. 重要性和意义
4. 相关的例子或应用
5. 可能的疑问点解答`;

        try {
            const response = await this.makeAIRequest([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ], {
                max_tokens: 2500,
                temperature: 0.7
            });

            return {
                success: true,
                result: response.content
            };
        } catch (error) {
            return {
                success: false,
                message: `解读失败: ${error.message}`
            };
        }
    }

    // 重写内容
    async rewriteContent(content, style = '学术风格', requirements = '') {
        const systemPrompt = `你是一位专业的写作专家，擅长用不同风格重写内容。请根据要求重新组织和表达内容。`;

        const userPrompt = `请用${style}重写以下内容：

原文内容：
${content}

${requirements ? `特殊要求：${requirements}` : ''}

请提供重写后的内容，要求：
1. 保持核心信息和观点
2. 采用指定的写作风格
3. 重新组织结构和逻辑
4. 使用不同的表达方式
5. 提升内容的质量和吸引力`;

        try {
            const response = await this.makeAIRequest([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ], {
                max_tokens: 2000,
                temperature: 0.8
            });

            return {
                success: true,
                result: response.content
            };
        } catch (error) {
            return {
                success: false,
                message: `重写失败: ${error.message}`
            };
        }
    }

    // 处理多模态内容（文本+图片）
    async processMultimodalContent(content, files = [], action = 'analyze', requirements = '') {
        // 注意：这里需要根据实际的多模态API进行调整
        // 目前先处理文本内容，文件信息作为上下文

        let fileContext = '';
        if (files.length > 0) {
            fileContext = `\n\n附加文件信息：\n${files.map(f => `- ${f.name} (${f.type})`).join('\n')}`;
        }

        const systemPrompt = `你是一位多模态内容分析专家，能够处理文本、图片等多种类型的内容。`;

        const userPrompt = `请对以下内容进行${action}：

文本内容：
${content}${fileContext}

${requirements ? `特殊要求：${requirements}` : ''}

请提供详细的分析和处理结果。`;

        try {
            const response = await this.makeAIRequest([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ], {
                max_tokens: 2500,
                temperature: 0.7
            });

            return {
                success: true,
                result: response.content
            };
        } catch (error) {
            return {
                success: false,
                message: `处理失败: ${error.message}`
            };
        }
    }

    // 重置为默认设置
    resetToDefaults() {
        localStorage.removeItem('ai-service-settings');
        this.settings = {};
        this.initializeDefaults();
        return true;
    }
}

// 创建全局AI服务管理器实例
const aiServiceManager = new AIServiceManager();
