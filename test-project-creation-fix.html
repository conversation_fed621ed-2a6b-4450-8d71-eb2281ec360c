<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目创建修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .migration-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1>项目创建修复测试</h1>
        
        <div class="migration-info">
            <h4>⚠️ 重要提示</h4>
            <p>在测试之前，请确保已执行数据库迁移脚本：</p>
            <code>database-migration-add-type-field.sql</code>
            <p>该脚本会为projects表添加缺失的type字段。</p>
        </div>

        <!-- 连接状态测试 -->
        <div class="test-section">
            <h3>1. 数据库连接测试</h3>
            <button class="btn btn-primary" onclick="testConnection()">测试连接</button>
            <div id="connection-status"></div>
        </div>

        <!-- 用户登录测试 -->
        <div class="test-section">
            <h3>2. 用户登录状态</h3>
            <button class="btn btn-secondary" onclick="checkLoginStatus()">检查登录状态</button>
            <div id="login-status"></div>
        </div>

        <!-- 项目创建测试 -->
        <div class="test-section">
            <h3>3. 项目创建测试</h3>
            <div class="form-group">
                <label class="form-label">项目标题</label>
                <input type="text" id="test-project-title" class="form-input" value="测试项目 - 修复验证" placeholder="输入项目标题">
            </div>
            <div class="form-group">
                <label class="form-label">项目描述</label>
                <textarea id="test-project-description" class="form-textarea" placeholder="输入项目描述">这是一个用于验证type字段修复的测试项目</textarea>
            </div>
            <div class="form-group">
                <label class="form-label">项目类型</label>
                <select id="test-project-type" class="form-select">
                    <option value="book">书籍</option>
                    <option value="paper">论文</option>
                    <option value="report">报告</option>
                    <option value="other">其他</option>
                </select>
            </div>
            <button class="btn btn-success" onclick="testProjectCreation()">创建测试项目</button>
            <div id="creation-status"></div>
        </div>

        <!-- 数据库结构检查 -->
        <div class="test-section">
            <h3>4. 数据库结构检查</h3>
            <button class="btn btn-secondary" onclick="checkDatabaseSchema()">检查projects表结构</button>
            <div id="schema-status"></div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h3>5. 测试日志</h3>
            <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script src="supabase-config.js"></script>
    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('test-log');
            logElement.innerHTML = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            testLog = [];
            document.getElementById('test-log').innerHTML = '';
        }

        async function testConnection() {
            log('开始测试数据库连接...');
            try {
                if (!supabaseManager) {
                    throw new Error('supabaseManager未初始化');
                }
                
                // 尝试获取当前用户
                const user = supabaseManager.currentUser;
                if (user) {
                    showStatus('connection-status', '✅ 数据库连接正常，用户已登录', 'success');
                    log('数据库连接测试成功');
                } else {
                    showStatus('connection-status', '⚠️ 数据库连接正常，但用户未登录', 'info');
                    log('数据库连接正常，但需要用户登录');
                }
            } catch (error) {
                showStatus('connection-status', `❌ 连接失败: ${error.message}`, 'error');
                log(`数据库连接测试失败: ${error.message}`, 'error');
            }
        }

        async function checkLoginStatus() {
            log('检查用户登录状态...');
            try {
                const user = supabaseManager.currentUser;
                if (user) {
                    showStatus('login-status', `✅ 用户已登录: ${user.email}`, 'success');
                    log(`用户登录状态: ${user.email}`);
                } else {
                    showStatus('login-status', '❌ 用户未登录，请先登录', 'error');
                    log('用户未登录');
                }
            } catch (error) {
                showStatus('login-status', `❌ 检查登录状态失败: ${error.message}`, 'error');
                log(`检查登录状态失败: ${error.message}`, 'error');
            }
        }

        async function testProjectCreation() {
            log('开始测试项目创建...');
            
            const title = document.getElementById('test-project-title').value.trim();
            const description = document.getElementById('test-project-description').value.trim();
            const type = document.getElementById('test-project-type').value;

            if (!title) {
                showStatus('creation-status', '❌ 请输入项目标题', 'error');
                log('项目创建失败: 标题为空');
                return;
            }

            try {
                const projectData = {
                    title: title,
                    description: description,
                    type: type,
                    status: 'active'
                };

                log(`尝试创建项目: ${JSON.stringify(projectData)}`);
                
                const project = await supabaseManager.createProject(projectData);
                
                showStatus('creation-status', `✅ 项目创建成功! ID: ${project.id}`, 'success');
                log(`项目创建成功: ${project.id}`);
                
                // 添加时间戳到标题，避免重复
                const timestamp = new Date().getTime();
                document.getElementById('test-project-title').value = `测试项目 - 修复验证 ${timestamp}`;
                
            } catch (error) {
                showStatus('creation-status', `❌ 项目创建失败: ${error.message}`, 'error');
                log(`项目创建失败: ${error.message}`, 'error');
                
                // 如果是type字段相关错误，提供解决方案
                if (error.message.includes('type') || error.message.includes('column')) {
                    log('检测到type字段相关错误，请执行数据库迁移脚本', 'error');
                }
            }
        }

        async function checkDatabaseSchema() {
            log('检查数据库结构...');
            try {
                // 尝试查询projects表的结构信息
                const { data, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('*')
                    .limit(1);

                if (error) {
                    throw error;
                }

                showStatus('schema-status', '✅ projects表结构正常', 'success');
                log('数据库结构检查通过');
                
            } catch (error) {
                showStatus('schema-status', `❌ 数据库结构检查失败: ${error.message}`, 'error');
                log(`数据库结构检查失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动执行基础检查
        window.addEventListener('load', async () => {
            log('页面加载完成，开始自动检查...');
            await testConnection();
            await checkLoginStatus();
        });
    </script>
</body>
</html>
