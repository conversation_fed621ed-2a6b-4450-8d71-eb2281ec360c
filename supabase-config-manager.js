// Supabase配置管理器
// 专门用于管理Supabase配置的读取、保存、验证和数据库管理功能

class SupabaseConfigManager {
    constructor() {
        this.configKey = 'supabase-config';
        this.defaultConfig = {
            url: '',
            anonKey: '',
            serviceKey: '',
            region: 'auto'
        };
        this.config = this.loadConfig();
        this.isConnected = false;
        this.connectionStatus = 'disconnected';
    }

    // 加载配置
    loadConfig() {
        try {
            const saved = localStorage.getItem(this.configKey);
            if (saved) {
                const config = JSON.parse(saved);
                return { ...this.defaultConfig, ...config };
            }
            return { ...this.defaultConfig };
        } catch (error) {
            console.error('加载Supabase配置失败:', error);
            return { ...this.defaultConfig };
        }
    }

    // 保存配置
    saveConfig(config) {
        try {
            this.config = { ...this.config, ...config };
            localStorage.setItem(this.configKey, JSON.stringify(this.config));
            
            // 更新supabaseManager的配置
            if (window.supabaseManager) {
                window.supabaseManager.updateConfig(this.config);
            }
            
            return true;
        } catch (error) {
            console.error('保存Supabase配置失败:', error);
            return false;
        }
    }

    // 验证配置
    validateConfig(config = this.config) {
        const errors = [];

        if (!config.url) {
            errors.push('项目URL不能为空');
        } else if (!this.isValidUrl(config.url)) {
            errors.push('项目URL格式不正确');
        } else if (!this.isValidSupabaseUrl(config.url)) {
            errors.push('项目URL必须是有效的Supabase服务地址');
        }

        if (!config.anonKey) {
            errors.push('匿名密钥不能为空');
        } else if (!this.isValidJWT(config.anonKey)) {
            errors.push('匿名密钥格式不正确');
        }

        if (config.serviceKey && !this.isValidJWT(config.serviceKey)) {
            errors.push('服务密钥格式不正确');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // 验证URL格式
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    // 验证Supabase URL（支持私有化部署）
    isValidSupabaseUrl(url) {
        try {
            const urlObj = new URL(url);

            // 检查协议 - 优先使用HTTPS，但允许本地开发和私有化部署使用HTTP
            const isHttps = urlObj.protocol === 'https:';
            const isHttp = urlObj.protocol === 'http:';

            if (!isHttps && !isHttp) {
                return false;
            }

            const hostname = urlObj.hostname;

            // 官方Supabase域名必须使用HTTPS
            if (url.includes('supabase.co')) {
                return isHttps;
            }

            // 特殊处理localhost - 允许HTTP
            if (hostname === 'localhost') {
                return true;
            }

            // 检查是否为IP地址格式
            const isIpAddress = /^\d+\.\d+\.\d+\.\d+$/.test(hostname);
            if (isIpAddress) {
                // 本地开发环境允许HTTP
                const isLocalIp = hostname.startsWith('127.') || hostname.startsWith('192.168.') || hostname.startsWith('10.');
                return isLocalIp;
            }

            // 对于私有化部署域名，检查基本格式
            const domainParts = hostname.split('.');
            if (domainParts.length < 2) {
                return false;
            }

            // 检查是否包含有效的TLD
            const tld = domainParts[domainParts.length - 1];
            if (tld.length < 2) {
                return false;
            }

            // 私有化部署允许HTTP（但建议使用HTTPS）
            return true;

        } catch (error) {
            return false;
        }
    }

    // 验证JWT格式
    isValidJWT(token) {
        if (!token) return false;
        const parts = token.split('.');
        return parts.length === 3;
    }

    // 测试数据库连接
    async testConnection(config = this.config) {
        try {
            const validation = this.validateConfig(config);
            if (!validation.isValid) {
                throw new Error('配置验证失败: ' + validation.errors.join(', '));
            }

            // 创建临时客户端进行测试
            const { createClient } = supabase;
            const testClient = createClient(config.url, config.anonKey);

            // 首先尝试简单的健康检查
            try {
                const { data, error } = await testClient
                    .from('user_profiles')
                    .select('count', { count: 'exact', head: true });

                if (error && error.code !== 'PGRST116') { // PGRST116是表不存在的错误，这里可以忽略
                    throw error;
                }

                this.isConnected = true;
                this.connectionStatus = 'connected';

                return {
                    success: true,
                    message: '数据库连接成功',
                    details: {
                        url: config.url,
                        region: await this.detectRegion(config.url),
                        timestamp: new Date().toISOString(),
                        method: 'direct_query'
                    }
                };

            } catch (queryError) {
                // 如果直接查询失败，检查是否是CORS问题
                if (this.isCorsError(queryError)) {
                    return this.handleCorsError(config, queryError);
                }
                throw queryError;
            }

        } catch (error) {
            this.isConnected = false;
            this.connectionStatus = 'error';

            return {
                success: false,
                message: '数据库连接失败',
                error: error.message,
                details: {
                    url: config.url,
                    timestamp: new Date().toISOString(),
                    errorType: this.getErrorType(error)
                }
            };
        }
    }

    // 检查是否是CORS错误
    isCorsError(error) {
        const errorMessage = error.message || '';
        return errorMessage.includes('CORS') ||
               errorMessage.includes('Access-Control-Allow-Origin') ||
               errorMessage.includes('Failed to fetch') ||
               errorMessage.includes('ERR_FAILED');
    }

    // 处理CORS错误
    async handleCorsError(config, originalError) {
        return {
            success: false,
            message: 'CORS配置问题',
            error: '私有化Supabase部署需要配置CORS策略',
            details: {
                url: config.url,
                timestamp: new Date().toISOString(),
                errorType: 'CORS',
                originalError: originalError.message,
                solutions: [
                    '在Supabase配置中添加允许的域名',
                    '配置Access-Control-Allow-Origin头',
                    '检查Supabase服务器的CORS设置',
                    '确保API端点正确配置'
                ],
                corsConfig: {
                    requiredHeaders: [
                        'Access-Control-Allow-Origin: *',
                        'Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers: Content-Type, Authorization, apikey'
                    ]
                }
            }
        };
    }

    // 获取错误类型
    getErrorType(error) {
        const errorMessage = error.message || '';

        if (this.isCorsError(error)) {
            return 'CORS';
        } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
            return 'NETWORK';
        } else if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
            return 'AUTH';
        } else if (errorMessage.includes('404') || errorMessage.includes('not found')) {
            return 'NOT_FOUND';
        } else {
            return 'UNKNOWN';
        }
    }

    // 检测数据库区域
    async detectRegion(url) {
        try {
            const hostname = new URL(url).hostname;
            const parts = hostname.split('.');
            if (parts.length >= 3) {
                const projectRef = parts[0];
                // 这里可以根据项目引用推断区域，或者通过API查询
                return 'auto-detected';
            }
            return 'unknown';
        } catch (error) {
            return 'unknown';
        }
    }

    // 获取数据库信息
    async getDatabaseInfo() {
        if (!this.isConnected) {
            throw new Error('数据库未连接');
        }

        try {
            const { createClient } = supabase;
            const client = createClient(this.config.url, this.config.anonKey);
            
            // 获取数据库统计信息
            const stats = await this.getDatabaseStats(client);
            const tables = await this.getTableInfo(client);
            
            return {
                success: true,
                data: {
                    connection: {
                        url: this.config.url,
                        region: this.config.region,
                        status: this.connectionStatus
                    },
                    statistics: stats,
                    tables: tables,
                    timestamp: new Date().toISOString()
                }
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 获取数据库统计信息
    async getDatabaseStats(client) {
        try {
            const stats = {};
            
            // 获取主要表的记录数
            const tables = ['projects', 'user_profiles', 'chapters', 'outlines'];
            
            for (const table of tables) {
                try {
                    const { count, error } = await client
                        .from(table)
                        .select('*', { count: 'exact', head: true });
                    
                    if (!error) {
                        stats[table] = count || 0;
                    }
                } catch (e) {
                    stats[table] = 'N/A';
                }
            }
            
            return stats;
        } catch (error) {
            console.error('获取数据库统计失败:', error);
            return {};
        }
    }

    // 获取表信息
    async getTableInfo(client) {
        try {
            // 这里可以查询information_schema来获取表结构信息
            // 由于RLS限制，可能需要使用服务密钥
            return {
                total_tables: 'N/A',
                last_updated: new Date().toISOString()
            };
        } catch (error) {
            console.error('获取表信息失败:', error);
            return {};
        }
    }

    // 执行数据库迁移
    async executeMigration(migrationScript) {
        if (!this.config.serviceKey) {
            throw new Error('执行迁移需要服务密钥');
        }

        try {
            const { createClient } = supabase;
            const serviceClient = createClient(this.config.url, this.config.serviceKey);
            
            // 这里需要实现SQL脚本执行逻辑
            // 注意：Supabase客户端库不直接支持执行任意SQL
            // 需要通过REST API或者RPC函数来实现
            
            return {
                success: true,
                message: '迁移执行成功',
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 创建数据库备份
    async createBackup() {
        try {
            // 这里实现备份逻辑
            // 可以导出主要表的数据
            const backupData = await this.exportTableData();
            
            const backup = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                data: backupData
            };
            
            // 下载备份文件
            this.downloadBackup(backup);
            
            return {
                success: true,
                message: '备份创建成功',
                timestamp: backup.timestamp
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 导出表数据
    async exportTableData() {
        const { createClient } = supabase;
        const client = createClient(this.config.url, this.config.anonKey);
        
        const tables = ['projects', 'outlines', 'chapters', 'references'];
        const exportData = {};
        
        for (const table of tables) {
            try {
                const { data, error } = await client
                    .from(table)
                    .select('*');
                
                if (!error) {
                    exportData[table] = data;
                }
            } catch (e) {
                console.warn(`导出表 ${table} 失败:`, e);
            }
        }
        
        return exportData;
    }

    // 下载备份文件
    downloadBackup(backup) {
        const dataStr = JSON.stringify(backup, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `supabase-backup-${backup.timestamp.replace(/[:.]/g, '-')}.json`;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    // 重置配置
    resetConfig() {
        this.config = { ...this.defaultConfig };
        localStorage.removeItem(this.configKey);
        this.isConnected = false;
        this.connectionStatus = 'disconnected';
    }

    // 获取当前配置
    getCurrentConfig() {
        return { ...this.config };
    }

    // 获取连接状态
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            status: this.connectionStatus,
            config: {
                url: this.config.url,
                hasAnonKey: !!this.config.anonKey,
                hasServiceKey: !!this.config.serviceKey,
                region: this.config.region
            }
        };
    }
}

// 创建全局实例
window.supabaseConfigManager = new SupabaseConfigManager();
