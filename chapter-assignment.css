/* 章节分配管理系统样式 */
/* 专业学术风格设计 */

:root {
    /* 主色调 - 学术蓝 */
    --primary-color: #1e40af;
    --primary-light: #3b82f6;
    --primary-dark: #1e3a8a;
    
    /* 辅助色 */
    --secondary-color: #64748b;
    --accent-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    
    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 语义色 */
    --success-color: #059669;
    --info-color: #0ea5e9;
    --warning-color: #d97706;
    --error-color: #dc2626;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: 14px;
    line-height: 1.5;
    color: var(--gray-700);
    background-color: var(--gray-50);
    overflow-x: hidden;
}

/* 顶部导航栏 */
.header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-xl);
    height: 64px;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.system-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 13px;
    color: var(--gray-500);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.project-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gray-100);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 13px;
    color: var(--gray-700);
}

.project-selector:hover {
    background: var(--gray-200);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 64px);
    max-width: 1400px;
    margin: 0 auto;
}

/* 侧边栏 */
.sidebar {
    width: 260px;
    background: white;
    border-right: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.nav-section h3 {
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-md);
}

.nav-section ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.nav-item a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-size: 13px;
    transition: all 0.2s ease;
}

.nav-item a:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.nav-item.active a {
    background: var(--primary-color);
    color: white;
}

.nav-item i {
    width: 16px;
    text-align: center;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.content-panel {
    display: none;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.content-panel.active {
    display: block;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.panel-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
}

.panel-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

/* 搜索框 */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: var(--spacing-sm);
    color: var(--gray-400);
    font-size: 12px;
}

.search-box input {
    padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) var(--spacing-xl);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 13px;
    width: 200px;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 选择框 */
select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 13px;
    background: white;
    cursor: pointer;
}

select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.stat-content h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    font-size: 13px;
    color: var(--gray-500);
}

/* 图表容器 */
.chart-container {
    margin: var(--spacing-xl);
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.chart-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
}

.chart-content {
    padding: var(--spacing-lg);
    height: 300px;
}

/* 最近活动 */
.recent-activity {
    margin: var(--spacing-xl);
}

.recent-activity h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
}

.activity-icon {
    width: 32px;
    height: 32px;
    background: var(--gray-100);
    color: var(--gray-600);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-xs);
}

.activity-meta {
    font-size: 12px;
    color: var(--gray-500);
}

/* 分配列表 */
.assignments-container {
    margin: var(--spacing-xl);
}

.assignments-header {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1fr 1fr 1fr 1fr 1.2fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.assignments-list {
    display: flex;
    flex-direction: column;
}

.assignment-item {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1fr 1fr 1fr 1fr 1.2fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    align-items: center;
    transition: background-color 0.2s ease;
}

.assignment-item:hover {
    background: var(--gray-50);
}

.assignment-title {
    font-weight: 500;
    color: var(--gray-800);
}

.assignment-subtitle {
    font-size: 12px;
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

.user-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--gray-100);
    border-radius: var(--radius-sm);
    font-size: 11px;
    color: var(--gray-600);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-in-progress {
    background: #dbeafe;
    color: #1e40af;
}

.status-reviewing {
    background: #fce7f3;
    color: #be185d;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.due-date {
    font-size: 12px;
    color: var(--gray-600);
}

.due-date.overdue {
    color: var(--danger-color);
    font-weight: 500;
}

.actions {
    display: flex;
    gap: var(--spacing-xs);
}

.action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: var(--gray-100);
    color: var(--gray-600);
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--gray-400);
    cursor: pointer;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-help {
    font-size: 12px;
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    max-width: 400px;
    z-index: 1100;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-800);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    font-size: 16px;
}

.notification-message {
    font-size: 13px;
    color: var(--gray-600);
}

/* 时间线视图样式 */
.timeline-container {
    padding: var(--spacing-lg);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.timeline-legend {
    display: flex;
    gap: var(--spacing-lg);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--gray-600);
}

.legend-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.legend-dot.status-pending {
    background: #fbbf24;
}

.legend-dot.status-in-progress {
    background: #3b82f6;
}

.legend-dot.status-completed {
    background: #10b981;
}

.timeline {
    position: relative;
    padding-left: var(--spacing-lg);
}

.timeline::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gray-200);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-lg);
    padding-left: var(--spacing-lg);
}

.timeline-marker {
    position: absolute;
    left: -8px;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px var(--gray-200);
}

.timeline-marker.status-pending {
    background: #fbbf24;
}

.timeline-marker.status-in-progress {
    background: #3b82f6;
}

.timeline-marker.status-completed {
    background: #10b981;
}

.timeline-content {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.timeline-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.timeline-meta {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: 12px;
    color: var(--gray-500);
}

.timeline-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 看板视图样式 */
.kanban-container {
    padding: var(--spacing-lg);
}

.kanban-header {
    margin-bottom: var(--spacing-lg);
}

.kanban-board {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.kanban-column {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
}

.kanban-column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--gray-200);
}

.kanban-column-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
}

.kanban-column-header .count {
    background: var(--primary-color);
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
}

.kanban-cards {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.kanban-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.kanban-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.kanban-card.overdue {
    border-left: 4px solid var(--danger-color);
}

.card-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 11px;
    color: var(--gray-500);
}

.card-author,
.card-due-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.card-due-date.overdue {
    color: var(--danger-color);
    font-weight: 500;
}

.card-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-progress .progress-bar {
    flex: 1;
    height: 4px;
}

.card-progress span {
    font-size: 10px;
    color: var(--gray-500);
}

/* 甘特图样式 */
.gantt-container {
    padding: var(--spacing-lg);
}

.gantt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.gantt-chart {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.gantt-timeline {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.gantt-timeline-header {
    display: flex;
    padding-left: 200px;
}

.gantt-date {
    width: 30px;
    padding: var(--spacing-sm) 0;
    text-align: center;
    font-size: 11px;
    color: var(--gray-600);
    border-right: 1px solid var(--gray-200);
}

.gantt-date.today {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.gantt-tasks {
    background: white;
}

.gantt-task {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--gray-100);
    position: relative;
}

.gantt-task-label {
    width: 200px;
    padding: 0 var(--spacing-md);
    font-size: 12px;
    color: var(--gray-700);
    border-right: 1px solid var(--gray-200);
}

.gantt-task-bar {
    position: relative;
    height: 20px;
    background: var(--primary-color);
    border-radius: 10px;
    margin: 0 var(--spacing-xs);
}

.gantt-task-progress {
    height: 100%;
    background: var(--primary-dark);
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* 审核列表样式 */
.reviews-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

.review-item {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.review-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.review-meta {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    font-size: 12px;
    color: var(--gray-500);
}

.review-comments {
    background: var(--gray-50);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    font-size: 13px;
    color: var(--gray-700);
}

.review-scores {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.score {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 500;
}

.review-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .sidebar {
        width: 220px;
    }

    .assignments-header,
    .assignment-item {
        grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    }

    .assignments-header .header-cell:nth-child(3),
    .assignment-item > div:nth-child(3),
    .assignments-header .header-cell:nth-child(6),
    .assignment-item > div:nth-child(6) {
        display: none;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--gray-200);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .assignments-header,
    .assignment-item {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .assignments-header {
        display: none;
    }

    .assignment-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .modal {
        width: 95%;
        margin: var(--spacing-md);
    }

    .notification {
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }
}

/* 编辑分配模态对话框样式 */
.assignment-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.assignment-modal.active {
    opacity: 1;
    visibility: visible;
}

.assignment-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.assignment-modal.active .assignment-modal-content {
    transform: translateY(0);
}

.assignment-modal-header {
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: 24px;
}

.assignment-modal-title {
    margin: 0 0 16px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: 12px;
}

.assignment-modal-title i {
    color: var(--primary-color);
}

.assignment-modal-body {
    padding: 0 24px 24px 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--gray-700);
    font-size: 14px;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background: white;
    box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.user-selector {
    position: relative;
}

.user-selector-input {
    cursor: pointer;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

.user-dropdown.active {
    display: block;
}

.user-option {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-option:hover {
    background: var(--gray-50);
}

.user-option.selected {
    background: var(--blue-50);
    color: var(--blue-600);
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--gray-600);
    font-weight: 500;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 500;
    color: var(--gray-800);
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: var(--gray-500);
}

.assignment-modal-footer {
    padding: 24px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 状态样式更新 */
.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-writing {
    background: #dbeafe;
    color: #1e40af;
}

.status-reviewing {
    background: #fce7f3;
    color: #be185d;
}

.status-completed {
    background: #d1fae5;
    color: #065f46;
}
