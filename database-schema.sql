-- 《大模型技术与油气应用概论》多用户协作系统数据库设计
-- 基于 Supabase PostgreSQL

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户配置表（扩展 auth.users）
CREATE TABLE public.user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    institution VARCHAR(100), -- 所属机构
    department VARCHAR(100),  -- 部门
    global_role VARCHAR(20) DEFAULT 'author' CHECK (global_role IN ('system_admin', 'user')), -- 系统级角色
    bio TEXT,
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 项目表
CREATE TABLE public.projects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES public.user_profiles(id) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived', 'suspended')),
    visibility VARCHAR(20) DEFAULT 'private' CHECK (visibility IN ('public', 'private', 'restricted')),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 项目成员表
CREATE TABLE public.project_members (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'author' CHECK (role IN ('owner', 'admin', 'editor', 'author', 'reviewer')),
    permissions JSONB DEFAULT '{"read": true, "write": false, "admin": false}',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    invited_by UUID REFERENCES public.user_profiles(id),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'invited')),
    invitation_token VARCHAR(255), -- 邀请令牌
    invitation_expires_at TIMESTAMP WITH TIME ZONE, -- 邀请过期时间
    UNIQUE(project_id, user_id)
);

-- 大纲结构表
CREATE TABLE public.outlines (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.outlines(id) ON DELETE CASCADE,
    title VARCHAR(300) NOT NULL,
    level INTEGER NOT NULL DEFAULT 0,
    sort_order INTEGER NOT NULL DEFAULT 0,
    description TEXT,
    status VARCHAR(20) DEFAULT 'planned' CHECK (status IN ('planned', 'in_progress', 'completed', 'reviewed')),
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 章节内容表
CREATE TABLE public.chapters (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    outline_id UUID REFERENCES public.outlines(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    title VARCHAR(300) NOT NULL,
    summary TEXT,
    content JSONB, -- Quill Delta 格式
    word_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'writing', 'review', 'approved', 'published')),
    version INTEGER DEFAULT 1,
    created_by UUID REFERENCES public.user_profiles(id),
    last_edited_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 章节分配表
CREATE TABLE public.chapter_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('author', 'co_author', 'reviewer', 'editor')),
    assigned_by UUID REFERENCES public.user_profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deadline TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'assigned' CHECK (status IN ('assigned', 'accepted', 'in_progress', 'completed', 'reviewed')),
    notes TEXT,
    UNIQUE(chapter_id, user_id, role)
);

-- 章节权限表
CREATE TABLE public.chapter_permissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    permission_type VARCHAR(20) NOT NULL CHECK (permission_type IN ('owner', 'editor', 'reviewer', 'reader')),
    granted_by UUID REFERENCES public.user_profiles(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(chapter_id, user_id)
);

-- 参考文献表
CREATE TABLE public.references (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    authors TEXT NOT NULL,
    source VARCHAR(300),
    publication_year INTEGER,
    url TEXT,
    doi VARCHAR(100),
    citation_style VARCHAR(20) DEFAULT 'apa',
    tags TEXT[],
    notes TEXT,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 审核流程表
CREATE TABLE public.review_processes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_review', 'approved', 'rejected', 'revision_required')),
    review_notes TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 评论系统表
CREATE TABLE public.comments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE, -- 支持回复
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position_start INTEGER, -- 评论位置（字符开始位置）
    position_end INTEGER,   -- 评论位置（字符结束位置）
    type VARCHAR(20) DEFAULT 'general' CHECK (type IN ('general', 'suggestion', 'correction', 'question')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 图表数据表
CREATE TABLE public.diagrams (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(50) NOT NULL, -- mindmap, flowchart, architecture, etc.
    content TEXT NOT NULL, -- Mermaid code
    description TEXT,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 评论表
CREATE TABLE public.comments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position JSONB, -- 评论在文档中的位置
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'deleted')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 版本历史表
CREATE TABLE public.chapter_versions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    title VARCHAR(300) NOT NULL,
    content JSONB NOT NULL,
    summary TEXT,
    changes_description TEXT,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 活动日志表
CREATE TABLE public.activity_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL,
    target_type VARCHAR(50), -- chapter, outline, reference, etc.
    target_id UUID,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户邀请表
CREATE TABLE public.user_invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    invited_by UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'editor', 'author', 'reviewer')),
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 通知表
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) NOT NULL, -- assignment, comment, review, etc.
    related_id UUID,
    data JSONB, -- 额外数据
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_projects_owner ON public.projects(owner_id);
CREATE INDEX idx_project_members_project ON public.project_members(project_id);
CREATE INDEX idx_project_members_user ON public.project_members(user_id);
CREATE INDEX idx_outlines_project ON public.outlines(project_id);
CREATE INDEX idx_outlines_parent ON public.outlines(parent_id);
CREATE INDEX idx_chapters_outline ON public.chapters(outline_id);
CREATE INDEX idx_chapters_project ON public.chapters(project_id);
CREATE INDEX idx_chapter_assignments_chapter ON public.chapter_assignments(chapter_id);
CREATE INDEX idx_chapter_assignments_user ON public.chapter_assignments(user_id);
CREATE INDEX idx_chapter_permissions_chapter ON public.chapter_permissions(chapter_id);
CREATE INDEX idx_chapter_permissions_user ON public.chapter_permissions(user_id);
CREATE INDEX idx_references_project ON public.references(project_id);
CREATE INDEX idx_diagrams_project ON public.diagrams(project_id);
CREATE INDEX idx_diagrams_chapter ON public.diagrams(chapter_id);
CREATE INDEX idx_review_processes_chapter ON public.review_processes(chapter_id);
CREATE INDEX idx_review_processes_reviewer ON public.review_processes(reviewer_id);
CREATE INDEX idx_comments_chapter ON public.comments(chapter_id);
CREATE INDEX idx_user_invitations_email ON public.user_invitations(email);
CREATE INDEX idx_user_invitations_token ON public.user_invitations(invitation_token);
CREATE INDEX idx_activity_logs_project ON public.activity_logs(project_id);
CREATE INDEX idx_activity_logs_user ON public.activity_logs(user_id);
CREATE INDEX idx_notifications_user ON public.notifications(user_id);

-- 行级安全策略 (RLS)
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.outlines ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.references ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.diagrams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_processes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- 用户配置表策略
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- 项目表策略
CREATE POLICY "Users can view projects they are members of" ON public.projects
    FOR SELECT USING (
        owner_id = auth.uid() OR
        id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Project owners can update projects" ON public.projects
    FOR UPDATE USING (owner_id = auth.uid());

-- 项目成员表策略
CREATE POLICY "Users can view project members for their projects" ON public.project_members
    FOR SELECT USING (
        project_id IN (
            SELECT id FROM public.projects 
            WHERE owner_id = auth.uid()
        ) OR
        user_id = auth.uid()
    );

-- 章节表策略
CREATE POLICY "Users can view chapters they have access to" ON public.chapters
    FOR SELECT USING (
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid()
        ) OR
        id IN (
            SELECT chapter_id FROM public.chapter_permissions 
            WHERE user_id = auth.uid()
        )
    );

-- 触发器函数：更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建更新时间戳触发器
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON public.projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_outlines_updated_at BEFORE UPDATE ON public.outlines FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chapters_updated_at BEFORE UPDATE ON public.chapters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_references_updated_at BEFORE UPDATE ON public.references FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_diagrams_updated_at BEFORE UPDATE ON public.diagrams FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON public.comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 触发器函数：自动创建用户配置
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, username, full_name, email)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        NEW.email
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建用户注册触发器
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ============================================================================
-- AI功能相关表结构
-- ============================================================================

-- AI任务表
CREATE TABLE public.ai_tasks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    task_type VARCHAR(50) NOT NULL CHECK (task_type IN ('writing', 'chart', 'reference', 'polish', 'expand')),
    input_data JSONB NOT NULL,
    output_data JSONB,
    model_used VARCHAR(100),
    tokens_used INTEGER DEFAULT 0,
    cost_usd DECIMAL(10,6) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    error_message TEXT,
    processing_time INTEGER, -- 处理时间（毫秒）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- AI生成内容表
CREATE TABLE public.ai_generated_content (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    task_id UUID REFERENCES public.ai_tasks(id) ON DELETE CASCADE,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('text', 'chart', 'reference', 'outline')),
    original_content TEXT,
    generated_content TEXT NOT NULL,
    confidence_score DECIMAL(3,2), -- AI生成内容的置信度
    is_accepted BOOLEAN DEFAULT FALSE,
    is_modified BOOLEAN DEFAULT FALSE,
    accepted_by UUID REFERENCES public.user_profiles(id),
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 图表库表
CREATE TABLE public.chart_library (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    chart_type VARCHAR(50) NOT NULL CHECK (chart_type IN ('flowchart', 'sequence', 'class', 'er', 'mindmap', 'architecture')),
    chart_data JSONB NOT NULL, -- Mermaid代码和配置
    preview_url TEXT,
    tags TEXT[],
    is_template BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI模型配置表
CREATE TABLE public.ai_model_configs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    model_type VARCHAR(50) NOT NULL CHECK (model_type IN ('chat', 'completion', 'embedding')),
    provider VARCHAR(50) NOT NULL,
    api_endpoint TEXT,
    max_tokens INTEGER DEFAULT 4000,
    temperature DECIMAL(3,2) DEFAULT 0.7,
    cost_per_1k_input DECIMAL(8,6),
    cost_per_1k_output DECIMAL(8,6),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI使用统计表
CREATE TABLE public.ai_usage_stats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    request_count INTEGER DEFAULT 0,
    token_count INTEGER DEFAULT 0,
    cost_usd DECIMAL(10,6) DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    UNIQUE(user_id, project_id, date, task_type)
);

-- 文献检索缓存表
CREATE TABLE public.reference_cache (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    search_query TEXT NOT NULL,
    search_hash VARCHAR(64) NOT NULL UNIQUE,
    results JSONB NOT NULL,
    source VARCHAR(50) NOT NULL, -- crossref, semantic_scholar, etc.
    total_results INTEGER,
    cached_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- 智能提示词模板表
CREATE TABLE public.prompt_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL CHECK (category IN ('writing', 'polishing', 'expansion', 'chart', 'analysis')),
    template TEXT NOT NULL,
    variables JSONB, -- 模板变量定义
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    usage_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 内容质量评估表
CREATE TABLE public.content_quality_scores (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    content_hash VARCHAR(64) NOT NULL,
    readability_score DECIMAL(5,2),
    academic_score DECIMAL(5,2),
    coherence_score DECIMAL(5,2),
    completeness_score DECIMAL(5,2),
    overall_score DECIMAL(5,2),
    suggestions JSONB,
    evaluated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 协作冲突记录表
CREATE TABLE public.collaboration_conflicts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    user1_id UUID REFERENCES public.user_profiles(id),
    user2_id UUID REFERENCES public.user_profiles(id),
    conflict_type VARCHAR(50) NOT NULL CHECK (conflict_type IN ('concurrent_edit', 'version_conflict', 'permission_conflict')),
    conflict_data JSONB NOT NULL,
    resolution_status VARCHAR(20) DEFAULT 'pending' CHECK (resolution_status IN ('pending', 'resolved', 'escalated')),
    resolved_by UUID REFERENCES public.user_profiles(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 系统配置表
CREATE TABLE public.system_configs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by UUID REFERENCES public.user_profiles(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 索引创建
-- ============================================================================

-- AI任务相关索引
CREATE INDEX idx_ai_tasks_user ON public.ai_tasks(user_id);
CREATE INDEX idx_ai_tasks_project ON public.ai_tasks(project_id);
CREATE INDEX idx_ai_tasks_type_status ON public.ai_tasks(task_type, status);
CREATE INDEX idx_ai_tasks_created_at ON public.ai_tasks(created_at);

-- AI生成内容索引
CREATE INDEX idx_ai_content_task ON public.ai_generated_content(task_id);
CREATE INDEX idx_ai_content_chapter ON public.ai_generated_content(chapter_id);
CREATE INDEX idx_ai_content_type ON public.ai_generated_content(content_type);
CREATE INDEX idx_ai_content_accepted ON public.ai_generated_content(is_accepted);

-- 图表库索引
CREATE INDEX idx_chart_library_project ON public.chart_library(project_id);
CREATE INDEX idx_chart_library_type ON public.chart_library(chart_type);
CREATE INDEX idx_chart_library_tags ON public.chart_library USING GIN(tags);
CREATE INDEX idx_chart_library_template ON public.chart_library(is_template);

-- 使用统计索引
CREATE INDEX idx_usage_stats_user_date ON public.ai_usage_stats(user_id, date);
CREATE INDEX idx_usage_stats_project_date ON public.ai_usage_stats(project_id, date);

-- 文献缓存索引
CREATE INDEX idx_reference_cache_hash ON public.reference_cache(search_hash);
CREATE INDEX idx_reference_cache_expires ON public.reference_cache(expires_at);

-- 质量评估索引
CREATE INDEX idx_quality_scores_chapter ON public.content_quality_scores(chapter_id);
CREATE INDEX idx_quality_scores_hash ON public.content_quality_scores(content_hash);

-- 冲突记录索引
CREATE INDEX idx_conflicts_chapter ON public.collaboration_conflicts(chapter_id);
CREATE INDEX idx_conflicts_users ON public.collaboration_conflicts(user1_id, user2_id);
CREATE INDEX idx_conflicts_status ON public.collaboration_conflicts(resolution_status);

-- ============================================================================
-- 扩展RLS策略
-- ============================================================================

-- AI任务表策略
ALTER TABLE public.ai_tasks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own AI tasks" ON public.ai_tasks
    FOR SELECT USING (
        user_id = auth.uid() OR
        project_id IN (
            SELECT project_id FROM public.project_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create AI tasks for their projects" ON public.ai_tasks
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        project_id IN (
            SELECT project_id FROM public.project_members
            WHERE user_id = auth.uid()
        )
    );

-- AI生成内容表策略
ALTER TABLE public.ai_generated_content ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view AI content for accessible chapters" ON public.ai_generated_content
    FOR SELECT USING (
        chapter_id IN (
            SELECT id FROM public.chapters
            WHERE project_id IN (
                SELECT project_id FROM public.project_members
                WHERE user_id = auth.uid()
            )
        )
    );

-- 图表库表策略
ALTER TABLE public.chart_library ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view charts for their projects" ON public.chart_library
    FOR SELECT USING (
        project_id IN (
            SELECT project_id FROM public.project_members
            WHERE user_id = auth.uid()
        ) OR is_template = true
    );

CREATE POLICY "Users can create charts for their projects" ON public.chart_library
    FOR INSERT WITH CHECK (
        created_by = auth.uid() AND
        project_id IN (
            SELECT project_id FROM public.project_members
            WHERE user_id = auth.uid()
        )
    );

-- AI使用统计表策略
ALTER TABLE public.ai_usage_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own usage stats" ON public.ai_usage_stats
    FOR SELECT USING (user_id = auth.uid());

-- 提示词模板表策略
ALTER TABLE public.prompt_templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view public and own templates" ON public.prompt_templates
    FOR SELECT USING (is_system = true OR created_by = auth.uid());

CREATE POLICY "Users can create their own templates" ON public.prompt_templates
    FOR INSERT WITH CHECK (created_by = auth.uid());

-- ============================================================================
-- 触发器函数
-- ============================================================================

-- 更新AI使用统计
CREATE OR REPLACE FUNCTION update_ai_usage_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        INSERT INTO public.ai_usage_stats (
            user_id, project_id, date, task_type,
            request_count, token_count, cost_usd, success_count
        ) VALUES (
            NEW.user_id, NEW.project_id, CURRENT_DATE, NEW.task_type,
            1, NEW.tokens_used, NEW.cost_usd, 1
        )
        ON CONFLICT (user_id, project_id, date, task_type)
        DO UPDATE SET
            request_count = ai_usage_stats.request_count + 1,
            token_count = ai_usage_stats.token_count + NEW.tokens_used,
            cost_usd = ai_usage_stats.cost_usd + NEW.cost_usd,
            success_count = ai_usage_stats.success_count + 1;
    ELSIF NEW.status = 'failed' AND OLD.status != 'failed' THEN
        INSERT INTO public.ai_usage_stats (
            user_id, project_id, date, task_type,
            request_count, error_count
        ) VALUES (
            NEW.user_id, NEW.project_id, CURRENT_DATE, NEW.task_type,
            1, 1
        )
        ON CONFLICT (user_id, project_id, date, task_type)
        DO UPDATE SET
            request_count = ai_usage_stats.request_count + 1,
            error_count = ai_usage_stats.error_count + 1;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建AI任务统计触发器
CREATE TRIGGER ai_task_stats_trigger
    AFTER UPDATE ON public.ai_tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_usage_stats();

-- 自动清理过期缓存
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS void AS $$
BEGIN
    DELETE FROM public.reference_cache
    WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- 创建定时清理任务（需要pg_cron扩展）
-- SELECT cron.schedule('cleanup-cache', '0 2 * * *', 'SELECT cleanup_expired_cache();');

-- 章节内容变更记录
CREATE OR REPLACE FUNCTION log_chapter_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- 记录活动日志
    INSERT INTO public.activity_logs (
        project_id, user_id, action, target_type, target_id, details
    ) VALUES (
        NEW.project_id,
        NEW.last_edited_by,
        'update_chapter',
        'chapter',
        NEW.id,
        jsonb_build_object(
            'old_version', OLD.version,
            'new_version', NEW.version,
            'word_count', NEW.word_count
        )
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建章节变更触发器
CREATE TRIGGER chapter_change_log_trigger
    AFTER UPDATE ON public.chapters
    FOR EACH ROW
    WHEN (OLD.content IS DISTINCT FROM NEW.content)
    EXECUTE FUNCTION log_chapter_changes();

-- 自动更新字数统计
CREATE OR REPLACE FUNCTION update_word_count()
RETURNS TRIGGER AS $$
BEGIN
    -- 简单的字数统计（可以根据需要优化）
    NEW.word_count = LENGTH(REGEXP_REPLACE(
        COALESCE(NEW.content->>'ops', ''),
        '[^\u4e00-\u9fa5a-zA-Z0-9]',
        '',
        'g'
    ));

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建字数统计触发器
CREATE TRIGGER word_count_trigger
    BEFORE INSERT OR UPDATE ON public.chapters
    FOR EACH ROW
    WHEN (NEW.content IS NOT NULL)
    EXECUTE FUNCTION update_word_count();
