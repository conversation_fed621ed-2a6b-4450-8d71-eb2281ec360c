-- 初始化数据脚本
-- 用于AI增强学术专著编写系统

-- ============================================================================
-- AI模型配置初始化
-- ============================================================================

INSERT INTO public.ai_model_configs (
    model_name, model_type, provider, api_endpoint, 
    max_tokens, temperature, cost_per_1k_input, cost_per_1k_output, is_active
) VALUES 
-- DeepSeek模型配置
('deepseek/deepseek-chat', 'chat', 'openrouter', 'https://openrouter.ai/api/v1', 4000, 0.7, 0.00014, 0.00028, true),
('deepseek/deepseek-coder', 'chat', 'openrouter', 'https://openrouter.ai/api/v1', 4000, 0.2, 0.00014, 0.00028, true),
-- 备用模型
('anthropic/claude-3-haiku', 'chat', 'openrouter', 'https://openrouter.ai/api/v1', 4000, 0.7, 0.00025, 0.00125, false),
('openai/gpt-3.5-turbo', 'chat', 'openrouter', 'https://openrouter.ai/api/v1', 4000, 0.7, 0.0005, 0.0015, false);

-- ============================================================================
-- 系统配置初始化
-- ============================================================================

INSERT INTO public.system_configs (config_key, config_value, description, is_public) VALUES 
-- AI服务配置
('ai_service_enabled', 'true', 'AI服务总开关', true),
('ai_daily_budget_usd', '50', '每日AI使用预算（美元）', false),
('ai_rate_limit_per_minute', '60', '每分钟AI请求限制', false),
('ai_max_concurrent_requests', '5', '最大并发AI请求数', false),

-- 内容配置
('max_chapter_length', '50000', '章节最大字符数', true),
('auto_save_interval', '30', '自动保存间隔（秒）', true),
('version_retention_days', '90', '版本保留天数', false),

-- 协作配置
('max_concurrent_editors', '3', '最大同时编辑人数', true),
('conflict_resolution_timeout', '300', '冲突解决超时时间（秒）', false),

-- 文件配置
('max_file_size_mb', '50', '最大文件大小（MB）', true),
('allowed_file_types', '["pdf", "doc", "docx", "txt", "md", "png", "jpg", "jpeg", "svg"]', '允许的文件类型', true),

-- 邮件配置
('email_notifications_enabled', 'true', '邮件通知开关', true),
('notification_digest_hour', '9', '通知摘要发送时间（小时）', false);

-- ============================================================================
-- 提示词模板初始化
-- ============================================================================

INSERT INTO public.prompt_templates (name, category, template, variables, description, is_system) VALUES 

-- 写作辅助模板
('学术章节生成', 'writing', 
'你是《大模型技术与油气应用概论》的专业写作助手。请根据以下大纲生成学术性章节内容：

章节标题：{{chapter_title}}
章节大纲：{{outline}}
目标字数：{{target_words}}字
写作要求：
1. 使用学术化、专业的语言风格
2. 确保内容准确、逻辑清晰
3. 适当引用相关技术概念
4. 保持客观、严谨的学术态度
5. 内容应适合大学教材使用

请生成完整的章节内容：', 
'{"chapter_title": "章节标题", "outline": "章节大纲", "target_words": "目标字数"}',
'用于生成学术章节的基础内容', true),

('内容续写', 'writing',
'请根据以下已有内容，继续编写后续内容：

已有内容：
{{existing_content}}

续写要求：
1. 保持与前文的逻辑连贯性
2. 维持相同的写作风格和语调
3. 确保内容的学术性和专业性
4. 续写长度约{{continue_length}}字

请继续编写：',
'{"existing_content": "已有内容", "continue_length": "续写长度"}',
'用于智能续写章节内容', true),

-- 润色模板
('学术润色', 'polishing',
'请对以下文本进行学术化润色，要求：

原文：
{{original_text}}

润色要求：
1. 保持原文的核心观点和逻辑结构
2. 提升语言表达的学术性和专业性
3. 修正语法错误和表达不当之处
4. 确保术语使用的准确性和一致性
5. 保持适合教材的表达风格

请提供润色后的文本：',
'{"original_text": "原始文本"}',
'用于学术文本的语言润色', true),

('风格统一', 'polishing',
'请将以下文本调整为与目标风格一致：

待调整文本：
{{text_to_adjust}}

目标风格示例：
{{style_example}}

调整要求：
1. 保持原文的核心内容不变
2. 调整语言风格与目标风格保持一致
3. 确保术语使用的统一性
4. 保持学术写作的严谨性

请提供调整后的文本：',
'{"text_to_adjust": "待调整文本", "style_example": "风格示例"}',
'用于统一全书的写作风格', true),

-- 扩展模板
('概念扩展', 'expansion',
'请对以下概念进行深度扩展：

核心概念：{{concept}}
当前描述：{{current_description}}
扩展方向：{{expansion_direction}}
目标长度：{{target_length}}字

扩展要求：
1. 深入阐述概念的内涵和外延
2. 提供具体的应用场景和案例
3. 分析相关的技术原理
4. 保持内容的准确性和权威性
5. 适合大学生理解水平

请提供扩展内容：',
'{"concept": "核心概念", "current_description": "当前描述", "expansion_direction": "扩展方向", "target_length": "目标长度"}',
'用于深度扩展技术概念', true),

-- 图表生成模板
('架构图生成', 'chart',
'请根据以下描述生成Mermaid架构图：

系统描述：{{system_description}}
主要组件：{{components}}
组件关系：{{relationships}}

图表要求：
1. 使用清晰的层次结构
2. 标注关键的数据流向
3. 突出核心组件和接口
4. 适合教材使用的简洁风格
5. 包含必要的标签说明

请生成Mermaid代码：',
'{"system_description": "系统描述", "components": "主要组件", "relationships": "组件关系"}',
'用于生成系统架构图', true),

('流程图生成', 'chart',
'请根据以下流程描述生成Mermaid流程图：

流程名称：{{process_name}}
主要步骤：{{steps}}
决策点：{{decision_points}}
异常处理：{{exception_handling}}

图表要求：
1. 清晰展示流程的各个步骤
2. 标明决策点和分支条件
3. 包含异常处理路径
4. 使用标准的流程图符号
5. 适合教学演示使用

请生成Mermaid代码：',
'{"process_name": "流程名称", "steps": "主要步骤", "decision_points": "决策点", "exception_handling": "异常处理"}',
'用于生成业务流程图', true),

-- 分析模板
('技术对比分析', 'analysis',
'请对以下技术进行对比分析：

对比技术：{{technologies}}
对比维度：{{comparison_dimensions}}
应用场景：{{application_scenarios}}

分析要求：
1. 客观分析各技术的优缺点
2. 从多个维度进行全面对比
3. 结合油气行业的实际需求
4. 提供选择建议和应用指导
5. 保持学术的客观性和严谨性

请提供对比分析：',
'{"technologies": "对比技术", "comparison_dimensions": "对比维度", "application_scenarios": "应用场景"}',
'用于技术方案的对比分析', true);

-- ============================================================================
-- 图表模板初始化
-- ============================================================================

INSERT INTO public.chart_library (title, description, chart_type, chart_data, is_template, created_by) VALUES 

('大模型技术架构图', '展示大模型的基本技术架构', 'architecture', 
'{"code": "graph TB\n    subgraph \"数据层\"\n        A[训练数据]\n        B[知识库]\n        C[实时数据]\n    end\n    subgraph \"模型层\"\n        D[预训练模型]\n        E[微调模型]\n        F[专业模型]\n    end\n    subgraph \"应用层\"\n        G[智能问答]\n        H[内容生成]\n        I[决策支持]\n    end\n    A --> D\n    B --> E\n    C --> F\n    D --> G\n    E --> H\n    F --> I", "type": "architecture"}', 
true, NULL),

('AI应用流程图', '展示AI技术在油气行业的应用流程', 'flowchart',
'{"code": "flowchart TD\n    A[数据采集] --> B[数据预处理]\n    B --> C[特征提取]\n    C --> D[模型训练]\n    D --> E[模型验证]\n    E --> F{验证通过?}\n    F -->|是| G[模型部署]\n    F -->|否| H[参数调优]\n    H --> D\n    G --> I[实际应用]\n    I --> J[效果评估]\n    J --> K[模型优化]", "type": "flowchart"}',
true, NULL),

('油气大模型生态图', '展示油气行业大模型技术生态', 'mindmap',
'{"code": "mindmap\n  root((油气大模型生态))\n    技术基础\n      算力资源\n      数据资源\n      算法模型\n    应用场景\n      勘探开发\n      生产运营\n      安全环保\n    产业链条\n      上游勘探\n      中游运输\n      下游炼化\n    支撑体系\n      标准规范\n      人才培养\n      政策支持", "type": "mindmap"}',
true, NULL);

-- ============================================================================
-- 示例项目数据（可选）
-- ============================================================================

-- 注意：这部分数据需要在有实际用户后才能插入
-- 这里提供插入示例，实际部署时可以注释掉

/*
-- 示例：插入示例项目（需要先有用户）
INSERT INTO public.projects (title, description, owner_id, status) VALUES 
('《大模型技术与油气应用概论》', '面向大学生的大模型技术教材', 
 (SELECT id FROM public.user_profiles LIMIT 1), 'active');

-- 示例：插入示例大纲
INSERT INTO public.outlines (project_id, title, level, sort_order, description, created_by) VALUES 
((SELECT id FROM public.projects LIMIT 1), '前言', 0, 1, '介绍大模型技术发展背景', 
 (SELECT id FROM public.user_profiles LIMIT 1)),
((SELECT id FROM public.projects LIMIT 1), '第一篇 理论技术篇', 0, 2, '大模型理论基础和技术原理', 
 (SELECT id FROM public.user_profiles LIMIT 1)),
((SELECT id FROM public.projects LIMIT 1), '第1章 大模型基本概念与内涵', 1, 3, '大模型的定义、分类和特点', 
 (SELECT id FROM public.user_profiles LIMIT 1));
*/

-- ============================================================================
-- 数据库优化配置
-- ============================================================================

-- 设置数据库参数优化
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- 创建性能监控视图
CREATE OR REPLACE VIEW ai_performance_stats AS
SELECT 
    task_type,
    COUNT(*) as total_requests,
    AVG(processing_time) as avg_processing_time,
    AVG(tokens_used) as avg_tokens_used,
    AVG(cost_usd) as avg_cost,
    COUNT(*) FILTER (WHERE status = 'completed') as success_count,
    COUNT(*) FILTER (WHERE status = 'failed') as error_count,
    (COUNT(*) FILTER (WHERE status = 'completed')::float / COUNT(*)) * 100 as success_rate
FROM public.ai_tasks 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY task_type;

-- 创建用户活跃度统计视图
CREATE OR REPLACE VIEW user_activity_stats AS
SELECT 
    up.full_name,
    up.institution,
    COUNT(DISTINCT al.id) as total_activities,
    COUNT(DISTINCT al.id) FILTER (WHERE al.created_at >= CURRENT_DATE) as today_activities,
    COUNT(DISTINCT al.id) FILTER (WHERE al.created_at >= CURRENT_DATE - INTERVAL '7 days') as week_activities,
    MAX(al.created_at) as last_activity
FROM public.user_profiles up
LEFT JOIN public.activity_logs al ON up.id = al.user_id
GROUP BY up.id, up.full_name, up.institution
ORDER BY total_activities DESC;

-- 创建项目进度统计视图
CREATE OR REPLACE VIEW project_progress_stats AS
SELECT 
    p.title as project_title,
    COUNT(DISTINCT o.id) as total_chapters,
    COUNT(DISTINCT c.id) as completed_chapters,
    ROUND((COUNT(DISTINCT c.id)::float / NULLIF(COUNT(DISTINCT o.id), 0)) * 100, 2) as completion_rate,
    SUM(c.word_count) as total_words,
    COUNT(DISTINCT pm.user_id) as team_size
FROM public.projects p
LEFT JOIN public.outlines o ON p.id = o.project_id AND o.level > 0
LEFT JOIN public.chapters c ON o.id = c.outline_id
LEFT JOIN public.project_members pm ON p.id = pm.project_id
GROUP BY p.id, p.title
ORDER BY completion_rate DESC;
