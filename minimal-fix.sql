-- 最小化修复脚本
-- 只修复用户管理页面必需的字段，快速解决当前问题

-- 1. 添加用户配置表的缺失字段
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE;

ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- 2. 添加项目成员表的缺失字段
ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active' 
CHECK (status IN ('active', 'inactive', 'pending', 'invited'));

-- 3. 创建用户邀请表（邀请功能必需）
CREATE TABLE IF NOT EXISTS public.user_invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    invited_by UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'editor', 'author', 'reviewer')),
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 启用行级安全
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- 5. 创建基本的RLS策略
CREATE POLICY IF NOT EXISTS "Users can manage invitations in their projects" 
ON public.user_invitations
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.project_members pm
        WHERE pm.project_id = user_invitations.project_id 
        AND pm.user_id = auth.uid()
        AND pm.role IN ('owner', 'admin')
    )
);

-- 6. 确保项目所有者在project_members表中
INSERT INTO public.project_members (project_id, user_id, role, status, joined_at)
SELECT 
    p.id as project_id,
    p.owner_id as user_id,
    'owner' as role,
    'active' as status,
    p.created_at as joined_at
FROM public.projects p
WHERE NOT EXISTS (
    SELECT 1 FROM public.project_members pm 
    WHERE pm.project_id = p.id AND pm.user_id = p.owner_id
)
ON CONFLICT (project_id, user_id) DO NOTHING;

-- 完成提示
SELECT 'minimal-fix completed successfully' as result;
