-- 分析书籍目录排序问题的SQL脚本
-- 用于诊断当前数据库中章节和大纲的排序状况

-- ========================================
-- 第一部分：查看当前大纲表的排序情况
-- ========================================

-- 1. 查看所有大纲项的排序字段
SELECT 
    id,
    title,
    level,
    sort_order,
    parent_id,
    project_id,
    created_at
FROM public.outlines 
ORDER BY project_id, sort_order, level;

-- 2. 检查大纲排序字段的分布情况
SELECT 
    project_id,
    COUNT(*) as total_outlines,
    MIN(sort_order) as min_sort_order,
    MAX(sort_order) as max_sort_order,
    COUNT(DISTINCT sort_order) as unique_sort_orders,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT sort_order) THEN '排序唯一'
        ELSE '存在重复排序'
    END as sort_uniqueness
FROM public.outlines 
GROUP BY project_id;

-- 3. 查找排序重复的大纲项
SELECT 
    project_id,
    sort_order,
    COUNT(*) as duplicate_count,
    STRING_AGG(title, ' | ') as duplicate_titles
FROM public.outlines 
GROUP BY project_id, sort_order
HAVING COUNT(*) > 1
ORDER BY project_id, sort_order;

-- ========================================
-- 第二部分：查看当前章节表的排序情况
-- ========================================

-- 4. 查看所有章节的排序字段
SELECT 
    id,
    title,
    order_index,
    outline_id,
    project_id,
    status,
    created_at
FROM public.chapters 
ORDER BY project_id, order_index;

-- 5. 检查章节排序字段的分布情况
SELECT 
    project_id,
    COUNT(*) as total_chapters,
    MIN(order_index) as min_order_index,
    MAX(order_index) as max_order_index,
    COUNT(DISTINCT order_index) as unique_order_indexes,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT order_index) THEN '排序唯一'
        ELSE '存在重复排序'
    END as order_uniqueness
FROM public.chapters 
GROUP BY project_id;

-- 6. 查找排序重复的章节
SELECT 
    project_id,
    order_index,
    COUNT(*) as duplicate_count,
    STRING_AGG(title, ' | ') as duplicate_titles
FROM public.chapters 
GROUP BY project_id, order_index
HAVING COUNT(*) > 1
ORDER BY project_id, order_index;

-- ========================================
-- 第三部分：分析大纲与章节的关联关系
-- ========================================

-- 7. 查看大纲与章节的关联情况
SELECT 
    o.project_id,
    o.title as outline_title,
    o.level as outline_level,
    o.sort_order as outline_sort_order,
    c.title as chapter_title,
    c.order_index as chapter_order_index,
    CASE 
        WHEN c.id IS NULL THEN '无对应章节'
        WHEN o.sort_order = c.order_index THEN '排序一致'
        ELSE '排序不一致'
    END as consistency_status
FROM public.outlines o
LEFT JOIN public.chapters c ON o.id = c.outline_id
ORDER BY o.project_id, o.sort_order;

-- 8. 统计关联一致性
SELECT 
    project_id,
    COUNT(*) as total_outlines,
    COUNT(c.id) as linked_chapters,
    COUNT(CASE WHEN o.sort_order = c.order_index THEN 1 END) as consistent_orders,
    ROUND(
        COUNT(CASE WHEN o.sort_order = c.order_index THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as consistency_percentage
FROM public.outlines o
LEFT JOIN public.chapters c ON o.id = c.outline_id
GROUP BY project_id;

-- ========================================
-- 第四部分：识别具体的排序问题
-- ========================================

-- 9. 查找缺失排序值的记录
SELECT 
    'outlines' as table_name,
    id,
    title,
    'sort_order为NULL' as issue
FROM public.outlines 
WHERE sort_order IS NULL

UNION ALL

SELECT 
    'chapters' as table_name,
    id,
    title,
    'order_index为NULL' as issue
FROM public.chapters 
WHERE order_index IS NULL;

-- 10. 查找排序值为0或负数的记录
SELECT 
    'outlines' as table_name,
    id,
    title,
    sort_order,
    'sort_order <= 0' as issue
FROM public.outlines 
WHERE sort_order <= 0

UNION ALL

SELECT 
    'chapters' as table_name,
    id,
    title,
    order_index,
    'order_index < 0' as issue
FROM public.chapters 
WHERE order_index < 0;

-- ========================================
-- 第五部分：书籍目录的理想排序分析
-- ========================================

-- 11. 分析当前目录结构是否符合书籍逻辑
WITH chapter_analysis AS (
    SELECT 
        project_id,
        title,
        order_index,
        CASE 
            WHEN title LIKE '%前言%' OR title LIKE '%序%' THEN 0
            WHEN title LIKE '%第0章%' OR title LIKE '%第零章%' THEN 0
            WHEN title LIKE '%第1章%' OR title LIKE '%第一章%' THEN 1
            WHEN title LIKE '%第2章%' OR title LIKE '%第二章%' THEN 2
            WHEN title LIKE '%第3章%' OR title LIKE '%第三章%' THEN 3
            WHEN title LIKE '%第4章%' OR title LIKE '%第四章%' THEN 4
            WHEN title LIKE '%第5章%' OR title LIKE '%第五章%' THEN 5
            WHEN title LIKE '%第6章%' OR title LIKE '%第六章%' THEN 6
            WHEN title LIKE '%第7章%' OR title LIKE '%第七章%' THEN 7
            WHEN title LIKE '%第8章%' OR title LIKE '%第八章%' THEN 8
            WHEN title LIKE '%第9章%' OR title LIKE '%第九章%' THEN 9
            WHEN title LIKE '%第10章%' OR title LIKE '%第十章%' THEN 10
            WHEN title LIKE '%附录%' THEN 100
            WHEN title LIKE '%参考文献%' THEN 101
            WHEN title LIKE '%索引%' THEN 102
            ELSE 50 -- 其他章节
        END as logical_order
    FROM public.chapters
)
SELECT 
    project_id,
    title,
    order_index as current_order,
    logical_order as suggested_order,
    CASE 
        WHEN order_index = logical_order THEN '顺序正确'
        ELSE '需要调整'
    END as order_status
FROM chapter_analysis
ORDER BY project_id, logical_order;

-- 12. 生成排序修复建议
SELECT 
    '-- 修复章节排序的建议SQL' as suggestion_header;

-- 这个查询将在后续步骤中生成具体的UPDATE语句
