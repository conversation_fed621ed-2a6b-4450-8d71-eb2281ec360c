-- 章节分配系统数据库表结构
-- 专业学术协作编著平台

-- 1. 项目角色表 (扩展现有的 project_members)
-- 为现有表添加新的角色类型
-- 角色类型: chief_editor, associate_editor, lead_author, co_author, reviewer, editorial_assistant

-- 2. 章节分配表
CREATE TABLE IF NOT EXISTS chapter_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chapter_id UUID NOT NULL REFERENCES chapters(id) ON DELETE CASCADE,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    
    -- 角色分配
    lead_author_id UUID REFERENCES auth.users(id),
    assigned_by UUID NOT NULL REFERENCES auth.users(id),
    
    -- 任务信息
    title TEXT NOT NULL,
    description TEXT,
    requirements TEXT, -- 具体要求和指导
    word_count_target INTEGER DEFAULT 0,
    
    -- 时间管理
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    due_date TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    submitted_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- 状态管理
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending',      -- 待确认
        'accepted',     -- 已接受
        'in_progress',  -- 进行中
        'submitted',    -- 已提交
        'reviewing',    -- 审核中
        'revising',     -- 修改中
        'approved',     -- 已批准
        'completed',    -- 已完成
        'rejected'      -- 已拒绝
    )),
    
    -- 优先级
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 章节协作者表
CREATE TABLE IF NOT EXISTS chapter_collaborators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL REFERENCES chapter_assignments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- 角色类型
    role TEXT NOT NULL CHECK (role IN (
        'co_author',           -- 协作作者
        'reviewer',            -- 审稿人
        'editorial_assistant'  -- 编辑助理
    )),
    
    -- 具体职责
    responsibilities TEXT,
    sections TEXT[], -- 负责的具体章节部分
    
    -- 状态
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending',    -- 待确认
        'accepted',   -- 已接受
        'declined',   -- 已拒绝
        'active',     -- 活跃中
        'completed'   -- 已完成
    )),
    
    -- 时间记录
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 确保同一用户在同一分配中不重复
    UNIQUE(assignment_id, user_id)
);

-- 4. 章节审核记录表
CREATE TABLE IF NOT EXISTS chapter_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL REFERENCES chapter_assignments(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- 审核信息
    review_type TEXT NOT NULL CHECK (review_type IN (
        'peer_review',     -- 同行评议
        'editorial_review', -- 编辑审核
        'final_review'     -- 最终审核
    )),
    
    -- 审核结果
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending',     -- 待审核
        'in_progress', -- 审核中
        'approved',    -- 通过
        'rejected',    -- 拒绝
        'needs_revision' -- 需要修改
    )),
    
    -- 评分 (1-5分)
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
    completeness_score INTEGER CHECK (completeness_score >= 1 AND completeness_score <= 5),
    originality_score INTEGER CHECK (originality_score >= 1 AND originality_score <= 5),
    
    -- 审核内容
    comments TEXT,
    suggestions TEXT,
    required_changes TEXT,
    
    -- 时间记录
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    submitted_at TIMESTAMP WITH TIME ZONE,
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 章节工作日志表
CREATE TABLE IF NOT EXISTS chapter_work_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL REFERENCES chapter_assignments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- 工作记录
    work_type TEXT NOT NULL CHECK (work_type IN (
        'writing',    -- 写作
        'editing',    -- 编辑
        'reviewing',  -- 审核
        'discussion', -- 讨论
        'research'    -- 研究
    )),
    
    -- 工作内容
    description TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    hours_spent DECIMAL(4,2) DEFAULT 0,
    
    -- 进度信息
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    
    -- 时间记录
    work_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 章节讨论表
CREATE TABLE IF NOT EXISTS chapter_discussions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL REFERENCES chapter_assignments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES chapter_discussions(id) ON DELETE CASCADE,
    
    -- 讨论内容
    subject TEXT,
    content TEXT NOT NULL,
    discussion_type TEXT DEFAULT 'general' CHECK (discussion_type IN (
        'general',     -- 一般讨论
        'question',    -- 问题
        'suggestion',  -- 建议
        'issue',       -- 问题报告
        'decision'     -- 决策
    )),
    
    -- 状态
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'resolved', 'closed')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 章节文件附件表
CREATE TABLE IF NOT EXISTS chapter_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    assignment_id UUID NOT NULL REFERENCES chapter_assignments(id) ON DELETE CASCADE,
    uploaded_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- 文件信息
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type TEXT NOT NULL,
    file_path TEXT NOT NULL,
    
    -- 文件类型
    attachment_type TEXT DEFAULT 'document' CHECK (attachment_type IN (
        'document',    -- 文档
        'image',       -- 图片
        'data',        -- 数据文件
        'reference',   -- 参考资料
        'template'     -- 模板
    )),
    
    -- 描述
    description TEXT,
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_project_id ON chapter_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_lead_author ON chapter_assignments(lead_author_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_status ON chapter_assignments(status);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_due_date ON chapter_assignments(due_date);

CREATE INDEX IF NOT EXISTS idx_chapter_collaborators_assignment ON chapter_collaborators(assignment_id);
CREATE INDEX IF NOT EXISTS idx_chapter_collaborators_user ON chapter_collaborators(user_id);
CREATE INDEX IF NOT EXISTS idx_chapter_collaborators_role ON chapter_collaborators(role);

CREATE INDEX IF NOT EXISTS idx_chapter_reviews_assignment ON chapter_reviews(assignment_id);
CREATE INDEX IF NOT EXISTS idx_chapter_reviews_reviewer ON chapter_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_chapter_reviews_status ON chapter_reviews(status);

CREATE INDEX IF NOT EXISTS idx_chapter_work_logs_assignment ON chapter_work_logs(assignment_id);
CREATE INDEX IF NOT EXISTS idx_chapter_work_logs_user ON chapter_work_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_chapter_work_logs_date ON chapter_work_logs(work_date);

CREATE INDEX IF NOT EXISTS idx_chapter_discussions_assignment ON chapter_discussions(assignment_id);
CREATE INDEX IF NOT EXISTS idx_chapter_discussions_parent ON chapter_discussions(parent_id);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_chapter_assignments_updated_at 
    BEFORE UPDATE ON chapter_assignments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chapter_collaborators_updated_at 
    BEFORE UPDATE ON chapter_collaborators 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chapter_reviews_updated_at 
    BEFORE UPDATE ON chapter_reviews 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chapter_discussions_updated_at 
    BEFORE UPDATE ON chapter_discussions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 行级安全策略 (RLS)
ALTER TABLE chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_work_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_discussions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chapter_attachments ENABLE ROW LEVEL SECURITY;

-- 基本的RLS策略 (可以根据具体需求调整)
-- 用户可以查看自己参与的章节分配
CREATE POLICY "Users can view their chapter assignments" ON chapter_assignments
    FOR SELECT USING (
        lead_author_id = auth.uid() OR
        assigned_by = auth.uid() OR
        project_id IN (
            SELECT project_id FROM project_members 
            WHERE user_id = auth.uid()
        )
    );

-- 项目成员可以查看项目内的协作者信息
CREATE POLICY "Project members can view collaborators" ON chapter_collaborators
    FOR SELECT USING (
        user_id = auth.uid() OR
        assignment_id IN (
            SELECT id FROM chapter_assignments 
            WHERE project_id IN (
                SELECT project_id FROM project_members 
                WHERE user_id = auth.uid()
            )
        )
    );
