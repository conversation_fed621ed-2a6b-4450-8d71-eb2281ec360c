<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节分配管理系统 - 独立版本</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="chapter-assignment.css">
    <style>
        /* 添加一些特定样式确保独立运行 */
        .standalone-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        
        .standalone-header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .standalone-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }
        
        .standalone-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin: 5px 0 0 0;
        }
        
        .standalone-user {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 8px 16px;
            border-radius: 20px;
        }
        
        .demo-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 14px;
        }
        
        .demo-notice strong {
            color: #78350f;
        }
    </style>
</head>
<body>
    <!-- 独立页面头部 -->
    <header class="standalone-header">
        <div class="container">
            <div>
                <h1 class="standalone-title">
                    <i class="fas fa-book-open"></i>
                    专业学术协作编著系统
                </h1>
                <p class="standalone-subtitle">章节分配管理 - 独立演示版本</p>
            </div>
            <div class="standalone-user">
                <i class="fas fa-user-circle"></i>
                <span id="current-user-name">演示用户</span>
                <span class="user-role" id="current-user-role">主编</span>
            </div>
        </div>
    </header>

    <!-- 演示说明 -->
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
        <div class="demo-notice">
            <strong>演示模式</strong> - 这是一个独立的章节分配系统演示，使用模拟数据展示完整功能。所有操作都是安全的，不会影响真实数据。
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <h3>章节管理</h3>
                    <ul>
                        <li class="nav-item active">
                            <a href="#overview" data-tab="overview">
                                <i class="fas fa-chart-pie"></i>
                                <span>项目概览</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#assignments" data-tab="assignments">
                                <i class="fas fa-tasks"></i>
                                <span>章节分配</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#progress" data-tab="progress">
                                <i class="fas fa-chart-line"></i>
                                <span>进度跟踪</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#reviews" data-tab="reviews">
                                <i class="fas fa-clipboard-check"></i>
                                <span>审核管理</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="nav-section">
                    <h3>协作工具</h3>
                    <ul>
                        <li class="nav-item">
                            <a href="#discussions" data-tab="discussions">
                                <i class="fas fa-comments"></i>
                                <span>讨论区</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#files" data-tab="files">
                                <i class="fas fa-folder"></i>
                                <span>文件管理</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#reports" data-tab="reports">
                                <i class="fas fa-chart-bar"></i>
                                <span>统计报告</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="nav-section">
                    <h3>系统工具</h3>
                    <ul>
                        <li class="nav-item">
                            <a href="chapter-assignment-quick-test.html">
                                <i class="fas fa-vial"></i>
                                <span>系统测试</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="comprehensive-test.html">
                                <i class="fas fa-cogs"></i>
                                <span>综合演示</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 项目概览面板 -->
            <section id="overview-panel" class="content-panel active">
                <div class="panel-header">
                    <h2>项目概览</h2>
                    <div class="panel-actions">
                        <button id="create-assignment-btn" class="btn btn-primary" onclick="showCreateAssignmentModal()">
                            <i class="fas fa-plus"></i>
                            新建分配
                        </button>
                        <button class="btn btn-secondary" onclick="refreshOverview()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-chapters">2</h3>
                            <p>总章节数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-authors">1</h3>
                            <p>参与作者</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completed-chapters">0</h3>
                            <p>已完成章节</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pending-reviews">1</h3>
                            <p>待审核</p>
                        </div>
                    </div>
                </div>

                <!-- 进度图表 -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>项目进度</h3>
                        <div class="chart-controls">
                            <select id="chart-period">
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                                <option value="quarter">本季度</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="progress-chart"></canvas>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="recent-activity">
                    <h3>最近活动</h3>
                    <div id="activity-list" class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">章节分配系统启动</div>
                                <div class="activity-meta">刚刚</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 章节分配面板 -->
            <section id="assignments-panel" class="content-panel">
                <div class="panel-header">
                    <h2>章节分配</h2>
                    <div class="panel-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="assignment-search" placeholder="搜索章节或作者...">
                        </div>
                        <select id="status-filter">
                            <option value="">所有状态</option>
                            <option value="pending">待确认</option>
                            <option value="in_progress">进行中</option>
                            <option value="reviewing">审核中</option>
                            <option value="completed">已完成</option>
                        </select>
                        <button class="btn btn-primary" onclick="showCreateAssignmentModal()">
                            <i class="fas fa-plus"></i>
                            新建分配
                        </button>
                    </div>
                </div>

                <!-- 分配列表 -->
                <div class="assignments-container">
                    <div class="assignments-header">
                        <div class="header-cell">章节</div>
                        <div class="header-cell">主笔作者</div>
                        <div class="header-cell">协作者</div>
                        <div class="header-cell">状态</div>
                        <div class="header-cell">进度</div>
                        <div class="header-cell">截止日期</div>
                        <div class="header-cell">操作</div>
                    </div>
                    <div id="assignments-list" class="assignments-list">
                        <!-- 分配项目将通过JavaScript动态加载 -->
                    </div>
                </div>
            </section>

            <!-- 其他面板 -->
            <section id="progress-panel" class="content-panel">
                <div class="panel-header">
                    <h2>进度跟踪</h2>
                    <div class="panel-actions">
                        <select id="progress-view">
                            <option value="timeline">时间线视图</option>
                            <option value="kanban">看板视图</option>
                            <option value="gantt">甘特图</option>
                        </select>
                    </div>
                </div>
                
                <div id="progress-content" class="progress-content">
                    <div style="text-align: center; padding: 40px; color: #6b7280;">
                        <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 16px;"></i>
                        <h3>进度跟踪</h3>
                        <p>选择上方的视图类型查看项目进度</p>
                    </div>
                </div>
            </section>

            <section id="reviews-panel" class="content-panel">
                <div class="panel-header">
                    <h2>审核管理</h2>
                </div>
                <div id="reviews-content" class="reviews-content">
                    <div style="text-align: center; padding: 40px; color: #6b7280;">
                        <i class="fas fa-clipboard-check" style="font-size: 48px; margin-bottom: 16px;"></i>
                        <h3>审核管理</h3>
                        <p>当有章节提交审核时，审核记录将显示在这里</p>
                    </div>
                </div>
            </section>

            <section id="discussions-panel" class="content-panel">
                <div class="panel-header">
                    <h2>讨论区</h2>
                </div>
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <i class="fas fa-comments" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <h3>讨论区</h3>
                    <p>团队协作交流平台</p>
                </div>
            </section>

            <section id="files-panel" class="content-panel">
                <div class="panel-header">
                    <h2>文件管理</h2>
                </div>
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <i class="fas fa-folder" style="font-size: 48px; margin-bottom: 16px;"></i>
                    <h3>文件管理</h3>
                    <p>项目相关文件和附件管理</p>
                </div>
            </section>

            <section id="reports-panel" class="content-panel">
                <div class="panel-header">
                    <h2>统计报告</h2>
                    <div class="panel-actions">
                        <button class="btn btn-secondary" onclick="exportReport()">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>
                <div id="reports-content" class="reports-content">
                    <div style="text-align: center; padding: 40px; color: #6b7280;">
                        <i class="fas fa-chart-bar" style="font-size: 48px; margin-bottom: 16px;"></i>
                        <h3>统计报告</h3>
                        <p>项目数据分析和报告生成</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <!-- 脚本文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="supabase-simple.js"></script>
    <script src="role-permission-manager.js"></script>
    <script src="chapter-assignment-clean.js"></script>
    
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('章节分配系统独立版本已加载');
            
            // 显示成功通知
            setTimeout(() => {
                if (window.assignmentManager) {
                    window.assignmentManager.showNotification('章节分配系统已成功启动！', 'success');
                    console.log('✅ 章节分配管理器已初始化');
                } else {
                    console.warn('⚠️ 章节分配管理器未找到');
                }
                
                if (window.rolePermissionManager) {
                    console.log('✅ 权限管理器已初始化');
                } else {
                    console.warn('⚠️ 权限管理器未找到');
                }
                
                if (window.supabaseManager) {
                    console.log('✅ Supabase管理器已初始化');
                } else {
                    console.warn('⚠️ Supabase管理器未找到');
                }
            }, 1000);
        });
    </script>
</body>
</html>
