/**
 * 多媒体功能处理器
 * 处理文生图、语音转文字、文本转语音、图片识别等功能
 */

// 通用加载状态管理函数
function showLoadingState(container, message, type = 'default') {
    const loadingHtml = `
        <div class="loading-overlay">
            <div class="loading-content">
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                </div>
                <div class="loading-message">${message}</div>
                <div class="loading-progress">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">处理中...</div>
                </div>
            </div>
        </div>
    `;

    // 移除现有的加载状态
    const existingLoading = container.querySelector('.loading-overlay');
    if (existingLoading) {
        existingLoading.remove();
    }

    // 添加新的加载状态
    container.insertAdjacentHTML('beforeend', loadingHtml);
    container.style.display = 'block';

    // 启动进度条动画
    const progressFill = container.querySelector('.progress-fill');
    if (progressFill) {
        progressFill.style.animation = 'progressAnimation 3s ease-in-out infinite';
    }
}

function hideLoadingState(container) {
    const loadingOverlay = container.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

// 图片生成相关函数
function setupImageGenerationListeners() {
    // 监听尺寸选择变化
    const sizeSelect = document.querySelector('.image-size-select');
    if (sizeSelect) {
        sizeSelect.addEventListener('change', function() {
            console.log('图片尺寸选择:', this.value);
        });
    }

    // 监听模型选择变化
    const modelSelect = document.querySelector('.image-model-select');
    if (modelSelect) {
        modelSelect.addEventListener('change', function() {
            console.log('图片模型选择:', this.value);
        });
    }
}

async function processImageGeneration() {
    const promptInput = document.querySelector('.ai-prompt-input');
    const sizeSelect = document.querySelector('.image-size-select');
    const modelSelect = document.querySelector('.image-model-select');
    const enhanceCheckbox = document.querySelector('.enhance-prompt');
    const safeCheckbox = document.querySelector('.safe-mode');
    const responseSection = document.querySelector('.ai-response-section');
    const imageContainer = document.querySelector('.generated-image-container');

    if (!promptInput || !promptInput.value.trim()) {
        showNotification('请输入图片描述', 'warning');
        return;
    }

    const prompt = promptInput.value.trim();
    const [width, height] = sizeSelect.value.split('x').map(Number);
    const model = modelSelect.value;
    const enhance = enhanceCheckbox.checked;
    const safe = safeCheckbox.checked;

    try {
        // 显示加载状态和进度指示器
        showLoadingState(responseSection, '正在生成图片，请稍候...', 'image');
        showNotification('正在生成图片，请稍候...', 'info');

        // 调用Pollinations服务生成图片
        const imageUrl = await window.pollinationsService.generateImage(prompt, {
            width,
            height,
            model,
            enhance,
            safe
        });

        // 隐藏加载状态
        hideLoadingState(responseSection);

        // 显示生成的图片
        const img = imageContainer.querySelector('.generated-image');
        img.src = imageUrl;
        img.onload = () => {
            responseSection.style.display = 'block';
            showNotification('图片生成成功！', 'success');
        };

        // 设置下载和插入按钮
        setupImageActions(imageUrl, prompt);

    } catch (error) {
        console.error('图片生成失败:', error);
        hideLoadingState(responseSection);
        showNotification(`图片生成失败: ${error.message}`, 'error');
    }
}

function setupImageActions(imageUrl, prompt) {
    const downloadBtn = document.querySelector('.download-image');
    const copyBtn = document.querySelector('.copy-image');
    const insertBtn = document.querySelector('.insert-image');

    if (downloadBtn) {
        downloadBtn.onclick = async () => {
            try {
                const blob = await window.pollinationsService.generateImageBlob(prompt);
                const filename = `generated-image-${Date.now()}.jpg`;
                window.pollinationsService.downloadFile(blob, filename);
                showNotification('图片下载成功！', 'success');
            } catch (error) {
                showNotification('图片下载失败', 'error');
            }
        };
    }

    if (copyBtn) {
        copyBtn.onclick = async () => {
            try {
                // 显示复制中状态
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 复制中...';
                copyBtn.disabled = true;

                // 获取图片并复制到剪贴板
                const response = await fetch(imageUrl);
                const blob = await response.blob();

                await navigator.clipboard.write([
                    new ClipboardItem({
                        [blob.type]: blob
                    })
                ]);

                // 显示成功状态
                copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                showNotification('图片已复制到剪贴板', 'success');

                // 2秒后恢复原状态
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.disabled = false;
                }, 2000);

            } catch (error) {
                console.error('复制图片失败:', error);
                showNotification('复制失败，请尝试右键保存图片', 'error');

                // 恢复按钮状态
                copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制图片';
                copyBtn.disabled = false;
            }
        };
    }

    if (insertBtn) {
        insertBtn.onclick = () => {
            const success = insertImageToEditor(imageUrl, prompt);
            if (success) {
                closeModal();
                showNotification('图片已插入到编辑器', 'success');
            }
            // 如果失败，不关闭对话框，让用户可以重试
        };
    }
}

function insertImageToEditor(imageUrl, altText) {
    if (!window.quillEditor) {
        console.error('编辑器未初始化');
        showNotification('编辑器未初始化，请先进入章节编写模式', 'error');
        return false;
    }

    try {
        const range = window.quillEditor.getSelection() || { index: window.quillEditor.getLength() };
        window.quillEditor.insertEmbed(range.index, 'image', imageUrl);
        window.quillEditor.insertText(range.index + 1, '\n');
        window.quillEditor.setSelection(range.index + 2);

        // 触发自动保存
        if (typeof debounceAutoSave === 'function') {
            debounceAutoSave();
        }

        console.log('图片已成功插入到编辑器');
        return true;
    } catch (error) {
        console.error('插入图片失败:', error);
        showNotification('插入图片失败: ' + error.message, 'error');
        return false;
    }
}

// 文本转语音相关函数
function setupTextToSpeechListeners() {
    const useCustomCheckbox = document.querySelector('.use-custom-text');
    const customTextSection = document.querySelector('.custom-text-section');

    if (useCustomCheckbox && customTextSection) {
        useCustomCheckbox.addEventListener('change', function() {
            customTextSection.style.display = this.checked ? 'block' : 'none';
        });
    }
}

async function processTextToSpeech() {
    const selectedTextDiv = document.querySelector('.ai-selected-text-content');
    const useCustomCheckbox = document.querySelector('.use-custom-text');
    const customTextInput = document.querySelector('.custom-text-input');
    const voiceSelect = document.querySelector('.voice-select');
    const responseSection = document.querySelector('.ai-response-section');
    const audioPlayer = document.querySelector('.generated-audio');

    let text;
    if (useCustomCheckbox && useCustomCheckbox.checked) {
        text = customTextInput.value.trim();
    } else {
        text = selectedTextDiv.textContent.trim();
    }

    if (!text) {
        showNotification('请选择文本或输入自定义文本', 'warning');
        return;
    }

    const voice = voiceSelect.value;

    try {
        // 显示加载状态和进度指示器
        showLoadingState(responseSection, '正在生成语音，请稍候...', 'audio');
        showNotification('正在生成语音，请稍候...', 'info');

        const audioBlob = await window.pollinationsService.textToSpeech(text, { voice });
        const audioUrl = URL.createObjectURL(audioBlob);

        // 隐藏加载状态
        hideLoadingState(responseSection);

        audioPlayer.src = audioUrl;
        responseSection.style.display = 'block';

        setupAudioActions(audioBlob, text);
        showNotification('语音生成成功！', 'success');

    } catch (error) {
        console.error('语音生成失败:', error);
        hideLoadingState(responseSection);
        showNotification(`语音生成失败: ${error.message}`, 'error');
    }
}

function setupAudioActions(audioBlob, text) {
    const downloadBtn = document.querySelector('.download-audio');
    const copyBtn = document.querySelector('.copy-audio');
    const playBtn = document.querySelector('.play-audio');
    const audioPlayer = document.querySelector('.generated-audio');

    if (downloadBtn) {
        downloadBtn.onclick = () => {
            const filename = `generated-audio-${Date.now()}.mp3`;
            window.pollinationsService.downloadFile(audioBlob, filename);
            showNotification('音频下载成功！', 'success');
        };
    }

    if (copyBtn) {
        copyBtn.onclick = async () => {
            try {
                // 显示复制中状态
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 复制中...';
                copyBtn.disabled = true;

                // 复制音频文件到剪贴板
                await navigator.clipboard.write([
                    new ClipboardItem({
                        [audioBlob.type]: audioBlob
                    })
                ]);

                // 显示成功状态
                copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                showNotification('音频已复制到剪贴板', 'success');

                // 2秒后恢复原状态
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.disabled = false;
                }, 2000);

            } catch (error) {
                console.error('复制音频失败:', error);
                showNotification('复制失败，请尝试下载音频文件', 'error');

                // 恢复按钮状态
                copyBtn.innerHTML = '<i class="fas fa-copy"></i> 复制音频';
                copyBtn.disabled = false;
            }
        };
    }

    if (playBtn) {
        playBtn.onclick = () => {
            audioPlayer.play();
        };
    }
}

// 语音转文字相关函数
function setupSpeechToTextListeners() {
    const startBtn = document.querySelector('.start-recording');
    const stopBtn = document.querySelector('.stop-recording');
    const audioInput = document.querySelector('.audio-file-input');
    const audioPreview = document.querySelector('.audio-preview');

    let mediaRecorder = null;
    let audioChunks = [];

    if (startBtn && stopBtn) {
        startBtn.addEventListener('click', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    processAudioFile(audioBlob);
                };

                mediaRecorder.start();
                startBtn.style.display = 'none';
                stopBtn.style.display = 'inline-block';
                updateRecordingStatus('正在录音...');

            } catch (error) {
                showNotification('无法访问麦克风', 'error');
            }
        });

        stopBtn.addEventListener('click', () => {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                startBtn.style.display = 'inline-block';
                stopBtn.style.display = 'none';
                updateRecordingStatus('录音完成，正在处理...');
            }
        });
    }

    if (audioInput) {
        audioInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                processAudioFile(file);
            }
        });
    }
}

function updateRecordingStatus(message) {
    const statusDiv = document.querySelector('.recording-status');
    if (statusDiv) {
        statusDiv.textContent = message;
    }
}

async function processAudioFile(audioFile) {
    const responseSection = document.querySelector('.ai-response-section');

    try {
        // 显示加载状态和进度指示器
        showLoadingState(responseSection, '正在转录音频，请稍候...', 'transcription');
        showNotification('正在转录音频，请稍候...', 'info');

        const transcription = await window.pollinationsService.speechToText(audioFile);

        // 隐藏加载状态
        hideLoadingState(responseSection);

        const transcriptionText = document.querySelector('.transcription-text');
        if (transcriptionText) {
            transcriptionText.value = transcription;
        }

        if (responseSection) {
            responseSection.style.display = 'block';
        }

        setupTranscriptionActions(transcription);
        showNotification('音频转录成功！', 'success');

    } catch (error) {
        console.error('音频转录失败:', error);

        // 隐藏加载状态
        hideLoadingState(responseSection);

        let errorMessage = '音频转录失败';
        let suggestion = '';

        if (error.message.includes('400')) {
            errorMessage = '音频格式不支持或文件损坏';
            suggestion = '请尝试使用WAV、MP3或M4A格式的音频文件';
        } else if (error.message.includes('401') || error.message.includes('403')) {
            errorMessage = '需要API密钥访问语音转文字功能';
            suggestion = '请在系统设置中配置Pollinations API密钥';
        } else if (error.message.includes('413')) {
            errorMessage = '音频文件过大';
            suggestion = '请使用小于25MB的音频文件';
        } else if (error.message.includes('所有语音转文字方法都失败了')) {
            errorMessage = 'Pollinations暂时不支持语音转文字功能';
            suggestion = '该功能可能需要付费API密钥或暂时不可用';
        } else {
            errorMessage = `转录失败: ${error.message}`;
            suggestion = '请检查网络连接或稍后重试';
        }

        showNotification(`${errorMessage}${suggestion ? ' - ' + suggestion : ''}`, 'error');
    }
}

async function processSpeechToText() {
    const audioInput = document.querySelector('.audio-file-input');
    
    if (!audioInput.files.length) {
        showNotification('请先录音或上传音频文件', 'warning');
        return;
    }

    const audioFile = audioInput.files[0];
    await processAudioFile(audioFile);
}

function setupTranscriptionActions(transcription) {
    const copyBtn = document.querySelector('.copy-transcription');
    const insertBtn = document.querySelector('.insert-transcription');

    if (copyBtn) {
        copyBtn.onclick = () => {
            navigator.clipboard.writeText(transcription).then(() => {
                showNotification('文本已复制到剪贴板', 'success');
            });
        };
    }

    if (insertBtn) {
        insertBtn.onclick = () => {
            const success = insertTextToEditor(transcription);
            if (success) {
                closeModal();
                showNotification('文本已插入到编辑器', 'success');
            }
            // 如果失败，不关闭对话框，让用户可以重试
        };
    }
}

function insertTextToEditor(text) {
    if (!window.quillEditor) {
        console.error('编辑器未初始化');
        showNotification('编辑器未初始化，请先进入章节编写模式', 'error');
        return false;
    }

    if (!text || text.trim() === '') {
        console.warn('插入的文本为空');
        showNotification('没有可插入的文本内容', 'warning');
        return false;
    }

    try {
        const range = window.quillEditor.getSelection() || { index: window.quillEditor.getLength() };
        window.quillEditor.insertText(range.index, text);
        window.quillEditor.setSelection(range.index + text.length);

        // 触发自动保存
        if (typeof debounceAutoSave === 'function') {
            debounceAutoSave();
        }

        console.log('文本已成功插入到编辑器');
        return true;
    } catch (error) {
        console.error('插入文本失败:', error);
        showNotification('插入文本失败: ' + error.message, 'error');
        return false;
    }
}

// 图片识别相关函数
function setupImageAnalysisListeners() {
    const imageInput = document.querySelector('.image-file-input');
    const imagePreview = document.querySelector('.image-preview');
    const urlInput = document.querySelector('.image-url-input');
    const quickQuestions = document.querySelectorAll('.quick-question');
    const promptInput = document.querySelector('.ai-prompt-input');

    if (imageInput) {
        imageInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                displayImagePreview(file, imagePreview);
            }
        });
    }

    if (urlInput) {
        urlInput.addEventListener('input', (e) => {
            const url = e.target.value.trim();
            if (url) {
                displayImagePreview(url, imagePreview);
            }
        });
    }

    quickQuestions.forEach(btn => {
        btn.addEventListener('click', () => {
            const question = btn.dataset.question;
            if (promptInput) {
                promptInput.value = question;
            }
        });
    });
}

function displayImagePreview(source, container) {
    if (!container) return;

    container.innerHTML = '';
    const img = document.createElement('img');
    img.style.maxWidth = '100%';
    img.style.maxHeight = '200px';
    img.style.borderRadius = '8px';

    if (typeof source === 'string') {
        // URL
        img.src = source;
    } else {
        // File
        const reader = new FileReader();
        reader.onload = (e) => {
            img.src = e.target.result;
        };
        reader.readAsDataURL(source);
    }

    container.appendChild(img);
}

async function processImageAnalysis() {
    const imageInput = document.querySelector('.image-file-input');
    const urlInput = document.querySelector('.image-url-input');
    const promptInput = document.querySelector('.ai-prompt-input');
    const responseSection = document.querySelector('.ai-response-section');
    const analysisText = document.querySelector('.analysis-text');

    let imageSource = null;
    if (imageInput.files.length > 0) {
        imageSource = imageInput.files[0];
    } else if (urlInput.value.trim()) {
        imageSource = urlInput.value.trim();
    }

    if (!imageSource) {
        showNotification('请上传图片或输入图片URL', 'warning');
        return;
    }

    const question = promptInput.value.trim() || '请描述这张图片';

    try {
        // 显示加载状态和进度指示器
        showLoadingState(responseSection, '正在分析图片，请稍候...', 'analysis');
        showNotification('正在分析图片，请稍候...', 'info');

        const analysis = await window.pollinationsService.analyzeImage(imageSource, question);

        // 隐藏加载状态
        hideLoadingState(responseSection);

        if (analysisText) {
            analysisText.textContent = analysis;
        }

        if (responseSection) {
            responseSection.style.display = 'block';
        }

        setupAnalysisActions(analysis);
        showNotification('图片分析完成！', 'success');

    } catch (error) {
        console.error('图片分析失败:', error);
        hideLoadingState(responseSection);
        showNotification(`图片分析失败: ${error.message}`, 'error');
    }
}

function setupAnalysisActions(analysis) {
    const copyBtn = document.querySelector('.copy-analysis');
    const insertBtn = document.querySelector('.insert-analysis');

    if (copyBtn) {
        copyBtn.onclick = () => {
            navigator.clipboard.writeText(analysis).then(() => {
                showNotification('分析结果已复制到剪贴板', 'success');
            });
        };
    }

    if (insertBtn) {
        insertBtn.onclick = () => {
            const success = insertTextToEditor(analysis);
            if (success) {
                closeModal();
                showNotification('分析结果已插入到编辑器', 'success');
            }
            // 如果失败，不关闭对话框，让用户可以重试
        };
    }
}
