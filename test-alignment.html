<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏对齐测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .alignment-demo {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-toolbar {
            width: 60px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .demo-header {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
            background: #f8fafc;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            min-height: 80px;
            justify-content: center;
        }
        .demo-icon {
            font-size: 1.5rem;
            color: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            text-align: center;
        }
        .demo-toggle {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            color: #64748b;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            margin: 0 auto;
            align-self: center;
        }
        .demo-tools {
            padding: 0.5rem 0.4rem;
            display: flex;
            flex-direction: column;
            gap: 0.4rem;
            align-items: center;
        }
        .demo-tool-item {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: #64748b;
            margin: 1px;
        }
        .comparison {
            display: flex;
            gap: 30px;
            justify-content: center;
            margin: 30px 0;
        }
        .comparison-item {
            text-align: center;
        }
        .comparison-label {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 5px 10px;
            border-radius: 4px;
        }
        .before {
            background: #ffebee;
            color: #c62828;
        }
        .after {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .center-line {
            position: relative;
        }
        .center-line::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 1px;
            background: #ff5722;
            transform: translateX(-50%);
            z-index: 10;
        }
        .instructions {
            background: #fff3e0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ff9800;
            margin-top: 20px;
        }
        .open-main-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px auto;
            display: block;
            text-decoration: none;
        }
        .open-main-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 工具栏居中对齐测试</h1>
        
        <div class="test-info">
            <strong>测试目标：</strong>确保收缩模式下工具图标🛠️和收缩按钮完全居中对齐
        </div>

        <div class="comparison">
            <div class="comparison-item">
                <div class="comparison-label before">修复前（可能偏左）</div>
                <div class="demo-toolbar">
                    <div class="demo-header">
                        <div class="demo-icon" style="justify-content: flex-start; padding-left: 8px;">🛠️</div>
                        <div class="demo-toggle" style="margin-left: 8px;">‹</div>
                    </div>
                    <div class="demo-tools">
                        <div class="demo-tool-item">🧮</div>
                        <div class="demo-tool-item">🎨</div>
                        <div class="demo-tool-item">🤖</div>
                    </div>
                </div>
            </div>

            <div class="comparison-item">
                <div class="comparison-label after">修复后（完全居中）</div>
                <div class="demo-toolbar center-line">
                    <div class="demo-header">
                        <div class="demo-icon">🛠️</div>
                        <div class="demo-toggle">‹</div>
                    </div>
                    <div class="demo-tools">
                        <div class="demo-tool-item">🧮</div>
                        <div class="demo-tool-item">🎨</div>
                        <div class="demo-tool-item">🤖</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 测试步骤：</h3>
            <ol>
                <li>点击下方按钮打开主应用</li>
                <li>找到右侧工具栏的收缩按钮（向右箭头 ›）</li>
                <li>点击收缩按钮，使工具栏进入收缩模式</li>
                <li>观察工具图标🛠️和收缩按钮‹是否完全居中对齐</li>
                <li>对比上方的"修复后"示例，验证对齐效果</li>
            </ol>
            
            <h3>🔧 修复内容：</h3>
            <ul>
                <li><strong>工具图标居中：</strong>使用 <code>display: block</code> 和 <code>text-align: center</code> 确保完全居中</li>
                <li><strong>隐藏文本：</strong>将 <code>toolbar-title-text</code> 改为 <code>display: none</code> 而不是仅设置宽度为0</li>
                <li><strong>容器优化：</strong>设置 <code>gap: 0</code> 和 <code>width: 100%</code> 确保无额外间距</li>
                <li><strong>收缩按钮：</strong>添加了 <code>margin: 0 auto</code> 和 <code>align-self: center</code></li>
                <li><strong>头部容器：</strong>使用 <code>align-items: center</code> 进行垂直居中</li>
            </ul>
        </div>

        <a href="index.html" class="open-main-btn">打开主应用进行测试</a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 工具栏对齐测试页面已加载');
            console.log('📐 请按照测试步骤验证居中对齐效果');
        });
    </script>
</body>
</html>
