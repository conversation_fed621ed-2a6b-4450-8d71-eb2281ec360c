# 📋 执行SQL脚本指南

## 🎯 目标
执行 `database-fix.sql` 脚本来修复用户管理页面的数据库问题。

## 🚀 快速步骤

### 步骤1：登录Supabase控制台
1. 访问：https://supabase.com/dashboard
2. 使用您的账户登录
3. 选择您的项目：`bigzfjlaypptochqpxzu`

### 步骤2：打开SQL编辑器
1. 在左侧菜单中找到 **"SQL Editor"**
2. 点击进入SQL编辑器
3. 点击 **"New query"** 创建新查询

### 步骤3：执行修复脚本
1. **复制脚本内容**
   - 您已经选中了 `database-fix.sql` 的全部内容
   - 按 `Ctrl+C` (Windows) 或 `Cmd+C` (Mac) 复制

2. **粘贴到SQL编辑器**
   - 在SQL编辑器的查询框中按 `Ctrl+V` (Windows) 或 `Cmd+V` (Mac) 粘贴

3. **执行脚本**
   - 点击右下角的 **"Run"** 按钮
   - 等待执行完成

### 步骤4：验证结果
执行成功后，您应该看到类似这样的输出：
```
NOTICE: 修复完成: 找到 5 个关键表, 3 个关键字段
```

## ✅ 执行后验证

### 1. 刷新用户管理页面
- 返回 `user-management.html`
- 刷新页面（F5）
- 检查是否正常加载用户列表

### 2. 测试邀请功能
- 点击"邀请管理"标签
- 应该能正常显示邀请列表（即使是空的）

### 3. 运行数据库检查
- 访问 `database-check.html`
- 运行自动检查
- 确认所有项目都显示为"存在"

## 🔍 常见问题

### Q: 执行时出现权限错误
**A:** 确保您是项目的所有者或管理员，有数据库修改权限。

### Q: 某些语句执行失败
**A:** 这是正常的，脚本使用了 `IF NOT EXISTS` 和 `IF EXISTS` 来避免重复创建。

### Q: 执行后页面还是有问题
**A:** 
1. 清除浏览器缓存
2. 检查浏览器控制台的错误信息
3. 运行 `database-check.html` 进行全面检查

## 📱 移动端用户
如果您在移动设备上：
1. 建议使用电脑访问Supabase控制台
2. 或者联系有电脑的团队成员帮助执行

## 🆘 需要帮助？
如果执行过程中遇到问题：
1. 截图错误信息
2. 记录执行到哪一步
3. 检查网络连接是否稳定

---

**执行完成后，您的多用户协作功能就可以正常使用了！** 🎉
