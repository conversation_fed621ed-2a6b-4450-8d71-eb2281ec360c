#!/bin/bash

# AI增强学术专著编写系统 - 数据备份脚本
# 版本: 1.0

set -e

# 配置
BACKUP_DIR="/opt/backups/llm-book-system"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    mkdir -p $BACKUP_DIR/{database,storage,config,logs}
    log_info "备份目录已创建: $BACKUP_DIR"
}

# 备份数据库
backup_database() {
    log_info "开始备份数据库..."
    
    # 获取数据库连接信息
    source .env
    
    # 备份数据库
    docker-compose exec -T postgres pg_dump \
        -U $POSTGRES_USER \
        -d $POSTGRES_DB \
        --no-owner \
        --no-privileges \
        --clean \
        --if-exists > $BACKUP_DIR/database/database_$DATE.sql
    
    # 压缩备份文件
    gzip $BACKUP_DIR/database/database_$DATE.sql
    
    log_success "数据库备份完成: database_$DATE.sql.gz"
}

# 备份存储文件
backup_storage() {
    log_info "开始备份存储文件..."
    
    if [ -d "volumes/storage_data" ]; then
        tar -czf $BACKUP_DIR/storage/storage_$DATE.tar.gz \
            -C volumes storage_data
        log_success "存储文件备份完成: storage_$DATE.tar.gz"
    else
        log_warning "存储目录不存在，跳过存储备份"
    fi
}

# 备份配置文件
backup_config() {
    log_info "开始备份配置文件..."
    
    tar -czf $BACKUP_DIR/config/config_$DATE.tar.gz \
        .env \
        docker-compose.yml \
        nginx/ \
        database-schema.sql \
        seed-data.sql \
        scripts/ \
        --exclude='scripts/*.log'
    
    log_success "配置文件备份完成: config_$DATE.tar.gz"
}

# 备份日志文件
backup_logs() {
    log_info "开始备份日志文件..."
    
    if [ -d "volumes/logs" ]; then
        tar -czf $BACKUP_DIR/logs/logs_$DATE.tar.gz \
            -C volumes logs
        log_success "日志文件备份完成: logs_$DATE.tar.gz"
    else
        log_warning "日志目录不存在，跳过日志备份"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份文件..."
    
    find $BACKUP_DIR -type f -mtime +$RETENTION_DAYS -delete
    
    log_success "旧备份文件清理完成"
}

# 生成备份报告
generate_backup_report() {
    local report_file="$BACKUP_DIR/backup_report_$DATE.txt"
    
    cat > $report_file << EOF
AI增强学术专著编写系统备份报告
=====================================

备份时间: $(date)
备份目录: $BACKUP_DIR

备份文件:
$(ls -lh $BACKUP_DIR/database/database_$DATE.sql.gz 2>/dev/null || echo "数据库备份: 失败")
$(ls -lh $BACKUP_DIR/storage/storage_$DATE.tar.gz 2>/dev/null || echo "存储备份: 跳过")
$(ls -lh $BACKUP_DIR/config/config_$DATE.tar.gz 2>/dev/null || echo "配置备份: 失败")
$(ls -lh $BACKUP_DIR/logs/logs_$DATE.tar.gz 2>/dev/null || echo "日志备份: 跳过")

磁盘使用情况:
$(df -h $BACKUP_DIR)

系统状态:
$(docker-compose ps)
EOF
    
    log_success "备份报告已生成: $report_file"
}

# 验证备份完整性
verify_backup() {
    log_info "验证备份完整性..."
    
    local errors=0
    
    # 检查数据库备份
    if [ -f "$BACKUP_DIR/database/database_$DATE.sql.gz" ]; then
        if gzip -t "$BACKUP_DIR/database/database_$DATE.sql.gz"; then
            log_success "数据库备份文件完整"
        else
            log_error "数据库备份文件损坏"
            errors=$((errors + 1))
        fi
    else
        log_error "数据库备份文件不存在"
        errors=$((errors + 1))
    fi
    
    # 检查配置备份
    if [ -f "$BACKUP_DIR/config/config_$DATE.tar.gz" ]; then
        if tar -tzf "$BACKUP_DIR/config/config_$DATE.tar.gz" >/dev/null 2>&1; then
            log_success "配置备份文件完整"
        else
            log_error "配置备份文件损坏"
            errors=$((errors + 1))
        fi
    else
        log_error "配置备份文件不存在"
        errors=$((errors + 1))
    fi
    
    if [ $errors -eq 0 ]; then
        log_success "备份验证通过"
        return 0
    else
        log_error "备份验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 发送备份通知（可选）
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以集成邮件通知、Slack通知等
    # 示例：发送邮件通知
    if command -v mail &> /dev/null && [ -n "$BACKUP_EMAIL" ]; then
        echo "$message" | mail -s "备份通知: $status" "$BACKUP_EMAIL"
        log_info "备份通知已发送到: $BACKUP_EMAIL"
    fi
}

# 主函数
main() {
    echo "=== AI增强学术专著编写系统备份脚本 ==="
    echo "开始时间: $(date)"
    echo ""
    
    # 检查Docker服务状态
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Docker服务未运行，请先启动服务"
        exit 1
    fi
    
    create_backup_dir
    
    # 执行备份
    backup_database
    backup_storage
    backup_config
    backup_logs
    
    # 验证备份
    if verify_backup; then
        cleanup_old_backups
        generate_backup_report
        
        local success_msg="备份成功完成于 $(date)"
        log_success "$success_msg"
        send_notification "成功" "$success_msg"
    else
        local error_msg="备份过程中发现错误，请检查日志"
        log_error "$error_msg"
        send_notification "失败" "$error_msg"
        exit 1
    fi
    
    echo ""
    echo "备份位置: $BACKUP_DIR"
    echo "结束时间: $(date)"
}

# 显示使用帮助
show_help() {
    cat << EOF
AI增强学术专著编写系统备份脚本

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -d, --dir DIR       指定备份目录 (默认: /opt/backups/llm-book-system)
  -r, --retention N   备份保留天数 (默认: 30)
  -e, --email EMAIL   备份通知邮箱

示例:
  $0                                    # 使用默认设置备份
  $0 -d /custom/backup/path            # 指定备份目录
  $0 -r 7                              # 保留7天的备份
  $0 -e <EMAIL>              # 发送通知到指定邮箱

环境变量:
  BACKUP_EMAIL        备份通知邮箱地址
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -r|--retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        -e|--email)
            BACKUP_EMAIL="$2"
            shift 2
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@"
