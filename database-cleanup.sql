-- 数据库清理和优化脚本
-- 用于修复chapters表中outline_id为null的问题以及其他数据一致性问题

-- ============================================
-- 第一步：数据分析和备份
-- ============================================

-- 1. 分析当前数据状况
SELECT 
    'chapters表总记录数' as description,
    COUNT(*) as count
FROM chapters
UNION ALL
SELECT 
    'outline_id为null的记录数' as description,
    COUNT(*) as count
FROM chapters 
WHERE outline_id IS NULL
UNION ALL
SELECT 
    'outline_id不为null的记录数' as description,
    COUNT(*) as count
FROM chapters 
WHERE outline_id IS NOT NULL
UNION ALL
SELECT 
    'outlines表总记录数' as description,
    COUNT(*) as count
FROM outlines;

-- 2. 查看有问题的章节记录
SELECT 
    id,
    title,
    outline_id,
    project_id,
    word_count,
    status,
    created_at,
    updated_at
FROM chapters 
WHERE outline_id IS NULL
ORDER BY created_at DESC;

-- 3. 查看大纲记录
SELECT 
    id,
    title,
    level,
    sort_order,
    project_id,
    created_at
FROM outlines 
ORDER BY project_id, sort_order;

-- ============================================
-- 第二步：数据清理
-- ============================================

-- 1. 删除重复的章节记录（保留最新的）
WITH duplicate_chapters AS (
    SELECT 
        id,
        title,
        project_id,
        ROW_NUMBER() OVER (
            PARTITION BY title, project_id 
            ORDER BY updated_at DESC, created_at DESC
        ) as rn
    FROM chapters
)
DELETE FROM chapters 
WHERE id IN (
    SELECT id FROM duplicate_chapters WHERE rn > 1
);

-- 2. 删除没有对应项目的孤立章节
DELETE FROM chapters 
WHERE project_id NOT IN (SELECT id FROM projects);

-- 3. 删除没有对应项目的孤立大纲
DELETE FROM outlines 
WHERE project_id NOT IN (SELECT id FROM projects);

-- ============================================
-- 第三步：修复outline_id关联
-- ============================================

-- 1. 尝试通过标题匹配修复outline_id
UPDATE chapters 
SET outline_id = (
    SELECT o.id 
    FROM outlines o 
    WHERE o.title = chapters.title 
    AND o.project_id = chapters.project_id
    AND o.level > 0  -- 只匹配章节级别的大纲
    LIMIT 1
)
WHERE outline_id IS NULL
AND EXISTS (
    SELECT 1 
    FROM outlines o 
    WHERE o.title = chapters.title 
    AND o.project_id = chapters.project_id
    AND o.level > 0
);

-- 2. 为仍然没有outline_id的章节创建对应的大纲记录
INSERT INTO outlines (id, project_id, title, level, sort_order, description, status, created_by, created_at, updated_at)
SELECT 
    gen_random_uuid() as id,
    c.project_id,
    c.title,
    1 as level,  -- 设为章节级别
    COALESCE((
        SELECT MAX(sort_order) + 1 
        FROM outlines o2 
        WHERE o2.project_id = c.project_id
    ), 1) as sort_order,
    '自动生成的大纲项' as description,
    'planned' as status,
    c.created_by,
    c.created_at,
    c.updated_at
FROM chapters c
WHERE c.outline_id IS NULL
AND NOT EXISTS (
    SELECT 1 
    FROM outlines o 
    WHERE o.title = c.title 
    AND o.project_id = c.project_id
);

-- 3. 更新章节的outline_id为新创建的大纲ID
UPDATE chapters 
SET outline_id = (
    SELECT o.id 
    FROM outlines o 
    WHERE o.title = chapters.title 
    AND o.project_id = chapters.project_id
    AND o.level > 0
    ORDER BY o.created_at DESC
    LIMIT 1
)
WHERE outline_id IS NULL;

-- ============================================
-- 第四步：数据一致性检查和修复
-- ============================================

-- 1. 确保所有章节都有有效的project_id
UPDATE chapters 
SET project_id = (
    SELECT id FROM projects LIMIT 1
)
WHERE project_id IS NULL 
AND EXISTS (SELECT 1 FROM projects);

-- 2. 确保所有大纲都有有效的project_id
UPDATE outlines 
SET project_id = (
    SELECT id FROM projects LIMIT 1
)
WHERE project_id IS NULL 
AND EXISTS (SELECT 1 FROM projects);

-- 3. 修复章节状态
UPDATE chapters 
SET status = 'draft' 
WHERE status IS NULL OR status = '';

-- 4. 修复大纲状态
UPDATE outlines 
SET status = 'planned' 
WHERE status IS NULL OR status = '';

-- 5. 修复字数统计
UPDATE chapters 
SET word_count = 0 
WHERE word_count IS NULL;

-- ============================================
-- 第五步：优化和索引
-- ============================================

-- 1. 重建索引（如果需要）
REINDEX INDEX IF EXISTS idx_chapters_outline_id;
REINDEX INDEX IF EXISTS idx_chapters_project_id;
REINDEX INDEX IF EXISTS idx_outlines_project_id;

-- 2. 更新表统计信息
ANALYZE chapters;
ANALYZE outlines;
ANALYZE projects;

-- ============================================
-- 第六步：验证修复结果
-- ============================================

-- 1. 验证outline_id修复情况
SELECT 
    'chapters表总记录数' as description,
    COUNT(*) as count
FROM chapters
UNION ALL
SELECT 
    'outline_id为null的记录数（应该为0）' as description,
    COUNT(*) as count
FROM chapters 
WHERE outline_id IS NULL
UNION ALL
SELECT 
    'outline_id不为null的记录数' as description,
    COUNT(*) as count
FROM chapters 
WHERE outline_id IS NOT NULL;

-- 2. 验证章节和大纲的关联
SELECT 
    c.id as chapter_id,
    c.title as chapter_title,
    c.outline_id,
    o.title as outline_title,
    o.level,
    CASE 
        WHEN o.id IS NULL THEN '❌ 大纲记录不存在'
        WHEN c.title = o.title THEN '✅ 标题匹配'
        ELSE '⚠️ 标题不匹配'
    END as status
FROM chapters c
LEFT JOIN outlines o ON c.outline_id = o.id
ORDER BY c.project_id, c.created_at;

-- 3. 检查数据完整性
SELECT 
    p.id as project_id,
    p.title as project_title,
    COUNT(DISTINCT o.id) as outline_count,
    COUNT(DISTINCT c.id) as chapter_count,
    COUNT(DISTINCT CASE WHEN c.outline_id IS NOT NULL THEN c.id END) as linked_chapter_count
FROM projects p
LEFT JOIN outlines o ON p.id = o.project_id
LEFT JOIN chapters c ON p.id = c.project_id
GROUP BY p.id, p.title
ORDER BY p.created_at;

-- ============================================
-- 清理完成报告
-- ============================================

SELECT 
    '🎉 数据库清理完成' as message,
    NOW() as completed_at;
