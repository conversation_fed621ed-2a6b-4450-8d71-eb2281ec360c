// 用户管理系统
class UserManagement {
    constructor() {
        this.currentProject = null;
        this.currentUser = null;
        this.users = [];
        this.invitations = [];
        this.isLoading = false;
    }

    // 初始化
    async initialize() {
        await this.checkAuthentication();
        await this.loadCurrentProject();
        await this.loadUsers();
        this.setupEventListeners();
    }

    // 检查用户认证
    async checkAuthentication() {
        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            window.location.href = 'auth.html';
            return;
        }
        this.currentUser = user;
    }

    // 加载当前项目
    async loadCurrentProject() {
        const projectId = localStorage.getItem('currentProjectId');
        if (!projectId) {
            window.location.href = 'project-management.html';
            return;
        }

        try {
            const { data, error } = await supabaseManager.supabase
                .from('projects')
                .select('*')
                .eq('id', projectId)
                .single();

            if (error) throw error;
            this.currentProject = data;

            // 检查用户权限
            await this.checkUserPermissions();
        } catch (error) {
            console.error('加载项目失败:', error);
            this.showNotification('加载项目失败', 'error');
            window.location.href = 'project-management.html';
        }
    }

    // 检查用户权限
    async checkUserPermissions() {
        try {
            const { data, error } = await supabaseManager.supabase
                .from('project_members')
                .select('role')
                .eq('project_id', this.currentProject.id)
                .eq('user_id', this.currentUser.id)
                .single();

            if (error) throw error;

            // 只有项目所有者和管理员可以访问用户管理
            if (!['owner', 'admin'].includes(data.role)) {
                this.showNotification('您没有权限访问用户管理', 'error');
                window.location.href = 'index.html';
                return;
            }

            this.currentUserRole = data.role;
        } catch (error) {
            console.error('检查权限失败:', error);
            this.showNotification('权限检查失败', 'error');
            window.location.href = 'project-management.html';
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 搜索和过滤
        document.getElementById('user-search').addEventListener('input', () => {
            this.filterUsers();
        });

        document.getElementById('role-filter').addEventListener('change', () => {
            this.filterUsers();
        });

        document.getElementById('status-filter').addEventListener('change', () => {
            this.filterUsers();
        });
    }

    // 加载用户列表
    async loadUsers() {
        this.isLoading = true;
        this.showLoadingState();

        try {
            // 获取项目成员 - 使用基础字段避免字段不存在的问题
            const { data: members, error } = await supabaseManager.supabase
                .from('project_members')
                .select(`
                    *,
                    user_profiles (
                        id,
                        username,
                        full_name,
                        email,
                        avatar_url,
                        institution,
                        department,
                        created_at
                    )
                `)
                .eq('project_id', this.currentProject.id);

            if (error) throw error;

            // 安全地映射用户数据，处理可能缺失的字段
            this.users = members.map(member => ({
                ...member.user_profiles,
                project_role: member.role || 'author',
                project_status: member.status || 'active',
                joined_at: member.joined_at || member.created_at,
                // 添加默认值以防字段缺失
                institution: member.user_profiles?.institution || '未设置',
                department: member.user_profiles?.department || '未设置'
            }));

            this.renderUsers();
            this.updateStatistics();

        } catch (error) {
            console.error('加载用户失败:', error);

            // 如果是字段不存在的错误，提供更友好的错误信息
            if (error.message && error.message.includes('does not exist')) {
                this.showNotification('数据库结构需要更新，请联系管理员或运行数据库修复脚本', 'error');
                this.showDatabaseFixSuggestion();
            } else {
                this.showNotification('加载用户失败: ' + error.message, 'error');
            }
        } finally {
            this.isLoading = false;
        }
    }

    // 显示数据库修复建议
    showDatabaseFixSuggestion() {
        const usersGrid = document.getElementById('users-grid');
        if (!usersGrid) return;

        usersGrid.innerHTML = `
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; background: #fef3c7; border: 1px solid #fde68a; border-radius: 12px; color: #92400e;">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 15px;"></i>
                <h3>数据库结构需要更新</h3>
                <p>检测到数据库缺少多用户协作功能所需的字段。</p>
                <div style="margin-top: 20px;">
                    <a href="database-check.html" class="btn btn-primary" style="margin-right: 10px;">
                        <i class="fas fa-tools"></i> 数据库检查与修复
                    </a>
                    <button class="btn btn-secondary" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> 重新加载
                    </button>
                </div>
                <div style="margin-top: 15px; font-size: 0.9rem; opacity: 0.8;">
                    <p>或者在Supabase控制台执行 database-fix.sql 脚本</p>
                </div>
            </div>
        `;
    }

    // 渲染用户列表
    renderUsers() {
        const usersGrid = document.getElementById('users-grid');
        if (!usersGrid) return;

        if (this.users.length === 0) {
            usersGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #6b7280;">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <p>暂无项目成员</p>
                </div>
            `;
            return;
        }

        usersGrid.innerHTML = this.users.map(user => this.createUserCard(user)).join('');
    }

    // 创建用户卡片
    createUserCard(user) {
        const initials = user.full_name ? user.full_name.substring(0, 2).toUpperCase() : 'U';
        const roleClass = `role-${user.project_role}`;
        const statusIcon = user.project_status === 'active' ? 'check-circle' : 'clock';
        const statusColor = user.project_status === 'active' ? '#10b981' : '#f59e0b';

        return `
            <div class="user-card" data-user-id="${user.id}">
                <div class="user-header">
                    <div class="user-avatar">
                        ${user.avatar_url ? `<img src="${user.avatar_url}" alt="${user.full_name}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` : initials}
                    </div>
                    <div class="user-info">
                        <h3>${user.full_name}</h3>
                        <p>@${user.username}</p>
                    </div>
                </div>
                
                <div class="user-details">
                    <div class="detail-row">
                        <span class="detail-label">角色:</span>
                        <span class="role-badge ${roleClass}">${this.getRoleDisplayName(user.project_role)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">状态:</span>
                        <span class="detail-value">
                            <i class="fas fa-${statusIcon}" style="color: ${statusColor};"></i>
                            ${this.getStatusDisplayName(user.project_status)}
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">机构:</span>
                        <span class="detail-value">${user.institution || '未设置'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">部门:</span>
                        <span class="detail-value">${user.department || '未设置'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">加入时间:</span>
                        <span class="detail-value">${new Date(user.joined_at).toLocaleDateString()}</span>
                    </div>
                </div>
                
                <div class="user-actions">
                    ${this.canEditUser(user) ? `
                        <button class="btn btn-small btn-edit" onclick="editUser('${user.id}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                    ` : ''}
                    ${this.canRemoveUser(user) ? `
                        <button class="btn btn-small btn-delete" onclick="removeUser('${user.id}')">
                            <i class="fas fa-trash"></i> 移除
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // 获取角色显示名称
    getRoleDisplayName(role) {
        const roleNames = {
            'owner': '项目所有者',
            'admin': '管理员',
            'editor': '编辑者',
            'author': '作者',
            'reviewer': '审阅者'
        };
        return roleNames[role] || role;
    }

    // 获取状态显示名称
    getStatusDisplayName(status) {
        const statusNames = {
            'active': '活跃',
            'inactive': '非活跃',
            'pending': '待确认',
            'invited': '已邀请'
        };
        return statusNames[status] || status;
    }

    // 检查是否可以编辑用户
    canEditUser(user) {
        // 项目所有者可以编辑所有人，管理员可以编辑除所有者外的所有人
        if (this.currentUserRole === 'owner') return true;
        if (this.currentUserRole === 'admin' && user.project_role !== 'owner') return true;
        return false;
    }

    // 检查是否可以移除用户
    canRemoveUser(user) {
        // 不能移除自己，不能移除项目所有者
        if (user.id === this.currentUser.id) return false;
        if (user.project_role === 'owner') return false;
        return this.canEditUser(user);
    }

    // 过滤用户
    filterUsers() {
        const searchTerm = document.getElementById('user-search').value.toLowerCase();
        const roleFilter = document.getElementById('role-filter').value;
        const statusFilter = document.getElementById('status-filter').value;

        const userCards = document.querySelectorAll('.user-card');
        
        userCards.forEach(card => {
            const userId = card.dataset.userId;
            const user = this.users.find(u => u.id === userId);
            
            if (!user) return;

            const matchesSearch = !searchTerm || 
                user.full_name.toLowerCase().includes(searchTerm) ||
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm);

            const matchesRole = !roleFilter || user.project_role === roleFilter;
            const matchesStatus = !statusFilter || user.project_status === statusFilter;

            if (matchesSearch && matchesRole && matchesStatus) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    // 显示加载状态
    showLoadingState() {
        const usersGrid = document.getElementById('users-grid');
        if (usersGrid) {
            usersGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #6b7280;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <p>加载中...</p>
                </div>
            `;
        }
    }

    // 加载邀请列表
    async loadInvitations() {
        try {
            // 首先尝试带关联查询的方式
            let { data: invitations, error } = await supabaseManager.supabase
                .from('user_invitations')
                .select(`
                    *,
                    invited_by_profile:user_profiles!invited_by (
                        full_name
                    )
                `)
                .eq('project_id', this.currentProject.id)
                .order('created_at', { ascending: false });

            // 如果关联查询失败，尝试简单查询
            if (error && error.code === 'PGRST200') {
                console.log('外键关系缺失，使用简单查询...');

                const { data: simpleInvitations, error: simpleError } = await supabaseManager.supabase
                    .from('user_invitations')
                    .select('*')
                    .eq('project_id', this.currentProject.id)
                    .order('created_at', { ascending: false });

                if (simpleError) throw simpleError;

                // 手动获取邀请人信息
                invitations = await this.enrichInvitationsWithUserInfo(simpleInvitations);
            } else if (error) {
                throw error;
            }

            this.invitations = invitations || [];
            this.renderInvitations();
            this.updateInvitationStats();

        } catch (error) {
            console.error('加载邀请失败:', error);

            // 检查是否是表不存在的错误
            if (error.message && (error.message.includes('relation') || error.message.includes('does not exist'))) {
                this.showInvitationTableMissing();
            } else if (error.code === 'PGRST200') {
                this.showForeignKeyMissing();
            } else {
                this.showNotification('加载邀请失败: ' + error.message, 'error');
            }
        }
    }

    // 手动获取邀请人信息
    async enrichInvitationsWithUserInfo(invitations) {
        const enrichedInvitations = [];

        for (const invitation of invitations) {
            let inviterName = '未知';

            if (invitation.invited_by) {
                try {
                    const { data: inviter } = await supabaseManager.supabase
                        .from('user_profiles')
                        .select('full_name')
                        .eq('id', invitation.invited_by)
                        .single();

                    if (inviter) {
                        inviterName = inviter.full_name;
                    }
                } catch (e) {
                    console.log('获取邀请人信息失败:', e);
                }
            }

            enrichedInvitations.push({
                ...invitation,
                invited_by_profile: { full_name: inviterName }
            });
        }

        return enrichedInvitations;
    }

    // 显示外键缺失的提示
    showForeignKeyMissing() {
        const invitationsList = document.getElementById('invitations-list');
        if (!invitationsList) return;

        invitationsList.innerHTML = `
            <div style="text-align: center; padding: 40px; background: #fef3c7; border: 1px solid #fde68a; border-radius: 12px; color: #92400e;">
                <i class="fas fa-link" style="font-size: 3rem; margin-bottom: 15px;"></i>
                <h3>数据库外键关系缺失</h3>
                <p>检测到 user_invitations 表缺少与 user_profiles 表的外键关系。</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="userManager.fixForeignKeys()" style="margin-right: 10px;">
                        <i class="fas fa-wrench"></i> 自动修复外键
                    </button>
                    <button class="btn btn-secondary" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> 重新加载
                    </button>
                </div>
                <div style="margin-top: 15px; font-size: 0.9rem; opacity: 0.8;">
                    <p>或者在Supabase控制台执行 fix-foreign-keys.sql 脚本</p>
                </div>
            </div>
        `;

        // 同时更新统计数据
        document.getElementById('pending-invitations').textContent = '?';
        document.getElementById('accepted-invitations').textContent = '?';
        document.getElementById('expired-invitations').textContent = '?';
    }

    // 尝试修复外键关系
    async fixForeignKeys() {
        this.showNotification('正在尝试修复外键关系...', 'info');

        try {
            // 这里只能提供指导，实际的外键创建需要在SQL编辑器中执行
            this.showNotification('请在Supabase控制台的SQL编辑器中执行 fix-foreign-keys.sql 脚本', 'warning');

            // 提供复制到剪贴板的功能
            const sqlScript = `
ALTER TABLE public.user_invitations
ADD CONSTRAINT user_invitations_invited_by_fkey
FOREIGN KEY (invited_by) REFERENCES public.user_profiles(id) ON DELETE CASCADE;

ALTER TABLE public.user_invitations
ADD CONSTRAINT user_invitations_project_id_fkey
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
            `;

            if (navigator.clipboard) {
                await navigator.clipboard.writeText(sqlScript);
                this.showNotification('修复脚本已复制到剪贴板，请在Supabase SQL编辑器中粘贴执行', 'success');
            }

        } catch (error) {
            console.error('修复外键失败:', error);
            this.showNotification('修复失败，请手动执行 fix-foreign-keys.sql 脚本', 'error');
        }
    }

    // 创建新用户（简化版本）
    async createUser(userData) {
        try {
            // 验证邮箱格式
            if (!this.isValidEmail(userData.email)) {
                throw new Error('邮箱格式无效');
            }

            console.log('开始创建用户:', userData.email);

            // 直接使用数据库插入方式创建用户（绕过 Auth 限制）
            const userId = crypto.randomUUID();
            console.log('使用直接插入方式创建用户:', userData.email);

            // 1. 创建用户配置
            const { data: profile, error: profileError } = await supabaseManager.supabase
                .from('user_profiles')
                .insert({
                    id: userId,
                    username: userData.username,
                    full_name: userData.full_name,
                    email: userData.email,
                    institution: userData.institution || '',
                    department: userData.department || '',
                    bio: userData.bio || ''
                })
                .select()
                .single();

            if (profileError) {
                console.error('创建用户配置失败:', profileError);
                throw new Error(`创建用户配置失败: ${profileError.message}`);
            }

            // 2. 添加到项目成员
            const currentUser = await supabaseManager.getCurrentUser();
            const { error: memberError } = await supabaseManager.supabase
                .from('project_members')
                .insert({
                    project_id: this.currentProject.id,
                    user_id: userId,
                    role: userData.role,
                    status: 'active',
                    invited_by: currentUser.id
                });

            if (memberError) {
                console.error('添加项目成员失败:', memberError);
                throw new Error(`添加项目成员失败: ${memberError.message}`);
            }

            return {
                success: true,
                user: profile,
                message: `用户 ${userData.full_name} 创建成功！（注意：该用户需要通过邀请链接设置登录密码）`,
                method: 'direct_insert'
            };

        } catch (error) {
            console.error('创建用户失败:', error);

            let errorMessage = '创建用户失败';

            if (error.message.includes('邮箱格式无效')) {
                errorMessage = '邮箱格式无效，请检查邮箱地址';
            } else if (error.message.includes('该邮箱已被注册')) {
                errorMessage = '该邮箱已被注册，请使用其他邮箱地址';
            } else if (error.message.includes('用户名已存在')) {
                errorMessage = '用户名已存在，请使用其他用户名';
            } else if (error.message.includes('invalid')) {
                errorMessage = '输入信息格式无效，请检查后重试';
            } else if (error.message.includes('duplicate')) {
                errorMessage = '用户信息重复，请检查邮箱和用户名';
            } else if (error.message) {
                errorMessage = error.message;
            }

            return {
                success: false,
                error: errorMessage
            };
        }
    }

    // 验证邮箱格式
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // 检查是否为测试邮箱域名（已禁用，现在支持所有域名）
    isTestEmailDomain(email) {
        // 现在允许所有域名，包括测试域名
        return false;
    }

    // 生成建议的邮箱域名
    getSuggestedEmailDomains() {
        return [
            'gmail.com',
            'outlook.com',
            'hotmail.com',
            'yahoo.com',
            '163.com',
            'qq.com',
            '126.com',
            'sina.com'
        ];
    }

    // 显示邀请表缺失的提示
    showInvitationTableMissing() {
        const invitationsList = document.getElementById('invitations-list');
        if (!invitationsList) return;

        invitationsList.innerHTML = `
            <div style="text-align: center; padding: 40px; background: #fef3c7; border: 1px solid #fde68a; border-radius: 12px; color: #92400e;">
                <i class="fas fa-database" style="font-size: 3rem; margin-bottom: 15px;"></i>
                <h3>邀请功能需要数据库更新</h3>
                <p>检测到数据库缺少 user_invitations 表。</p>
                <div style="margin-top: 20px;">
                    <a href="database-check.html" class="btn btn-primary" style="margin-right: 10px;">
                        <i class="fas fa-tools"></i> 数据库检查与修复
                    </a>
                    <button class="btn btn-secondary" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> 重新加载
                    </button>
                </div>
                <div style="margin-top: 15px; font-size: 0.9rem; opacity: 0.8;">
                    <p>请在Supabase控制台执行 database-fix.sql 脚本</p>
                </div>
            </div>
        `;

        // 同时更新统计数据
        document.getElementById('pending-invitations').textContent = '?';
        document.getElementById('accepted-invitations').textContent = '?';
        document.getElementById('expired-invitations').textContent = '?';
    }

    // 渲染邀请列表
    renderInvitations() {
        const invitationsList = document.getElementById('invitations-list');
        if (!invitationsList) return;

        if (this.invitations.length === 0) {
            invitationsList.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #6b7280;">
                    <i class="fas fa-envelope" style="font-size: 3rem; margin-bottom: 15px;"></i>
                    <p>暂无邀请记录</p>
                </div>
            `;
            return;
        }

        invitationsList.innerHTML = `
            <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f9fafb;">
                        <tr>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">邮箱</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">角色</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">状态</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">邀请人</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">邀请时间</th>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.invitations.map(invitation => this.createInvitationRow(invitation)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    // 创建邀请行
    createInvitationRow(invitation) {
        const statusClass = {
            'pending': 'background: #fef3c7; color: #92400e;',
            'accepted': 'background: #d1fae5; color: #065f46;',
            'expired': 'background: #fee2e2; color: #991b1b;',
            'cancelled': 'background: #f3f4f6; color: #6b7280;'
        };

        const isExpired = new Date(invitation.expires_at) < new Date();
        const actualStatus = isExpired && invitation.status === 'pending' ? 'expired' : invitation.status;

        return `
            <tr style="border-bottom: 1px solid #e5e7eb;">
                <td style="padding: 15px;">${invitation.email}</td>
                <td style="padding: 15px;">
                    <span class="role-badge role-${invitation.role}">${this.getRoleDisplayName(invitation.role)}</span>
                </td>
                <td style="padding: 15px;">
                    <span style="padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; ${statusClass[actualStatus]}">
                        ${this.getInvitationStatusName(actualStatus)}
                    </span>
                </td>
                <td style="padding: 15px;">${invitation.invited_by_profile?.full_name || '未知'}</td>
                <td style="padding: 15px;">${new Date(invitation.created_at).toLocaleDateString()}</td>
                <td style="padding: 15px;">
                    ${invitation.status === 'pending' && !isExpired ? `
                        <button class="btn btn-small btn-secondary" onclick="resendInvitation('${invitation.id}')" style="margin-right: 5px;">
                            <i class="fas fa-redo"></i> 重发
                        </button>
                        <button class="btn btn-small btn-delete" onclick="cancelInvitation('${invitation.id}')">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    ` : '-'}
                </td>
            </tr>
        `;
    }

    // 获取邀请状态名称
    getInvitationStatusName(status) {
        const statusNames = {
            'pending': '待处理',
            'accepted': '已接受',
            'expired': '已过期',
            'cancelled': '已取消'
        };
        return statusNames[status] || status;
    }

    // 更新邀请统计
    updateInvitationStats() {
        const pending = this.invitations.filter(inv => inv.status === 'pending' && new Date(inv.expires_at) >= new Date()).length;
        const accepted = this.invitations.filter(inv => inv.status === 'accepted').length;
        const expired = this.invitations.filter(inv => inv.status === 'pending' && new Date(inv.expires_at) < new Date()).length;

        document.getElementById('pending-invitations').textContent = pending;
        document.getElementById('accepted-invitations').textContent = accepted;
        document.getElementById('expired-invitations').textContent = expired;
    }

    // 加载角色权限矩阵
    loadRolesMatrix() {
        const rolesMatrix = document.getElementById('roles-matrix');

        const permissions = [
            { name: '项目管理', owner: true, admin: true, editor: false, author: false, reviewer: false },
            { name: '成员邀请', owner: true, admin: true, editor: false, author: false, reviewer: false },
            { name: '角色分配', owner: true, admin: true, editor: false, author: false, reviewer: false },
            { name: '大纲编辑', owner: true, admin: true, editor: true, author: false, reviewer: false },
            { name: '章节分配', owner: true, admin: true, editor: true, author: false, reviewer: false },
            { name: '章节编写', owner: true, admin: true, editor: true, author: true, reviewer: false },
            { name: '内容审核', owner: true, admin: true, editor: true, author: false, reviewer: false },
            { name: '评论反馈', owner: true, admin: true, editor: true, author: true, reviewer: true },
            { name: '文献管理', owner: true, admin: true, editor: true, author: true, reviewer: false },
            { name: '进度查看', owner: true, admin: true, editor: true, author: true, reviewer: true }
        ];

        rolesMatrix.innerHTML = `
            <div style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                <div style="padding: 20px; border-bottom: 1px solid #e5e7eb;">
                    <h3 style="margin: 0; color: #1f2937;">权限矩阵</h3>
                    <p style="margin: 10px 0 0 0; color: #6b7280;">不同角色的功能权限对照表</p>
                </div>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f9fafb;">
                        <tr>
                            <th style="padding: 15px; text-align: left; font-weight: 600; color: #374151;">功能</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #374151;">项目所有者</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #374151;">管理员</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #374151;">编辑者</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #374151;">作者</th>
                            <th style="padding: 15px; text-align: center; font-weight: 600; color: #374151;">审阅者</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${permissions.map(permission => `
                            <tr style="border-bottom: 1px solid #e5e7eb;">
                                <td style="padding: 15px; font-weight: 500;">${permission.name}</td>
                                <td style="padding: 15px; text-align: center;">
                                    <i class="fas fa-${permission.owner ? 'check' : 'times'}" style="color: ${permission.owner ? '#10b981' : '#ef4444'};"></i>
                                </td>
                                <td style="padding: 15px; text-align: center;">
                                    <i class="fas fa-${permission.admin ? 'check' : 'times'}" style="color: ${permission.admin ? '#10b981' : '#ef4444'};"></i>
                                </td>
                                <td style="padding: 15px; text-align: center;">
                                    <i class="fas fa-${permission.editor ? 'check' : 'times'}" style="color: ${permission.editor ? '#10b981' : '#ef4444'};"></i>
                                </td>
                                <td style="padding: 15px; text-align: center;">
                                    <i class="fas fa-${permission.author ? 'check' : 'times'}" style="color: ${permission.author ? '#10b981' : '#ef4444'};"></i>
                                </td>
                                <td style="padding: 15px; text-align: center;">
                                    <i class="fas fa-${permission.reviewer ? 'check' : 'times'}" style="color: ${permission.reviewer ? '#10b981' : '#ef4444'};"></i>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    // 加载统计信息
    loadStatistics() {
        // 这里可以加载更详细的统计信息
        this.updateStatistics();
    }

    // 更新统计信息
    updateStatistics() {
        const totalUsers = this.users.length;
        const activeUsers = this.users.filter(u => u.project_status === 'active').length;

        document.getElementById('total-users').textContent = totalUsers;
        document.getElementById('active-users').textContent = activeUsers;

        // 模拟在线用户数（实际应该从实时数据获取）
        document.getElementById('online-users').textContent = Math.floor(activeUsers * 0.6);

        // 模拟本月新增用户（实际应该从数据库统计）
        const thisMonth = this.users.filter(u => {
            const joinDate = new Date(u.joined_at);
            const now = new Date();
            return joinDate.getMonth() === now.getMonth() && joinDate.getFullYear() === now.getFullYear();
        }).length;
        document.getElementById('new-users-month').textContent = thisMonth;
    }

    // 显示通知
    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// 全局函数
function switchTab(tabName) {
    // 更新标签状态
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');

    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');

    // 加载对应内容
    switch(tabName) {
        case 'invitations':
            userManager.loadInvitations();
            break;
        case 'roles':
            userManager.loadRolesMatrix();
            break;
        case 'statistics':
            userManager.loadStatistics();
            break;
    }
}

function showCreateUserModal() {
    document.getElementById('create-user-modal').style.display = 'block';
}

function showInviteModal() {
    document.getElementById('invite-modal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 处理创建用户
async function handleCreateUser(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const userData = {
        username: formData.get('username'),
        full_name: formData.get('full_name'),
        email: formData.get('email'),
        password: formData.get('password'),
        institution: formData.get('institution'),
        department: formData.get('department'),
        role: formData.get('role'),
        bio: formData.get('bio'),
        send_welcome_email: formData.get('send_welcome_email') === 'on'
    };

    // 显示加载状态
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 创建中...';
    submitBtn.disabled = true;

    try {
        // 使用用户管理类的方法创建用户
        const result = await userManager.createUser(userData);

        if (result.success) {
            // 4. 发送欢迎邮件（如果选择）
            if (userData.send_welcome_email) {
                // 这里可以集成邮件服务
                console.log('发送欢迎邮件给:', userData.email);
            }

            // 成功提示
            userManager.showNotification(result.message, 'success');

            // 关闭模态框并重新加载用户列表
            closeModal('create-user-modal');
            event.target.reset();
            await userManager.loadUsers();
        } else {
            userManager.showNotification(result.error, 'error');
        }

    } catch (error) {
        console.error('创建用户失败:', error);
        userManager.showNotification('创建用户时发生未知错误', 'error');
    } finally {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

async function handleInviteUser(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const email = formData.get('email');
    const role = formData.get('role');
    const expiresDays = parseInt(formData.get('expires_days'));
    const message = formData.get('message');

    try {
        // 检查用户是否已存在
        const { data: existingUser } = await supabaseManager.supabase
            .from('user_profiles')
            .select('id')
            .eq('email', email)
            .single();

        if (existingUser) {
            // 检查是否已是项目成员
            const { data: existingMember } = await supabaseManager.supabase
                .from('project_members')
                .select('id')
                .eq('project_id', userManager.currentProject.id)
                .eq('user_id', existingUser.id)
                .single();

            if (existingMember) {
                userManager.showNotification('该用户已是项目成员', 'error');
                return;
            }
        }

        // 生成邀请令牌
        const invitationToken = generateInvitationToken();
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + expiresDays);

        // 创建邀请记录
        const { data: invitation, error } = await supabaseManager.supabase
            .from('user_invitations')
            .insert({
                email: email,
                project_id: userManager.currentProject.id,
                invited_by: userManager.currentUser.id,
                role: role,
                invitation_token: invitationToken,
                expires_at: expiresAt.toISOString()
            })
            .select()
            .single();

        if (error) throw error;

        // 发送邀请通知（这里可以集成邮件服务）
        await sendInvitationNotification(email, invitation, message);

        userManager.showNotification('邀请已发送', 'success');
        closeModal('invite-modal');
        form.reset();

        // 刷新邀请列表
        if (document.getElementById('invitations-tab').classList.contains('active')) {
            userManager.loadInvitations();
        }

    } catch (error) {
        console.error('发送邀请失败:', error);
        userManager.showNotification('发送邀请失败: ' + error.message, 'error');
    }
}

function editUser(userId) {
    const user = userManager.users.find(u => u.id === userId);
    if (!user) return;

    // 创建编辑模态框
    const modal = createEditUserModal(user);
    document.body.appendChild(modal);
    modal.style.display = 'block';
}

async function removeUser(userId) {
    const user = userManager.users.find(u => u.id === userId);
    if (!user) return;

    if (!confirm(`确定要移除用户 "${user.full_name}" 吗？`)) {
        return;
    }

    try {
        // 移除项目成员
        const { error } = await supabaseManager.supabase
            .from('project_members')
            .delete()
            .eq('project_id', userManager.currentProject.id)
            .eq('user_id', userId);

        if (error) throw error;

        userManager.showNotification('用户已移除', 'success');

        // 重新加载用户列表
        await userManager.loadUsers();

    } catch (error) {
        console.error('移除用户失败:', error);
        userManager.showNotification('移除用户失败: ' + error.message, 'error');
    }
}

// 生成邀请令牌
function generateInvitationToken() {
    return 'inv_' + Math.random().toString(36).substring(2) + Date.now().toString(36);
}

// 发送邀请通知
async function sendInvitationNotification(email, invitation, message) {
    // 这里可以集成邮件服务，现在先创建系统通知
    const inviteUrl = `${window.location.origin}/accept-invitation.html?token=${invitation.invitation_token}`;

    console.log('邀请链接:', inviteUrl);
    console.log('邀请邮箱:', email);
    console.log('邀请消息:', message);

    // 可以在这里集成邮件服务，如SendGrid、Mailgun等
    return true;
}

// 创建编辑用户模态框
function createEditUserModal(user) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'edit-user-modal';

    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">编辑用户</h2>
                <button class="close-btn" onclick="closeEditUserModal()">&times;</button>
            </div>

            <form id="edit-user-form" onsubmit="handleEditUser(event, '${user.id}')">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-input" value="${user.full_name}" readonly>
                </div>

                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-input" value="${user.email}" readonly>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">角色</label>
                        <select class="form-select" name="role" required ${user.project_role === 'owner' ? 'disabled' : ''}>
                            <option value="admin" ${user.project_role === 'admin' ? 'selected' : ''}>管理员</option>
                            <option value="editor" ${user.project_role === 'editor' ? 'selected' : ''}>编辑者</option>
                            <option value="author" ${user.project_role === 'author' ? 'selected' : ''}>作者</option>
                            <option value="reviewer" ${user.project_role === 'reviewer' ? 'selected' : ''}>审阅者</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select class="form-select" name="status" required>
                            <option value="active" ${user.project_status === 'active' ? 'selected' : ''}>活跃</option>
                            <option value="inactive" ${user.project_status === 'inactive' ? 'selected' : ''}>非活跃</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-save"></i> 保存更改
                    </button>
                </div>
            </form>
        </div>
    `;

    return modal;
}

// 处理编辑用户
async function handleEditUser(event, userId) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const role = formData.get('role');
    const status = formData.get('status');

    try {
        const { error } = await supabaseManager.supabase
            .from('project_members')
            .update({
                role: role,
                status: status
            })
            .eq('project_id', userManager.currentProject.id)
            .eq('user_id', userId);

        if (error) throw error;

        userManager.showNotification('用户信息已更新', 'success');
        closeEditUserModal();

        // 重新加载用户列表
        await userManager.loadUsers();

    } catch (error) {
        console.error('更新用户失败:', error);
        userManager.showNotification('更新用户失败: ' + error.message, 'error');
    }
}

// 关闭编辑用户模态框
function closeEditUserModal() {
    const modal = document.getElementById('edit-user-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.removeChild(modal);
    }
}

// 重发邀请
async function resendInvitation(invitationId) {
    try {
        const invitation = userManager.invitations.find(inv => inv.id === invitationId);
        if (!invitation) return;

        // 更新邀请过期时间
        const newExpiresAt = new Date();
        newExpiresAt.setDate(newExpiresAt.getDate() + 14); // 延长14天

        const { error } = await supabaseManager.supabase
            .from('user_invitations')
            .update({
                expires_at: newExpiresAt.toISOString()
            })
            .eq('id', invitationId);

        if (error) throw error;

        // 重新发送邀请通知
        await sendInvitationNotification(invitation.email, invitation, '这是一个重新发送的邀请');

        userManager.showNotification('邀请已重新发送', 'success');
        userManager.loadInvitations();

    } catch (error) {
        console.error('重发邀请失败:', error);
        userManager.showNotification('重发邀请失败: ' + error.message, 'error');
    }
}

// 取消邀请
async function cancelInvitation(invitationId) {
    if (!confirm('确定要取消这个邀请吗？')) {
        return;
    }

    try {
        const { error } = await supabaseManager.supabase
            .from('user_invitations')
            .update({
                status: 'cancelled'
            })
            .eq('id', invitationId);

        if (error) throw error;

        userManager.showNotification('邀请已取消', 'success');
        userManager.loadInvitations();

    } catch (error) {
        console.error('取消邀请失败:', error);
        userManager.showNotification('取消邀请失败: ' + error.message, 'error');
    }
}

// 初始化
const userManager = new UserManagement();
window.addEventListener('load', () => {
    userManager.initialize();
});

// 点击模态框外部关闭
window.addEventListener('click', (event) => {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
});
