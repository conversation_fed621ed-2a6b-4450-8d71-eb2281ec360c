<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试创建用户功能</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #4338ca;
        }
        
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .result.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .result.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .info {
            background: #eff6ff;
            color: #1e40af;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #bfdbfe;
        }
        
        .test-data {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .test-data h3 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .test-data p {
            margin: 5px 0;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🧪 测试创建用户功能</h1>
        
        <div class="info">
            <strong>测试说明：</strong>
            <br>此页面用于测试新的创建用户功能，使用简化的直接插入方式，绕过 Supabase Auth 的邮箱验证限制。
        </div>
        
        <div class="test-data">
            <h3>📋 测试数据建议</h3>
            <p><strong>邮箱：</strong> <EMAIL>, <EMAIL>, <EMAIL></p>
            <p><strong>用户名：</strong> test_user, demo_user, sample_user</p>
            <p><strong>全名：</strong> 测试用户, 演示用户, 示例用户</p>
        </div>
        
        <form id="test-form">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-input" name="username" value="test_user" required>
                </div>
                <div class="form-group">
                    <label class="form-label">全名</label>
                    <input type="text" class="form-input" name="full_name" value="测试用户" required>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">邮箱地址</label>
                <input type="email" class="form-input" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">初始密码</label>
                <input type="password" class="form-input" name="password" value="test123456" required>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">机构</label>
                    <input type="text" class="form-input" name="institution" value="测试机构">
                </div>
                <div class="form-group">
                    <label class="form-label">部门</label>
                    <input type="text" class="form-input" name="department" value="测试部门">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">项目角色</label>
                <select class="form-input" name="role" required>
                    <option value="author">作者</option>
                    <option value="editor">编辑者</option>
                    <option value="admin">管理员</option>
                    <option value="reviewer">审阅者</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">个人简介</label>
                <textarea class="form-input" name="bio" rows="3">这是一个测试用户账户</textarea>
            </div>
            
            <button type="submit" class="btn" id="submit-btn">
                🚀 测试创建用户
            </button>
        </form>
        
        <div id="result"></div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script>
        // 简化的用户管理类，只包含创建用户功能
        class SimpleUserManager {
            constructor() {
                this.currentProject = null;
            }

            // 验证邮箱格式
            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            // 创建新用户（简化版本）
            async createUser(userData) {
                try {
                    // 验证邮箱格式
                    if (!this.isValidEmail(userData.email)) {
                        throw new Error('邮箱格式无效');
                    }

                    console.log('开始创建用户:', userData.email);

                    // 直接使用数据库插入方式创建用户（绕过 Auth 限制）
                    const userId = crypto.randomUUID();
                    console.log('使用直接插入方式创建用户:', userData.email);

                    // 1. 创建用户配置
                    const { data: profile, error: profileError } = await supabaseManager.supabase
                        .from('user_profiles')
                        .insert({
                            id: userId,
                            username: userData.username,
                            full_name: userData.full_name,
                            email: userData.email,
                            institution: userData.institution || '',
                            department: userData.department || '',
                            bio: userData.bio || ''
                        })
                        .select()
                        .single();

                    if (profileError) {
                        console.error('创建用户配置失败:', profileError);
                        throw new Error(`创建用户配置失败: ${profileError.message}`);
                    }

                    // 2. 添加到项目成员
                    const currentUser = await supabaseManager.getCurrentUser();
                    const { error: memberError } = await supabaseManager.supabase
                        .from('project_members')
                        .insert({
                            project_id: this.currentProject.id,
                            user_id: userId,
                            role: userData.role,
                            status: 'active',
                            invited_by: currentUser.id
                        });

                    if (memberError) {
                        console.error('添加项目成员失败:', memberError);
                        throw new Error(`添加项目成员失败: ${memberError.message}`);
                    }

                    return {
                        success: true,
                        user: profile,
                        message: `用户 ${userData.full_name} 创建成功！（注意：该用户需要通过邀请链接设置登录密码）`,
                        method: 'direct_insert'
                    };

                } catch (error) {
                    console.error('创建用户失败:', error);

                    let errorMessage = '创建用户失败';

                    if (error.message.includes('邮箱格式无效')) {
                        errorMessage = '邮箱格式无效，请检查邮箱地址';
                    } else if (error.message.includes('duplicate')) {
                        errorMessage = '用户信息重复，请检查邮箱和用户名';
                    } else if (error.message) {
                        errorMessage = error.message;
                    }

                    return {
                        success: false,
                        error: errorMessage
                    };
                }
            }
        }
    </script>
    <script>
        // 初始化用户管理器
        let testUserManager;
        
        // 页面加载时初始化
        window.addEventListener('load', async () => {
            try {
                // 检查用户登录状态
                const user = await supabaseManager.getCurrentUser();
                if (!user) {
                    showResult('请先登录后再测试创建用户功能', 'error');
                    return;
                }

                // 获取当前项目（直接查询数据库）
                const { data: projects, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('*')
                    .limit(1);

                if (error) {
                    showResult('获取项目失败: ' + error.message, 'error');
                    return;
                }

                if (!projects || projects.length === 0) {
                    showResult('未找到项目，请先创建项目', 'error');
                    return;
                }

                // 初始化用户管理器
                testUserManager = new SimpleUserManager();
                testUserManager.currentProject = projects[0]; // 使用第一个项目

                showResult(`✅ 初始化成功，当前项目：${projects[0].title}`, 'success');

            } catch (error) {
                console.error('初始化失败:', error);
                showResult('初始化失败: ' + error.message, 'error');
            }
        });
        
        // 处理表单提交
        document.getElementById('test-form').addEventListener('submit', async (event) => {
            event.preventDefault();
            
            if (!testUserManager) {
                showResult('用户管理器未初始化', 'error');
                return;
            }
            
            const formData = new FormData(event.target);
            const userData = {
                username: formData.get('username'),
                full_name: formData.get('full_name'),
                email: formData.get('email'),
                password: formData.get('password'),
                institution: formData.get('institution'),
                department: formData.get('department'),
                role: formData.get('role'),
                bio: formData.get('bio')
            };
            
            // 显示加载状态
            const submitBtn = document.getElementById('submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '⏳ 创建中...';
            submitBtn.disabled = true;
            
            try {
                console.log('开始测试创建用户:', userData);
                const result = await testUserManager.createUser(userData);
                
                if (result.success) {
                    showResult(`✅ ${result.message}`, 'success');
                    console.log('创建成功:', result);
                } else {
                    showResult(`❌ ${result.error}`, 'error');
                    console.error('创建失败:', result);
                }
                
            } catch (error) {
                console.error('测试失败:', error);
                showResult(`❌ 测试失败: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
        
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        // 生成随机测试数据
        function generateRandomData() {
            const timestamp = Date.now();
            document.querySelector('[name="username"]').value = `test_${timestamp}`;
            document.querySelector('[name="full_name"]').value = `测试用户_${timestamp}`;
            document.querySelector('[name="email"]').value = `test_${timestamp}@gmail.com`;
        }
        
        // 添加生成随机数据按钮
        document.addEventListener('DOMContentLoaded', () => {
            const form = document.getElementById('test-form');
            const randomBtn = document.createElement('button');
            randomBtn.type = 'button';
            randomBtn.className = 'btn';
            randomBtn.style.background = '#10b981';
            randomBtn.style.marginBottom = '10px';
            randomBtn.textContent = '🎲 生成随机测试数据';
            randomBtn.onclick = generateRandomData;
            
            form.insertBefore(randomBtn, form.lastElementChild.previousElementSibling);
        });
    </script>
</body>
</html>
