// 协作功能管理
class CollaborationManager {
    constructor() {
        this.currentProject = null;
        this.currentUser = null;
        this.onlineUsers = new Set();
        this.realtimeSubscriptions = [];
    }

    // 初始化协作功能
    async initialize() {
        await this.checkAuthentication();
        this.setupEventListeners();
        await this.loadUserProjects();

        // 如果没有当前项目，设置一个默认项目用于演示
        if (!this.currentProject) {
            this.currentProject = {
                id: 'demo-project-1',
                title: '《大模型技术与油气应用概论》',
                description: '演示项目',
                status: 'active'
            };
            this.currentProjectId = 'demo-project-1';
            console.log('设置默认演示项目');
        }

        // 加载当前用户在项目中的角色
        await this.loadCurrentUserRole();
    }

    // 检查用户认证状态
    async checkAuthentication() {
        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            // 重定向到登录页面
            window.location.href = 'auth.html';
            return;
        }
        
        this.currentUser = user;
        await this.loadUserProfile();
        this.showUserInterface();
    }

    // 加载用户配置
    async loadUserProfile() {
        try {
            const profile = await supabaseManager.loadUserProfile();
            if (profile) {
                this.updateUserDisplay(profile);
            }
        } catch (error) {
            console.error('加载用户配置失败:', error);
        }
    }

    // 加载当前用户在项目中的角色
    async loadCurrentUserRole() {
        if (!this.currentUser || !this.currentProject) {
            console.warn('无法加载用户角色：缺少用户或项目信息');
            return;
        }

        try {
            // 检查是否为系统管理员
            if (this.currentUser.global_role === 'system_admin') {
                this.currentUserRole = 'system_admin';
                console.log('用户是系统管理员');
                return;
            }

            // 从数据库获取用户在当前项目中的角色
            const projectId = this.currentProject.id || this.currentProjectId;
            if (window.supabaseManager && window.supabaseManager.supabase) {
                const { data: member, error } = await window.supabaseManager.supabase
                    .from('project_members')
                    .select('role')
                    .eq('project_id', projectId)
                    .eq('user_id', this.currentUser.id)
                    .eq('status', 'active')
                    .single();

                if (!error && member) {
                    this.currentUserRole = member.role;
                    console.log(`用户在项目中的角色: ${member.role}`);
                    return;
                }
            }

            // 如果数据库查询失败，检查是否为项目所有者
            if (this.currentProject.owner_id === this.currentUser.id) {
                this.currentUserRole = 'owner';
                console.log('用户是项目所有者');
            } else {
                // 默认角色
                this.currentUserRole = 'author';
                console.log('使用默认角色: author');
            }

        } catch (error) {
            console.error('加载用户角色失败:', error);
            this.currentUserRole = 'author'; // 默认角色
        }
    }

    // 更新用户显示
    updateUserDisplay(profile) {
        const userNameEl = document.getElementById('user-name');
        const userAvatarEl = document.getElementById('user-avatar');
        
        if (userNameEl) {
            userNameEl.textContent = profile.full_name || profile.username;
        }
        
        if (userAvatarEl) {
            userAvatarEl.src = profile.avatar_url || this.generateAvatarUrl(profile.username);
        }
    }

    // 生成头像URL
    generateAvatarUrl(username) {
        return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=4f46e5&color=fff`;
    }

    // 显示用户界面
    showUserInterface() {
        const userMenu = document.getElementById('user-menu');
        if (userMenu) {
            userMenu.style.display = 'block';
        }

        // 显示项目概览页面的操作按钮（如果存在）
        const saveBtnOverview = document.getElementById('save-btn-overview');
        const exportBtnOverview = document.getElementById('export-btn-overview');

        if (saveBtnOverview) {
            saveBtnOverview.style.display = 'inline-flex';
        }
        if (exportBtnOverview) {
            exportBtnOverview.style.display = 'inline-flex';
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 协作标签切换
        document.querySelectorAll('.collab-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchCollabTab(e.target.dataset.tab);
            });
        });

        // 项目下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.project-dropdown')) {
                this.closeProjectDropdown();
            }
            if (!e.target.closest('.user-menu')) {
                this.closeUserDropdown();
            }
        });
    }

    // 加载用户项目
    async loadUserProjects() {
        try {
            const projects = await supabaseManager.getUserProjects();
            this.renderProjectList(projects);
        } catch (error) {
            console.error('加载项目列表失败:', error);
        }
    }

    // 渲染项目列表
    renderProjectList(projects) {
        // 只更新内联项目列表
        this.updateProjectList('project-list-inline', projects);
    }

    // 更新指定的项目列表
    updateProjectList(listId, projects) {
        const projectList = document.getElementById(listId);
        if (!projectList) return;

        const contentContainer = projectList.querySelector('.project-list-content');
        if (!contentContainer) return;

        // 清空现有项目（保留创建新项目按钮）
        const existingItems = contentContainer.querySelectorAll('.project-item:not(.create-new)');
        existingItems.forEach(item => item.remove());

        // 移除旧的分割线
        const existingDividers = contentContainer.querySelectorAll('.project-divider');
        existingDividers.forEach(divider => divider.remove());

        if (projects.length === 0) {
            // 显示空状态
            const emptyState = document.createElement('div');
            emptyState.className = 'project-list-empty';
            emptyState.innerHTML = `
                <i class="fas fa-folder-open"></i>
                <div>暂无项目</div>
            `;
            contentContainer.appendChild(emptyState);
            return;
        }

        // 添加分割线
        const divider = document.createElement('div');
        divider.className = 'project-divider';
        contentContainer.appendChild(divider);

        // 添加项目
        projects.forEach(project => {
            const projectItem = document.createElement('div');
            projectItem.className = 'project-item';
            projectItem.onclick = () => this.selectProject(project);

            const roleText = this.getRoleDisplayText(project.project_members[0].role);

            projectItem.innerHTML = `
                <i class="fas fa-book project-item-icon"></i>
                <div class="project-info">
                    <div class="project-title">${project.title}</div>
                    <div class="project-role">${roleText}</div>
                </div>
                <div class="project-status ${project.status}"></div>
            `;

            contentContainer.appendChild(projectItem);
        });
    }

    // 获取角色显示文本
    getRoleDisplayText(role) {
        const roleMap = {
            'owner': '项目所有者',
            'admin': '管理员',
            'editor': '编辑者',
            'author': '作者',
            'reviewer': '审阅者'
        };
        return roleMap[role] || role;
    }

    // 选择项目
    async selectProject(project) {
        this.currentProject = project;
        this.currentProjectId = project.id; // 设置项目ID
        this.closeProjectDropdown();

        // 更新界面显示
        const selectedProject = document.getElementById('selected-project');
        const selectedProjectInline = document.getElementById('selected-project-inline');
        const currentProjectName = document.getElementById('current-project-name');

        if (selectedProject) selectedProject.textContent = project.title;
        if (selectedProjectInline) selectedProjectInline.textContent = project.title;
        if (currentProjectName) currentProjectName.textContent = project.title;

        const projectInfo = document.getElementById('project-info');
        const navMenu = document.getElementById('nav-menu');

        if (projectInfo) projectInfo.style.display = 'block';
        if (navMenu) navMenu.style.display = 'block';

        // 显示项目概览页面的操作按钮
        const saveBtnOverview = document.getElementById('save-btn-overview');
        const exportBtnOverview = document.getElementById('export-btn-overview');

        if (saveBtnOverview) saveBtnOverview.style.display = 'inline-flex';
        if (exportBtnOverview) exportBtnOverview.style.display = 'inline-flex';

        // 加载项目数据
        await this.loadProjectData();

        // 设置实时协作
        this.setupRealtimeCollaboration();

        // 显示项目概览面板
        this.showPanel('overview');
    }

    // 加载项目数据
    async loadProjectData() {
        try {
            // 检查是否有当前项目
            if (!this.currentProject || !this.currentProject.id) {
                console.log('没有选择项目，尝试从全局状态获取项目信息');
                await this.tryLoadProjectFromGlobalState();

                // 如果仍然没有项目，显示提示
                if (!this.currentProject || !this.currentProject.id) {
                    console.log('无法获取项目信息，显示空状态');
                    this.showNoProjectState();
                    return;
                }
            }

            // 加载大纲
            const outline = await supabaseManager.getProjectOutline(this.currentProject.id);
            if (typeof currentProject !== 'undefined') {
                currentProject.outline = outline;
            }

            // 加载团队成员
            await this.loadTeamMembers();

            // 加载章节分配
            await this.loadChapterAssignments();

            // 同步项目数据到本地
            await this.syncProjectData();

            // 更新仪表板
            this.updateDashboard();

            // 更新项目概览
            if (typeof updateProjectOverview === 'function') {
                setTimeout(() => updateProjectOverview(this.currentProject), 100);
            }

            // 加载大纲数据
            if (typeof loadOutlineFromServer === 'function') {
                await loadOutlineFromServer();
            }

        } catch (error) {
            console.error('加载项目数据失败:', error);
            this.showProjectLoadError(error);
        }
    }

    // 加载团队成员
    async loadTeamMembers() {
        try {
            const { data, error } = await supabaseManager.supabase
                .from('project_members')
                .select(`
                    *,
                    user_profiles(*)
                `)
                .eq('project_id', this.currentProject.id);

            if (error) throw error;
            
            this.currentProject.members = data;
            this.renderTeamMembers(data);
            
        } catch (error) {
            console.error('加载团队成员失败:', error);
        }
    }

    // 尝试从全局状态加载项目
    async tryLoadProjectFromGlobalState() {
        try {
            // 尝试从全局 currentProject 变量获取
            if (typeof currentProject !== 'undefined' && currentProject.id) {
                console.log('从全局 currentProject 获取项目信息:', currentProject.title);
                this.currentProject = {
                    id: currentProject.id,
                    title: currentProject.title,
                    description: currentProject.description,
                    status: currentProject.status
                };
                this.currentProjectId = currentProject.id;
                return;
            }

            // 尝试从 localStorage 获取
            const savedProjectId = localStorage.getItem('currentProjectId');
            if (savedProjectId) {
                console.log('从 localStorage 获取项目ID:', savedProjectId);
                const { data: project, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('*')
                    .eq('id', savedProjectId)
                    .single();

                if (!error && project) {
                    this.currentProject = project;
                    this.currentProjectId = project.id;
                    console.log('成功加载项目:', project.title);
                    return;
                }
            }

            // 尝试获取用户的第一个项目
            const user = await supabaseManager.getCurrentUser();
            if (user) {
                const { data: projects, error } = await supabaseManager.supabase
                    .from('projects')
                    .select('*')
                    .eq('owner_id', user.id)
                    .limit(1);

                if (!error && projects && projects.length > 0) {
                    this.currentProject = projects[0];
                    this.currentProjectId = projects[0].id;
                    console.log('使用用户的第一个项目:', projects[0].title);
                    return;
                }
            }

            console.log('无法从任何来源获取项目信息');
        } catch (error) {
            console.error('尝试加载项目失败:', error);
        }
    }

    // 显示无项目状态
    showNoProjectState() {
        // 显示团队成员空状态
        this.renderEmptyTeamMembers();

        // 显示章节分配空状态
        this.renderEmptyChapterAssignments();

        // 显示权限矩阵空状态
        const matrixContainer = document.getElementById('permissions-matrix');
        if (matrixContainer) {
            matrixContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🔐</div>
                    <h3>请先选择项目</h3>
                    <p>选择一个项目后即可查看权限设置</p>
                </div>
            `;
        }

        console.log('已显示无项目状态');
    }

    // 显示项目加载错误
    showProjectLoadError(error) {
        const errorMessage = `项目数据加载失败: ${error.message}`;
        console.error(errorMessage);

        // 可以在这里添加用户友好的错误提示
        if (typeof showNotification === 'function') {
            showNotification(errorMessage, 'error');
        }
    }

    // 渲染空的团队成员状态
    renderEmptyTeamMembers() {
        const container = document.getElementById('team-members-list');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">👥</div>
                    <h3>请先选择项目</h3>
                    <p>选择一个项目后即可查看团队成员</p>
                </div>
            `;
        }
    }

    // 渲染团队成员
    renderTeamMembers(members) {
        const container = document.getElementById('team-members-list');
        if (!container) return;

        container.innerHTML = '';

        if (!members || members.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">👥</div>
                    <h3>暂无团队成员</h3>
                    <p>邀请成员加入项目开始协作</p>
                </div>
            `;
            return;
        }

        members.forEach(member => {
            const memberDiv = document.createElement('div');
            memberDiv.className = 'member-item';

            const profile = member.user_profiles;
            const roleText = this.getRoleDisplayText(member.role);
            
            memberDiv.innerHTML = `
                <div class="member-avatar">
                    <img src="${profile.avatar_url || this.generateAvatarUrl(profile.username)}" alt="${profile.full_name}">
                    <div class="member-status ${this.isUserOnline(profile.id) ? 'online' : 'offline'}"></div>
                </div>
                <div class="member-info">
                    <div class="member-name">${profile.full_name}</div>
                    <div class="member-role">${roleText}</div>
                    <div class="member-institution">${profile.institution || ''}</div>
                </div>
                <div class="member-actions">
                    ${this.canManageUser(member) ? `
                        <button class="btn-icon" onclick="editMemberRole('${member.id}')" title="编辑角色">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="removeMember('${member.id}')" title="移除成员">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            `;
            
            container.appendChild(memberDiv);
        });
    }

    // 检查用户是否在线
    isUserOnline(userId) {
        return this.onlineUsers.has(userId);
    }

    // 检查是否可以管理用户
    canManageUser(member) {
        if (!this.currentProject) return false;
        
        const currentUserMember = this.currentProject.members.find(m => m.user_id === this.currentUser.id);
        if (!currentUserMember) return false;
        
        const roleHierarchy = { 'owner': 4, 'admin': 3, 'editor': 2, 'author': 1, 'reviewer': 1 };
        
        return roleHierarchy[currentUserMember.role] > roleHierarchy[member.role];
    }

    // 设置实时协作
    setupRealtimeCollaboration() {
        // 清除之前的订阅
        this.realtimeSubscriptions.forEach(sub => {
            supabaseManager.unsubscribe(sub);
        });
        this.realtimeSubscriptions = [];

        // 订阅项目变化
        const projectSub = supabaseManager.supabase
            .channel(`project-${this.currentProject.id}`)
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'chapters',
                filter: `project_id=eq.${this.currentProject.id}`
            }, (payload) => {
                this.handleChapterChange(payload);
            })
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'outlines',
                filter: `project_id=eq.${this.currentProject.id}`
            }, (payload) => {
                this.handleOutlineChange(payload);
            })
            .subscribe();

        this.realtimeSubscriptions.push(projectSub);

        // 订阅在线状态
        this.setupPresence();
    }

    // 设置在线状态
    setupPresence() {
        const presenceChannel = supabaseManager.supabase.channel(`presence-${this.currentProject.id}`, {
            config: {
                presence: {
                    key: this.currentUser.id,
                }
            }
        });

        presenceChannel
            .on('presence', { event: 'sync' }, () => {
                const state = presenceChannel.presenceState();
                this.updateOnlineUsers(state);
            })
            .on('presence', { event: 'join' }, ({ key, newPresences }) => {
                this.onlineUsers.add(key);
                this.updateOnlineUsersDisplay();
            })
            .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
                this.onlineUsers.delete(key);
                this.updateOnlineUsersDisplay();
            })
            .subscribe(async (status) => {
                if (status === 'SUBSCRIBED') {
                    await presenceChannel.track({
                        user_id: this.currentUser.id,
                        online_at: new Date().toISOString(),
                    });
                }
            });

        this.realtimeSubscriptions.push(presenceChannel);
    }

    // 更新在线用户
    updateOnlineUsers(state) {
        this.onlineUsers.clear();
        Object.keys(state).forEach(userId => {
            this.onlineUsers.add(userId);
        });
        this.updateOnlineUsersDisplay();
    }

    // 更新在线用户显示
    updateOnlineUsersDisplay() {
        const container = document.getElementById('online-users');
        container.innerHTML = '';

        if (this.currentProject && this.currentProject.members) {
            const onlineMembers = this.currentProject.members.filter(member => 
                this.onlineUsers.has(member.user_id)
            );

            onlineMembers.forEach(member => {
                const profile = member.user_profiles;
                const userDiv = document.createElement('div');
                userDiv.className = 'online-user';
                userDiv.title = profile.full_name;
                
                userDiv.innerHTML = `
                    <img src="${profile.avatar_url || this.generateAvatarUrl(profile.username)}" alt="${profile.full_name}">
                `;
                
                container.appendChild(userDiv);
            });
        }
    }

    // 处理章节变化
    handleChapterChange(payload) {
        console.log('章节变化:', payload);
        // 更新章节列表或编辑器内容
        if (typeof updateChaptersList === 'function') {
            updateChaptersList();
        }
    }

    // 处理大纲变化
    handleOutlineChange(payload) {
        console.log('大纲变化:', payload);
        // 更新大纲树
        if (typeof renderOutlineTree === 'function') {
            renderOutlineTree();
        }
    }

    // 加载团队成员
    async loadTeamMembers() {
        try {
            // 尝试同步项目信息
            this.syncProjectFromGlobal();

            // 检查是否有当前项目
            if (!this.currentProject || !this.currentProject.id) {
                console.log('没有选择项目，无法加载团队成员');
                this.renderEmptyTeamMembers();
                return;
            }

            console.log('查询项目成员，项目ID:', this.currentProject.id);

            const { data: members, error } = await supabaseManager.supabase
                .from('project_members')
                .select(`
                    *,
                    user_profiles (
                        id,
                        username,
                        full_name,
                        email,
                        avatar_url,
                        institution,
                        department
                    )
                `)
                .eq('project_id', this.currentProject.id)
                .eq('status', 'active');

            console.log('项目成员查询结果:', { members, error });

            if (error) throw error;

            // 保存团队成员数据到当前项目
            if (this.currentProject) {
                this.currentProject.members = members || [];
            }

            this.renderTeamMembers(members);
        } catch (error) {
            console.error('加载团队成员失败:', error);
            this.renderEmptyTeamMembers();
        }
    }

    // 渲染团队成员
    renderTeamMembers(members) {
        const membersContainer = document.getElementById('team-members-list');
        if (!membersContainer) return;

        if (members.length === 0) {
            membersContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users"></i>
                    <p>暂无团队成员</p>
                </div>
            `;
            return;
        }

        membersContainer.innerHTML = members.map(member => `
            <div class="member-card">
                <div class="member-avatar">
                    ${member.user_profiles.avatar_url ?
                        `<img src="${member.user_profiles.avatar_url}" alt="${member.user_profiles.full_name}">` :
                        `<div class="avatar-placeholder">${member.user_profiles.full_name.substring(0, 2).toUpperCase()}</div>`
                    }
                </div>
                <div class="member-info">
                    <h4>${member.user_profiles.full_name}</h4>
                    <p class="member-email">${member.user_profiles.email}</p>
                    <p class="member-institution">${member.user_profiles.institution || '未设置机构'}</p>
                    <span class="role-badge role-${member.role}">${this.getRoleDisplayName(member.role)}</span>
                </div>
                <div class="member-actions">
                    ${this.canManageMembers() ? `
                        <button class="btn-icon" onclick="collaborationManager.editMemberRole('${member.user_id}')" title="编辑角色">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${member.role !== 'owner' ? `
                            <button class="btn-icon btn-danger" onclick="collaborationManager.removeMember('${member.user_id}')" title="移除成员">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    // 加载章节分配
    async loadChapterAssignments() {
        try {
            // 尝试同步项目信息
            this.syncProjectFromGlobal();

            // 检查是否有当前项目
            if (!this.currentProject || !this.currentProject.id) {
                console.log('没有选择项目，无法加载章节分配');
                this.renderEmptyChapterAssignments();
                return;
            }

            // 显示加载状态
            this.showAssignmentsLoading();

            // 首先尝试从数据库加载现有的章节分配
            const { data: assignments, error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .select(`
                    *,
                    chapters (
                        id,
                        title,
                        status,
                        outline_id
                    ),
                    lead_author:user_profiles!lead_author_id (
                        id,
                        full_name,
                        email
                    ),
                    reviewer:user_profiles!reviewer_id (
                        id,
                        full_name,
                        email
                    ),
                    assigned_by_profile:user_profiles!assigned_by (
                        id,
                        full_name,
                        email
                    ),
                    chapter_collaborators (
                        id,
                        user_id,
                        role,
                        status,
                        user_profiles (
                            id,
                            full_name,
                            email
                        )
                    )
                `)
                .eq('project_id', this.currentProject.id);

            if (error) {
                console.warn('从数据库加载章节分配失败:', error);
            }

            // 处理协作者数据，确保格式正确
            if (assignments && assignments.length > 0) {
                assignments.forEach(assignment => {
                    if (assignment.chapter_collaborators) {
                        assignment.collaborators = assignment.chapter_collaborators.map(collab => ({
                            id: collab.user_profiles.id,
                            full_name: collab.user_profiles.full_name,
                            email: collab.user_profiles.email,
                            role: collab.role,
                            status: collab.status
                        }));
                    } else {
                        assignment.collaborators = [];
                    }
                });
            }

            // 获取项目的所有章节和大纲数据
            const chaptersData = await this.loadProjectChapters();

            // 如果没有章节分配数据，为现有章节创建默认分配
            if (!assignments || assignments.length === 0) {
                console.log('数据库中没有章节分配数据，为现有章节创建默认分配');
                await this.createDefaultAssignments(chaptersData);
                // 重新加载分配数据
                return this.loadChapterAssignments();
            }

            // 更新统计数据
            this.updateAssignmentStats(assignments || []);

            // 渲染分配列表
            this.renderChapterAssignments(assignments || []);

            // 设置搜索和过滤事件
            this.setupAssignmentFilters();

        } catch (error) {
            console.error('加载章节分配失败:', error);
            this.renderEmptyChapterAssignments();
        }
    }

    // 加载项目章节数据
    async loadProjectChapters() {
        try {
            // 获取项目的大纲数据
            const { data: outlines, error: outlinesError } = await supabaseManager.supabase
                .from('outlines')
                .select('*')
                .eq('project_id', this.currentProject.id)
                .order('sort_order');

            if (outlinesError) {
                console.warn('加载大纲数据失败:', outlinesError);
            }

            // 获取项目的章节数据
            const { data: chapters, error: chaptersError } = await supabaseManager.supabase
                .from('chapters')
                .select('*')
                .eq('project_id', this.currentProject.id)
                .order('created_at');

            if (chaptersError) {
                console.warn('加载章节数据失败:', chaptersError);
            }

            // 合并大纲和章节数据
            const combinedData = [];

            // 添加大纲项目
            if (outlines && outlines.length > 0) {
                outlines.forEach(outline => {
                    combinedData.push({
                        id: outline.id,
                        title: outline.title,
                        description: outline.description,
                        type: 'outline',
                        status: outline.status,
                        level: outline.level,
                        sort_order: outline.sort_order
                    });
                });
            }

            // 添加章节项目
            if (chapters && chapters.length > 0) {
                chapters.forEach(chapter => {
                    combinedData.push({
                        id: chapter.id,
                        title: chapter.title,
                        description: chapter.summary,
                        type: 'chapter',
                        status: chapter.status,
                        outline_id: chapter.outline_id,
                        created_at: chapter.created_at
                    });
                });
            }

            return combinedData;

        } catch (error) {
            console.error('加载项目章节数据失败:', error);
            return [];
        }
    }

    // 为现有章节创建默认分配
    async createDefaultAssignments(chaptersData) {
        try {
            // 获取项目所有者信息
            const projectOwner = await this.getProjectOwner();
            if (!projectOwner) {
                console.error('无法获取项目所有者信息');
                return;
            }

            // 获取当前用户
            const currentUser = await supabaseManager.getCurrentUser();
            if (!currentUser) {
                console.error('无法获取当前用户信息');
                return;
            }

            // 为避免重复，先分离章节和大纲数据
            const chapters = chaptersData.filter(item => item.type === 'chapter');
            const outlines = chaptersData.filter(item => item.type === 'outline' && item.level <= 2);

            // 获取已有章节对应的大纲ID，避免重复创建
            const chapterOutlineIds = new Set(chapters.map(chapter => chapter.outline_id).filter(id => id));

            // 过滤掉已有对应章节的大纲项
            const outlinesWithoutChapters = outlines.filter(outline => !chapterOutlineIds.has(outline.id));

            // 合并要处理的项目：优先处理章节，然后处理没有对应章节的大纲项
            const itemsToProcess = [...chapters, ...outlinesWithoutChapters];

            console.log(`准备创建分配: ${chapters.length} 个章节, ${outlinesWithoutChapters.length} 个独立大纲项`);

            // 为每个项目创建默认分配
            const assignmentsToCreate = [];

            itemsToProcess.forEach((item, index) => {
                // 计算截止日期（从现在开始，每个章节间隔一周）
                const dueDate = new Date();
                dueDate.setDate(dueDate.getDate() + (index + 1) * 7);

                const assignmentData = {
                    project_id: this.currentProject.id,
                    title: item.title,
                    description: item.description || '请完成本章节的编写工作',
                    lead_author_id: projectOwner.id,
                    reviewer_id: projectOwner.id,
                    assigned_by: currentUser.id,
                    due_date: dueDate.toISOString(),
                    status: 'pending',
                    priority: 'medium',
                    word_count_target: 3000 // 默认目标字数
                };

                // 只有章节类型才设置 chapter_id
                if (item.type === 'chapter') {
                    assignmentData.chapter_id = item.id;
                }

                assignmentsToCreate.push(assignmentData);
            });

            if (assignmentsToCreate.length > 0) {
                const { data, error } = await supabaseManager.supabase
                    .from('chapter_assignments')
                    .insert(assignmentsToCreate)
                    .select();

                if (error) {
                    console.error('创建默认分配失败:', error);
                    return;
                }

                console.log(`成功创建 ${data.length} 个默认章节分配`);

                // 为每个分配创建默认协作者记录
                await this.createDefaultCollaborators(data, projectOwner.id);
            }

        } catch (error) {
            console.error('创建默认分配失败:', error);
        }
    }

    // 获取项目所有者信息
    async getProjectOwner() {
        try {
            const { data, error } = await supabaseManager.supabase
                .from('projects')
                .select(`
                    owner_id,
                    user_profiles!projects_owner_id_fkey (
                        id,
                        full_name,
                        email,
                        username
                    )
                `)
                .eq('id', this.currentProject.id)
                .single();

            if (error) {
                console.error('获取项目所有者失败:', error);
                return null;
            }

            return data.user_profiles;

        } catch (error) {
            console.error('获取项目所有者失败:', error);
            return null;
        }
    }

    // 创建默认协作者记录
    async createDefaultCollaborators(assignments, ownerId) {
        try {
            const collaboratorsToCreate = [];

            assignments.forEach(assignment => {
                // 为每个分配添加项目所有者作为协作者
                collaboratorsToCreate.push({
                    assignment_id: assignment.id,
                    user_id: ownerId,
                    role: 'collaborator',
                    responsibilities: '协助章节编写和内容审核',
                    status: 'accepted'
                });
            });

            if (collaboratorsToCreate.length > 0) {
                const { error } = await supabaseManager.supabase
                    .from('chapter_collaborators')
                    .insert(collaboratorsToCreate);

                if (error) {
                    console.warn('创建默认协作者记录失败:', error);
                } else {
                    console.log(`成功创建 ${collaboratorsToCreate.length} 个默认协作者记录`);
                }
            }

        } catch (error) {
            console.error('创建默认协作者记录失败:', error);
        }
    }

    // 渲染空的章节分配状态
    renderEmptyChapterAssignments() {
        const assignmentsContainer = document.getElementById('chapter-assignments-list');
        if (assignmentsContainer) {
            assignmentsContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>请先选择项目</h3>
                    <p>选择一个项目后即可查看章节分配</p>
                </div>
            `;
        }
    }

    // 显示加载状态
    showAssignmentsLoading() {
        const assignmentsContainer = document.getElementById('chapter-assignments-list');
        if (!assignmentsContainer) return;

        assignmentsContainer.innerHTML = `
            <div class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>加载章节分配中...</p>
            </div>
        `;
    }

    // 更新统计数据
    updateAssignmentStats(assignments) {
        const totalChapters = assignments.length;
        const totalAuthors = new Set(assignments.map(a => a.lead_author_id)).size;
        const completedChapters = assignments.filter(a => a.status === 'completed').length;
        const pendingReviews = assignments.filter(a => a.status === 'reviewing').length;

        // 更新统计显示
        const elements = {
            'total-chapters': totalChapters,
            'total-authors': totalAuthors,
            'completed-chapters': completedChapters,
            'pending-reviews': pendingReviews
        };

        for (const [id, value] of Object.entries(elements)) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }
    }

    // 获取项目成员列表
    async getProjectMembers() {
        try {
            console.log('获取项目成员 - 当前项目:', this.currentProject);

            // 如果已经加载了团队成员，直接返回
            if (this.currentProject && this.currentProject.members && this.currentProject.members.length > 0) {
                console.log('使用缓存的成员数据:', this.currentProject.members);
                return this.currentProject.members.map(member => ({
                    id: member.user_profiles?.id || member.user_id,
                    full_name: member.user_profiles?.full_name || '未知用户',
                    email: member.user_profiles?.email || '',
                    role: member.role || 'author',
                    avatar: (member.user_profiles?.full_name || '未知')[0].toUpperCase()
                }));
            }

            // 如果没有加载团队成员，从数据库获取
            if (this.currentProject && this.currentProject.id) {
                await this.loadTeamMembers();

                if (this.currentProject.members && this.currentProject.members.length > 0) {
                    return this.currentProject.members.map(member => ({
                        id: member.user_profiles?.id || member.user_id,
                        full_name: member.user_profiles?.full_name || '未知用户',
                        email: member.user_profiles?.email || '',
                        role: member.role || 'author',
                        avatar: (member.user_profiles?.full_name || '未知')[0].toUpperCase()
                    }));
                }
            }

            // 如果仍然没有成员，至少返回当前用户
            const currentUser = await supabaseManager.getCurrentUser();
            if (currentUser) {
                const { data: userProfile } = await supabaseManager.supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('id', currentUser.id)
                    .single();

                if (userProfile) {
                    return [{
                        id: userProfile.id,
                        full_name: userProfile.full_name || currentUser.email,
                        email: userProfile.email || currentUser.email,
                        role: 'owner',
                        avatar: (userProfile.full_name || currentUser.email)[0].toUpperCase()
                    }];
                }
            }

            return [];
        } catch (error) {
            console.error('获取项目成员失败:', error);
            return [];
        }
    }

    // 渲染章节分配
    renderChapterAssignments(assignments) {
        const assignmentsContainer = document.getElementById('chapter-assignments-list');
        if (!assignmentsContainer) return;

        if (assignments.length === 0) {
            assignmentsContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>暂无章节分配</h3>
                    <p>点击"新建分配"开始分配章节给团队成员</p>
                    <button class="btn btn-primary" onclick="collaborationManager.showCreateAssignmentModal()">
                        <i class="fas fa-plus"></i>
                        新建分配
                    </button>
                </div>
            `;
            return;
        }

        // 渲染分配列表
        assignmentsContainer.innerHTML = assignments.map(assignment => {
            const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
            const isOverdue = dueDate && dueDate < new Date() && assignment.status !== 'completed';
            const progress = this.calculateProgress(assignment);

            return `
                <div class="assignment-item">
                    <div class="assignment-title">
                        ${assignment.title || assignment.chapters?.title || '未知章节'}
                        <div class="assignment-subtitle">
                            ${assignment.description || '暂无描述'}
                        </div>
                    </div>
                    <div class="user-badge">
                        <i class="fas fa-user"></i>
                        ${assignment.lead_author?.full_name || '未分配'}
                    </div>
                    <div class="user-badge">
                        <i class="fas fa-users"></i>
                        <span id="collaborators-${assignment.id}">加载中...</span>
                    </div>
                    <div class="user-badge">
                        <i class="fas fa-user-check"></i>
                        ${assignment.reviewer?.full_name || '未分配'}
                    </div>
                    <div>
                        <span class="status-badge status-${assignment.status}">
                            ${this.getStatusText(assignment.status)}
                        </span>
                    </div>
                    <div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                        <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                            ${progress}%
                        </div>
                    </div>
                    <div class="due-date ${isOverdue ? 'overdue' : ''}">
                        ${dueDate ? dueDate.toLocaleDateString('zh-CN') : '无截止日期'}
                    </div>
                    <div class="actions">
                        ${assignment.id ? `
                            <button class="action-btn" onclick="collaborationManager.editAssignment('${assignment.id}')" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn" onclick="collaborationManager.viewAssignment('${assignment.id}')" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn" onclick="collaborationManager.deleteAssignment('${assignment.id}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : `
                            <span style="color: #6b7280; font-size: 12px;">无效记录</span>
                        `}
                    </div>
                </div>
            `;
        }).join('');

        // 异步加载协作者信息
        this.loadCollaboratorsForAssignments(assignments);
    }

    // 异步加载协作者信息
    async loadCollaboratorsForAssignments(assignments) {
        for (const assignment of assignments) {
            try {
                const collaboratorsElement = document.getElementById(`collaborators-${assignment.id}`);
                if (!collaboratorsElement) continue;

                // 如果已经有协作者数据，直接显示
                if (assignment.collaborators && assignment.collaborators.length > 0) {
                    const collaboratorNames = assignment.collaborators.map(c => c.full_name).join(', ');
                    collaboratorsElement.textContent = collaboratorNames;
                } else {
                    collaboratorsElement.textContent = '无协作者';
                }
            } catch (error) {
                console.error(`加载协作者信息失败 (${assignment.id}):`, error);
                const collaboratorsElement = document.getElementById(`collaborators-${assignment.id}`);
                if (collaboratorsElement) {
                    collaboratorsElement.textContent = '加载失败';
                }
            }
        }
    }

    // 计算进度
    calculateProgress(assignment) {
        if (assignment.status === 'completed') return 100;
        if (assignment.status === 'reviewing') return 85;
        if (assignment.status === 'writing') return 50;
        if (assignment.status === 'pending') return 5;
        return 0;
    }

    // 获取状态文本
    getStatusText(status) {
        const statusMap = {
            'pending': '待分配',
            'writing': '编制中',
            'reviewing': '审核中',
            'completed': '已完成',
            'rejected': '已拒绝'
        };
        return statusMap[status] || status;
    }

    // 设置搜索和过滤事件
    setupAssignmentFilters() {
        const searchInput = document.getElementById('assignment-search');
        const statusFilter = document.getElementById('status-filter');

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterAssignments(e.target.value, statusFilter?.value || '');
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterAssignments(searchInput?.value || '', e.target.value);
            });
        }
    }

    // 过滤分配
    filterAssignments(searchTerm, statusFilter) {
        const items = document.querySelectorAll('.assignment-item');

        items.forEach(item => {
            const title = item.querySelector('.assignment-title')?.textContent.toLowerCase() || '';
            const author = item.querySelector('.user-badge')?.textContent.toLowerCase() || '';
            const status = item.querySelector('.status-badge')?.className || '';

            const matchesSearch = !searchTerm ||
                title.includes(searchTerm.toLowerCase()) ||
                author.includes(searchTerm.toLowerCase());

            const matchesStatus = !statusFilter || status.includes(`status-${statusFilter}`);

            item.style.display = matchesSearch && matchesStatus ? 'grid' : 'none';
        });
    }

    // 显示创建分配模态框
    async showCreateAssignmentModal() {
        try {
            // 获取项目的章节和大纲数据
            const chaptersData = await this.loadProjectChapters();
            const members = await this.getProjectMembers();

            if (chaptersData.length === 0) {
                alert('当前项目还没有章节，请先创建章节大纲。');
                return;
            }

            if (members.length === 0) {
                alert('当前项目还没有团队成员，请先邀请成员加入项目。');
                return;
            }

            // 创建模态对话框HTML
            const modalHtml = `
                <div class="assignment-modal" id="createAssignmentModal">
                    <div class="assignment-modal-content">
                        <div class="assignment-modal-header">
                            <h3 class="assignment-modal-title">
                                <i class="fas fa-plus"></i>
                                新建章节分配
                            </h3>
                            <button class="assignment-modal-close" onclick="collaborationManager.closeCreateModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="assignment-modal-body">
                            <form id="createAssignmentForm">
                                <div class="form-group">
                                    <label class="form-label">选择章节 *</label>
                                    <select class="form-select" id="chapterSelect" required onchange="collaborationManager.onChapterSelectChange()">
                                        <option value="">请选择章节</option>
                                        ${chaptersData.map(item => `
                                            <option value="${item.id}" data-type="${item.type}" data-title="${item.title || ''}" data-description="${item.description || ''}">
                                                ${item.title || '未命名'} ${item.type === 'outline' ? '(大纲)' : '(章节)'}
                                            </option>
                                        `).join('')}
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">章节描述</label>
                                    <textarea class="form-textarea" id="assignmentDescription" placeholder="请输入章节描述和要求"></textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">主笔作者 *</label>
                                        <select class="form-select" id="leadAuthorSelect" required>
                                            <option value="">请选择主笔作者</option>
                                            ${members.map(member => `
                                                <option value="${member.id}">
                                                    ${member.full_name} (${member.role})
                                                </option>
                                            `).join('')}
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">审核者</label>
                                        <select class="form-select" id="reviewerSelect">
                                            <option value="">请选择审核者</option>
                                            ${members.map(member => `
                                                <option value="${member.id}">
                                                    ${member.full_name} (${member.role})
                                                </option>
                                            `).join('')}
                                        </select>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">目标字数</label>
                                        <input type="number" class="form-input" id="wordCountTarget" placeholder="3000" min="0">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">截止日期</label>
                                        <input type="date" class="form-input" id="dueDate">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">优先级</label>
                                    <select class="form-select" id="prioritySelect">
                                        <option value="medium">中等</option>
                                        <option value="low">低</option>
                                        <option value="high">高</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">具体要求</label>
                                    <textarea class="form-textarea" id="requirements" placeholder="请详细说明章节的写作要求和标准"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="assignment-modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="collaborationManager.closeCreateModal()">
                                取消
                            </button>
                            <button type="button" class="btn btn-primary" onclick="collaborationManager.submitCreateAssignment()">
                                创建分配
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 显示模态对话框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 激活模态对话框
            setTimeout(() => {
                const modal = document.getElementById('createAssignmentModal');
                if (modal) {
                    modal.classList.add('active');
                }
            }, 10);

            // 设置默认值
            this.setDefaultAssignmentValues(members);

        } catch (error) {
            console.error('显示创建分配模态框失败:', error);
            alert('显示创建分配对话框失败，请稍后重试。');
        }
    }

    // 设置默认分配值
    async setDefaultAssignmentValues(members) {
        try {
            // 获取项目所有者
            const projectOwner = await this.getProjectOwner();

            // 设置默认的主笔作者和审核者
            const leadAuthorSelect = document.getElementById('leadAuthorSelect');
            const reviewerSelect = document.getElementById('reviewerSelect');

            if (leadAuthorSelect && members.length > 0) {
                // 优先使用项目所有者，如果没有则使用第一个成员
                const defaultAuthorId = projectOwner ? projectOwner.id : members[0].id;
                leadAuthorSelect.value = defaultAuthorId;
            }

            if (reviewerSelect && members.length > 0) {
                // 优先使用项目所有者，如果没有则使用第一个成员
                const defaultReviewerId = projectOwner ? projectOwner.id : members[0].id;
                reviewerSelect.value = defaultReviewerId;
            }

            // 设置默认截止日期（一周后）
            const dueDateInput = document.getElementById('dueDate');
            if (dueDateInput) {
                const nextWeek = new Date();
                nextWeek.setDate(nextWeek.getDate() + 7);
                dueDateInput.value = nextWeek.toISOString().split('T')[0];
            }

            // 设置默认字数
            const wordCountInput = document.getElementById('wordCountTarget');
            if (wordCountInput) {
                wordCountInput.value = '3000';
            }

        } catch (error) {
            console.error('设置默认值失败:', error);
        }
    }

    // 章节选择变化处理
    onChapterSelectChange() {
        const chapterSelect = document.getElementById('chapterSelect');
        const descriptionTextarea = document.getElementById('assignmentDescription');

        if (chapterSelect && descriptionTextarea) {
            const selectedOption = chapterSelect.options[chapterSelect.selectedIndex];
            if (selectedOption && selectedOption.value) {
                const description = selectedOption.dataset.description || '';
                if (description && description !== 'undefined') {
                    descriptionTextarea.value = description;
                } else {
                    descriptionTextarea.value = '请完成本章节的编写工作';
                }
            }
        }
    }

    // 提交创建分配
    async submitCreateAssignment() {
        try {
            const form = document.getElementById('createAssignmentForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const chapterSelect = document.getElementById('chapterSelect');
            const selectedOption = chapterSelect.options[chapterSelect.selectedIndex];

            if (!selectedOption || !selectedOption.value) {
                alert('请选择一个章节');
                return;
            }

            const assignmentData = {
                project_id: this.currentProject.id,
                title: selectedOption.dataset.title || selectedOption.text.replace(' (大纲)', '').replace(' (章节)', ''),
                description: document.getElementById('assignmentDescription').value || '请完成本章节的编写工作',
                lead_author_id: document.getElementById('leadAuthorSelect').value,
                reviewer_id: document.getElementById('reviewerSelect').value || null,
                word_count_target: parseInt(document.getElementById('wordCountTarget').value) || 3000,
                due_date: document.getElementById('dueDate').value || null,
                priority: document.getElementById('prioritySelect').value,
                requirements: document.getElementById('requirements').value || '',
                status: 'pending'
            };

            // 只有章节类型才设置 chapter_id
            if (selectedOption.dataset.type === 'chapter') {
                assignmentData.chapter_id = chapterSelect.value;
            }

            // 验证必填字段
            if (!assignmentData.title || assignmentData.title.trim() === '') {
                alert('章节标题不能为空');
                return;
            }

            if (!assignmentData.lead_author_id) {
                alert('请选择主笔作者');
                return;
            }

            // 获取当前用户
            const currentUser = await supabaseManager.getCurrentUser();
            if (!currentUser) {
                alert('用户未登录');
                return;
            }

            // 创建分配记录
            const insertData = {
                ...assignmentData,
                assigned_by: currentUser.id
            };

            const { data, error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .insert([insertData])
                .select()
                .single();

            if (error) {
                console.error('创建分配失败:', error);
                alert('创建分配失败: ' + error.message);
                return;
            }

            // 如果有审核者且不是主笔作者，创建协作者记录
            if (assignmentData.reviewer_id && assignmentData.reviewer_id !== assignmentData.lead_author_id) {
                await supabaseManager.supabase
                    .from('chapter_collaborators')
                    .insert([{
                        assignment_id: data.id,
                        user_id: assignmentData.reviewer_id,
                        role: 'reviewer',
                        responsibilities: '负责章节内容的审核和反馈',
                        status: 'accepted'
                    }]);
            }

            alert('章节分配创建成功！');
            this.closeCreateModal();

            // 重新加载分配列表
            await this.loadChapterAssignments();

        } catch (error) {
            console.error('提交创建分配失败:', error);
            alert('创建分配失败，请稍后重试。');
        }
    }

    // 关闭创建模态框
    closeCreateModal() {
        const modal = document.getElementById('createAssignmentModal');
        if (modal) {
            modal.classList.remove('active');
            setTimeout(() => {
                modal.remove();
            }, 300); // 等待动画完成
        }
    }

    // 编辑分配
    async editAssignment(assignmentId) {
        console.log('编辑分配:', assignmentId);

        if (!assignmentId || assignmentId === 'undefined') {
            console.error('无效的分配ID:', assignmentId);
            alert('无效的分配ID');
            return;
        }

        // 查找要编辑的分配
        const assignment = await this.findAssignmentById(assignmentId);
        if (!assignment) {
            alert('未找到指定的分配记录');
            return;
        }

        await this.showEditAssignmentModal(assignment);
    }

    // 查找分配记录
    async findAssignmentById(assignmentId) {
        if (!assignmentId || assignmentId === 'undefined') {
            console.error('无效的分配ID:', assignmentId);
            return null;
        }

        try {
            // 从数据库查询分配记录
            const { data: assignment, error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .select(`
                    *,
                    chapters (
                        id,
                        title,
                        status,
                        summary
                    ),
                    lead_author:user_profiles!lead_author_id (
                        id,
                        full_name,
                        email,
                        username
                    ),
                    reviewer:user_profiles!reviewer_id (
                        id,
                        full_name,
                        email,
                        username
                    ),
                    assigned_by_profile:user_profiles!assigned_by (
                        id,
                        full_name,
                        email,
                        username
                    )
                `)
                .eq('id', assignmentId)
                .single();

            if (error) {
                console.error('查找分配记录失败:', error);
                return null;
            }

            // 获取协作者信息
            const { data: collaborators, error: collabError } = await supabaseManager.supabase
                .from('chapter_collaborators')
                .select(`
                    *,
                    user_profiles (
                        id,
                        full_name,
                        email,
                        username
                    )
                `)
                .eq('assignment_id', assignmentId);

            if (collabError) {
                console.warn('获取协作者信息失败:', collabError);
            }

            // 组装完整的分配数据
            const fullAssignment = {
                ...assignment,
                title: assignment.chapters?.title || assignment.title,
                description: assignment.description || assignment.chapters?.summary || '',
                collaborators: collaborators || []
            };

            return fullAssignment;

        } catch (error) {
            console.error('查找分配记录失败:', error);
            return null;
        }
    }

    // 显示编辑分配模态对话框
    async showEditAssignmentModal(assignment) {
        // 确保团队成员数据已加载
        await this.loadTeamMembers();

        const members = await this.getProjectMembers();

        // 创建模态对话框HTML
        const modalHtml = `
            <div class="assignment-modal" id="editAssignmentModal">
                <div class="assignment-modal-content">
                    <div class="assignment-modal-header">
                        <h3 class="assignment-modal-title">
                            <i class="fas fa-edit"></i>
                            编辑章节分配
                        </h3>
                    </div>
                    <div class="assignment-modal-body">
                        <form id="editAssignmentForm">
                            <input type="hidden" id="assignmentId" value="${assignment.id}">

                            <div class="form-group">
                                <label class="form-label">章节标题</label>
                                <input type="text" class="form-input" id="assignmentTitle" value="${assignment.title}" readonly>
                            </div>

                            <div class="form-group">
                                <label class="form-label">章节描述</label>
                                <textarea class="form-textarea" id="assignmentDescription" placeholder="请输入章节描述">${assignment.description}</textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">主笔作者</label>
                                    <div class="user-selector">
                                        <input type="text" class="form-input user-selector-input" id="leadAuthorInput"
                                               value="${assignment.lead_author?.full_name || ''}" readonly
                                               placeholder="选择主笔作者">
                                        <div class="user-dropdown" id="leadAuthorDropdown">
                                            ${members.map(member => `
                                                <div class="user-option ${member.id === assignment.lead_author?.id ? 'selected' : ''}"
                                                     data-action="selectUser" data-type="leadAuthor" data-user-id="${member.id}" data-user-name="${member.full_name}">
                                                    <div class="user-avatar-small">${member.avatar}</div>
                                                    <div class="user-info">
                                                        <div class="user-name">${member.full_name}</div>
                                                        <div class="user-role">${member.role}</div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">审核者</label>
                                    <div class="user-selector">
                                        <input type="text" class="form-input user-selector-input" id="reviewerInput"
                                               value="${assignment.reviewer?.full_name || ''}" readonly
                                               placeholder="选择审核者">
                                        <div class="user-dropdown" id="reviewerDropdown">
                                            ${members.map(member => `
                                                <div class="user-option ${member.id === assignment.reviewer?.id ? 'selected' : ''}"
                                                     data-action="selectUser" data-type="reviewer" data-user-id="${member.id}" data-user-name="${member.full_name}">
                                                    <div class="user-avatar-small">${member.avatar}</div>
                                                    <div class="user-info">
                                                        <div class="user-name">${member.full_name}</div>
                                                        <div class="user-role">${member.role}</div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">协作者</label>
                                <div class="user-selector">
                                    <input type="text" class="form-input user-selector-input" id="collaboratorsInput"
                                           value="${assignment.collaborators?.map(c => c.full_name).join(', ') || ''}" readonly
                                           placeholder="选择协作者（可多选）">
                                    <div class="user-dropdown" id="collaboratorsDropdown">
                                        ${members.map(member => {
                                            const isSelected = assignment.collaborators?.some(c => c.id === member.id);
                                            return `
                                                <div class="user-option ${isSelected ? 'selected' : ''}"
                                                     data-action="toggleCollaborator" data-user-id="${member.id}" data-user-name="${member.full_name}">
                                                    <div class="user-avatar-small">${member.avatar}</div>
                                                    <div class="user-info">
                                                        <div class="user-name">${member.full_name}</div>
                                                        <div class="user-role">${member.role}</div>
                                                    </div>
                                                    ${isSelected ? '<i class="fas fa-check" style="color: #10b981;"></i>' : ''}
                                                </div>
                                            `;
                                        }).join('')}
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="assignmentStatus">
                                        <option value="pending" ${assignment.status === 'pending' ? 'selected' : ''}>待分配</option>
                                        <option value="writing" ${assignment.status === 'writing' ? 'selected' : ''}>编制中</option>
                                        <option value="reviewing" ${assignment.status === 'reviewing' ? 'selected' : ''}>审核中</option>
                                        <option value="completed" ${assignment.status === 'completed' ? 'selected' : ''}>已完成</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">截止日期</label>
                                    <input type="date" class="form-input" id="assignmentDueDate"
                                           value="${assignment.due_date ? new Date(assignment.due_date).toISOString().split('T')[0] : ''}">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="assignment-modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="collaborationManager.closeEditModal()">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="collaborationManager.saveAssignment()">
                            <i class="fas fa-save"></i>
                            保存
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 初始化选中的协作者
        this.selectedCollaborators = assignment.collaborators ? [...assignment.collaborators] : [];

        // 绑定事件监听器（在DOM元素创建后立即绑定）
        this.bindModalEventListeners();

        // 显示模态对话框
        setTimeout(() => {
            document.getElementById('editAssignmentModal').classList.add('active');
        }, 10);
    }

    // 绑定模态对话框事件监听器
    bindModalEventListeners() {
        // 等待DOM元素完全创建
        setTimeout(() => {
            // 绑定输入框点击事件
            const leadAuthorInput = document.getElementById('leadAuthorInput');
            const reviewerInput = document.getElementById('reviewerInput');
            const collaboratorsInput = document.getElementById('collaboratorsInput');

            if (leadAuthorInput) {
                leadAuthorInput.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleUserDropdown('leadAuthor');
                });
            }
            if (reviewerInput) {
                reviewerInput.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleUserDropdown('reviewer');
                });
            }
            if (collaboratorsInput) {
                collaboratorsInput.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleUserDropdown('collaborators');
                });
            }

            // 绑定用户选项点击事件
            document.querySelectorAll('#editAssignmentModal .user-option').forEach(option => {
                const action = option.getAttribute('data-action');

                if (action === 'selectUser') {
                    const type = option.getAttribute('data-type');
                    const userId = option.getAttribute('data-user-id');
                    const userName = option.getAttribute('data-user-name');
                    option.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.selectUser(type, userId, userName);
                    });
                } else if (action === 'toggleCollaborator') {
                    const userId = option.getAttribute('data-user-id');
                    const userName = option.getAttribute('data-user-name');
                    option.addEventListener('click', async (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        await this.toggleCollaborator(userId, userName);
                    });
                }
            });

            // 点击外部关闭下拉框（使用命名函数以便移除）
            this.outsideClickHandler = (e) => {
                if (!e.target.closest('.user-selector')) {
                    document.querySelectorAll('.user-dropdown').forEach(d => d.classList.remove('active'));
                }
            };
            document.addEventListener('click', this.outsideClickHandler);
        }, 0);
    }

    // 切换用户下拉框显示
    toggleUserDropdown(type) {
        const dropdown = document.getElementById(`${type}Dropdown`);
        const isActive = dropdown.classList.contains('active');

        // 关闭所有下拉框
        document.querySelectorAll('.user-dropdown').forEach(d => d.classList.remove('active'));

        // 切换当前下拉框
        if (!isActive) {
            dropdown.classList.add('active');
        }
    }

    // 选择用户
    selectUser(type, userId, userName) {
        const input = document.getElementById(`${type}Input`);
        const dropdown = document.getElementById(`${type}Dropdown`);

        input.value = userName;
        dropdown.classList.remove('active');

        // 更新选中状态
        dropdown.querySelectorAll('.user-option').forEach(option => {
            option.classList.remove('selected');
        });
        const selectedOption = dropdown.querySelector(`[data-user-id="${userId}"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
        }
    }

    // 切换协作者选择
    async toggleCollaborator(userId, userName) {
        const members = await this.getProjectMembers();
        if (!Array.isArray(members)) {
            console.error('获取团队成员失败');
            return;
        }

        const member = members.find(m => m.id === userId);

        if (!member) return;

        const existingIndex = this.selectedCollaborators.findIndex(c => c.id === userId);

        if (existingIndex >= 0) {
            // 移除协作者
            this.selectedCollaborators.splice(existingIndex, 1);
        } else {
            // 添加协作者
            this.selectedCollaborators.push(member);
        }

        // 更新输入框显示
        const input = document.getElementById('collaboratorsInput');
        input.value = this.selectedCollaborators.map(c => c.full_name).join(', ');

        // 更新选中状态
        const option = document.querySelector(`[data-user-id="${userId}"][data-action="toggleCollaborator"]`);
        if (option) {
            if (existingIndex >= 0) {
                option.classList.remove('selected');
                const checkIcon = option.querySelector('.fa-check');
                if (checkIcon) checkIcon.remove();
            } else {
                option.classList.add('selected');
                option.insertAdjacentHTML('beforeend', '<i class="fas fa-check" style="color: #10b981;"></i>');
            }
        }
    }

    // 关闭编辑模态对话框
    closeEditModal() {
        // 移除外部点击事件监听器
        if (this.outsideClickHandler) {
            document.removeEventListener('click', this.outsideClickHandler);
            this.outsideClickHandler = null;
        }

        const modal = document.getElementById('editAssignmentModal');
        if (modal) {
            modal.classList.remove('active');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    // 保存分配
    async saveAssignment() {
        try {
            const assignmentId = document.getElementById('assignmentId').value;
            const leadAuthor = await this.getSelectedUser('leadAuthor');
            const reviewer = await this.getSelectedUser('reviewer');

            const updateData = {
                description: document.getElementById('assignmentDescription').value,
                status: document.getElementById('assignmentStatus').value,
                due_date: document.getElementById('assignmentDueDate').value,
                lead_author_id: leadAuthor ? leadAuthor.id : null,
                reviewer_id: reviewer ? reviewer.id : null,
                updated_at: new Date().toISOString()
            };

            console.log('保存分配数据:', { assignmentId, updateData, collaborators: this.selectedCollaborators });

            // 更新章节分配基本信息
            const { error: updateError } = await supabaseManager.supabase
                .from('chapter_assignments')
                .update(updateData)
                .eq('id', assignmentId);

            if (updateError) {
                console.error('更新分配失败:', updateError);
                alert('保存分配失败: ' + updateError.message);
                return;
            }

            // 处理协作者关系
            if (this.selectedCollaborators && this.selectedCollaborators.length > 0) {
                // 先删除现有的协作者关系
                const { error: deleteError } = await supabaseManager.supabase
                    .from('chapter_collaborators')
                    .delete()
                    .eq('assignment_id', assignmentId);

                if (deleteError) {
                    console.error('删除现有协作者失败:', deleteError);
                }

                // 添加新的协作者关系
                const collaboratorData = this.selectedCollaborators.map(collaborator => ({
                    assignment_id: assignmentId,
                    user_id: collaborator.id,
                    role: 'collaborator',  // 修复：使用数据库约束允许的角色值
                    status: 'pending'
                }));

                console.log('准备插入的协作者数据:', collaboratorData);
                console.log('分配ID:', assignmentId);
                console.log('选中的协作者:', this.selectedCollaborators);

                const { error: insertError } = await supabaseManager.supabase
                    .from('chapter_collaborators')
                    .insert(collaboratorData);

                if (insertError) {
                    console.error('添加协作者失败:', insertError);
                    alert('保存协作者失败: ' + insertError.message);
                    return;
                }
            } else {
                // 如果没有协作者，删除所有现有的协作者关系
                const { error: deleteError } = await supabaseManager.supabase
                    .from('chapter_collaborators')
                    .delete()
                    .eq('assignment_id', assignmentId);

                if (deleteError) {
                    console.error('删除协作者失败:', deleteError);
                }
            }

            alert('分配保存成功！');
            this.closeEditModal();

            // 刷新列表
            await this.refreshAssignments();
        } catch (error) {
            console.error('保存分配失败:', error);
            alert('保存分配失败: ' + error.message);
        }
    }

    // 获取选中的用户
    async getSelectedUser(type) {
        const input = document.getElementById(`${type}Input`);
        const userName = input.value;

        if (!userName) return null;

        const members = await this.getProjectMembers();
        if (!Array.isArray(members)) {
            console.error('获取团队成员失败');
            return null;
        }

        return members.find(m => m.full_name === userName);
    }

    // 初始化事件监听器
    initializeEventListeners() {
        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-selector')) {
                document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }

    // 查看分配详情
    viewAssignment(assignmentId) {
        console.log('查看分配详情:', assignmentId);

        // 查找要查看的分配
        const assignment = this.findAssignmentById(assignmentId);
        if (!assignment) {
            this.showNotification('未找到指定的分配记录', 'error');
            return;
        }

        this.showViewAssignmentModal(assignment);
    }

    // 显示查看分配详情模态对话框
    showViewAssignmentModal(assignment) {
        const modalHtml = `
            <div class="assignment-modal" id="viewAssignmentModal">
                <div class="assignment-modal-content">
                    <div class="assignment-modal-header">
                        <h3 class="assignment-modal-title">
                            <i class="fas fa-eye"></i>
                            章节分配详情
                        </h3>
                        <button class="modal-close-btn" onclick="collaborationManager.closeViewModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="assignment-modal-body">
                        <div class="assignment-details">
                            <div class="detail-section">
                                <h4><i class="fas fa-book"></i> 章节信息</h4>
                                <div class="detail-item">
                                    <label>章节标题：</label>
                                    <span>${assignment.title}</span>
                                </div>
                                <div class="detail-item">
                                    <label>章节描述：</label>
                                    <span>${assignment.description || '暂无描述'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>目标字数：</label>
                                    <span>${assignment.word_count_target || '未设置'} 字</span>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h4><i class="fas fa-users"></i> 人员分配</h4>
                                <div class="detail-item">
                                    <label>主笔作者：</label>
                                    <span class="user-info">
                                        <i class="fas fa-user"></i>
                                        ${assignment.lead_author?.full_name || '未分配'}
                                        ${assignment.lead_author?.email ? `(${assignment.lead_author.email})` : ''}
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <label>协作者：</label>
                                    <span class="user-info">
                                        <i class="fas fa-users"></i>
                                        ${assignment.collaborators && assignment.collaborators.length > 0
                                            ? assignment.collaborators.map(c => c.full_name).join(', ')
                                            : '无协作者'}
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <label>审核者：</label>
                                    <span class="user-info">
                                        <i class="fas fa-user-check"></i>
                                        ${assignment.reviewer?.full_name || '未分配'}
                                        ${assignment.reviewer?.email ? `(${assignment.reviewer.email})` : ''}
                                    </span>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h4><i class="fas fa-chart-line"></i> 进度状态</h4>
                                <div class="detail-item">
                                    <label>当前状态：</label>
                                    <span class="status-badge status-${assignment.status}">
                                        ${this.getStatusText(assignment.status)}
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <label>完成进度：</label>
                                    <div class="progress-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: ${this.calculateProgress(assignment)}%"></div>
                                        </div>
                                        <span class="progress-text">${this.calculateProgress(assignment)}%</span>
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <label>优先级：</label>
                                    <span class="priority-badge priority-${assignment.priority || 'medium'}">
                                        ${this.getPriorityText(assignment.priority)}
                                    </span>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h4><i class="fas fa-calendar"></i> 时间信息</h4>
                                <div class="detail-item">
                                    <label>创建时间：</label>
                                    <span>${assignment.created_at ? new Date(assignment.created_at).toLocaleString('zh-CN') : '未知'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>截止日期：</label>
                                    <span class="${assignment.due_date && new Date(assignment.due_date) < new Date() ? 'overdue' : ''}">
                                        ${assignment.due_date ? new Date(assignment.due_date).toLocaleDateString('zh-CN') : '无截止日期'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="assignment-modal-footer">
                        <button class="btn btn-secondary" onclick="collaborationManager.closeViewModal()">
                            关闭
                        </button>
                        ${assignment.id ? `
                            <button class="btn btn-primary" onclick="collaborationManager.editAssignment('${assignment.id}'); collaborationManager.closeViewModal();">
                                <i class="fas fa-edit"></i>
                                编辑分配
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态对话框
        setTimeout(() => {
            document.getElementById('viewAssignmentModal').classList.add('active');
        }, 10);
    }

    // 关闭查看模态对话框
    closeViewModal() {
        const modal = document.getElementById('viewAssignmentModal');
        if (modal) {
            modal.classList.remove('active');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    // 获取优先级文本
    getPriorityText(priority) {
        const priorityMap = {
            'high': '高优先级',
            'medium': '中优先级',
            'low': '低优先级'
        };
        return priorityMap[priority] || '中优先级';
    }

    // 删除分配
    async deleteAssignment(assignmentId) {
        if (!confirm('确定要删除这个分配吗？此操作不可撤销。')) {
            return;
        }

        try {
            console.log('删除分配:', assignmentId);

            // 首先尝试从数据库删除
            const { error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .delete()
                .eq('id', assignmentId);

            if (error) {
                console.warn('数据库删除失败，使用模拟删除:', error);

                // 在模拟环境下，从DOM中直接移除该项
                this.removeAssignmentFromDOM(assignmentId);
                this.showNotification('分配已删除（模拟操作）', 'success');

                // 更新统计数据
                this.updateAssignmentStatsAfterDelete();
            } else {
                this.showNotification('分配已成功删除', 'success');
                // 重新加载分配列表
                await this.loadChapterAssignments();
            }

        } catch (error) {
            console.error('删除分配失败:', error);
            this.showNotification('删除分配失败: ' + error.message, 'error');
        }
    }

    // 显示通知消息
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;
            min-width: 300px;
            max-width: 500px;
            font-size: 14px;
            font-weight: 500;
            animation: slideInRight 0.3s ease;
            background: ${this.getNotificationColor(type)};
            color: white;
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, 3000);
    }

    // 获取通知图标
    getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // 获取通知颜色
    getNotificationColor(type) {
        const colors = {
            'success': '#10b981',
            'error': '#ef4444',
            'warning': '#f59e0b',
            'info': '#3b82f6'
        };
        return colors[type] || '#3b82f6';
    }

    // 从DOM中移除分配项
    removeAssignmentFromDOM(assignmentId) {
        const assignmentItems = document.querySelectorAll('.assignment-item');
        for (const item of assignmentItems) {
            const editButton = item.querySelector(`button[onclick*="editAssignment('${assignmentId}')"]`);
            if (editButton) {
                // 添加删除动画
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '0';
                item.style.transform = 'translateX(-100%)';

                // 延迟移除元素
                setTimeout(() => {
                    item.remove();
                }, 300);
                break;
            }
        }
    }

    // 删除后更新统计数据
    updateAssignmentStatsAfterDelete() {
        const remainingItems = document.querySelectorAll('.assignment-item').length - 1; // -1 因为删除动画还在进行

        // 更新总章节数
        const totalElement = document.querySelector('.assignment-stats .stat-item:first-child .stat-number');
        if (totalElement) {
            totalElement.textContent = Math.max(0, remainingItems);
        }

        // 重新计算其他统计数据
        setTimeout(() => {
            const items = document.querySelectorAll('.assignment-item');
            let completedCount = 0;
            let reviewingCount = 0;
            const authors = new Set();

            items.forEach(item => {
                const statusElement = item.querySelector('.status-badge');
                if (statusElement) {
                    const statusText = statusElement.textContent.trim();
                    if (statusText === '已完成') {
                        completedCount++;
                    } else if (statusText === '审核中') {
                        reviewingCount++;
                    }
                }

                const authorElement = item.querySelector('.user-badge:first-of-type');
                if (authorElement) {
                    const authorName = authorElement.textContent.trim();
                    if (authorName && authorName !== '未分配') {
                        authors.add(authorName);
                    }
                }
            });

            // 更新统计显示
            const statsElements = document.querySelectorAll('.assignment-stats .stat-item .stat-number');
            if (statsElements.length >= 4) {
                statsElements[0].textContent = items.length; // 总章节
                statsElements[1].textContent = authors.size; // 参与作者
                statsElements[2].textContent = completedCount; // 已完成
                statsElements[3].textContent = reviewingCount; // 待审核
            }
        }, 350);
    }

    // 刷新分配
    refreshAssignments() {
        this.loadChapterAssignments();
    }

    // 切换协作标签
    switchCollabTab(tabName) {
        // 更新标签状态
        document.querySelectorAll('.collab-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.collab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // 确保项目信息同步
        this.syncProjectFromGlobal();

        // 加载对应内容
        switch(tabName) {
            case 'members':
                this.loadTeamMembers();
                break;
            case 'assignments':
                this.loadChapterAssignments();
                break;
            case 'permissions':
                this.loadPermissionsMatrix();
                break;
        }
    }

    // 从全局状态同步项目信息
    syncProjectFromGlobal() {
        try {
            // 从localStorage获取项目ID
            const selectedProjectId = localStorage.getItem('selectedProjectId');

            // 从全局currentProject变量获取项目信息
            if (typeof currentProject !== 'undefined' && currentProject.id) {
                this.currentProject = {
                    id: currentProject.id,
                    title: currentProject.title,
                    description: currentProject.description,
                    status: currentProject.status,
                    created_at: currentProject.created_at,
                    updated_at: currentProject.updated_at
                };
                this.currentProjectId = currentProject.id;
                console.log('从全局状态同步项目信息:', this.currentProject.title);
                return;
            }

            // 如果全局状态没有项目ID，但localStorage有，使用localStorage的
            if (selectedProjectId && !this.currentProjectId) {
                this.currentProjectId = selectedProjectId;
                console.log('从localStorage同步项目ID:', selectedProjectId);
            }

            // 如果都没有，设置默认项目用于演示
            if (!this.currentProject && !this.currentProjectId) {
                this.currentProject = {
                    id: 'demo-project-1',
                    title: '《大模型技术与油气应用概论》',
                    description: '演示项目',
                    status: 'active'
                };
                this.currentProjectId = 'demo-project-1';
                console.log('设置默认演示项目');
            }
        } catch (error) {
            console.error('同步项目信息失败:', error);
        }
    }

    // 更新仪表板
    updateDashboard() {
        if (!this.currentProject) return;

        // 更新统计数据
        const totalChapters = this.countTotalChapters();
        const completedChapters = this.countCompletedChapters();
        const teamMembers = this.currentProject.members ? this.currentProject.members.length : 0;

        // 更新统计数据到项目概览
        this.updateOverviewStats(totalChapters, completedChapters, teamMembers);

        // 加载最近活动
        this.loadRecentActivities();

        // 加载我的任务
        this.loadMyTasks();
    }

    // 计算总章节数
    countTotalChapters() {
        if (!currentProject.outline) return 0;
        
        function countItems(items) {
            let count = 0;
            items.forEach(item => {
                if (item.level > 0) count++; // 只计算章节，不计算篇
                if (item.children) {
                    count += countItems(item.children);
                }
            });
            return count;
        }
        
        return countItems(currentProject.outline);
    }

    // 计算已完成章节数
    countCompletedChapters() {
        return Object.keys(currentProject.chapters || {}).length;
    }

    // 显示面板
    showPanel(panelName) {
        if (typeof showPanel === 'function') {
            showPanel(panelName);

            // 如果显示的是项目概览面板，确保数据更新
            if (panelName === 'overview') {
                setTimeout(() => {
                    if (typeof updateProjectOverview === 'function') {
                        updateProjectOverview(this.currentProject);
                    }
                    this.updateDashboard();
                }, 200);
            }
        }
    }

    // 项目下拉菜单控制
    toggleProjectDropdown() {
        console.log('🔥 collaboration.js toggleProjectDropdown被调用了！');

        const dropdownInline = document.getElementById('project-list-inline');
        const btnInline = document.getElementById('project-btn-inline');

        if (!dropdownInline || !btnInline) {
            console.error('项目下拉列表元素未找到');
            return;
        }

        console.log('切换项目下拉框，当前状态:', dropdownInline.classList.contains('show'));

        // 切换下拉菜单显示状态
        const isOpen = dropdownInline.classList.contains('show');

        if (isOpen) {
            this.closeProjectDropdown();
            console.log('项目下拉框已隐藏');
        } else {
            dropdownInline.classList.add('show');
            dropdownInline.style.display = 'block';
            btnInline.classList.add('active');

            // 加载最新的项目列表
            if (typeof loadUserProjects === 'function') {
                loadUserProjects();
            }

            console.log('项目下拉框已显示');
        }
    }

    closeProjectDropdown() {
        const dropdownInline = document.getElementById('project-list-inline');
        const btnInline = document.getElementById('project-btn-inline');

        if (dropdownInline) {
            dropdownInline.classList.remove('show');
            dropdownInline.style.display = 'none';
        }
        if (btnInline) {
            btnInline.classList.remove('active');
        }
    }

    // 用户下拉菜单控制
    toggleUserAvatarDropdown() {
        const dropdown = document.getElementById('user-dropdown');
        dropdown.classList.toggle('show');
    }

    closeUserDropdown() {
        const dropdown = document.getElementById('user-dropdown');
        dropdown.classList.remove('show');
    }

    // 加载最近活动
    async loadRecentActivities() {
        try {
            const activitiesList = document.getElementById('recent-activities-list');
            if (!activitiesList) return;

            // 模拟最近活动数据
            const activities = [
                {
                    id: 1,
                    type: 'chapter_updated',
                    user: '魏启安',
                    action: '更新了章节',
                    target: '第1章 大模型基本概念与内涵',
                    time: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前
                },
                {
                    id: 2,
                    type: 'comment_added',
                    user: '魏启安',
                    action: '添加了评论',
                    target: '第2章 大模型技术原理',
                    time: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前
                }
            ];

            activitiesList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas ${this.getActivityIcon(activity.type)}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">
                            <strong>${activity.user}</strong> ${activity.action}
                            <span class="activity-target">${activity.target}</span>
                        </div>
                        <div class="activity-time">${this.formatTime(activity.time)}</div>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('加载最近活动失败:', error);
        }
    }



    // 加载权限矩阵
    async loadPermissionsMatrix() {
        try {
            const matrixContainer = document.getElementById('permissions-matrix');
            if (!matrixContainer) return;

            // 尝试同步项目信息
            this.syncProjectFromGlobal();

            // 检查是否有当前项目
            if (!this.currentProject || !this.currentProject.id) {
                console.log('没有选择项目，无法加载权限矩阵');
                matrixContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🔐</div>
                        <h3>请先选择项目</h3>
                        <p>选择一个项目后即可查看权限设置</p>
                    </div>
                `;
                return;
            }

            // 尝试从数据库加载成员权限
            let members = [];
            try {
                const { data: dbMembers, error } = await supabaseManager.supabase
                    .from('project_members')
                    .select(`
                        user_id,
                        role,
                        user_profiles!project_members_user_id_fkey(full_name)
                    `)
                    .eq('project_id', this.currentProject.id);

                if (!error && dbMembers) {
                    members = dbMembers;
                }
            } catch (error) {
                console.warn('从数据库加载权限失败，使用模拟数据:', error);
            }

            // 定义权限矩阵
            const permissions = [
                { name: '项目管理', owner: true, admin: true, editor: false, author: false, reviewer: false },
                { name: '成员邀请', owner: true, admin: true, editor: false, author: false, reviewer: false },
                { name: '角色分配', owner: true, admin: true, editor: false, author: false, reviewer: false },
                { name: '大纲编辑', owner: true, admin: true, editor: true, author: false, reviewer: false },
                { name: '章节分配', owner: true, admin: true, editor: true, author: false, reviewer: false },
                { name: '章节编写', owner: true, admin: true, editor: true, author: true, reviewer: false },
                { name: '内容审核', owner: true, admin: true, editor: true, author: false, reviewer: true },
                { name: '评论反馈', owner: true, admin: true, editor: true, author: true, reviewer: true },
                { name: '文献管理', owner: true, admin: true, editor: true, author: true, reviewer: false },
                { name: '进度查看', owner: true, admin: true, editor: true, author: true, reviewer: true }
            ];

            // 显示权限矩阵
            matrixContainer.innerHTML = `
                <div class="permissions-table">
                    <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <thead style="background: #f8f9fa;">
                            <tr>
                                <th style="padding: 15px; text-align: left; border-bottom: 1px solid #dee2e6;">功能</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #dee2e6;">项目所有者</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #dee2e6;">管理员</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #dee2e6;">编辑者</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #dee2e6;">作者</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #dee2e6;">审阅者</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${permissions.map(permission => `
                                <tr style="border-bottom: 1px solid #f1f3f4;">
                                    <td style="padding: 12px 15px; font-weight: 500;">${permission.name}</td>
                                    <td style="padding: 12px 15px; text-align: center;"><i class="fas fa-${permission.owner ? 'check' : 'times'}" style="color: ${permission.owner ? '#10b981' : '#ef4444'};"></i></td>
                                    <td style="padding: 12px 15px; text-align: center;"><i class="fas fa-${permission.admin ? 'check' : 'times'}" style="color: ${permission.admin ? '#10b981' : '#ef4444'};"></i></td>
                                    <td style="padding: 12px 15px; text-align: center;"><i class="fas fa-${permission.editor ? 'check' : 'times'}" style="color: ${permission.editor ? '#10b981' : '#ef4444'};"></i></td>
                                    <td style="padding: 12px 15px; text-align: center;"><i class="fas fa-${permission.author ? 'check' : 'times'}" style="color: ${permission.author ? '#10b981' : '#ef4444'};"></i></td>
                                    <td style="padding: 12px 15px; text-align: center;"><i class="fas fa-${permission.reviewer ? 'check' : 'times'}" style="color: ${permission.reviewer ? '#10b981' : '#ef4444'};"></i></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                ${members && members.length > 0 ? `
                    <div style="margin-top: 20px;">
                        <h4 style="margin-bottom: 15px; color: #374151;">当前团队成员</h4>
                        <div class="team-members-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 15px;">
                            ${members.map(member => `
                                <div class="member-card" style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e5e7eb; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="member-avatar" style="width: 40px; height: 40px; border-radius: 50%; background: #3b82f6; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">
                                            ${(member.user_profiles?.full_name || '未知')[0].toUpperCase()}
                                        </div>
                                        <div>
                                            <div style="font-weight: 500; color: #111827;">${member.user_profiles?.full_name || '未知用户'}</div>
                                            <div style="font-size: 12px; color: #6b7280; text-transform: capitalize;">${member.role}</div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;

        } catch (error) {
            console.error('加载权限矩阵失败:', error);
        }
    }

    // 辅助方法
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        return Math.floor(diff / 86400000) + '天前';
    }

    getChapterStatusText(status) {
        const statusMap = {
            'draft': '草稿',
            'writing': '编写中',
            'review': '审阅中',
            'approved': '已批准',
            'published': '已发布'
        };
        return statusMap[status] || status;
    }

    getActivityIcon(type) {
        const iconMap = {
            'chapter_updated': 'fa-edit',
            'comment_added': 'fa-comment',
            'member_joined': 'fa-user-plus',
            'project_created': 'fa-plus-circle'
        };
        return iconMap[type] || 'fa-info-circle';
    }

    getRoleText(role) {
        const roleMap = {
            'owner': '所有者',
            'editor': '编辑者',
            'author': '作者',
            'reviewer': '审阅者'
        };
        return roleMap[role] || role;
    }

    hasPermission(role, permission) {
        const permissions = {
            'owner': ['查看', '编辑', '评论', '管理'],
            'editor': ['查看', '编辑', '评论'],
            'author': ['查看', '编辑'],
            'reviewer': ['查看', '评论']
        };
        return permissions[role]?.includes(permission) || false;
    }

    updatePermission(userId, permission, granted) {
        console.log(`更新权限: 用户${userId}, 权限${permission}, 授予${granted}`);
        // 这里可以添加实际的权限更新逻辑
    }

    reassignChapter(chapterId) {
        console.log(`重新分配章节: ${chapterId}`);
        // 这里可以添加重新分配章节的逻辑
    }

    // 加载我的任务
    async loadMyTasks() {
        try {
            const user = await supabaseManager.getCurrentUser();
            if (!user || !this.currentProjectId) {
                this.displayEmptyTasks();
                return;
            }

            // 简化查询，避免RLS问题
            const { data: tasks, error } = await supabaseManager.supabase
                .from('chapters')
                .select('id, title, status, updated_at')
                .eq('created_by', user.id)
                .eq('project_id', this.currentProjectId)
                .order('updated_at', { ascending: false })
                .limit(5);

            if (error) {
                console.warn('加载任务时出现警告:', error);
                this.displayEmptyTasks();
                return;
            }

            const tasksList = document.getElementById('my-tasks-list');
            if (!tasksList) return;

            if (!tasks || tasks.length === 0) {
                tasksList.innerHTML = '<div class="no-tasks">暂无任务</div>';
                return;
            }

            tasksList.innerHTML = tasks.map(task => `
                <div class="task-item" data-chapter-id="${task.id}">
                    <div class="task-title">${task.title}</div>
                    <div class="task-status status-${task.status}">${this.getChapterStatusText(task.status)}</div>
                    <div class="task-time">${this.formatTime(task.updated_at)}</div>
                </div>
            `).join('');

            // 添加点击事件
            tasksList.querySelectorAll('.task-item').forEach(item => {
                item.addEventListener('click', () => {
                    const chapterId = item.dataset.chapterId;
                    this.openChapter(chapterId);
                });
            });

        } catch (error) {
            console.error('加载我的任务失败:', error);
            this.displayEmptyTasks();
        }
    }

    // 显示空任务列表
    displayEmptyTasks() {
        const tasksList = document.getElementById('my-tasks-list');
        if (tasksList) {
            tasksList.innerHTML = `
                <div class="no-tasks">
                    <i class="fas fa-tasks"></i>
                    <p>暂无任务</p>
                    <small>双击本书目录中的章节开始编写</small>
                </div>
            `;
        }
    }

    // 打开章节
    openChapter(chapterId) {
        if (typeof showPanel === 'function') {
            showPanel('editor');
            // 这里可以添加加载特定章节的逻辑
        }
    }

    // 安全的DOM元素更新方法
    safeUpdateElement(elementId, content) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = content;
        } else {
            console.warn(`Element with ID '${elementId}' not found`);
        }
    }

    // 更新项目概览统计数据
    updateOverviewStats(totalChapters, completedChapters, teamMembers) {
        // 项目概览页面的元素ID
        const overviewElements = {
            'total-chapters-count': totalChapters,
            'completed-chapters-count': completedChapters,
            'team-members-count': teamMembers,
            'writing-chapters': Math.max(0, totalChapters - completedChapters),
            'review-chapters': 0 // 暂时设为0
        };

        // 更新所有统计元素
        Object.entries(overviewElements).forEach(([elementId, value]) => {
            this.safeUpdateElement(elementId, value);
        });

        // 更新进度条
        const progressPercentage = totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0;
        this.safeUpdateElement('overall-progress-text', `${progressPercentage}%`);

        const progressBar = document.getElementById('overall-progress-bar');
        if (progressBar) {
            progressBar.style.width = `${progressPercentage}%`;
        }

        // 调用全局的项目概览更新函数
        if (typeof updateProjectOverview === 'function') {
            setTimeout(() => updateProjectOverview(this.currentProject), 100);
        }
    }

    // 计算总章节数
    countTotalChapters() {
        if (!this.currentProject || !this.currentProject.outline) return 0;
        return this.currentProject.outline.filter(item => item.level > 0).length;
    }

    // 计算已完成章节数
    countCompletedChapters() {
        if (!this.currentProject || !this.currentProject.chapters) return 0;
        return Object.values(this.currentProject.chapters).filter(chapter =>
            chapter.status === 'completed' || chapter.status === 'published'
        ).length;
    }

    // 同步项目数据到本地
    async syncProjectData() {
        if (!this.currentProjectId) return;

        try {
            // 同步项目基本信息到全局变量
            if (typeof currentProject !== 'undefined') {
                currentProject.title = this.currentProject.title;
                currentProject.description = this.currentProject.description;
                currentProject.status = this.currentProject.status;

                // 确保outline数组存在
                if (!currentProject.outline) {
                    currentProject.outline = [];
                }

                // 确保chapters对象存在
                if (!currentProject.chapters) {
                    currentProject.chapters = {};
                }
            }
        } catch (error) {
            console.error('同步项目数据失败:', error);
        }
    }
}

// 创建全局协作管理器实例
const collaborationManager = new CollaborationManager();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
    collaborationManager.initialize();
});

// 全局函数
function toggleProjectDropdown() {
    console.log('🔥 全局toggleProjectDropdown被调用');

    try {
        if (typeof collaborationManager !== 'undefined' && collaborationManager.toggleProjectDropdown) {
            console.log('调用collaborationManager.toggleProjectDropdown');
            collaborationManager.toggleProjectDropdown();
        } else {
            console.log('collaborationManager不存在，使用备用逻辑');
            // 备用逻辑：直接操作DOM
            const dropdownInline = document.getElementById('project-list-inline');
            const btnInline = document.getElementById('project-btn-inline');

            if (!dropdownInline || !btnInline) {
                console.error('项目下拉列表元素未找到');
                return;
            }

            console.log('切换项目下拉框，当前状态:', dropdownInline.classList.contains('show'));

            // 切换下拉菜单显示状态
            const isOpen = dropdownInline.classList.contains('show');

            if (isOpen) {
                dropdownInline.classList.remove('show');
                dropdownInline.style.display = 'none';
                btnInline.classList.remove('active');
                console.log('项目下拉框已隐藏');
            } else {
                dropdownInline.classList.add('show');
                dropdownInline.style.display = 'block';
                btnInline.classList.add('active');

                // 加载最新的项目列表
                if (typeof loadUserProjects === 'function') {
                    loadUserProjects();
                }

                console.log('项目下拉框已显示');
            }
        }
    } catch (error) {
        console.error('toggleProjectDropdown执行失败:', error);
    }
}

function toggleUserDropdown() {
    collaborationManager.toggleUserAvatarDropdown();
}

function showCreateProject() {
    // 显示创建项目对话框
    showModal('创建新项目', `
        <div class="form-group">
            <label>项目标题：</label>
            <input type="text" id="project-title" class="form-input" placeholder="输入项目标题">
        </div>
        <div class="form-group">
            <label>项目描述：</label>
            <textarea id="project-description" class="form-textarea" placeholder="输入项目描述"></textarea>
        </div>
    `, async () => {
        const title = document.getElementById('project-title').value;
        const description = document.getElementById('project-description').value;
        
        if (!title.trim()) {
            showNotification('请输入项目标题', 'warning');
            return;
        }
        
        try {
            const project = await supabaseManager.createProject({
                title: title,
                description: description
            });
            
            showNotification('项目创建成功', 'success');
            await collaborationManager.loadUserProjects();
            collaborationManager.selectProject(project);
        } catch (error) {
            showNotification('创建项目失败: ' + error.message, 'error');
        }
    });
}

function signOut() {
    supabaseManager.signOut();
}

// 扩展CollaborationManager类的方法
CollaborationManager.prototype.canManageMembers = function() {
    return this.currentUserRole && ['system_admin', 'owner', 'admin'].includes(this.currentUserRole);
};

CollaborationManager.prototype.canAssignChapters = function() {
    return this.currentUserRole && ['system_admin', 'owner', 'admin', 'editor'].includes(this.currentUserRole);
};

CollaborationManager.prototype.canManageAssignments = function() {
    return this.currentUserRole && ['system_admin', 'owner', 'admin', 'editor'].includes(this.currentUserRole);
};

CollaborationManager.prototype.canViewProject = function() {
    return this.currentUserRole && ['system_admin', 'owner', 'admin', 'editor', 'author', 'reviewer'].includes(this.currentUserRole);
};

CollaborationManager.prototype.canEditContent = function() {
    return this.currentUserRole && ['system_admin', 'owner', 'admin', 'editor', 'author'].includes(this.currentUserRole);
};

CollaborationManager.prototype.canReviewContent = function() {
    return this.currentUserRole && ['system_admin', 'owner', 'admin', 'editor', 'reviewer'].includes(this.currentUserRole);
};

CollaborationManager.prototype.getRoleDisplayName = function(role) {
    const roleNames = {
        'owner': '项目所有者',
        'admin': '管理员',
        'editor': '编辑者',
        'author': '作者',
        'reviewer': '审阅者'
    };
    return roleNames[role] || role;
};

CollaborationManager.prototype.getAssignmentStatusName = function(status) {
    const statusNames = {
        'assigned': '已分配',
        'accepted': '已接受',
        'in_progress': '进行中',
        'completed': '已完成',
        'reviewed': '已审核'
    };
    return statusNames[status] || status;
};

CollaborationManager.prototype.showAssignChapterModal = function() {
    this.showNotification('分配章节功能开发中...', 'info');
};

CollaborationManager.prototype.editMemberRole = function(userId) {
    this.showNotification('编辑角色功能开发中...', 'info');
};

CollaborationManager.prototype.removeMember = async function(userId) {
    if (!confirm('确定要移除该成员吗？')) {
        return;
    }

    try {
        const { error } = await supabaseManager.supabase
            .from('project_members')
            .delete()
            .eq('project_id', this.currentProject.id)
            .eq('user_id', userId);

        if (error) throw error;

        this.showNotification('成员已移除', 'success');
        this.loadTeamMembers();
    } catch (error) {
        console.error('移除成员失败:', error);
        this.showNotification('移除成员失败: ' + error.message, 'error');
    }
};




// 创建全局协作管理器实例
window.collaborationManager = new CollaborationManager();
