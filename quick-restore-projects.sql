-- 快速恢复项目可见性
-- 立即执行此脚本恢复项目管理页面

-- 1. 禁用所有表的RLS（恢复到原始状态）
ALTER TABLE public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;

-- 2. 检查项目是否可见
SELECT 
    'Projects found:' as status,
    COUNT(*) as count 
FROM public.projects;

-- 3. 显示所有项目
SELECT 
    id,
    title,
    description,
    owner_id,
    status,
    created_at
FROM public.projects 
ORDER BY created_at DESC;

-- 完成
SELECT 'Projects should now be visible!' as result;
