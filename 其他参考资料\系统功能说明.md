# 《大模型技术与油气应用概论》多用户协作系统功能说明

## 系统概述

本系统是专门为《大模型技术与油气应用概论》编写而开发的多用户协作平台，支持团队协作、权限管理、实时编辑等专业功能。

## 核心功能模块

### 1. 用户认证与管理

#### 用户注册
- **邮箱注册**：支持邮箱验证注册
- **用户信息**：姓名、用户名、所属机构、部门等
- **角色分配**：管理员、编辑者、作者、审阅者

#### 用户登录
- **安全登录**：基于 Supabase Auth 的安全认证
- **会话管理**：自动保持登录状态
- **密码重置**：支持邮箱密码重置

#### 用户配置
- **个人资料**：头像、简介、联系方式
- **偏好设置**：界面主题、通知设置
- **权限查看**：当前用户的权限范围

### 2. 项目管理

#### 项目创建
- **项目信息**：标题、描述、状态
- **所有者权限**：项目创建者自动成为所有者
- **可见性控制**：公开、私有、受限访问

#### 项目选择
- **项目列表**：显示用户参与的所有项目
- **角色标识**：显示用户在各项目中的角色
- **快速切换**：便捷的项目切换功能

#### 项目设置
- **基本信息**：修改项目标题和描述
- **成员管理**：添加、移除团队成员
- **权限配置**：设置不同角色的权限

### 3. 团队协作

#### 成员管理
- **邀请用户**：通过邮箱邀请新成员
- **角色分配**：为成员分配不同角色
- **权限控制**：细粒度的权限管理

#### 在线状态
- **实时显示**：显示当前在线的团队成员
- **活动状态**：显示成员的活动状态
- **协作提醒**：实时协作冲突提醒

#### 章节分配
- **任务分配**：将章节分配给特定成员
- **进度跟踪**：跟踪各章节的完成进度
- **协作编辑**：支持多人协作编辑同一章节

### 4. 大纲管理

#### 结构化大纲
- **层级管理**：支持篇-章-节-小节的层级结构
- **拖拽排序**：可视化的大纲重组
- **状态跟踪**：计划中、进行中、已完成等状态

#### 大纲编辑
- **添加章节**：快速添加新的章节
- **编辑修改**：修改章节标题和层级
- **删除管理**：安全的章节删除功能

#### 导入导出
- **文本导入**：支持从文本文件导入大纲
- **JSON导出**：导出结构化的大纲数据
- **格式转换**：支持多种格式转换

### 5. 章节编写

#### 富文本编辑
- **Quill编辑器**：专业的富文本编辑功能
- **格式支持**：粗体、斜体、标题、列表等
- **插入功能**：表格、图片、链接、公式

#### 实时协作
- **同步编辑**：多用户实时协作编辑
- **冲突解决**：自动处理编辑冲突
- **变更追踪**：记录所有编辑变更

#### 版本控制
- **版本历史**：保存每次编辑的版本
- **版本对比**：对比不同版本的差异
- **版本回滚**：恢复到历史版本

#### 权限控制
- **编辑权限**：基于角色的编辑权限
- **审阅权限**：审阅者可添加评论
- **只读权限**：只读用户无法编辑

### 6. 评论与审阅

#### 章节评论
- **位置评论**：在特定位置添加评论
- **回复功能**：支持评论回复和讨论
- **状态管理**：活跃、已解决、已删除状态

#### 审阅流程
- **审阅分配**：将章节分配给审阅者
- **审阅状态**：草稿、审阅中、已批准等
- **审阅意见**：详细的审阅意见和建议

#### 通知系统
- **实时通知**：新评论和审阅的实时通知
- **邮件提醒**：重要事件的邮件提醒
- **消息中心**：统一的消息管理中心

### 7. 参考文献管理

#### 文献录入
- **手动录入**：手动添加文献信息
- **批量导入**：支持CSV、JSON格式导入
- **格式标准**：支持多种引用格式

#### 文献管理
- **分类标签**：为文献添加分类标签
- **搜索过滤**：快速搜索和过滤文献
- **引用插入**：在章节中快速插入引用

#### 引用格式
- **多种格式**：APA、MLA、Chicago等格式
- **自动格式化**：自动生成标准引用格式
- **批量导出**：导出完整的参考文献列表

### 8. 结构图绘制

#### 图表类型
- **思维导图**：可视化的思维导图
- **流程图**：业务流程和技术流程图
- **架构图**：系统架构和技术架构图

#### 编辑功能
- **Mermaid语法**：基于Mermaid的图表编辑
- **实时预览**：编辑时实时预览效果
- **模板库**：预设的图表模板

#### 图表管理
- **图表保存**：保存和管理图表
- **版本控制**：图表的版本管理
- **导出功能**：导出为图片或SVG格式

### 9. 进度管理

#### 项目概览
- **统计仪表板**：项目整体进度统计
- **完成度分析**：各章节完成度分析
- **团队活跃度**：团队成员活跃度统计

#### 任务跟踪
- **个人任务**：显示个人分配的任务
- **截止日期**：任务的截止日期管理
- **优先级**：任务优先级设置

#### 活动日志
- **操作记录**：记录所有用户操作
- **时间线**：项目活动时间线
- **活动分析**：活动数据分析

### 10. 数据管理

#### 自动保存
- **实时保存**：编辑内容自动保存
- **云端同步**：数据自动同步到云端
- **离线支持**：支持离线编辑和同步

#### 备份恢复
- **自动备份**：定期自动备份数据
- **手动备份**：用户手动创建备份
- **数据恢复**：从备份恢复数据

#### 导入导出
- **项目导出**：导出完整项目数据
- **格式支持**：支持多种导出格式
- **数据迁移**：支持数据迁移和同步

## 权限体系

### 角色定义

#### 项目所有者 (Owner)
- 完全的项目控制权
- 可以添加/移除成员
- 可以分配角色和权限
- 可以删除项目

#### 管理员 (Admin)
- 项目管理权限
- 可以管理成员和权限
- 可以编辑所有章节
- 不能删除项目

#### 编辑者 (Editor)
- 可以编辑分配的章节
- 可以添加评论和建议
- 可以管理参考文献
- 可以创建结构图

#### 作者 (Author)
- 可以编写分配的章节
- 可以添加评论
- 只读访问其他章节
- 可以查看项目进度

#### 审阅者 (Reviewer)
- 只读访问所有章节
- 可以添加审阅评论
- 可以查看项目进度
- 不能编辑内容

### 权限矩阵

| 功能 | 所有者 | 管理员 | 编辑者 | 作者 | 审阅者 |
|------|--------|--------|--------|------|--------|
| 项目管理 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 成员管理 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 大纲编辑 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 章节编写 | ✅ | ✅ | ✅ | ✅* | ❌ |
| 添加评论 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 文献管理 | ✅ | ✅ | ✅ | ✅ | ❌ |
| 结构图绘制 | ✅ | ✅ | ✅ | ✅ | ❌ |
| 查看进度 | ✅ | ✅ | ✅ | ✅ | ✅ |

*作者只能编辑分配给自己的章节

## 技术特性

### 实时协作
- **WebSocket连接**：基于Supabase Realtime
- **冲突解决**：自动处理编辑冲突
- **状态同步**：实时同步用户状态

### 数据安全
- **行级安全**：基于RLS的数据安全
- **权限验证**：多层权限验证机制
- **数据加密**：传输和存储数据加密

### 性能优化
- **懒加载**：按需加载数据和组件
- **缓存机制**：智能缓存策略
- **CDN加速**：静态资源CDN加速

### 跨平台支持
- **响应式设计**：适配各种设备
- **浏览器兼容**：支持主流浏览器
- **移动端优化**：移动设备友好

## 使用场景

### 学术写作
- 多人协作编写学术专著
- 分章节分工合作
- 统一的引用和格式管理

### 团队协作
- 跨机构团队协作
- 远程协作编写
- 实时沟通和反馈

### 项目管理
- 写作进度管理
- 任务分配和跟踪
- 质量控制和审阅

### 知识管理
- 结构化知识组织
- 版本控制和历史追踪
- 知识共享和传承

这个多用户协作系统为《大模型技术与油气应用概论》的编写提供了完整的解决方案，支持从项目规划到最终出版的全流程管理。
