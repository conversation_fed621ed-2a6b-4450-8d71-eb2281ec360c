// 项目管理系统修复脚本
// 用于修复可能出现的问题和兼容性问题

// 修复函数集合
const ProjectManagementFixes = {
    
    // 修复Supabase连接问题
    async fixSupabaseConnection() {
        try {
            console.log('🔧 检查Supabase连接...');
            
            if (typeof supabaseManager === 'undefined') {
                console.error('❌ Supabase管理器未初始化');
                return false;
            }

            // 测试连接
            const { data, error } = await supabaseManager.supabase
                .from('projects')
                .select('count')
                .limit(1);

            if (error) {
                console.error('❌ Supabase连接失败:', error);
                return false;
            }

            console.log('✅ Supabase连接正常');
            return true;
        } catch (error) {
            console.error('❌ Supabase连接检查失败:', error);
            return false;
        }
    },

    // 修复本地存储问题
    fixLocalStorage() {
        try {
            console.log('🔧 检查本地存储...');
            
            // 检查localStorage是否可用
            if (typeof Storage === 'undefined') {
                console.error('❌ 浏览器不支持localStorage');
                return false;
            }

            // 测试写入和读取
            const testKey = 'test_' + Date.now();
            localStorage.setItem(testKey, 'test');
            const testValue = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);

            if (testValue !== 'test') {
                console.error('❌ localStorage读写失败');
                return false;
            }

            console.log('✅ 本地存储正常');
            return true;
        } catch (error) {
            console.error('❌ 本地存储检查失败:', error);
            return false;
        }
    },

    // 修复CSS样式问题
    fixCSSStyles() {
        try {
            console.log('🔧 检查CSS样式...');
            
            // 检查关键样式是否加载
            const testElement = document.createElement('div');
            testElement.className = 'project-card';
            document.body.appendChild(testElement);
            
            const styles = window.getComputedStyle(testElement);
            const hasStyles = styles.borderRadius !== 'auto' && styles.borderRadius !== '';
            
            document.body.removeChild(testElement);

            if (!hasStyles) {
                console.warn('⚠️ CSS样式可能未正确加载');
                this.loadFallbackStyles();
            } else {
                console.log('✅ CSS样式正常');
            }

            return true;
        } catch (error) {
            console.error('❌ CSS样式检查失败:', error);
            return false;
        }
    },

    // 加载备用样式
    loadFallbackStyles() {
        const fallbackCSS = `
            .project-card {
                border-radius: 12px !important;
                padding: 24px !important;
                border: 2px solid #e5e7eb !important;
                background: #f9fafb !important;
            }
            .skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
                background-size: 200% 100% !important;
                animation: loading 1.5s infinite !important;
            }
            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }
        `;

        const style = document.createElement('style');
        style.textContent = fallbackCSS;
        document.head.appendChild(style);
        
        console.log('✅ 备用样式已加载');
    },

    // 修复项目管理器初始化问题
    async fixProjectManagerInit() {
        try {
            console.log('🔧 检查项目管理器初始化...');
            
            if (typeof projectManager === 'undefined') {
                console.warn('⚠️ 项目管理器未定义，尝试重新初始化...');
                
                // 重新加载项目管理脚本
                const script = document.createElement('script');
                script.src = 'project-management.js';
                script.onload = () => {
                    console.log('✅ 项目管理脚本重新加载完成');
                };
                document.head.appendChild(script);
                
                return false;
            }

            console.log('✅ 项目管理器正常');
            return true;
        } catch (error) {
            console.error('❌ 项目管理器检查失败:', error);
            return false;
        }
    },

    // 修复导出服务问题
    fixExportService() {
        try {
            console.log('🔧 检查导出服务...');
            
            if (typeof exportService === 'undefined') {
                console.warn('⚠️ 导出服务未定义');
                return false;
            }

            if (!exportService.supportedFormats || exportService.supportedFormats.length === 0) {
                console.warn('⚠️ 导出格式配置异常');
                return false;
            }

            console.log('✅ 导出服务正常');
            return true;
        } catch (error) {
            console.error('❌ 导出服务检查失败:', error);
            return false;
        }
    },

    // 修复模态框问题
    fixModalIssues() {
        try {
            console.log('🔧 检查模态框功能...');
            
            // 检查模态框元素
            const modalOverlay = document.getElementById('modal-overlay');
            if (!modalOverlay) {
                console.warn('⚠️ 模态框元素缺失，尝试创建...');
                this.createModalElements();
            }

            // 检查模态框事件
            this.fixModalEvents();

            console.log('✅ 模态框功能正常');
            return true;
        } catch (error) {
            console.error('❌ 模态框检查失败:', error);
            return false;
        }
    },

    // 创建模态框元素
    createModalElements() {
        const modalHTML = `
            <div id="modal-overlay" class="modal-overlay" style="display: none;">
                <div id="modal-container" class="modal-container">
                    <div class="modal-header">
                        <h3 id="modal-title">标题</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>
                    <div id="modal-content" class="modal-content"></div>
                    <div class="modal-footer">
                        <div class="modal-actions" id="modal-actions"></div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        console.log('✅ 模态框元素已创建');
    },

    // 修复模态框事件
    fixModalEvents() {
        // 确保全局关闭函数存在
        if (typeof window.closeModal !== 'function') {
            window.closeModal = function() {
                const overlay = document.getElementById('modal-overlay');
                if (overlay) {
                    overlay.style.display = 'none';
                }
            };
        }

        // 点击外部关闭模态框
        document.addEventListener('click', (e) => {
            const overlay = document.getElementById('modal-overlay');
            if (e.target === overlay) {
                window.closeModal();
            }
        });
    },

    // 修复通知系统
    fixNotificationSystem() {
        try {
            console.log('🔧 检查通知系统...');
            
            // 检查通知容器
            let notificationContainer = document.getElementById('notification-container');
            if (!notificationContainer) {
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'notification-container';
                notificationContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    pointer-events: none;
                `;
                document.body.appendChild(notificationContainer);
                console.log('✅ 通知容器已创建');
            }

            // 确保全局通知函数存在
            if (typeof window.showNotification !== 'function') {
                window.showNotification = function(message, type = 'info') {
                    const container = document.getElementById('notification-container');
                    if (!container) return;

                    const notification = document.createElement('div');
                    notification.className = `notification notification-${type}`;
                    notification.style.cssText = `
                        background: ${this.getNotificationColor(type)};
                        color: white;
                        padding: 12px 20px;
                        border-radius: 6px;
                        margin-bottom: 10px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                        pointer-events: auto;
                        animation: slideIn 0.3s ease;
                    `;
                    notification.textContent = message;

                    container.appendChild(notification);

                    setTimeout(() => {
                        notification.remove();
                    }, 3000);
                };

                window.getNotificationColor = function(type) {
                    const colors = {
                        'success': '#10b981',
                        'error': '#ef4444',
                        'warning': '#f59e0b',
                        'info': '#3b82f6'
                    };
                    return colors[type] || colors.info;
                };
            }

            console.log('✅ 通知系统正常');
            return true;
        } catch (error) {
            console.error('❌ 通知系统检查失败:', error);
            return false;
        }
    },

    // 运行所有修复
    async runAllFixes() {
        console.log('🚀 开始运行所有修复...');
        
        const fixes = [
            { name: 'Supabase连接', fn: this.fixSupabaseConnection },
            { name: '本地存储', fn: this.fixLocalStorage },
            { name: 'CSS样式', fn: this.fixCSSStyles },
            { name: '项目管理器', fn: this.fixProjectManagerInit },
            { name: '导出服务', fn: this.fixExportService },
            { name: '模态框', fn: this.fixModalIssues },
            { name: '通知系统', fn: this.fixNotificationSystem }
        ];

        const results = [];
        
        for (const fix of fixes) {
            try {
                const result = await fix.fn.call(this);
                results.push({ name: fix.name, success: result });
                console.log(`${result ? '✅' : '❌'} ${fix.name}: ${result ? '修复成功' : '修复失败'}`);
            } catch (error) {
                results.push({ name: fix.name, success: false, error: error.message });
                console.error(`❌ ${fix.name}: 修复失败 - ${error.message}`);
            }
        }

        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        
        console.log(`🎯 修复完成: ${successCount}/${totalCount} 项成功`);
        
        return results;
    },

    // 诊断系统状态
    async diagnoseSystem() {
        console.log('🔍 开始系统诊断...');
        
        const diagnostics = {
            browser: this.getBrowserInfo(),
            screen: this.getScreenInfo(),
            storage: this.checkStorageSupport(),
            network: await this.checkNetworkStatus(),
            dependencies: this.checkDependencies()
        };

        console.log('📊 系统诊断结果:', diagnostics);
        return diagnostics;
    },

    // 获取浏览器信息
    getBrowserInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };
    },

    // 获取屏幕信息
    getScreenInfo() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio,
            colorDepth: screen.colorDepth
        };
    },

    // 检查存储支持
    checkStorageSupport() {
        return {
            localStorage: typeof Storage !== 'undefined',
            sessionStorage: typeof sessionStorage !== 'undefined',
            indexedDB: typeof indexedDB !== 'undefined'
        };
    },

    // 检查网络状态
    async checkNetworkStatus() {
        try {
            const response = await fetch('https://httpbin.org/get', { 
                method: 'GET',
                mode: 'cors'
            });
            return {
                online: true,
                latency: Date.now() - performance.now()
            };
        } catch (error) {
            return {
                online: false,
                error: error.message
            };
        }
    },

    // 检查依赖项
    checkDependencies() {
        return {
            supabase: typeof supabase !== 'undefined',
            supabaseManager: typeof supabaseManager !== 'undefined',
            projectManager: typeof projectManager !== 'undefined',
            exportService: typeof exportService !== 'undefined',
            fontAwesome: !!document.querySelector('link[href*="font-awesome"]')
        };
    }
};

// 自动运行基础修复
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        ProjectManagementFixes.runAllFixes();
    }, 1000);
});

// 导出到全局
window.ProjectManagementFixes = ProjectManagementFixes;
