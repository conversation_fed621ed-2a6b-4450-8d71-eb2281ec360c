#!/bin/bash

# AI增强学术专著编写系统 - ARM64架构部署脚本
# 版本: 1.0
# 支持: ARM64/aarch64 架构

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查ARM架构
check_arm_architecture() {
    log_info "检查系统架构..."
    
    local arch=$(uname -m)
    case $arch in
        aarch64|arm64)
            log_success "检测到ARM64架构: $arch"
            ;;
        armv7l|armv6l)
            log_warning "检测到32位ARM架构: $arch"
            log_warning "建议使用64位ARM架构以获得更好性能"
            ;;
        x86_64)
            log_error "检测到x86_64架构，请使用标准部署脚本"
            exit 1
            ;;
        *)
            log_error "不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 检查Docker对ARM的支持
check_docker_arm_support() {
    log_info "检查Docker ARM支持..."
    
    # 检查Docker版本
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    local docker_version=$(docker --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
    log_info "Docker版本: $docker_version"
    
    # 检查是否支持多架构
    if docker buildx version &> /dev/null; then
        log_success "Docker支持多架构构建"
    else
        log_warning "Docker不支持buildx，某些功能可能受限"
    fi
    
    # 测试ARM镜像拉取
    log_info "测试ARM镜像拉取..."
    if docker pull --platform linux/arm64 hello-world &> /dev/null; then
        log_success "ARM镜像拉取正常"
        docker rmi hello-world &> /dev/null || true
    else
        log_error "ARM镜像拉取失败"
        exit 1
    fi
}

# 检查Supabase组件ARM兼容性
check_supabase_arm_compatibility() {
    log_info "检查Supabase组件ARM兼容性..."
    
    local components=(
        "postgres:15"
        "postgrest/postgrest:v11.2.0"
        "supabase/gotrue:v2.99.0"
        "supabase/realtime:v2.25.35"
        "supabase/storage-api:v0.40.4"
        "redis:7-alpine"
        "nginx:alpine"
    )
    
    local compatible_count=0
    local total_count=${#components[@]}
    
    for component in "${components[@]}"; do
        log_info "检查 $component..."
        
        # 尝试拉取ARM版本
        if docker pull --platform linux/arm64 "$component" &> /dev/null; then
            log_success "$component: ARM64兼容 ✓"
            compatible_count=$((compatible_count + 1))
        else
            log_warning "$component: ARM64不兼容 ✗"
            
            # 检查是否有替代方案
            case $component in
                "supabase/gotrue"*)
                    log_info "GoTrue可能需要从源码构建"
                    ;;
                "supabase/realtime"*)
                    log_info "Realtime可能需要从源码构建"
                    ;;
                "supabase/storage-api"*)
                    log_info "Storage API可能需要从源码构建"
                    ;;
            esac
        fi
    done
    
    log_info "兼容性检查完成: $compatible_count/$total_count 组件兼容"
    
    if [ $compatible_count -eq $total_count ]; then
        log_success "所有组件都支持ARM64架构"
        return 0
    elif [ $compatible_count -ge 4 ]; then
        log_warning "部分组件不兼容，将使用替代方案"
        return 1
    else
        log_error "太多组件不兼容ARM64架构"
        return 2
    fi
}

# 选择部署策略
select_deployment_strategy() {
    log_info "选择ARM64部署策略..."
    
    check_supabase_arm_compatibility
    local compatibility_result=$?
    
    case $compatibility_result in
        0)
            log_success "使用标准Supabase部署策略"
            DEPLOYMENT_STRATEGY="standard"
            COMPOSE_FILE="docker-compose.arm64.yml"
            ;;
        1)
            log_warning "使用混合部署策略（部分组件替代）"
            DEPLOYMENT_STRATEGY="hybrid"
            COMPOSE_FILE="docker-compose.arm64.yml"
            ;;
        2)
            log_warning "使用自建API部署策略"
            DEPLOYMENT_STRATEGY="custom"
            COMPOSE_FILE="docker-compose.arm64.yml"
            ;;
    esac
    
    log_info "选择的部署策略: $DEPLOYMENT_STRATEGY"
}

# 创建ARM64特定的环境配置
create_arm64_env() {
    log_info "创建ARM64环境配置..."
    
    if [ -f .env ]; then
        log_warning ".env文件已存在，跳过生成"
        return
    fi
    
    # 基于模板创建环境文件
    cp .env.example .env
    
    # 添加ARM64特定配置
    cat >> .env << EOF

# ARM64架构特定配置
ARCHITECTURE=arm64
DEPLOYMENT_STRATEGY=${DEPLOYMENT_STRATEGY}
DOCKER_PLATFORM=linux/arm64

# 性能优化配置（ARM服务器通常内存较小）
DB_POOL_SIZE=10
DB_MAX_CONNECTIONS=50
CACHE_TTL_SHORT=600
CACHE_TTL_MEDIUM=1800

# ARM优化的资源限制
MAX_CONCURRENT_USERS=50
AI_MAX_CONCURRENT_REQUESTS=3
EOF
    
    log_success "ARM64环境配置创建完成"
}

# 优化ARM性能配置
optimize_arm_performance() {
    log_info "优化ARM性能配置..."
    
    # 创建ARM优化的Docker Compose覆盖文件
    cat > docker-compose.override.yml << EOF
version: '3.8'

services:
  postgres:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
    command: >
      postgres
      -c wal_level=logical
      -c max_connections=50
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB

  redis:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru

  postgrest:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  gotrue:
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  realtime:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  storage:
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  nginx:
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
EOF
    
    log_success "ARM性能优化配置完成"
}

# 构建自定义ARM镜像（如果需要）
build_custom_arm_images() {
    if [ "$DEPLOYMENT_STRATEGY" != "custom" ]; then
        return 0
    fi
    
    log_info "构建自定义ARM镜像..."
    
    # 创建API网关的ARM Dockerfile
    mkdir -p api-gateway
    
    cat > api-gateway/Dockerfile.arm64 << 'EOF'
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制源码
COPY . .

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["node", "index.js"]
EOF
    
    # 创建简单的API网关
    cat > api-gateway/package.json << 'EOF'
{
  "name": "llm-book-api-gateway",
  "version": "1.0.0",
  "description": "ARM64 compatible API gateway for LLM Book System",
  "main": "index.js",
  "dependencies": {
    "express": "^4.18.2",
    "pg": "^8.11.0",
    "jsonwebtoken": "^9.0.0",
    "cors": "^2.8.5",
    "helmet": "^7.0.0"
  }
}
EOF
    
    cat > api-gateway/index.js << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { Pool } = require('pg');

const app = express();
const port = process.env.PORT || 3000;

// 中间件
app.use(helmet());
app.use(cors());
app.use(express.json());

// 数据库连接
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', architecture: 'arm64' });
});

// 基本API路由
app.get('/api/projects', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM projects');
    res.json(result.rows);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

app.listen(port, () => {
  console.log(`API Gateway running on port ${port} (ARM64)`);
});
EOF
    
    log_success "自定义ARM镜像配置完成"
}

# 启动ARM64服务
start_arm64_services() {
    log_info "启动ARM64服务..."
    
    # 使用ARM64特定的compose文件
    export COMPOSE_FILE="docker-compose.arm64.yml"
    
    # 拉取镜像
    log_info "拉取ARM64镜像..."
    docker-compose -f $COMPOSE_FILE pull --platform linux/arm64
    
    # 启动服务
    log_info "启动服务..."
    docker-compose -f $COMPOSE_FILE up -d
    
    log_info "等待服务启动..."
    sleep 45  # ARM服务器启动可能较慢
    
    # 检查服务状态
    if docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_success "ARM64服务启动成功"
    else
        log_error "ARM64服务启动失败，请检查日志"
        docker-compose -f $COMPOSE_FILE logs
        exit 1
    fi
}

# ARM特定的验证
verify_arm64_deployment() {
    log_info "验证ARM64部署..."
    
    # 检查架构
    local postgres_arch=$(docker-compose -f $COMPOSE_FILE exec -T postgres uname -m)
    log_info "PostgreSQL容器架构: $postgres_arch"
    
    # 检查数据库连接
    if docker-compose -f $COMPOSE_FILE exec -T postgres pg_isready -U supabase -d llm_book_system; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败"
        exit 1
    fi
    
    # 检查API服务
    local api_endpoints=(
        "http://localhost:3001/health"
        "http://localhost:9999/health"
    )
    
    for endpoint in "${api_endpoints[@]}"; do
        if curl -f -s "$endpoint" >/dev/null 2>&1; then
            log_success "API服务正常: $endpoint"
        else
            log_warning "API服务异常: $endpoint"
        fi
    done
    
    log_success "ARM64部署验证完成"
}

# 显示ARM64部署信息
show_arm64_deployment_info() {
    log_success "=== ARM64部署完成 ==="
    echo ""
    echo "架构信息："
    echo "  - 系统架构: $(uname -m)"
    echo "  - 部署策略: $DEPLOYMENT_STRATEGY"
    echo "  - Compose文件: $COMPOSE_FILE"
    echo ""
    echo "服务访问地址："
    echo "  - 主应用: http://localhost"
    echo "  - API端点: http://localhost:3001"
    echo "  - 认证服务: http://localhost:9999"
    echo "  - 实时服务: http://localhost:4000"
    echo "  - 存储服务: http://localhost:5000"
    echo ""
    echo "ARM64特定命令："
    echo "  - 查看日志: docker-compose -f $COMPOSE_FILE logs"
    echo "  - 停止服务: docker-compose -f $COMPOSE_FILE down"
    echo "  - 重启服务: docker-compose -f $COMPOSE_FILE restart"
    echo ""
    log_warning "ARM64注意事项："
    echo "1. ARM服务器性能可能较低，请适当调整并发设置"
    echo "2. 某些AI功能可能响应较慢"
    echo "3. 建议定期监控系统资源使用情况"
    echo "4. 如遇到兼容性问题，请联系技术支持"
}

# 主函数
main() {
    echo "=== AI增强学术专著编写系统 ARM64部署脚本 ==="
    echo ""
    
    check_arm_architecture
    check_docker_arm_support
    select_deployment_strategy
    
    # 创建目录结构
    mkdir -p volumes/{postgres_data,storage_data,redis_data,logs/nginx}
    mkdir -p nginx/ssl
    
    create_arm64_env
    optimize_arm_performance
    build_custom_arm_images
    start_arm64_services
    verify_arm64_deployment
    show_arm64_deployment_info
    
    log_success "ARM64部署完成！"
}

# 显示帮助
show_help() {
    cat << EOF
AI增强学术专著编写系统 ARM64部署脚本

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -s, --strategy STR  指定部署策略 (standard|hybrid|custom)
  -c, --check         仅检查ARM兼容性
  -f, --force         强制部署，跳过兼容性检查

示例:
  $0                  # 自动检测并部署
  $0 -c              # 仅检查兼容性
  $0 -s hybrid       # 使用混合策略部署
  $0 -f              # 强制部署

注意: 此脚本专为ARM64架构设计
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--strategy)
            DEPLOYMENT_STRATEGY="$2"
            shift 2
            ;;
        -c|--check)
            check_arm_architecture
            check_docker_arm_support
            check_supabase_arm_compatibility
            exit 0
            ;;
        -f|--force)
            FORCE_DEPLOY="true"
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$@"
