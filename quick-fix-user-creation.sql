-- 快速修复用户创建问题
-- 删除阻止直接插入的外键约束

-- 1. 删除 user_profiles 表的外键约束
ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_id_fkey;

-- 2. 删除其他可能的外键约束
ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_user_id_fkey;

ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS profiles_id_fkey;

-- 3. 验证约束已删除
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'user_profiles' 
AND table_schema = 'public';

-- 4. 测试插入（应该成功）
SELECT 'Foreign key constraints removed!' as result;
SELECT 'User creation should now work!' as status;
