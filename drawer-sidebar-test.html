<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可伸缩侧边栏测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            overflow: hidden;
            /* 防止双滚动条 */
        }

        /* 主容器 */
        .app-container {
            display: flex;
            height: 100vh;
            position: relative;
        }

        /* 侧边栏 */
        .sidebar {
            width: 320px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1000;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        /* 侧边栏头部 */
        .sidebar-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 70px;
            flex-shrink: 0;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
            justify-content: center;
            flex-direction: column;
            gap: 0.75rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1.25rem;
            font-size: 1.25rem;
            font-weight: 600;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .logo-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .logo-icon {
            font-size: 1.5rem;
            color: #fbbf24;
            flex-shrink: 0;
        }

        /* 切换按钮 */
        .toggle-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            flex-shrink: 0;
            font-size: 0.875rem;
        }

        .toggle-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .sidebar.collapsed .toggle-btn {
            margin: 0;
            width: 28px;
            height: 28px;
        }

        /* 导航菜单 */
        .nav-menu {
            padding: 0.5rem 0;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* 隐藏导航菜单滚动条 */
        .nav-menu::-webkit-scrollbar {
            width: 4px;
        }

        .nav-menu::-webkit-scrollbar-track {
            background: transparent;
        }

        .nav-menu::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        .nav-menu::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .nav-item {
            margin: 0.25rem 1rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            position: relative;
        }

        .sidebar.collapsed .nav-item {
            margin: 0.25rem 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.875rem 1rem;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            min-height: 44px;
            /* 确保触摸目标大小 */
        }

        .sidebar.collapsed .nav-link {
            padding: 0.875rem;
            justify-content: center;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(4px);
        }

        .sidebar.collapsed .nav-link:hover {
            transform: none;
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-icon {
            font-size: 1.125rem;
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .nav-text {
            margin-left: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            margin: 0;
            overflow: hidden;
            transform: translateX(-10px);
        }

        /* 工具提示 */
        .nav-item .tooltip {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: #1f2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1001;
            margin-left: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .nav-item .tooltip::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            border: 4px solid transparent;
            border-right-color: #1f2937;
        }

        .sidebar.collapsed .nav-item:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            background: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        /* 顶部栏 */
        .top-bar {
            height: 70px;
            background: white;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            padding: 0 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        /* 内容区域 */
        .content-area {
            padding: 1.5rem;
            height: calc(100vh - 70px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* 隐藏内容区域滚动条样式 */
        .content-area::-webkit-scrollbar {
            width: 8px;
        }

        .content-area::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        .content-area::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .content-area::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 右侧工具栏 */
        .right-toolbar {
            width: 350px;
            min-width: 280px;
            max-width: 600px;
            background: white;
            border-left: 1px solid #e2e8f0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 999;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .right-toolbar.resizing {
            transition: none;
        }

        .right-toolbar.collapsed {
            width: 60px;
        }

        /* 右侧工具栏头部 */
        .right-toolbar-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 70px;
            background: #f8fafc;
        }

        .right-toolbar.collapsed .right-toolbar-header {
            padding: 1rem 0.5rem;
            justify-content: center;
            flex-direction: column;
            gap: 0.75rem;
        }

        .toolbar-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            transition: opacity 0.3s ease;
        }

        .right-toolbar.collapsed .toolbar-title-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .toolbar-icon {
            font-size: 1.25rem;
            color: #3b82f6;
            flex-shrink: 0;
        }

        /* 右侧切换按钮 */
        .right-toggle-btn {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            color: #64748b;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .right-toggle-btn:hover {
            background: #e2e8f0;
            color: #475569;
            transform: scale(1.05);
        }

        .right-toolbar.collapsed .right-toggle-btn {
            margin: 0;
            width: 28px;
            height: 28px;
        }

        /* 工具选择器 */
        .tool-selector {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: white;
        }

        .right-toolbar.collapsed .tool-selector {
            display: none;
        }

        .tool-select-wrapper {
            position: relative;
        }

        .tool-select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: white;
            font-size: 0.875rem;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tool-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .tool-select:hover {
            border-color: #9ca3af;
        }

        /* 工具信息显示 */
        .tool-info {
            padding: 0.75rem 1.5rem;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.75rem;
            color: #64748b;
            line-height: 1.4;
        }

        .right-toolbar.collapsed .tool-info {
            display: none;
        }

        /* iframe容器 */
        .tool-iframe-container {
            flex: 1;
            padding: 1rem;
            background: white;
            overflow: hidden;
        }

        .right-toolbar.collapsed .tool-iframe-container {
            display: none;
        }

        .tool-iframe {
            width: 100%;
            height: 100%;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
        }

        /* 收缩状态的工具图标 */
        .collapsed-tools {
            display: none;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1rem 0.5rem;
            flex: 1;
        }

        .right-toolbar.collapsed .collapsed-tools {
            display: flex;
        }

        /* 拖拽调整宽度手柄 */
        .resize-handle {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: transparent;
            cursor: col-resize;
            z-index: 1001;
            transition: background-color 0.2s ease;
        }

        .resize-handle:hover {
            background: #3b82f6;
        }

        .resize-handle.resizing {
            background: #2563eb;
        }

        .resize-handle::before {
            content: '';
            position: absolute;
            left: -2px;
            top: 0;
            bottom: 0;
            width: 8px;
            background: transparent;
        }

        .collapsed-tool-item {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
            color: #64748b;
            position: relative;
        }

        .collapsed-tool-item:hover {
            background: #e2e8f0;
            color: #475569;
            transform: scale(1.05);
        }

        .collapsed-tool-item.active {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #3b82f6;
        }

        /* 工具管理模态框 */
        .tool-management-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .tool-management-modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #6b7280;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .modal-body {
            padding: 1.5rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        /* 工具表格 */
        .tools-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
        }

        .tools-table th,
        .tools-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .tools-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .tools-table td {
            color: #6b7280;
        }

        .tool-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-edit,
        .btn-delete {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-edit {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .btn-edit:hover {
            background: #bfdbfe;
        }

        .btn-delete {
            background: #fee2e2;
            color: #dc2626;
        }

        .btn-delete:hover {
            background: #fecaca;
        }

        /* 工具表单 */
        .tool-form {
            display: grid;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }

        .form-input,
        .form-textarea {
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
        }

        .btn-primary,
        .btn-secondary {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .demo-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
            border: 1px solid #e5e7eb;
        }

        .demo-card h3 {
            color: #1f2937;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }

        .demo-card p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        /* 控制按钮 */
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 1rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
            z-index: 1002;
            cursor: move;
            user-select: none;
            transition: box-shadow 0.2s ease;
        }

        .control-panel:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        .control-panel.dragging {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
            transform: scale(1.02);
        }

        .control-panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
            cursor: move;
        }

        .control-panel-title {
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
            margin: 0;
        }

        .drag-handle {
            color: #9ca3af;
            font-size: 0.75rem;
            cursor: move;
        }

        .control-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .control-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.25rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -320px;
                z-index: 1001;
            }

            .sidebar.open {
                left: 0;
            }

            .sidebar.collapsed {
                left: -70px;
            }

            .sidebar.collapsed.open {
                left: 0;
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .mobile-overlay.active {
                opacity: 1;
                visibility: visible;
            }

            /* 移动端右侧工具栏 */
            .right-toolbar {
                position: fixed;
                right: -350px;
                top: 0;
                height: 100vh;
                z-index: 1002;
            }

            .right-toolbar.open {
                right: 0;
            }

            .right-toolbar.collapsed {
                right: -60px;
            }

            .right-toolbar.collapsed.open {
                right: 0;
            }
        }

        /* 动画效果 */
        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .nav-item {
            animation: slideIn 0.3s ease forwards;
        }

        .nav-item:nth-child(1) {
            animation-delay: 0.1s;
        }

        .nav-item:nth-child(2) {
            animation-delay: 0.15s;
        }

        .nav-item:nth-child(3) {
            animation-delay: 0.2s;
        }

        .nav-item:nth-child(4) {
            animation-delay: 0.25s;
        }

        .nav-item:nth-child(5) {
            animation-delay: 0.3s;
        }

        .nav-item:nth-child(6) {
            animation-delay: 0.35s;
        }
    </style>
</head>

<body>
    <!-- 移动端遮罩 -->
    <div class="mobile-overlay" id="mobileOverlay"></div>

    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <!-- 侧边栏头部 -->
            <div class="sidebar-header">
                <div class="logo">
                    <i class="logo-icon fas fa-book-open"></i>
                    <span class="logo-text">项目管理</span>
                </div>
                <button class="toggle-btn" id="toggleBtn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- 导航菜单 -->
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="overview">
                        <i class="nav-icon fas fa-home"></i>
                        <span class="nav-text">项目概览</span>
                    </a>
                    <div class="tooltip">项目概览</div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="outline">
                        <i class="nav-icon fas fa-list"></i>
                        <span class="nav-text">书籍目录</span>
                    </a>
                    <div class="tooltip">书籍目录</div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="editor">
                        <i class="nav-icon fas fa-edit"></i>
                        <span class="nav-text">章节编写</span>
                    </a>
                    <div class="tooltip">章节编写</div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="references">
                        <i class="nav-icon fas fa-bookmark"></i>
                        <span class="nav-text">文献管理</span>
                    </a>
                    <div class="tooltip">文献管理</div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="collaboration">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">协作管理</span>
                    </a>
                    <div class="tooltip">协作管理</div>
                </div>

                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="settings">
                        <i class="nav-icon fas fa-cog"></i>
                        <span class="nav-text">系统设置</span>
                    </a>
                    <div class="tooltip">系统设置</div>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <h1 class="page-title" id="pageTitle">项目概览</h1>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <div class="demo-card">
                    <h3>🎨 可伸缩侧边栏设计测试</h3>
                    <p>这是一个独立的测试页面，用于展示和测试可伸缩的drawer样式侧边栏设计。</p>
                    <p><strong>主要特性：</strong></p>
                    <ul style="margin-left: 1.5rem; color: #6b7280;">
                        <li>平滑的展开/收缩动画效果</li>
                        <li>悬停时显示工具提示</li>
                        <li>响应式设计，支持移动端</li>
                        <li>美观的渐变背景和阴影效果</li>
                        <li>活跃状态指示</li>
                    </ul>
                </div>

                <div class="demo-card">
                    <h3>🔧 操作说明</h3>
                    <p><strong>桌面端：</strong></p>
                    <ul style="margin-left: 1.5rem; color: #6b7280; margin-bottom: 1rem;">
                        <li>点击侧边栏顶部的菜单按钮切换展开/收缩状态</li>
                        <li>收缩状态下悬停菜单项查看工具提示</li>
                        <li>点击不同菜单项切换页面内容</li>
                    </ul>
                    <p><strong>移动端：</strong></p>
                    <ul style="margin-left: 1.5rem; color: #6b7280;">
                        <li>侧边栏默认隐藏，点击控制按钮显示</li>
                        <li>点击遮罩层或菜单项关闭侧边栏</li>
                        <li>支持触摸滑动操作</li>
                    </ul>
                </div>

                <div class="demo-card">
                    <h3>📱 响应式特性</h3>
                    <p>侧边栏设计完全响应式，在不同屏幕尺寸下提供最佳用户体验：</p>
                    <ul style="margin-left: 1.5rem; color: #6b7280;">
                        <li><strong>大屏幕（>768px）：</strong>固定侧边栏，支持展开/收缩</li>
                        <li><strong>小屏幕（≤768px）：</strong>抽屉式侧边栏，覆盖主内容</li>
                        <li><strong>触摸优化：</strong>适合移动设备的触摸目标大小</li>
                    </ul>
                </div>

                <div class="demo-card">
                    <h3>🎯 设计亮点</h3>
                    <p>这个侧边栏设计融合了现代Web应用的最佳实践：</p>
                    <ul style="margin-left: 1.5rem; color: #6b7280;">
                        <li><strong>流畅动画：</strong>使用CSS3 cubic-bezier缓动函数</li>
                        <li><strong>视觉层次：</strong>渐变背景、阴影和透明度</li>
                        <li><strong>交互反馈：</strong>悬停效果和状态指示</li>
                        <li><strong>无障碍访问：</strong>键盘导航和屏幕阅读器支持</li>
                        <li><strong>性能优化：</strong>硬件加速和高效的CSS选择器</li>
                    </ul>
                </div>
            </div>
        </main>

        <!-- 右侧工具栏 -->
        <aside class="right-toolbar" id="rightToolbar">
            <!-- 拖拽调整宽度手柄 -->
            <div class="resize-handle" id="resizeHandle"></div>

            <!-- 工具栏头部 -->
            <div class="right-toolbar-header">
                <div class="toolbar-title">
                    <span class="toolbar-icon" onclick="openToolManagement()" style="cursor: pointer;"
                        title="点击管理工具">🛠️</span>
                    <span class="toolbar-title-text">工具集</span>
                </div>
                <button class="right-toggle-btn" id="rightToggleBtn" onclick="toggleRightToolbar()">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- 工具选择器 -->
            <div class="tool-selector">
                <div class="tool-select-wrapper">
                    <select class="tool-select" id="toolSelect" onchange="loadTool()">
                        <option value="">选择工具...</option>
                    </select>
                </div>
            </div>

            <!-- 工具信息 -->
            <div class="tool-info" id="toolInfo">
                选择一个工具开始使用
            </div>

            <!-- iframe容器 -->
            <div class="tool-iframe-container">
                <iframe class="tool-iframe" id="toolIframe" src="about:blank"></iframe>
            </div>

            <!-- 收缩状态的工具图标 -->
            <div class="collapsed-tools" id="collapsedTools">
                <!-- 动态生成工具图标 -->
            </div>
        </aside>
    </div>

    <!-- 工具管理模态框 -->
    <div class="tool-management-modal" id="toolManagementModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">工具管理</h3>
                <button class="modal-close" onclick="closeToolManagement()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 添加工具表单 -->
                <div class="tool-form">
                    <div class="form-group">
                        <label class="form-label">工具名称</label>
                        <input type="text" class="form-input" id="toolName" placeholder="输入工具名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">工具URL</label>
                        <input type="url" class="form-input" id="toolUrl" placeholder="输入工具URL地址">
                    </div>
                    <div class="form-group">
                        <label class="form-label">工具介绍</label>
                        <textarea class="form-textarea" id="toolDescription" placeholder="输入工具介绍"></textarea>
                    </div>
                    <div class="form-actions">
                        <button class="btn-secondary" onclick="clearForm()">清空</button>
                        <button class="btn-primary" onclick="saveTool()">保存工具</button>
                    </div>
                </div>

                <!-- 工具列表 -->
                <table class="tools-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>URL</th>
                            <th>介绍</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="toolsTableBody">
                        <!-- 动态生成工具列表 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel" id="controlPanel">
        <div class="control-panel-header" id="controlPanelHeader">
            <div class="control-panel-title">测试控制</div>
            <div class="drag-handle">⋮⋮</div>
        </div>
        <div class="control-buttons">
            <button class="control-btn" onclick="toggleSidebar()">切换侧边栏</button>
            <button class="control-btn" onclick="toggleRightToolbar()">切换工具栏</button>
            <button class="control-btn" onclick="openToolManagement()">工具管理</button>
            <button class="control-btn" onclick="toggleMobile()">移动端模式</button>
            <button class="control-btn" onclick="resetDemo()">重置演示</button>
        </div>
    </div>

    <script>
        // DOM元素
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('toggleBtn');
        const mobileOverlay = document.getElementById('mobileOverlay');
        const pageTitle = document.getElementById('pageTitle');
        const navLinks = document.querySelectorAll('.nav-link');

        // 右侧工具栏元素
        const rightToolbar = document.getElementById('rightToolbar');
        const rightToggleBtn = document.getElementById('rightToggleBtn');
        const toolSelect = document.getElementById('toolSelect');
        const toolInfo = document.getElementById('toolInfo');
        const toolIframe = document.getElementById('toolIframe');
        const collapsedTools = document.getElementById('collapsedTools');
        const toolManagementModal = document.getElementById('toolManagementModal');
        const toolsTableBody = document.getElementById('toolsTableBody');

        // 控制面板元素
        const controlPanel = document.getElementById('controlPanel');
        const controlPanelHeader = document.getElementById('controlPanelHeader');

        // 右侧工具栏调整宽度元素
        const resizeHandle = document.getElementById('resizeHandle');

        // 状态管理
        let isMobile = window.innerWidth <= 768;
        let isOpen = true;
        let rightToolbarOpen = true;
        let currentEditingTool = null;

        // 拖动状态
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let controlPanelPosition = { x: 20, y: 20 };

        // 右侧工具栏调整宽度状态
        let isResizing = false;
        let rightToolbarWidth = 350;
        const minToolbarWidth = 280;
        const maxToolbarWidth = 600;

        // 工具数据存储
        let tools = JSON.parse(localStorage.getItem('webTools') || '[]');

        // 默认工具
        const defaultTools = [
            {
                id: 'calculator',
                name: '计算器',
                url: 'https://www.calculator.net/basic-calculator.html',
                description: '基础计算器工具，支持基本数学运算'
            },
            {
                id: 'color-picker',
                name: '颜色选择器',
                url: 'https://htmlcolorcodes.com/color-picker/',
                description: '在线颜色选择器，支持多种颜色格式'
            },
            {
                id: 'ichat',
                name: 'AI chat',
                url: 'http://ichat.gonghe.net.cn',
                description: 'AI 对话服务工具'
            }
        ];

        // 初始化
        function init() {
            // 初始化默认工具
            if (tools.length === 0) {
                tools = [...defaultTools];
                saveTools();
            }

            updateLayout();
            bindEvents();
            loadToolsList();
            updateCollapsedTools();
            initDragAndDrop();
            initRightToolbarResize();
        }

        // 绑定事件
        function bindEvents() {
            // 切换按钮
            toggleBtn.addEventListener('click', toggleSidebar);

            // 导航链接
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    setActiveNav(link);
                    updatePageTitle(link.dataset.page);

                    // 移动端点击后关闭侧边栏
                    if (isMobile) {
                        closeSidebar();
                    }
                });
            });

            // 移动端遮罩
            mobileOverlay.addEventListener('click', closeSidebar);

            // 窗口大小变化
            window.addEventListener('resize', () => {
                isMobile = window.innerWidth <= 768;
                updateLayout();
            });

            // 键盘导航
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && isMobile && isOpen) {
                    closeSidebar();
                }
            });
        }

        // 切换侧边栏
        function toggleSidebar() {
            if (isMobile) {
                isOpen ? closeSidebar() : openSidebar();
            } else {
                sidebar.classList.toggle('collapsed');
                updateToggleIcon();
            }
        }

        // 打开侧边栏（移动端）
        function openSidebar() {
            sidebar.classList.add('open');
            mobileOverlay.classList.add('active');
            isOpen = true;
            document.body.style.overflow = 'hidden';
        }

        // 关闭侧边栏（移动端）
        function closeSidebar() {
            sidebar.classList.remove('open');
            mobileOverlay.classList.remove('active');
            isOpen = false;
            document.body.style.overflow = '';
        }

        // 更新布局
        function updateLayout() {
            if (isMobile) {
                sidebar.classList.remove('collapsed');
                if (!isOpen) {
                    sidebar.classList.remove('open');
                }
            } else {
                sidebar.classList.remove('open');
                mobileOverlay.classList.remove('active');
                document.body.style.overflow = '';
                isOpen = true;
            }
            updateToggleIcon();
        }

        // 更新切换按钮图标
        function updateToggleIcon() {
            const icon = toggleBtn.querySelector('i');
            if (isMobile) {
                icon.className = 'fas fa-bars';
            } else {
                icon.className = sidebar.classList.contains('collapsed') ? 'fas fa-chevron-right' : 'fas fa-chevron-left';
            }
        }

        // 设置活跃导航
        function setActiveNav(activeLink) {
            navLinks.forEach(link => link.classList.remove('active'));
            activeLink.classList.add('active');
        }

        // 更新页面标题
        function updatePageTitle(page) {
            const titles = {
                overview: '项目概览',
                outline: '书籍目录',
                editor: '章节编写',
                references: '文献管理',
                collaboration: '协作管理',
                settings: '系统设置'
            };
            pageTitle.textContent = titles[page] || '项目概览';
        }

        // 右侧工具栏功能（已移动到文件末尾的新版本）

        function loadToolsList() {
            // 清空选择器
            toolSelect.innerHTML = '<option value="">选择工具...</option>';

            // 添加工具选项
            tools.forEach(tool => {
                const option = document.createElement('option');
                option.value = tool.id;
                option.textContent = tool.name;
                toolSelect.appendChild(option);
            });
        }

        function loadTool() {
            const selectedToolId = toolSelect.value;
            if (!selectedToolId) {
                toolInfo.textContent = '选择一个工具开始使用';
                toolIframe.src = 'about:blank';
                return;
            }

            const tool = tools.find(t => t.id === selectedToolId);
            if (tool) {
                toolInfo.textContent = tool.description;
                toolIframe.src = tool.url;

                // 更新收缩状态的活跃工具
                updateCollapsedToolsActive(selectedToolId);
            }
        }

        function updateCollapsedTools() {
            collapsedTools.innerHTML = '';

            tools.forEach(tool => {
                const toolItem = document.createElement('div');
                toolItem.className = 'collapsed-tool-item';
                toolItem.dataset.toolId = tool.id;
                toolItem.innerHTML = getToolIcon(tool.name);
                toolItem.title = tool.name;
                toolItem.onclick = () => loadToolFromCollapsed(tool.id);
                collapsedTools.appendChild(toolItem);
            });
        }

        function updateCollapsedToolsActive(activeToolId) {
            const toolItems = collapsedTools.querySelectorAll('.collapsed-tool-item');
            toolItems.forEach(item => {
                item.classList.toggle('active', item.dataset.toolId === activeToolId);
            });
        }

        function loadToolFromCollapsed(toolId) {
            if (!rightToolbarOpen) {
                toggleRightToolbar();
            }

            toolSelect.value = toolId;
            loadTool();
        }

        function getToolIcon(toolName) {
            // 根据工具名称返回合适的图标
            const iconMap = {
                '计算器': '🧮',
                '颜色选择器': '🎨',
                'JSON格式化': '📄',
                '时钟': '⏰',
                '天气': '🌤️',
                '翻译': '🌐',
                '笔记': '📝',
                '图片': '🖼️',
                '音乐': '🎵',
                '视频': '🎬'
            };

            // 尝试匹配关键词
            for (const [key, icon] of Object.entries(iconMap)) {
                if (toolName.includes(key)) {
                    return icon;
                }
            }

            return '🛠️'; // 默认图标
        }

        // 工具管理功能
        function openToolManagement() {
            toolManagementModal.classList.add('show');
            loadToolsTable();
        }

        function closeToolManagement() {
            toolManagementModal.classList.remove('show');
            clearForm();
            currentEditingTool = null;
        }

        function loadToolsTable() {
            toolsTableBody.innerHTML = '';

            tools.forEach(tool => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${tool.name}</td>
                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${tool.url}">${tool.url}</td>
                    <td style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${tool.description}">${tool.description}</td>
                    <td class="tool-actions">
                        <button class="btn-edit" onclick="editTool('${tool.id}')">编辑</button>
                        <button class="btn-delete" onclick="deleteTool('${tool.id}')">删除</button>
                    </td>
                `;
                toolsTableBody.appendChild(row);
            });
        }

        function saveTool() {
            const name = document.getElementById('toolName').value.trim();
            const url = document.getElementById('toolUrl').value.trim();
            const description = document.getElementById('toolDescription').value.trim();

            if (!name || !url) {
                alert('请填写工具名称和URL地址');
                return;
            }

            if (currentEditingTool) {
                // 编辑现有工具
                const toolIndex = tools.findIndex(t => t.id === currentEditingTool);
                if (toolIndex !== -1) {
                    tools[toolIndex] = {
                        ...tools[toolIndex],
                        name,
                        url,
                        description
                    };
                }
            } else {
                // 添加新工具
                const newTool = {
                    id: 'tool_' + Date.now(),
                    name,
                    url,
                    description
                };
                tools.push(newTool);
            }

            saveTools();
            loadToolsList();
            loadToolsTable();
            updateCollapsedTools();
            clearForm();
            currentEditingTool = null;
        }

        function editTool(toolId) {
            const tool = tools.find(t => t.id === toolId);
            if (tool) {
                document.getElementById('toolName').value = tool.name;
                document.getElementById('toolUrl').value = tool.url;
                document.getElementById('toolDescription').value = tool.description;
                currentEditingTool = toolId;
            }
        }

        function deleteTool(toolId) {
            if (confirm('确定要删除这个工具吗？')) {
                tools = tools.filter(t => t.id !== toolId);
                saveTools();
                loadToolsList();
                loadToolsTable();
                updateCollapsedTools();

                // 如果删除的是当前选中的工具，清空iframe
                if (toolSelect.value === toolId) {
                    toolSelect.value = '';
                    loadTool();
                }
            }
        }

        function clearForm() {
            document.getElementById('toolName').value = '';
            document.getElementById('toolUrl').value = '';
            document.getElementById('toolDescription').value = '';
            currentEditingTool = null;
        }

        function saveTools() {
            localStorage.setItem('webTools', JSON.stringify(tools));
        }

        // 测试控制函数
        function toggleMobile() {
            isMobile = !isMobile;
            updateLayout();
            alert(`已切换到${isMobile ? '移动端' : '桌面端'}模式`);
        }

        function resetDemo() {
            sidebar.classList.remove('collapsed', 'open');
            rightToolbar.classList.remove('collapsed');
            mobileOverlay.classList.remove('active');
            document.body.style.overflow = '';
            isOpen = true;
            rightToolbarOpen = true;
            isMobile = window.innerWidth <= 768;
            updateLayout();

            // 重置到第一个导航项
            setActiveNav(navLinks[0]);
            updatePageTitle('overview');

            // 重置右侧工具栏
            toolSelect.value = '';
            loadTool();

            // 更新切换按钮图标
            const rightIcon = rightToggleBtn.querySelector('i');
            rightIcon.className = 'fas fa-chevron-right';
        }

        // 拖动功能实现
        function initDragAndDrop() {
            // 从localStorage恢复位置
            const savedPosition = localStorage.getItem('controlPanelPosition');
            if (savedPosition) {
                controlPanelPosition = JSON.parse(savedPosition);
                updateControlPanelPosition();
            }

            // 鼠标按下事件
            controlPanelHeader.addEventListener('mousedown', startDrag);
            controlPanel.addEventListener('mousedown', startDrag);
        }

        function startDrag(e) {
            // 只有在点击头部或面板本身时才开始拖动
            if (e.target.closest('.control-btn')) {
                return; // 不拖动按钮
            }

            isDragging = true;
            controlPanel.classList.add('dragging');

            const rect = controlPanel.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;

            // 添加全局事件监听器
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);

            // 防止文本选择
            e.preventDefault();
        }

        function drag(e) {
            if (!isDragging) return;

            const x = e.clientX - dragOffset.x;
            const y = e.clientY - dragOffset.y;

            // 限制在视窗范围内
            const maxX = window.innerWidth - controlPanel.offsetWidth;
            const maxY = window.innerHeight - controlPanel.offsetHeight;

            controlPanelPosition.x = Math.max(0, Math.min(x, maxX));
            controlPanelPosition.y = Math.max(0, Math.min(y, maxY));

            updateControlPanelPosition();
        }

        function stopDrag() {
            if (!isDragging) return;

            isDragging = false;
            controlPanel.classList.remove('dragging');

            // 移除全局事件监听器
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);

            // 保存位置到localStorage
            localStorage.setItem('controlPanelPosition', JSON.stringify(controlPanelPosition));
        }

        function updateControlPanelPosition() {
            controlPanel.style.left = controlPanelPosition.x + 'px';
            controlPanel.style.top = controlPanelPosition.y + 'px';
            controlPanel.style.right = 'auto';
            controlPanel.style.bottom = 'auto';
        }

        // 窗口大小改变时重新调整位置
        window.addEventListener('resize', () => {
            const maxX = window.innerWidth - controlPanel.offsetWidth;
            const maxY = window.innerHeight - controlPanel.offsetHeight;

            controlPanelPosition.x = Math.max(0, Math.min(controlPanelPosition.x, maxX));
            controlPanelPosition.y = Math.max(0, Math.min(controlPanelPosition.y, maxY));

            updateControlPanelPosition();
        });

        // 右侧工具栏调整宽度功能
        function initRightToolbarResize() {
            // 从localStorage恢复宽度
            const savedWidth = localStorage.getItem('rightToolbarWidth');
            if (savedWidth) {
                rightToolbarWidth = parseInt(savedWidth);
                updateRightToolbarWidth();
            }

            // 添加拖拽事件监听器
            resizeHandle.addEventListener('mousedown', startResize);
        }

        function startResize(e) {
            isResizing = true;
            resizeHandle.classList.add('resizing');
            rightToolbar.classList.add('resizing');

            // 添加全局事件监听器
            document.addEventListener('mousemove', resize);
            document.addEventListener('mouseup', stopResize);

            // 防止文本选择
            e.preventDefault();
            document.body.style.userSelect = 'none';
        }

        function resize(e) {
            if (!isResizing) return;

            // 计算新宽度（从右边界向左拖拽）
            const rect = rightToolbar.getBoundingClientRect();
            const newWidth = rect.right - e.clientX;

            // 限制宽度范围
            rightToolbarWidth = Math.max(minToolbarWidth, Math.min(newWidth, maxToolbarWidth));

            updateRightToolbarWidth();
        }

        function stopResize() {
            if (!isResizing) return;

            isResizing = false;
            resizeHandle.classList.remove('resizing');
            rightToolbar.classList.remove('resizing');

            // 移除全局事件监听器
            document.removeEventListener('mousemove', resize);
            document.removeEventListener('mouseup', stopResize);

            // 恢复文本选择
            document.body.style.userSelect = '';

            // 保存宽度到localStorage
            localStorage.setItem('rightToolbarWidth', rightToolbarWidth.toString());
        }

        function updateRightToolbarWidth() {
            if (!rightToolbarOpen) return; // 收缩状态下不更新宽度

            rightToolbar.style.width = rightToolbarWidth + 'px';

            // 更新移动端响应式CSS
            updateResponsiveStyles();
        }

        function updateResponsiveStyles() {
            // 动态更新移动端的隐藏位置
            const style = document.getElementById('dynamic-styles') || document.createElement('style');
            style.id = 'dynamic-styles';

            style.textContent = `
                @media (max-width: 768px) {
                    .right-toolbar {
                        right: -${rightToolbarWidth}px;
                    }
                    .right-toolbar.collapsed {
                        right: -60px;
                    }
                }
            `;

            if (!document.getElementById('dynamic-styles')) {
                document.head.appendChild(style);
            }
        }

        // 修改toggleRightToolbar函数以支持宽度调整
        function toggleRightToolbar() {
            rightToolbarOpen = !rightToolbarOpen;
            rightToolbar.classList.toggle('collapsed', !rightToolbarOpen);

            if (rightToolbarOpen) {
                updateRightToolbarWidth(); // 展开时应用保存的宽度
            }

            // 更新切换按钮图标
            const icon = rightToggleBtn.querySelector('i');
            icon.className = rightToolbarOpen ? 'fas fa-chevron-right' : 'fas fa-chevron-left';
        }

        // 初始化应用
        init();
    </script>
</body>

</html>