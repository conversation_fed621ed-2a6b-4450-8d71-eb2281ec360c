<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理系统测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .test-header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .test-item:last-child {
            border-bottom: none;
        }

        .test-description {
            flex: 1;
            margin-right: 16px;
        }

        .test-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-running {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }

        .test-button {
            margin-left: 12px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .btn-test {
            background: #3b82f6;
            color: white;
        }

        .btn-test:hover {
            background: #2563eb;
        }

        .btn-test:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .test-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 16px;
        }

        .log-entry {
            margin-bottom: 4px;
        }

        .log-success {
            color: #10b981;
        }

        .log-error {
            color: #ef4444;
        }

        .log-warning {
            color: #f59e0b;
        }

        .log-info {
            color: #3b82f6;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> 项目管理系统测试</h1>
            <p>测试新的项目管理流程，确保所有功能正常运行</p>
            <div class="progress-bar">
                <div class="progress-fill" id="overall-progress" style="width: 0%"></div>
            </div>
            <div style="display: flex; gap: 12px; margin-top: 16px;">
                <button class="btn btn-primary" onclick="runAllTests()">
                    <i class="fas fa-play"></i> 运行所有测试
                </button>
                <button class="btn btn-secondary" onclick="clearLogs()">
                    <i class="fas fa-trash"></i> 清除日志
                </button>
                <button class="btn btn-outline" onclick="exportTestReport()">
                    <i class="fas fa-download"></i> 导出报告
                </button>
            </div>
        </div>

        <!-- 认证和路由测试 -->
        <div class="test-section">
            <h2><i class="fas fa-shield-alt"></i> 认证和路由测试</h2>
            <div class="test-item">
                <div class="test-description">
                    <strong>用户登录状态检查</strong>
                    <p>验证用户登录状态检查功能</p>
                </div>
                <span class="test-status status-pending" id="auth-check-status">待测试</span>
                <button class="test-button btn-test" onclick="testAuthCheck()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>项目选择验证</strong>
                    <p>验证项目选择和权限检查</p>
                </div>
                <span class="test-status status-pending" id="project-validation-status">待测试</span>
                <button class="test-button btn-test" onclick="testProjectValidation()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>页面路由跳转</strong>
                    <p>测试登录后的页面跳转逻辑</p>
                </div>
                <span class="test-status status-pending" id="routing-status">待测试</span>
                <button class="test-button btn-test" onclick="testRouting()">测试</button>
            </div>
        </div>

        <!-- 项目管理功能测试 -->
        <div class="test-section">
            <h2><i class="fas fa-folder-open"></i> 项目管理功能测试</h2>
            <div class="test-item">
                <div class="test-description">
                    <strong>项目列表加载</strong>
                    <p>测试项目列表的加载和显示</p>
                </div>
                <span class="test-status status-pending" id="project-list-status">待测试</span>
                <button class="test-button btn-test" onclick="testProjectList()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>项目创建功能</strong>
                    <p>测试新项目的创建流程</p>
                </div>
                <span class="test-status status-pending" id="project-create-status">待测试</span>
                <button class="test-button btn-test" onclick="testProjectCreate()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>项目编辑功能</strong>
                    <p>测试项目信息的编辑和更新</p>
                </div>
                <span class="test-status status-pending" id="project-edit-status">待测试</span>
                <button class="test-button btn-test" onclick="testProjectEdit()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>项目删除功能</strong>
                    <p>测试项目的删除操作</p>
                </div>
                <span class="test-status status-pending" id="project-delete-status">待测试</span>
                <button class="test-button btn-test" onclick="testProjectDelete()">测试</button>
            </div>
        </div>

        <!-- 导出功能测试 -->
        <div class="test-section">
            <h2><i class="fas fa-download"></i> 导出功能测试</h2>
            <div class="test-item">
                <div class="test-description">
                    <strong>JSON导出</strong>
                    <p>测试项目数据的JSON格式导出</p>
                </div>
                <span class="test-status status-pending" id="export-json-status">待测试</span>
                <button class="test-button btn-test" onclick="testExportJSON()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>PDF导出</strong>
                    <p>测试项目数据的PDF格式导出</p>
                </div>
                <span class="test-status status-pending" id="export-pdf-status">待测试</span>
                <button class="test-button btn-test" onclick="testExportPDF()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>DOCX导出</strong>
                    <p>测试项目数据的DOCX格式导出</p>
                </div>
                <span class="test-status status-pending" id="export-docx-status">待测试</span>
                <button class="test-button btn-test" onclick="testExportDOCX()">测试</button>
            </div>
        </div>

        <!-- 用户体验测试 -->
        <div class="test-section">
            <h2><i class="fas fa-user-check"></i> 用户体验测试</h2>
            <div class="test-item">
                <div class="test-description">
                    <strong>加载状态显示</strong>
                    <p>测试加载提示和骨架屏效果</p>
                </div>
                <span class="test-status status-pending" id="loading-status">待测试</span>
                <button class="test-button btn-test" onclick="testLoadingStates()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>响应式设计</strong>
                    <p>测试在不同屏幕尺寸下的显示效果</p>
                </div>
                <span class="test-status status-pending" id="responsive-status">待测试</span>
                <button class="test-button btn-test" onclick="testResponsive()">测试</button>
            </div>
            <div class="test-item">
                <div class="test-description">
                    <strong>错误处理</strong>
                    <p>测试错误情况下的用户提示</p>
                </div>
                <span class="test-status status-pending" id="error-handling-status">待测试</span>
                <button class="test-button btn-test" onclick="testErrorHandling()">测试</button>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h2><i class="fas fa-list-alt"></i> 测试日志</h2>
            <div class="test-log" id="test-log">
                <div class="log-entry log-info">[INFO] 测试系统已准备就绪</div>
            </div>
        </div>
    </div>

    <script src="supabase-config.js"></script>
    <script src="export-service.js"></script>
    <script src="project-management-test.js"></script>
</body>
</html>
