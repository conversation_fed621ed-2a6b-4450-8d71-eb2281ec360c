# 《大模型技术与油气应用概论》专著编写系统

## 项目简介

这是一个专门为编写《大模型技术与油气应用概论》而开发的专用服务系统，基于HTML5技术构建，提供了完整的学术专著编写工具链。

## 功能特性

### 🎯 核心功能

1. **大纲管理**
   - 层级化章节结构管理
   - 拖拽排序和重组
   - 实时编辑和更新
   - 导入导出支持

2. **章节编写**
   - 富文本编辑器（基于Quill.js）
   - 实时保存和版本控制
   - 模板插入功能
   - 协作编写支持

3. **结构图绘制**
   - 思维导图生成
   - 流程图设计
   - 架构图绘制
   - 基于Mermaid语法

4. **参考文献管理**
   - 文献信息录入
   - 多格式导入支持
   - 引用管理
   - 自动格式化

5. **进度管理**
   - 写作进度跟踪
   - 章节完成状态
   - 统计分析
   - 时间管理

### 🛠️ 技术特性

- **纯前端实现**：无需服务器，本地运行
- **响应式设计**：支持多种设备和屏幕尺寸
- **本地存储**：数据自动保存到浏览器本地存储
- **模块化架构**：易于扩展和维护
- **现代化UI**：基于现代设计理念的用户界面

## 快速开始

### 1. 环境要求

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 无需安装额外软件

### 2. 启动系统

1. 下载项目文件到本地
2. 用浏览器打开 `index.html` 文件
3. 系统自动加载，开始使用

### 3. 基本使用

#### 大纲管理
- 点击左侧"大纲管理"进入大纲编辑界面
- 使用"添加章节"按钮创建新的章节
- 点击章节项目可以编辑或删除
- 支持多级层次结构

#### 章节编写
- 点击"章节编写"切换到编辑模式
- 从下拉菜单选择要编辑的章节
- 使用富文本编辑器编写内容
- 系统自动保存编辑内容

#### 结构图绘制
- 选择"结构图"功能
- 选择图表类型（思维导图、流程图、架构图）
- 编写Mermaid语法代码
- 实时预览图表效果

## 文件结构

```
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── app.js             # 主要JavaScript逻辑
├── sample-data.js     # 示例数据
├── README.md          # 说明文档
└── assets/            # 资源文件夹（如需要）
```

## 数据格式

### 项目数据结构

```javascript
{
    title: "书籍标题",
    outline: [              // 大纲结构
        {
            id: "唯一标识",
            title: "章节标题",
            level: 0,          // 层级（0=篇，1=章，2=节）
            children: []       // 子章节
        }
    ],
    chapters: {             // 章节内容
        "章节ID": {
            title: "标题",
            summary: "摘要",
            content: {},       // Quill Delta格式
            lastModified: "时间戳"
        }
    },
    references: [],         // 参考文献
    diagrams: {},          // 图表数据
    progress: {}           // 进度信息
}
```

## 使用技巧

### 快捷键
- `Ctrl + S`：保存当前内容
- 点击大纲项目：快速跳转到对应章节

### 模板使用
- 章节模板：提供标准的学术章节结构
- 表格模板：快速插入格式化表格
- 图片模板：标准的图片引用格式
- 公式模板：数学公式插入

### 图表语法
系统使用Mermaid语法绘制图表，支持：

```mermaid
# 思维导图
mindmap
  root((主题))
    分支1
    分支2

# 流程图
flowchart TD
    A[开始] --> B[处理]
    B --> C[结束]

# 架构图
graph TB
    subgraph "层级1"
        A1[组件1]
        A2[组件2]
    end
```

## 数据管理

### 导出功能
- **项目导出**：完整的JSON格式项目文件
- **章节导出**：单个章节的内容
- **大纲导出**：结构化的大纲文件

### 导入功能
- **JSON导入**：完整项目数据导入
- **文本导入**：纯文本大纲导入
- **文献导入**：支持CSV、JSON格式

### 备份建议
- 定期使用"导出项目"功能备份数据
- 重要内容建议多处备份
- 可以将导出的JSON文件用于版本控制

## 扩展开发

### 添加新功能
1. 在 `app.js` 中添加功能函数
2. 在 `index.html` 中添加UI元素
3. 在 `styles.css` 中添加样式

### 自定义模板
修改 `insertTemplate()` 函数添加新的内容模板

### 集成外部服务
可以通过修改保存和加载函数集成云存储服务

## 技术栈

- **HTML5**：页面结构
- **CSS3**：样式和布局
- **JavaScript (ES6+)**：核心逻辑
- **Quill.js**：富文本编辑器
- **Mermaid.js**：图表绘制
- **Font Awesome**：图标库

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献指南

欢迎提交问题报告和功能建议：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址：[GitHub Repository]
- 邮箱：[联系邮箱]
- 文档：[在线文档地址]

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础功能实现
- 大纲管理功能
- 章节编写功能
- 结构图绘制功能
- 参考文献管理
- 进度跟踪功能

---

**注意**：本系统专门为《大模型技术与油气应用概论》的编写而设计，但也可以适用于其他学术专著的编写工作。
