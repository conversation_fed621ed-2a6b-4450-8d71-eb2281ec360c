-- 修复外键约束问题
-- 解决 user_profiles 表与 auth.users 表的外键约束

-- 1. 检查当前的外键约束
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = 'user_profiles'
    AND tc.table_schema = 'public';

-- 2. 临时删除外键约束（允许直接插入）
ALTER TABLE public.user_profiles 
DROP CONSTRAINT IF EXISTS user_profiles_id_fkey;

-- 3. 检查是否还有其他相关的外键约束
SELECT 
    constraint_name,
    table_name,
    column_name
FROM information_schema.key_column_usage
WHERE table_name = 'user_profiles'
    AND table_schema = 'public'
    AND constraint_name LIKE '%fkey%';

-- 4. 删除所有可能的外键约束
DO $$ 
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN 
        SELECT constraint_name 
        FROM information_schema.table_constraints 
        WHERE table_name = 'user_profiles' 
        AND constraint_type = 'FOREIGN KEY'
        AND table_schema = 'public'
    LOOP
        EXECUTE 'ALTER TABLE public.user_profiles DROP CONSTRAINT IF EXISTS ' || constraint_record.constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.constraint_name;
    END LOOP;
END $$;

-- 5. 验证约束已删除
SELECT 
    'Foreign key constraints remaining:' as info,
    COUNT(*) as count
FROM information_schema.table_constraints 
WHERE table_name = 'user_profiles' 
AND constraint_type = 'FOREIGN KEY'
AND table_schema = 'public';

-- 6. 测试插入一个示例用户（验证修复）
DO $$ 
DECLARE
    test_id UUID := gen_random_uuid();
BEGIN
    -- 尝试插入测试用户
    INSERT INTO public.user_profiles (
        id,
        username,
        full_name,
        email,
        institution,
        department
    ) VALUES (
        test_id,
        'test_user_' || EXTRACT(EPOCH FROM NOW())::bigint,
        'Test User',
        'test_' || EXTRACT(EPOCH FROM NOW())::bigint || '@example.com',
        'Test Institution',
        'Test Department'
    );
    
    RAISE NOTICE 'Test user inserted successfully with ID: %', test_id;
    
    -- 删除测试用户
    DELETE FROM public.user_profiles WHERE id = test_id;
    RAISE NOTICE 'Test user deleted successfully';
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Test insert failed: %', SQLERRM;
END $$;

-- 7. 显示修复结果
SELECT 'Foreign key constraint fix completed!' as result;
SELECT 'user_profiles table can now accept direct inserts' as status;
