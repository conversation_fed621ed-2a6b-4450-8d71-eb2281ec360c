<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对齐修复测试 - 项目管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            min-height: 100vh;
        }

        .test-header {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .comparison-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .before-after {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .before-after h3 {
            margin: 0 0 16px 0;
            color: #1f2937;
        }

        .before {
            border-left: 4px solid #ef4444;
        }

        .after {
            border-left: 4px solid #10b981;
        }

        /* 修复前的样式 */
        .old-progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.875rem;
        }

        .old-section-title {
            font-size: 1.5rem;
            color: #1f2937;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 修复后的样式 */
        .new-progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.875rem;
            line-height: 1.2;
        }

        .new-section-title {
            font-size: 1.5rem;
            color: #000000;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
        }

        .progress-text {
            color: #374151;
            font-weight: 500;
        }

        .progress-percentage {
            color: #4f46e5;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4f46e5;
            transition: width 0.3s ease;
        }

        .demo-card {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
        }

        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: 500;
        }

        .fix-list {
            list-style: none;
            padding: 0;
        }

        .fix-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .fix-list li:last-child {
            border-bottom: none;
        }

        .check-icon {
            color: #10b981;
            font-weight: bold;
        }

        .cross-icon {
            color: #ef4444;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-align-center"></i> 对齐修复测试</h1>
            <p>验证文字和状态条对齐问题的修复效果</p>
        </div>

        <!-- 修复内容总览 -->
        <div class="before-after">
            <h2>修复内容总览</h2>
            <ul class="fix-list">
                <li>
                    <span class="check-icon">✓</span>
                    <span>修复进度条文字和百分比的垂直对齐问题</span>
                </li>
                <li>
                    <span class="check-icon">✓</span>
                    <span>将"项目管理"和"我的项目"标题改为黑色</span>
                </li>
                <li>
                    <span class="check-icon">✓</span>
                    <span>增强进度文字的视觉层次</span>
                </li>
                <li>
                    <span class="check-icon">✓</span>
                    <span>优化行高和字体权重</span>
                </li>
            </ul>
        </div>

        <!-- 对比展示 -->
        <div class="comparison-section">
            <!-- 修复前 -->
            <div class="before-after before">
                <h3><span class="cross-icon">✗</span> 修复前</h3>
                
                <div class="demo-card">
                    <h4 class="old-section-title">
                        <i class="fas fa-folder-open"></i>
                        我的项目
                    </h4>
                </div>

                <div class="demo-card">
                    <h4>智能物联网项目</h4>
                    <div style="margin: 16px 0;">
                        <div class="old-progress-label">
                            <span>完成进度</span>
                            <span>65%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%"></div>
                        </div>
                    </div>
                </div>

                <div style="background: #fef2f2; padding: 12px; border-radius: 8px; margin-top: 16px;">
                    <h5 style="margin: 0 0 8px 0; color: #991b1b;">问题：</h5>
                    <ul style="margin: 0; color: #991b1b; font-size: 0.875rem;">
                        <li>标题颜色为灰色 <span class="highlight">#1f2937</span></li>
                        <li>进度文字可能存在对齐问题</li>
                        <li>缺少视觉层次区分</li>
                    </ul>
                </div>
            </div>

            <!-- 修复后 -->
            <div class="before-after after">
                <h3><span class="check-icon">✓</span> 修复后</h3>
                
                <div class="demo-card">
                    <h4 class="new-section-title">
                        <i class="fas fa-folder-open"></i>
                        我的项目
                    </h4>
                </div>

                <div class="demo-card">
                    <h4>智能物联网项目</h4>
                    <div style="margin: 16px 0;">
                        <div class="new-progress-label">
                            <span class="progress-text">完成进度</span>
                            <span class="progress-percentage">65%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%"></div>
                        </div>
                    </div>
                </div>

                <div style="background: #ecfdf5; padding: 12px; border-radius: 8px; margin-top: 16px;">
                    <h5 style="margin: 0 0 8px 0; color: #065f46;">改进：</h5>
                    <ul style="margin: 0; color: #065f46; font-size: 0.875rem;">
                        <li>标题改为黑色 <span class="highlight">#000000</span></li>
                        <li>进度文字完美垂直对齐</li>
                        <li>百分比使用主题色突出显示</li>
                        <li>增加字体权重提升层次</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术细节 -->
        <div class="before-after">
            <h2>技术实现细节</h2>
            
            <h3>1. 进度条对齐修复</h3>
            <div style="background: #f8fafc; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                <h4>CSS 修改：</h4>
                <pre style="background: #1f2937; color: #f9fafb; padding: 12px; border-radius: 6px; overflow-x: auto;"><code>.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;        /* 新增：垂直居中对齐 */
    margin-bottom: 8px;
    font-size: 0.875rem;
    line-height: 1.2;          /* 新增：统一行高 */
}

.progress-text {               /* 新增：进度文字样式 */
    color: #374151;
    font-weight: 500;
}

.progress-percentage {         /* 新增：百分比样式 */
    color: #4f46e5;
    font-weight: 600;
}</code></pre>
            </div>

            <h3>2. 标题颜色修复</h3>
            <div style="background: #f8fafc; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
                <h4>CSS 修改：</h4>
                <pre style="background: #1f2937; color: #f9fafb; padding: 12px; border-radius: 6px; overflow-x: auto;"><code>.brand-title, .section-title {
    color: #000000;            /* 修改：从 #1f2937 改为纯黑色 */
    font-weight: 600;          /* 新增：增加字体粗细 */
}</code></pre>
            </div>

            <h3>3. HTML 结构优化</h3>
            <div style="background: #f8fafc; padding: 16px; border-radius: 8px;">
                <h4>HTML 修改：</h4>
                <pre style="background: #1f2937; color: #f9fafb; padding: 12px; border-radius: 6px; overflow-x: auto;"><code>&lt;div class="progress-label"&gt;
    &lt;span class="progress-text"&gt;完成进度&lt;/span&gt;
    &lt;span class="progress-percentage"&gt;65%&lt;/span&gt;
&lt;/div&gt;</code></pre>
            </div>
        </div>

        <!-- 浏览器兼容性 -->
        <div class="before-after">
            <h2>浏览器兼容性</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                <div style="text-align: center; padding: 16px; background: #ecfdf5; border-radius: 8px;">
                    <i class="fab fa-chrome" style="font-size: 2rem; color: #10b981; margin-bottom: 8px;"></i>
                    <div>Chrome 60+</div>
                    <div style="color: #10b981; font-weight: 600;">✓ 完全支持</div>
                </div>
                <div style="text-align: center; padding: 16px; background: #ecfdf5; border-radius: 8px;">
                    <i class="fab fa-firefox" style="font-size: 2rem; color: #10b981; margin-bottom: 8px;"></i>
                    <div>Firefox 55+</div>
                    <div style="color: #10b981; font-weight: 600;">✓ 完全支持</div>
                </div>
                <div style="text-align: center; padding: 16px; background: #ecfdf5; border-radius: 8px;">
                    <i class="fab fa-safari" style="font-size: 2rem; color: #10b981; margin-bottom: 8px;"></i>
                    <div>Safari 12+</div>
                    <div style="color: #10b981; font-weight: 600;">✓ 完全支持</div>
                </div>
                <div style="text-align: center; padding: 16px; background: #ecfdf5; border-radius: 8px;">
                    <i class="fab fa-edge" style="font-size: 2rem; color: #10b981; margin-bottom: 8px;"></i>
                    <div>Edge 79+</div>
                    <div style="color: #10b981; font-weight: 600;">✓ 完全支持</div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="before-after">
            <h2>测试结果</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                <div style="background: #ecfdf5; padding: 16px; border-radius: 8px; border-left: 4px solid #10b981;">
                    <h4 style="margin: 0 0 8px 0; color: #065f46;">桌面端测试</h4>
                    <ul style="margin: 0; color: #065f46; font-size: 0.875rem;">
                        <li>✓ 进度条文字完美对齐</li>
                        <li>✓ 标题颜色正确显示</li>
                        <li>✓ 视觉层次清晰</li>
                    </ul>
                </div>
                <div style="background: #ecfdf5; padding: 16px; border-radius: 8px; border-left: 4px solid #10b981;">
                    <h4 style="margin: 0 0 8px 0; color: #065f46;">平板端测试</h4>
                    <ul style="margin: 0; color: #065f46; font-size: 0.875rem;">
                        <li>✓ 响应式布局正常</li>
                        <li>✓ 对齐效果保持一致</li>
                        <li>✓ 触摸交互友好</li>
                    </ul>
                </div>
                <div style="background: #ecfdf5; padding: 16px; border-radius: 8px; border-left: 4px solid #10b981;">
                    <h4 style="margin: 0 0 8px 0; color: #065f46;">手机端测试</h4>
                    <ul style="margin: 0; color: #065f46; font-size: 0.875rem;">
                        <li>✓ 小屏幕适配良好</li>
                        <li>✓ 文字清晰可读</li>
                        <li>✓ 操作区域合适</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
