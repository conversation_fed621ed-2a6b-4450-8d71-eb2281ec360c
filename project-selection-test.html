<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目选择功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .project-overview-test {
            border: 2px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
        }
        .project-overview-test h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .project-overview-test p {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-flask"></i> 项目选择功能测试</h1>
        
        <!-- 项目选择器测试 -->
        <div class="test-section">
            <h2 class="test-title">1. 项目选择器组件测试</h2>
            <div class="project-selector-inline" id="project-selector-inline">
                <button class="project-btn-inline" id="project-btn-inline">
                    <div class="project-btn-content">
                        <i class="fas fa-folder project-btn-icon"></i>
                        <span class="project-btn-text" id="selected-project-inline">选择项目</span>
                    </div>
                    <i class="fas fa-chevron-down project-btn-arrow"></i>
                </button>
                <div class="project-list" id="project-list-inline">
                    <div class="project-list-content">
                        <div class="project-item create-new" onclick="showCreateProject()">
                            <i class="fas fa-plus project-item-icon"></i>
                            <span class="project-item-text">创建新项目</span>
                        </div>
                        <div class="project-divider"></div>
                        <!-- 项目列表将在这里动态生成 -->
                    </div>
                </div>
            </div>
            <div id="selector-test-result" class="test-result test-info">等待测试...</div>
        </div>

        <!-- 项目概览信息测试 -->
        <div class="test-section">
            <h2 class="test-title">2. 项目概览信息同步测试</h2>
            <div class="project-overview-test">
                <h3 id="dashboard-project-title">《大模型技术与油气应用概论》</h3>
                <p id="dashboard-project-description">面向大学生的大模型技术教材</p>
                <div class="project-status-container">
                    <div class="project-status active" id="dashboard-project-status-indicator" title="项目状态：进行中"></div>
                </div>
            </div>
            <div id="overview-test-result" class="test-result test-info">等待测试...</div>
        </div>

        <!-- 全局状态测试 -->
        <div class="test-section">
            <h2 class="test-title">3. 全局状态管理测试</h2>
            <div id="global-state-info">
                <p><strong>当前项目ID:</strong> <span id="current-project-id">未设置</span></p>
                <p><strong>当前项目标题:</strong> <span id="current-project-title">未设置</span></p>
                <p><strong>协作管理器项目ID:</strong> <span id="collab-project-id">未设置</span></p>
            </div>
            <div id="global-test-result" class="test-result test-info">等待测试...</div>
        </div>

        <!-- 测试控制按钮 -->
        <div class="test-section">
            <h2 class="test-title">4. 测试控制</h2>
            <button class="test-button" onclick="runAllTests()">
                <i class="fas fa-play"></i> 运行所有测试
            </button>
            <button class="test-button" onclick="testProjectSelector()">
                <i class="fas fa-mouse-pointer"></i> 测试项目选择器
            </button>
            <button class="test-button" onclick="testProjectOverview()">
                <i class="fas fa-eye"></i> 测试概览同步
            </button>
            <button class="test-button" onclick="testGlobalState()">
                <i class="fas fa-globe"></i> 测试全局状态
            </button>
            <button class="test-button" onclick="simulateProjectSelection()">
                <i class="fas fa-magic"></i> 模拟项目选择
            </button>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <h2 class="test-title">5. 测试日志</h2>
            <div id="test-log" style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="supabase-config.js"></script>
    <script src="supabase-config-manager.js"></script>
    <script src="app.js"></script>
    
    <script>
        // 测试日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[TEST] ${message}`);
        }

        // 模拟项目数据
        const mockProjects = [
            {
                id: 'test-project-1',
                title: '大模型技术与油气应用概论',
                description: '面向大学生的大模型技术教材',
                status: 'active',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 'test-project-2',
                title: '人工智能在石油勘探中的应用',
                description: '深入探讨AI技术在石油勘探领域的实际应用',
                status: 'active',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            },
            {
                id: 'test-project-3',
                title: '深度学习与地质建模',
                description: '结合深度学习技术进行地质建模的研究',
                status: 'suspended',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ];

        // 运行所有测试
        async function runAllTests() {
            log('开始运行所有测试...', 'info');
            
            try {
                await testProjectSelector();
                await testProjectOverview();
                await testGlobalState();
                await simulateProjectSelection();
                
                log('所有测试完成！', 'success');
            } catch (error) {
                log(`测试过程中出现错误: ${error.message}`, 'error');
            }
        }

        // 测试项目选择器
        async function testProjectSelector() {
            log('测试项目选择器...', 'info');
            const resultElement = document.getElementById('selector-test-result');
            
            try {
                // 检查项目选择器元素是否存在
                const projectBtn = document.getElementById('project-btn-inline');
                const projectList = document.getElementById('project-list-inline');
                const selectedText = document.getElementById('selected-project-inline');
                
                if (!projectBtn || !projectList || !selectedText) {
                    throw new Error('项目选择器元素缺失');
                }
                
                // 检查事件监听器是否绑定
                const hasClickListener = projectBtn.onclick || projectBtn.addEventListener;
                
                resultElement.className = 'test-result test-success';
                resultElement.textContent = '✅ 项目选择器组件正常，元素完整，事件监听器已绑定';
                log('项目选择器测试通过', 'success');
                
            } catch (error) {
                resultElement.className = 'test-result test-error';
                resultElement.textContent = `❌ 项目选择器测试失败: ${error.message}`;
                log(`项目选择器测试失败: ${error.message}`, 'error');
            }
        }

        // 测试项目概览同步
        async function testProjectOverview() {
            log('测试项目概览同步...', 'info');
            const resultElement = document.getElementById('overview-test-result');
            
            try {
                // 检查概览元素是否存在
                const titleElement = document.getElementById('dashboard-project-title');
                const descElement = document.getElementById('dashboard-project-description');
                const statusElement = document.getElementById('dashboard-project-status-indicator');
                
                if (!titleElement || !descElement || !statusElement) {
                    throw new Error('项目概览元素缺失');
                }
                
                // 检查updateProjectOverview函数是否存在
                if (typeof updateProjectOverview !== 'function') {
                    throw new Error('updateProjectOverview函数不存在');
                }
                
                resultElement.className = 'test-result test-success';
                resultElement.textContent = '✅ 项目概览组件正常，更新函数可用';
                log('项目概览测试通过', 'success');
                
            } catch (error) {
                resultElement.className = 'test-result test-error';
                resultElement.textContent = `❌ 项目概览测试失败: ${error.message}`;
                log(`项目概览测试失败: ${error.message}`, 'error');
            }
        }

        // 测试全局状态管理
        async function testGlobalState() {
            log('测试全局状态管理...', 'info');
            const resultElement = document.getElementById('global-test-result');
            
            try {
                // 检查全局变量是否存在
                if (typeof currentProject === 'undefined') {
                    throw new Error('currentProject全局变量不存在');
                }
                
                // 更新显示
                updateGlobalStateDisplay();
                
                resultElement.className = 'test-result test-success';
                resultElement.textContent = '✅ 全局状态管理正常';
                log('全局状态测试通过', 'success');
                
            } catch (error) {
                resultElement.className = 'test-result test-error';
                resultElement.textContent = `❌ 全局状态测试失败: ${error.message}`;
                log(`全局状态测试失败: ${error.message}`, 'error');
            }
        }

        // 更新全局状态显示
        function updateGlobalStateDisplay() {
            const currentProjectIdElement = document.getElementById('current-project-id');
            const currentProjectTitleElement = document.getElementById('current-project-title');
            const collabProjectIdElement = document.getElementById('collab-project-id');
            
            if (typeof currentProject !== 'undefined') {
                currentProjectIdElement.textContent = currentProject.id || '未设置';
                currentProjectTitleElement.textContent = currentProject.title || '未设置';
            }
            
            if (typeof collaborationManager !== 'undefined') {
                collabProjectIdElement.textContent = collaborationManager.currentProjectId || '未设置';
            } else {
                collabProjectIdElement.textContent = '协作管理器未初始化';
            }
        }

        // 模拟项目选择
        async function simulateProjectSelection() {
            log('模拟项目选择过程...', 'info');
            
            try {
                // 选择第一个模拟项目
                const testProject = mockProjects[0];
                log(`模拟选择项目: ${testProject.title}`, 'info');
                
                // 检查selectProject函数是否存在
                if (typeof selectProject !== 'function') {
                    throw new Error('selectProject函数不存在');
                }
                
                // 执行项目选择
                await selectProject(testProject);
                
                // 验证结果
                setTimeout(() => {
                    const titleElement = document.getElementById('dashboard-project-title');
                    const selectedText = document.getElementById('selected-project-inline');
                    
                    if (titleElement.textContent === testProject.title && 
                        selectedText.textContent === testProject.title) {
                        log('✅ 项目选择模拟成功，界面已同步更新', 'success');
                    } else {
                        log('❌ 项目选择模拟失败，界面未正确更新', 'error');
                    }
                    
                    updateGlobalStateDisplay();
                }, 1000);
                
            } catch (error) {
                log(`模拟项目选择失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成', 'info');
            
            // 等待主应用初始化
            setTimeout(() => {
                log('开始初始化测试环境...', 'info');
                updateGlobalStateDisplay();
                
                // 绑定项目选择器事件（如果还没有绑定）
                const projectBtn = document.getElementById('project-btn-inline');
                if (projectBtn && typeof toggleProjectDropdown === 'function') {
                    projectBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleProjectDropdown();
                    });
                    log('项目选择器事件已绑定', 'info');
                }
                
                log('测试环境初始化完成', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
