<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI完全重构测试 - 书籍智能编纂系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 2rem;
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .test-header h1 {
            color: #111827;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }
        
        .test-header p {
            color: #6b7280;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .comparison-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .comparison-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .before-title {
            color: #dc2626;
        }
        
        .after-title {
            color: #059669;
        }
        
        .demo-area {
            min-height: 400px;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .feature-list {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .feature-list h3 {
            color: #111827;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .feature-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            color: #374151;
        }
        
        .feature-list li i {
            color: #059669;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-magic"></i> UI完全重构测试</h1>
            <p>全面重新设计的下拉框和项目概览页面，解决所有视觉和交互问题</p>
        </div>
        
        <div class="comparison-grid">
            <!-- 新版本展示 -->
            <div class="comparison-section">
                <h2 class="comparison-title after-title">
                    <i class="fas fa-star"></i> 全新设计版本
                </h2>
                
                <div class="demo-area">
                    <!-- 项目概览卡片 -->
                    <div class="project-overview">
                        <div class="project-header">
                            <div class="project-title">
                                <h3>《大模型技术与油气应用概论》</h3>
                                <p>面向石油工程师的人工智能技术指南</p>
                            </div>
                            <div class="project-actions">
                                <div class="project-status">
                                    <div class="status-badge status-active">
                                        <i class="fas fa-play"></i>
                                        进行中
                                    </div>
                                </div>
                                <div class="project-tools">
                                    <button class="btn-icon" title="保存项目">
                                        <i class="fas fa-save"></i>
                                    </button>
                                    <button class="btn-icon" title="导出">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn-icon" title="刷新">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 新版下拉框 -->
                    <div class="project-selector-inline">
                        <button class="project-btn-inline" onclick="toggleNewDropdown()">
                            <div class="project-btn-content">
                                <i class="fas fa-folder project-btn-icon"></i>
                                <span class="project-btn-text">《大模型技术与油气应用概论》</span>
                            </div>
                            <i class="fas fa-chevron-down project-btn-arrow"></i>
                        </button>
                        <div class="project-list" id="new-dropdown">
                            <div class="project-list-content">
                                <div class="project-item create-new">
                                    <i class="fas fa-plus project-item-icon"></i>
                                    <span class="project-item-text">创建新项目</span>
                                </div>
                                <div class="project-divider"></div>
                                <div class="project-item">
                                    <i class="fas fa-book project-item-icon"></i>
                                    <div class="project-info">
                                        <div class="project-title">《大模型技术与油气应用概论》</div>
                                        <div class="project-role">主编</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                                <div class="project-item">
                                    <i class="fas fa-book project-item-icon"></i>
                                    <div class="project-info">
                                        <div class="project-title">《人工智能在石油勘探中的应用》</div>
                                        <div class="project-role">副编</div>
                                    </div>
                                    <div class="project-status suspended"></div>
                                </div>
                                <div class="project-item">
                                    <i class="fas fa-book project-item-icon"></i>
                                    <div class="project-info">
                                        <div class="project-title">《深度学习与地质建模》</div>
                                        <div class="project-role">参与者</div>
                                    </div>
                                    <div class="project-status archived"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="feature-list">
                    <h3><i class="fas fa-check-circle"></i> 重构亮点</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> 宽度增加到380-520px，完整显示项目名称</li>
                        <li><i class="fas fa-check"></i> 高对比度设计，文字清晰可读</li>
                        <li><i class="fas fa-check"></i> 移除所有绿色进度条，使用蓝色主题</li>
                        <li><i class="fas fa-check"></i> 现代化动画和交互效果</li>
                        <li><i class="fas fa-check"></i> 优化的间距和布局</li>
                        <li><i class="fas fa-check"></i> 清晰的视觉层次</li>
                    </ul>
                </div>
            </div>
            
            <!-- 问题说明 -->
            <div class="comparison-section">
                <h2 class="comparison-title before-title">
                    <i class="fas fa-exclamation-triangle"></i> 原版本问题
                </h2>
                
                <div class="demo-area">
                    <div style="padding: 2rem; background: #fef2f2; border-radius: 12px; border: 2px solid #fecaca;">
                        <h4 style="color: #dc2626; margin-bottom: 1rem;">
                            <i class="fas fa-times-circle"></i> 主要问题
                        </h4>
                        <ul style="color: #7f1d1d; line-height: 1.6;">
                            <li>• 下拉框宽度过窄（仅200px）</li>
                            <li>• 项目名称显示不完整</li>
                            <li>• 文字拥挤，间距不合理</li>
                            <li>• 奇怪的绿色进度条元素</li>
                            <li>• 颜色对比度差，看不清楚</li>
                            <li>• 整体视觉效果丑陋</li>
                            <li>• 缺乏现代化设计感</li>
                        </ul>
                    </div>
                    
                    <div style="padding: 2rem; background: #f0f9ff; border-radius: 12px; border: 2px solid #bae6fd;">
                        <h4 style="color: #0369a1; margin-bottom: 1rem;">
                            <i class="fas fa-lightbulb"></i> 解决方案
                        </h4>
                        <ul style="color: #0c4a6e; line-height: 1.6;">
                            <li>• 宽度增加90-160%</li>
                            <li>• 高对比度白色背景</li>
                            <li>• 统一蓝色主题色彩</li>
                            <li>• 现代化圆角和阴影</li>
                            <li>• 优化的字体和间距</li>
                            <li>• 流畅的动画效果</li>
                            <li>• 清晰的状态指示</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleNewDropdown() {
            const dropdown = document.getElementById('new-dropdown');
            const button = dropdown.previousElementSibling;
            
            dropdown.classList.toggle('show');
            button.classList.toggle('active');
        }
        
        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.project-selector-inline')) {
                document.querySelectorAll('.project-list').forEach(list => {
                    list.classList.remove('show');
                    if (list.previousElementSibling) {
                        list.previousElementSibling.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>
</html>
