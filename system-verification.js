// 系统验证和测试脚本
class SystemVerification {
    constructor() {
        this.testResults = [];
        this.verificationSteps = [];
        this.currentStep = 0;
    }

    // 运行完整的系统验证
    async runFullVerification() {
        console.log('🔍 开始系统验证...');
        
        this.verificationSteps = [
            { name: '数据库连接验证', test: this.verifyDatabaseConnection },
            { name: '用户认证系统验证', test: this.verifyAuthSystem },
            { name: '项目管理功能验证', test: this.verifyProjectManagement },
            { name: '用户管理功能验证', test: this.verifyUserManagement },
            { name: '权限控制系统验证', test: this.verifyPermissionSystem },
            { name: '协作功能验证', test: this.verifyCollaborationFeatures },
            { name: '数据安全验证', test: this.verifyDataSecurity },
            { name: '性能测试', test: this.verifyPerformance }
        ];

        for (const step of this.verificationSteps) {
            this.currentStep++;
            console.log(`\n📋 步骤 ${this.currentStep}/${this.verificationSteps.length}: ${step.name}`);
            
            try {
                const result = await step.test.call(this);
                this.addTestResult(step.name, result.success, result.details, result.error);
            } catch (error) {
                this.addTestResult(step.name, false, '测试执行失败', error.message);
            }
        }

        this.generateVerificationReport();
    }

    // 验证数据库连接
    async verifyDatabaseConnection() {
        try {
            // 检查Supabase连接
            const { data, error } = await supabaseManager.supabase
                .from('user_profiles')
                .select('count')
                .limit(1);

            if (error) throw error;

            // 检查关键表是否存在
            const tables = [
                'user_profiles', 'projects', 'project_members', 
                'chapters', 'chapter_assignments', 'user_invitations',
                'review_processes', 'comments', 'notifications'
            ];

            const tableChecks = [];
            for (const table of tables) {
                try {
                    const { error } = await supabaseManager.supabase
                        .from(table)
                        .select('*')
                        .limit(1);
                    
                    tableChecks.push({ table, exists: !error });
                } catch (e) {
                    tableChecks.push({ table, exists: false });
                }
            }

            const missingTables = tableChecks.filter(check => !check.exists);
            
            return {
                success: missingTables.length === 0,
                details: `检查了 ${tables.length} 个表，${missingTables.length} 个缺失`,
                error: missingTables.length > 0 ? `缺失表: ${missingTables.map(t => t.table).join(', ')}` : null
            };

        } catch (error) {
            return {
                success: false,
                details: '数据库连接失败',
                error: error.message
            };
        }
    }

    // 验证用户认证系统
    async verifyAuthSystem() {
        try {
            // 检查当前用户状态
            const user = await supabaseManager.getCurrentUser();
            
            if (!user) {
                return {
                    success: false,
                    details: '用户未登录',
                    error: '需要登录才能进行验证'
                };
            }

            // 检查用户配置
            const profile = await supabaseManager.loadUserProfile();
            
            return {
                success: !!profile,
                details: `用户: ${profile?.full_name || user.email}`,
                error: !profile ? '用户配置加载失败' : null
            };

        } catch (error) {
            return {
                success: false,
                details: '认证系统验证失败',
                error: error.message
            };
        }
    }

    // 验证项目管理功能
    async verifyProjectManagement() {
        try {
            // 检查项目列表加载
            const { data: projects, error } = await supabaseManager.supabase
                .from('projects')
                .select('*')
                .limit(5);

            if (error) throw error;

            // 检查项目成员关系
            const { data: members, error: memberError } = await supabaseManager.supabase
                .from('project_members')
                .select('*')
                .limit(5);

            if (memberError) throw memberError;

            return {
                success: true,
                details: `找到 ${projects.length} 个项目，${members.length} 个成员关系`,
                error: null
            };

        } catch (error) {
            return {
                success: false,
                details: '项目管理功能验证失败',
                error: error.message
            };
        }
    }

    // 验证用户管理功能
    async verifyUserManagement() {
        try {
            // 检查用户邀请表
            const { data: invitations, error: inviteError } = await supabaseManager.supabase
                .from('user_invitations')
                .select('*')
                .limit(5);

            if (inviteError) throw inviteError;

            // 检查用户配置表
            const { data: profiles, error: profileError } = await supabaseManager.supabase
                .from('user_profiles')
                .select('*')
                .limit(5);

            if (profileError) throw profileError;

            return {
                success: true,
                details: `${profiles.length} 个用户配置，${invitations.length} 个邀请记录`,
                error: null
            };

        } catch (error) {
            return {
                success: false,
                details: '用户管理功能验证失败',
                error: error.message
            };
        }
    }

    // 验证权限控制系统
    async verifyPermissionSystem() {
        try {
            const user = await supabaseManager.getCurrentUser();
            if (!user) throw new Error('用户未登录');

            // 检查RLS策略是否生效
            const { data: userProfiles, error: profileError } = await supabaseManager.supabase
                .from('user_profiles')
                .select('*');

            // 用户应该只能看到自己的配置或有权限的配置
            const canSeeOtherProfiles = userProfiles && userProfiles.length > 1;

            // 检查项目成员权限
            const { data: members, error: memberError } = await supabaseManager.supabase
                .from('project_members')
                .select('*');

            return {
                success: !profileError && !memberError,
                details: `权限检查完成，可访问 ${userProfiles?.length || 0} 个用户配置`,
                error: profileError?.message || memberError?.message || null
            };

        } catch (error) {
            return {
                success: false,
                details: '权限控制系统验证失败',
                error: error.message
            };
        }
    }

    // 验证协作功能
    async verifyCollaborationFeatures() {
        try {
            // 检查章节分配表
            const { data: assignments, error: assignError } = await supabaseManager.supabase
                .from('chapter_assignments')
                .select('*')
                .limit(5);

            if (assignError) throw assignError;

            // 检查审核流程表
            const { data: reviews, error: reviewError } = await supabaseManager.supabase
                .from('review_processes')
                .select('*')
                .limit(5);

            if (reviewError) throw reviewError;

            // 检查评论系统
            const { data: comments, error: commentError } = await supabaseManager.supabase
                .from('comments')
                .select('*')
                .limit(5);

            if (commentError) throw commentError;

            return {
                success: true,
                details: `${assignments.length} 个分配，${reviews.length} 个审核，${comments.length} 个评论`,
                error: null
            };

        } catch (error) {
            return {
                success: false,
                details: '协作功能验证失败',
                error: error.message
            };
        }
    }

    // 验证数据安全
    async verifyDataSecurity() {
        try {
            // 检查是否能访问不应该访问的数据
            const user = await supabaseManager.getCurrentUser();
            if (!user) throw new Error('用户未登录');

            // 尝试访问其他用户的私有数据（应该被RLS阻止）
            const { data: otherUserData, error } = await supabaseManager.supabase
                .from('user_profiles')
                .select('*')
                .neq('id', user.id);

            // 检查通知权限
            const { data: notifications, error: notifError } = await supabaseManager.supabase
                .from('notifications')
                .select('*')
                .limit(5);

            return {
                success: true,
                details: `数据安全检查完成，访问了 ${notifications?.length || 0} 个通知`,
                error: null
            };

        } catch (error) {
            return {
                success: false,
                details: '数据安全验证失败',
                error: error.message
            };
        }
    }

    // 验证性能
    async verifyPerformance() {
        try {
            const startTime = Date.now();

            // 测试数据库查询性能
            const { data: projects, error } = await supabaseManager.supabase
                .from('projects')
                .select(`
                    *,
                    project_members (
                        *,
                        user_profiles (*)
                    )
                `)
                .limit(10);

            const queryTime = Date.now() - startTime;

            if (error) throw error;

            // 测试页面加载性能
            const pageLoadTime = performance.now();

            return {
                success: queryTime < 2000, // 查询应该在2秒内完成
                details: `查询时间: ${queryTime}ms，页面加载: ${pageLoadTime.toFixed(2)}ms`,
                error: queryTime >= 2000 ? '查询性能较慢' : null
            };

        } catch (error) {
            return {
                success: false,
                details: '性能测试失败',
                error: error.message
            };
        }
    }

    // 添加测试结果
    addTestResult(testName, passed, details, error = null) {
        this.testResults.push({
            name: testName,
            passed: passed,
            details: details,
            error: error,
            timestamp: new Date().toISOString()
        });
        
        const status = passed ? '✅' : '❌';
        const errorMsg = error ? ` (${error})` : '';
        console.log(`${status} ${testName}: ${details}${errorMsg}`);
    }

    // 生成验证报告
    generateVerificationReport() {
        console.log('\n📊 系统验证报告');
        console.log('='.repeat(50));
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const successRate = ((passedTests / totalTests) * 100).toFixed(2);
        
        console.log(`\n🎯 验证总结:`);
        console.log(`总验证项: ${totalTests}`);
        console.log(`通过: ${passedTests}`);
        console.log(`失败: ${failedTests}`);
        console.log(`成功率: ${successRate}%`);
        
        console.log(`\n📋 详细结果:`);
        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${result.name}`);
            console.log(`   详情: ${result.details}`);
            if (result.error) {
                console.log(`   错误: ${result.error}`);
            }
        });
        
        // 生成建议
        this.generateRecommendations();
        
        // 保存报告
        const report = {
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                successRate: successRate
            },
            results: this.testResults,
            timestamp: new Date().toISOString(),
            recommendations: this.getRecommendations()
        };
        
        localStorage.setItem('systemVerificationReport', JSON.stringify(report));
        console.log(`\n💾 验证报告已保存到本地存储`);
        
        return report;
    }

    // 生成建议
    generateRecommendations() {
        console.log(`\n💡 改进建议:`);
        
        const failedTests = this.testResults.filter(r => !r.passed);
        
        if (failedTests.length === 0) {
            console.log('🎉 所有验证项都通过了！系统运行良好。');
        } else {
            failedTests.forEach(test => {
                console.log(`- ${test.name}: ${test.error || '需要修复'}`);
            });
        }
        
        // 通用建议
        console.log('\n📝 通用建议:');
        console.log('- 定期运行系统验证确保功能正常');
        console.log('- 监控数据库性能和查询效率');
        console.log('- 定期备份重要数据');
        console.log('- 保持系统和依赖项更新');
    }

    // 获取建议列表
    getRecommendations() {
        const recommendations = [];
        const failedTests = this.testResults.filter(r => !r.passed);
        
        failedTests.forEach(test => {
            recommendations.push({
                category: test.name,
                issue: test.error || '验证失败',
                suggestion: this.getSuggestionForTest(test.name)
            });
        });
        
        return recommendations;
    }

    // 获取特定测试的建议
    getSuggestionForTest(testName) {
        const suggestions = {
            '数据库连接验证': '检查Supabase配置和网络连接',
            '用户认证系统验证': '确认用户已登录且配置正确',
            '项目管理功能验证': '检查项目数据和权限设置',
            '用户管理功能验证': '验证用户表结构和数据完整性',
            '权限控制系统验证': '检查RLS策略和权限配置',
            '协作功能验证': '确认协作相关表和功能正常',
            '数据安全验证': '检查数据访问权限和安全策略',
            '性能测试': '优化数据库查询和页面加载性能'
        };
        
        return suggestions[testName] || '请检查相关配置和实现';
    }
}

// 导出验证类
window.SystemVerification = SystemVerification;

// 提供快速验证函数
window.runSystemVerification = async function() {
    const verification = new SystemVerification();
    const report = await verification.runFullVerification();
    return report;
};

console.log('🔧 系统验证脚本已加载');
console.log('💡 使用 runSystemVerification() 开始验证');
