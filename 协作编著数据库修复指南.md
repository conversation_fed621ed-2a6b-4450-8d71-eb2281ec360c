# 协作编著功能数据库修复指南

## 问题描述

当前协作编著功能遇到以下数据库结构问题：

1. **字段缺失**：`chapters.order_index` 字段不存在
2. **表结构不完整**：缺少完整的章节分配相关表
3. **数据约束问题**：章节分配时 `chapter_id` 为空导致创建失败

## 解决方案

### 步骤1：执行数据库修复脚本

在 Supabase 控制台的 SQL Editor 中执行以下文件：

```sql
-- 执行 fix-collaboration-database-schema.sql
```

这个脚本将：
- 为 `chapters` 表添加 `order_index` 字段
- 创建完整的 `chapter_assignments` 表
- 创建 `chapter_collaborators` 表
- 设置适当的索引和 RLS 策略
- 创建辅助视图和函数

### 步骤2：验证表结构

执行以下查询验证表结构是否正确：

```sql
-- 检查 chapters 表结构
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'chapters' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 检查 chapter_assignments 表是否存在
SELECT COUNT(*) as assignment_table_exists 
FROM information_schema.tables 
WHERE table_name = 'chapter_assignments' 
AND table_schema = 'public';

-- 检查 chapter_collaborators 表是否存在
SELECT COUNT(*) as collaborators_table_exists 
FROM information_schema.tables 
WHERE table_name = 'chapter_collaborators' 
AND table_schema = 'public';
```

### 步骤3：为现有项目创建默认分配

如果你有现有的项目和章节，可以使用以下函数为它们创建默认的章节分配：

```sql
-- 查看所有项目
SELECT id, title, owner_id FROM public.projects;

-- 为特定项目创建默认章节分配（替换 'your-project-id' 为实际的项目ID）
SELECT create_default_chapter_assignments('your-project-id');

-- 示例：为《大模型技术与油气应用概论》项目创建分配
SELECT create_default_chapter_assignments('7316988a-6c2d-4e5e-96c3-8c0ea8f87a79');
```

### 步骤4：验证数据

执行以下查询验证数据是否正确创建：

```sql
-- 查看章节分配统计
SELECT * FROM project_collaboration_stats;

-- 查看具体的章节分配
SELECT 
    title,
    status,
    lead_author_name,
    reviewer_name,
    due_date,
    priority
FROM chapter_assignments_detailed 
WHERE project_title LIKE '%大模型%'
ORDER BY created_at;

-- 检查章节的 order_index 是否已设置
SELECT id, title, order_index, created_at 
FROM public.chapters 
WHERE project_id = 'your-project-id'
ORDER BY order_index;
```

## 修复后的功能

执行修复后，协作编著功能将能够：

1. **正确加载章节数据**：不再出现 `order_index` 字段错误
2. **创建章节分配**：可以为章节分配主笔作者、协作者和审核者
3. **管理协作关系**：支持多人协作编写章节
4. **权限控制**：基于项目成员角色的访问控制
5. **进度跟踪**：查看章节分配状态和完成情况

## 注意事项

1. **备份数据**：执行脚本前建议备份重要数据
2. **权限检查**：确保当前用户有足够权限执行 DDL 操作
3. **RLS 策略**：新的 RLS 策略可能影响现有查询，请测试相关功能
4. **索引性能**：新增的索引将提高查询性能，但可能略微影响写入性能

## 故障排除

### 如果遇到权限错误：
```sql
-- 检查当前用户权限
SELECT current_user, session_user;

-- 如果需要，可以临时禁用 RLS（仅在开发环境）
ALTER TABLE public.chapter_assignments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_collaborators DISABLE ROW LEVEL SECURITY;
```

### 如果需要重置章节分配：
```sql
-- 清空现有分配（谨慎使用）
DELETE FROM public.chapter_collaborators;
DELETE FROM public.chapter_assignments;

-- 重新创建默认分配
SELECT create_default_chapter_assignments('your-project-id');
```

### 如果需要修改默认分配逻辑：
可以直接修改 `create_default_chapter_assignments` 函数，调整默认的优先级、截止日期等参数。

## 完成确认

修复完成后，协作编著页面应该能够：
- 正常加载而不出现控制台错误
- 显示真实的项目数据和团队成员
- 成功创建和管理章节分配
- 正确显示权限矩阵和协作状态

如果仍有问题，请检查浏览器控制台的具体错误信息，并根据错误信息进一步调试。
