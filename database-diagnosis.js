// 数据库诊断脚本
// 用于检查章节分配功能的数据库状态

class DatabaseDiagnosis {
    constructor() {
        this.results = [];
    }

    async runDiagnosis() {
        console.log('🔍 开始数据库诊断...');
        
        try {
            // 1. 检查用户认证状态
            await this.checkUserAuth();
            
            // 2. 检查项目数据
            await this.checkProjects();
            
            // 3. 检查章节数据
            await this.checkChapters();
            
            // 4. 检查章节分配数据
            await this.checkChapterAssignments();
            
            // 5. 检查用户配置数据
            await this.checkUserProfiles();
            
            // 6. 生成报告
            this.generateReport();
            
        } catch (error) {
            console.error('诊断过程中出现错误:', error);
            this.results.push({
                category: 'ERROR',
                message: `诊断失败: ${error.message}`,
                status: 'CRITICAL'
            });
        }
    }

    async checkUserAuth() {
        try {
            const user = await supabaseManager.getCurrentUser();
            if (user) {
                this.results.push({
                    category: '用户认证',
                    message: `用户已登录: ${user.email}`,
                    status: 'OK',
                    data: { userId: user.id, email: user.email }
                });
            } else {
                this.results.push({
                    category: '用户认证',
                    message: '用户未登录',
                    status: 'ERROR'
                });
            }
        } catch (error) {
            this.results.push({
                category: '用户认证',
                message: `认证检查失败: ${error.message}`,
                status: 'ERROR'
            });
        }
    }

    async checkProjects() {
        try {
            const { data: projects, error } = await supabaseManager.supabase
                .from('projects')
                .select('id, title, status, owner_id')
                .limit(10);

            if (error) throw error;

            this.results.push({
                category: '项目数据',
                message: `找到 ${projects?.length || 0} 个项目`,
                status: projects?.length > 0 ? 'OK' : 'WARNING',
                data: projects
            });
        } catch (error) {
            this.results.push({
                category: '项目数据',
                message: `项目查询失败: ${error.message}`,
                status: 'ERROR'
            });
        }
    }

    async checkChapters() {
        try {
            const { data: chapters, error } = await supabaseManager.supabase
                .from('chapters')
                .select('id, title, project_id, status')
                .limit(10);

            if (error) throw error;

            this.results.push({
                category: '章节数据',
                message: `找到 ${chapters?.length || 0} 个章节`,
                status: chapters?.length > 0 ? 'OK' : 'WARNING',
                data: chapters
            });
        } catch (error) {
            this.results.push({
                category: '章节数据',
                message: `章节查询失败: ${error.message}`,
                status: 'ERROR'
            });
        }
    }

    async checkChapterAssignments() {
        try {
            const { data: assignments, error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .select(`
                    id,
                    chapter_id,
                    user_id,
                    role,
                    status,
                    chapters (title),
                    user_profiles (full_name, email)
                `)
                .limit(10);

            if (error) throw error;

            this.results.push({
                category: '章节分配',
                message: `找到 ${assignments?.length || 0} 个章节分配`,
                status: assignments?.length > 0 ? 'OK' : 'WARNING',
                data: assignments
            });
        } catch (error) {
            this.results.push({
                category: '章节分配',
                message: `章节分配查询失败: ${error.message}`,
                status: 'ERROR'
            });
        }
    }

    async checkUserProfiles() {
        try {
            const { data: profiles, error } = await supabaseManager.supabase
                .from('user_profiles')
                .select('id, full_name, email')
                .limit(10);

            if (error) throw error;

            this.results.push({
                category: '用户配置',
                message: `找到 ${profiles?.length || 0} 个用户配置`,
                status: profiles?.length > 0 ? 'OK' : 'WARNING',
                data: profiles
            });
        } catch (error) {
            this.results.push({
                category: '用户配置',
                message: `用户配置查询失败: ${error.message}`,
                status: 'ERROR'
            });
        }
    }

    generateReport() {
        console.log('\n📊 数据库诊断报告');
        console.log('='.repeat(50));
        
        this.results.forEach(result => {
            const statusIcon = {
                'OK': '✅',
                'WARNING': '⚠️',
                'ERROR': '❌',
                'CRITICAL': '🚨'
            }[result.status] || '❓';
            
            console.log(`${statusIcon} ${result.category}: ${result.message}`);
            
            if (result.data && result.status === 'OK') {
                const sampleData = Array.isArray(result.data) ? result.data.slice(0, 2) : result.data;
                console.log(`   数据样本:`, sampleData);
            }
        });
        
        console.log('='.repeat(50));
        
        // 生成建议
        this.generateRecommendations();
    }

    generateRecommendations() {
        const errors = this.results.filter(r => r.status === 'ERROR' || r.status === 'CRITICAL');
        const warnings = this.results.filter(r => r.status === 'WARNING');
        
        console.log('\n💡 建议操作:');
        
        if (errors.length > 0) {
            console.log('🔴 严重问题需要立即解决:');
            errors.forEach(error => {
                console.log(`   - ${error.message}`);
            });
        }
        
        if (warnings.length > 0) {
            console.log('🟡 需要注意的问题:');
            warnings.forEach(warning => {
                console.log(`   - ${warning.message}`);
            });
        }
        
        if (errors.length === 0 && warnings.length === 0) {
            console.log('✅ 数据库状态良好！');
        }
    }
}

// 导出诊断类
window.DatabaseDiagnosis = DatabaseDiagnosis;

// 提供快速诊断函数
window.runDatabaseDiagnosis = async function() {
    const diagnosis = new DatabaseDiagnosis();
    await diagnosis.runDiagnosis();
    return diagnosis.results;
};
