// 项目导出服务
class ExportService {
    constructor() {
        this.supportedFormats = ['json', 'pdf', 'docx', 'html'];
    }

    // 导出项目
    async exportProject(projectId, format) {
        if (!this.supportedFormats.includes(format)) {
            throw new Error(`不支持的导出格式: ${format}`);
        }

        try {
            // 获取完整的项目数据
            const projectData = await this.getCompleteProjectData(projectId);
            
            switch (format) {
                case 'json':
                    return await this.exportToJSON(projectData);
                case 'pdf':
                    return await this.exportToPDF(projectData);
                case 'docx':
                    return await this.exportToDOCX(projectData);
                case 'html':
                    return await this.exportToHTML(projectData);
                default:
                    throw new Error(`未实现的导出格式: ${format}`);
            }
        } catch (error) {
            console.error('导出失败:', error);
            throw error;
        }
    }

    // 获取完整的项目数据
    async getCompleteProjectData(projectId) {
        try {
            // 获取项目基本信息
            const { data: project, error: projectError } = await supabaseManager.supabase
                .from('projects')
                .select('*')
                .eq('id', projectId)
                .single();

            if (projectError) throw projectError;

            // 获取项目大纲
            const { data: outlines, error: outlineError } = await supabaseManager.supabase
                .from('outlines')
                .select('*')
                .eq('project_id', projectId)
                .order('sort_order');

            if (outlineError) throw outlineError;

            // 获取章节内容
            const { data: chapters, error: chapterError } = await supabaseManager.supabase
                .from('chapters')
                .select('*')
                .eq('project_id', projectId);

            if (chapterError) throw chapterError;

            // 获取参考文献
            const { data: references, error: refError } = await supabaseManager.supabase
                .from('references')
                .select('*')
                .eq('project_id', projectId);

            if (refError) throw refError;

            // 获取项目成员
            const { data: members, error: memberError } = await supabaseManager.supabase
                .from('project_members')
                .select(`
                    *,
                    user_profiles(*)
                `)
                .eq('project_id', projectId);

            if (memberError) throw memberError;

            return {
                project,
                outlines: this.buildOutlineTree(outlines),
                chapters,
                references: references || [],
                members: members || [],
                exportDate: new Date().toISOString(),
                exportVersion: '1.0'
            };
        } catch (error) {
            console.error('获取项目数据失败:', error);
            throw error;
        }
    }

    // 构建大纲树结构
    buildOutlineTree(flatData) {
        const tree = [];
        const map = {};

        // 创建映射
        flatData.forEach(item => {
            map[item.id] = { ...item, children: [] };
        });

        // 构建树结构
        flatData.forEach(item => {
            if (item.parent_id && map[item.parent_id]) {
                map[item.parent_id].children.push(map[item.id]);
            } else {
                tree.push(map[item.id]);
            }
        });

        return tree;
    }

    // 导出为JSON格式
    async exportToJSON(projectData) {
        const jsonData = JSON.stringify(projectData, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        
        this.downloadFile(blob, `${projectData.project.title}_${this.getDateString()}.json`);
        
        return {
            success: true,
            format: 'json',
            size: blob.size
        };
    }

    // 导出为PDF格式（使用浏览器打印接口）
    async exportToPDF(projectData) {
        try {
            console.log('开始PDF格式导出（浏览器打印方式）...');

            // 生成HTML内容，添加打印专用样式
            const htmlContent = await this.generatePrintableHTMLContent(projectData);

            // 在新窗口中打开HTML内容并触发打印
            this.openHTMLForPrint(htmlContent, projectData.project.title);

            return {
                success: true,
                format: 'pdf',
                fileName: `${projectData.project.title}_${this.getDateString()}.pdf`,
                note: '已在新窗口中打开，请使用浏览器的打印功能保存为PDF'
            };
        } catch (error) {
            console.error('PDF导出失败:', error);
            throw new Error('PDF导出失败: ' + error.message);
        }
    }





    // 导出为DOCX格式（使用HTML内容）
    async exportToDOCX(projectData) {
        try {
            console.log('开始DOCX格式导出（HTML内容）...');

            // 生成HTML内容
            const htmlContent = await this.generateHTMLContent(projectData);

            // 创建Blob并下载为DOCX文件
            const blob = new Blob([htmlContent], { type: 'text/html; charset=utf-8' });
            const fileName = `${projectData.project.title}_${this.getDateString()}.docx`;
            this.downloadFile(blob, fileName);

            return {
                success: true,
                format: 'docx',
                fileName,
                note: '已导出为DOCX格式（HTML内容，可用Word或浏览器打开）'
            };
        } catch (error) {
            console.error('DOCX导出失败:', error);
            throw new Error('DOCX导出失败: ' + error.message);
        }
    }



    // 导出为HTML格式
    async exportToHTML(projectData) {
        try {
            console.log('开始HTML格式导出...');

            // 生成HTML内容
            const htmlContent = await this.generateHTMLContent(projectData);

            // 创建Blob并下载为HTML文件
            const blob = new Blob([htmlContent], { type: 'text/html; charset=utf-8' });
            const fileName = `${projectData.project.title}_${this.getDateString()}.html`;
            this.downloadFile(blob, fileName);

            return {
                success: true,
                format: 'html',
                fileName,
                note: '已导出为HTML格式（标准网页格式，支持中文，可用浏览器打开）'
            };

        } catch (error) {
            console.error('HTML导出失败:', error);
            throw new Error('HTML导出失败: ' + error.message);
        }
    }

    // 生成统一的HTML内容（供PDF、DOCX、HTML格式使用）
    async generateHTMLContent(projectData) {
        let htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHTML(projectData.project.title)}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            line-height: 1.6;
            margin: 40px auto;
            max-width: 800px;
            color: #333;
            background-color: #fff;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 25px;
        }
        .project-info {
            background-color: #f8f9fa;
            padding: 25px;
            border-left: 5px solid #3498db;
            margin: 30px 0;
            border-radius: 5px;
        }
        .project-info p {
            margin: 10px 0;
        }
        .outline {
            margin-left: 20px;
            background-color: #fdfdfd;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
        }
        .outline p {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px dotted #ddd;
        }
        .chapter {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .chapter h3 {
            margin-top: 0;
            color: #2980b9;
        }
        .export-info {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        @media print {
            body {
                margin: 20px;
                max-width: none;
            }
            .project-info, .outline, .chapter {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <h1>${this.escapeHTML(projectData.project.title)}</h1>

    <div class="project-info">
        <p><strong>📝 项目描述：</strong>${this.escapeHTML(projectData.project.description || '无')}</p>
        <p><strong>📅 创建时间：</strong>${new Date(projectData.project.created_at).toLocaleDateString()}</p>
        <p><strong>📤 导出时间：</strong>${new Date(projectData.exportDate).toLocaleDateString()}</p>
        <p><strong>📊 项目状态：</strong>${this.escapeHTML(projectData.project.status || '未知')}</p>
    </div>

    <h2>📋 目录大纲</h2>
    <div class="outline">
        ${this.generateOutlineHTML(projectData.outlines)}
    </div>`;

        // 添加章节内容
        if (projectData.chapters && projectData.chapters.length > 0) {
            htmlContent += '<h2>📚 章节内容</h2>';
            projectData.chapters.forEach(chapter => {
                htmlContent += `
    <div class="chapter">
        <h3>${this.escapeHTML(chapter.title || '未命名章节')}</h3>
        ${chapter.summary ? `<p><strong>章节摘要：</strong>${this.escapeHTML(chapter.summary)}</p>` : ''}
        ${chapter.word_count ? `<p><strong>字数统计：</strong>${chapter.word_count} 字</p>` : ''}
    </div>`;
            });
        }

        // 添加参考文献
        if (projectData.references && projectData.references.length > 0) {
            htmlContent += '<h2>📖 参考文献</h2><div class="outline">';
            projectData.references.forEach((ref, index) => {
                htmlContent += `<p>[${index + 1}] ${this.escapeHTML(ref.title)} - ${this.escapeHTML(ref.authors)} (${ref.year})</p>`;
            });
            htmlContent += '</div>';
        }

        htmlContent += `
    <div class="export-info">
        <p>📄 本文档由专业著作智能编纂系统导出</p>
        <p>🕒 导出时间：${new Date().toLocaleString()}</p>
    </div>
</body>
</html>`;

        return htmlContent;
    }

    // 生成打印专用的HTML内容（供PDF导出使用）
    async generatePrintableHTMLContent(projectData) {
        let htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHTML(projectData.project.title)}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "SimHei", "Arial Unicode MS", sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20mm;
            color: #333;
            background-color: #fff;
            font-size: 12pt;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24pt;
            page-break-after: avoid;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            font-size: 18pt;
            page-break-after: avoid;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
            font-size: 14pt;
            page-break-after: avoid;
        }
        .project-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-left: 5px solid #3498db;
            margin: 20px 0;
            border-radius: 5px;
            page-break-inside: avoid;
        }
        .project-info p {
            margin: 8px 0;
        }
        .outline {
            margin-left: 15px;
            background-color: #fdfdfd;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            page-break-inside: avoid;
        }
        .outline p {
            margin: 6px 0;
            padding: 3px 0;
            border-bottom: 1px dotted #ddd;
        }
        .chapter {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #fafafa;
            page-break-inside: avoid;
        }
        .chapter h3 {
            margin-top: 0;
            color: #2980b9;
        }
        .export-info {
            text-align: center;
            color: #7f8c8d;
            font-size: 10pt;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            page-break-inside: avoid;
        }

        /* 打印专用样式 */
        @media print {
            body {
                margin: 0;
                padding: 15mm;
                font-size: 11pt;
            }
            h1 {
                font-size: 20pt;
                margin-bottom: 20px;
            }
            h2 {
                font-size: 16pt;
                margin-top: 20px;
            }
            h3 {
                font-size: 13pt;
            }
            .project-info, .outline, .chapter {
                break-inside: avoid;
                margin: 15px 0;
            }
            .no-print {
                display: none;
            }
        }

        /* 打印按钮样式 */
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: 1px solid #ddd;
        }
        .print-btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            font-family: inherit;
        }
        .print-btn:hover {
            background: #4338ca;
        }
        .print-instructions {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <button class="print-btn" onclick="window.print()">🖨️ 打印为PDF</button>
        <button class="print-btn" onclick="window.close()" style="background: #6b7280;">❌ 关闭</button>
        <div class="print-instructions">
            点击"打印为PDF"按钮，在打印对话框中选择"另存为PDF"或"Microsoft Print to PDF"
        </div>
    </div>

    <h1>${this.escapeHTML(projectData.project.title)}</h1>

    <div class="project-info">
        <p><strong>📝 项目描述：</strong>${this.escapeHTML(projectData.project.description || '无')}</p>
        <p><strong>📅 创建时间：</strong>${new Date(projectData.project.created_at).toLocaleDateString()}</p>
        <p><strong>📤 导出时间：</strong>${new Date(projectData.exportDate).toLocaleDateString()}</p>
        <p><strong>📊 项目状态：</strong>${this.escapeHTML(projectData.project.status || '未知')}</p>
    </div>

    <h2>📋 目录大纲</h2>
    <div class="outline">
        ${this.generateOutlineHTML(projectData.outlines)}
    </div>`;

        // 添加章节内容
        if (projectData.chapters && projectData.chapters.length > 0) {
            htmlContent += '<h2>📚 章节内容</h2>';
            projectData.chapters.forEach(chapter => {
                htmlContent += `
    <div class="chapter">
        <h3>${this.escapeHTML(chapter.title || '未命名章节')}</h3>
        ${chapter.summary ? `<p><strong>章节摘要：</strong>${this.escapeHTML(chapter.summary)}</p>` : ''}
        ${chapter.word_count ? `<p><strong>字数统计：</strong>${chapter.word_count} 字</p>` : ''}
    </div>`;
            });
        }

        // 添加参考文献
        if (projectData.references && projectData.references.length > 0) {
            htmlContent += '<h2>📖 参考文献</h2><div class="outline">';
            projectData.references.forEach((ref, index) => {
                htmlContent += `<p>[${index + 1}] ${this.escapeHTML(ref.title)} - ${this.escapeHTML(ref.authors)} (${ref.year})</p>`;
            });
            htmlContent += '</div>';
        }

        htmlContent += `
    <div class="export-info">
        <p>📄 本文档由专业著作智能编纂系统导出</p>
        <p>🕒 导出时间：${new Date().toLocaleString()}</p>
    </div>

    <script>
        // 自动聚焦到打印按钮
        window.addEventListener('load', () => {
            // 可选：自动打开打印对话框
            // setTimeout(() => window.print(), 1000);
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
</body>
</html>`;

        return htmlContent;
    }

    // 在新窗口中打开HTML内容并准备打印
    openHTMLForPrint(htmlContent, title) {
        // 创建新窗口
        const printWindow = window.open('', '_blank', 'width=1024,height=768,scrollbars=yes,resizable=yes');

        if (!printWindow) {
            throw new Error('无法打开新窗口，请检查浏览器的弹窗拦截设置');
        }

        // 写入HTML内容
        printWindow.document.write(htmlContent);
        printWindow.document.close();

        // 设置窗口标题
        printWindow.document.title = `${title} - 打印预览`;

        // 等待内容加载完成后聚焦窗口
        printWindow.addEventListener('load', () => {
            printWindow.focus();
        });

        console.log('PDF打印窗口已打开，请使用浏览器的打印功能保存为PDF');
    }

    // 转义HTML特殊字符
    escapeHTML(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .replace(/\n/g, '<br>');
    }

    // 生成大纲HTML
    generateOutlineHTML(outlines, level = 0) {
        let html = '';
        outlines.forEach(item => {
            const indent = '&nbsp;&nbsp;'.repeat(level * 2);
            html += `<p>${indent}${this.escapeHTML(item.title)}</p>`;
            if (item.children && item.children.length > 0) {
                html += this.generateOutlineHTML(item.children, level + 1);
            }
        });
        return html;
    }

    // 转义RTF特殊字符（保留用于兼容）
    escapeRTF(text) {
        if (!text) return '';
        return text
            .replace(/\\/g, '\\\\')
            .replace(/\{/g, '\\{')
            .replace(/\}/g, '\\}')
            .replace(/\n/g, '\\par ')
            .replace(/\r/g, '');
    }

    // 添加大纲到RTF（保留用于兼容）
    addOutlineToRTF(outlines, level = 0) {
        let rtfContent = '';
        outlines.forEach(item => {
            const indent = '\\tab '.repeat(level);
            rtfContent += `${indent}${this.escapeRTF(item.title)}\\par`;
            if (item.children && item.children.length > 0) {
                rtfContent += this.addOutlineToRTF(item.children, level + 1);
            }
        });
        return rtfContent;
    }







    // 下载文件
    downloadFile(blob, fileName) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    // 获取日期字符串
    getDateString() {
        const now = new Date();
        return now.toISOString().split('T')[0].replace(/-/g, '');
    }
}

// 创建全局导出服务实例
const exportService = new ExportService();
