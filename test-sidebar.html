<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏收缩测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 左侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-book"></i>
                    <span class="logo-text">书籍智能编撰系统</span>
                </div>
                <button class="toggle-btn" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <nav class="nav-menu">
                <div class="nav-item active">
                    <i class="fas fa-eye nav-icon"></i>
                    <span class="nav-text">项目概览</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-list nav-icon"></i>
                    <span class="nav-text">书籍目录</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-edit nav-icon"></i>
                    <span class="nav-text">章节编写</span>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-area">
                <h1>测试右侧工具栏收缩功能</h1>
                <p>点击右侧工具栏的收缩按钮测试收缩效果。</p>
                <p>收缩后应该只显示工具图标，宽度为60px。</p>
            </div>

            <!-- 右侧工具栏 -->
            <div class="right-toolbar" id="rightToolbar">
                <!-- 工具栏头部 -->
                <div class="right-toolbar-header">
                    <div class="toolbar-title">
                        <i class="fas fa-tools toolbar-icon"></i>
                        <span class="toolbar-title-text">工具栏</span>
                    </div>
                    <button class="right-toggle-btn" onclick="toggleRightToolbar()">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <!-- 工具选择器 -->
                <div class="tool-selector">
                    <div class="tool-select-wrapper">
                        <select class="tool-select">
                            <option value="">选择工具</option>
                            <option value="pdf">PDF预览</option>
                            <option value="outline">大纲管理</option>
                            <option value="reference">参考资料</option>
                        </select>
                    </div>
                </div>

                <!-- 工具信息 -->
                <div class="tool-info">
                    选择一个工具开始使用
                </div>

                <!-- iframe容器 -->
                <div class="tool-iframe-container">
                    <div style="padding: 2rem; text-align: center; color: #64748b;">
                        <i class="fas fa-tools" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <p>工具内容区域</p>
                    </div>
                </div>

                <!-- 收缩状态的工具图标 -->
                <div class="collapsed-tools">
                    <div class="collapsed-tool-item active" title="PDF预览">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="collapsed-tool-item" title="大纲管理">
                        <i class="fas fa-list-ul"></i>
                    </div>
                    <div class="collapsed-tool-item" title="参考资料">
                        <i class="fas fa-bookmark"></i>
                    </div>
                    <div class="collapsed-tool-item" title="设置">
                        <i class="fas fa-cog"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('collapsed');
        }

        function toggleRightToolbar() {
            const toolbar = document.getElementById('rightToolbar');
            const toggleBtn = toolbar.querySelector('.right-toggle-btn i');
            
            toolbar.classList.toggle('collapsed');
            
            if (toolbar.classList.contains('collapsed')) {
                toggleBtn.className = 'fas fa-chevron-left';
            } else {
                toggleBtn.className = 'fas fa-chevron-right';
            }
        }

        // 工具图标点击事件
        document.querySelectorAll('.collapsed-tool-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除其他活动状态
                document.querySelectorAll('.collapsed-tool-item').forEach(i => i.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
