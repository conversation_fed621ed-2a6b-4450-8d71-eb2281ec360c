-- 综合考虑"篇-章-节"层级结构的书籍目录排序修复脚本
-- 支持完整的书籍层级：前言 → 第X篇 → 第Y章 → Z.W节 → 附录

-- ========================================
-- 第一步：查看当前排序状况和层级结构
-- ========================================

-- 查看当前大纲的层级分布
SELECT 
    o.project_id,
    o.title,
    o.level,
    o.sort_order,
    CASE 
        WHEN o.title ILIKE '%篇%' THEN '篇'
        WHEN o.title ILIKE '%章%' THEN '章'
        WHEN o.title ILIKE '%节%' OR o.title ILIKE '%.%' THEN '节'
        WHEN o.title ILIKE '%前言%' OR o.title ILIKE '%序%' THEN '前言'
        WHEN o.title ILIKE '%附录%' THEN '附录'
        ELSE '其他'
    END as content_type,
    o.created_at
FROM public.outlines o
ORDER BY o.project_id, o.sort_order NULLS LAST, o.level, o.created_at;

-- ========================================
-- 第二步：修复大纲排序（outlines表）- 完整层级
-- ========================================

-- 1. 前言和序言 (0-9)
UPDATE public.outlines 
SET sort_order = 0
WHERE (title ILIKE '%前言%' OR title ILIKE '%序%' OR title ILIKE '%preface%')
AND (sort_order IS NULL OR sort_order != 0);

-- 2. 目录 (5)
UPDATE public.outlines 
SET sort_order = 5
WHERE (title ILIKE '%目录%' OR title ILIKE '%contents%')
AND (sort_order IS NULL OR sort_order != 5);

-- 3. 第0章/引言/概述 (10-19)
UPDATE public.outlines 
SET sort_order = 10
WHERE (title ILIKE '%第0章%' OR title ILIKE '%第零章%' OR title ILIKE '%概述%' OR title ILIKE '%引言%' OR title ILIKE '%导论%')
AND (sort_order IS NULL OR sort_order != 10);

-- 4. 第一篇 (1000-1999)
UPDATE public.outlines 
SET sort_order = 1000
WHERE (title ILIKE '%第一篇%' OR title ILIKE '%第1篇%')
AND (sort_order IS NULL OR sort_order != 1000);

-- 5. 第二篇 (2000-2999)
UPDATE public.outlines 
SET sort_order = 2000
WHERE (title ILIKE '%第二篇%' OR title ILIKE '%第2篇%')
AND (sort_order IS NULL OR sort_order != 2000);

-- 6. 第三篇 (3000-3999)
UPDATE public.outlines 
SET sort_order = 3000
WHERE (title ILIKE '%第三篇%' OR title ILIKE '%第3篇%')
AND (sort_order IS NULL OR sort_order != 3000);

-- 7. 第四篇 (4000-4999)
UPDATE public.outlines 
SET sort_order = 4000
WHERE (title ILIKE '%第四篇%' OR title ILIKE '%第4篇%')
AND (sort_order IS NULL OR sort_order != 4000);

-- 8. 第五篇 (5000-5999)
UPDATE public.outlines 
SET sort_order = 5000
WHERE (title ILIKE '%第五篇%' OR title ILIKE '%第5篇%')
AND (sort_order IS NULL OR sort_order != 5000);

-- 9. 第一章 (1100-1199，根据所属篇调整)
-- 如果属于第一篇
UPDATE public.outlines 
SET sort_order = 1100
WHERE (title ILIKE '%第一章%' OR title ILIKE '%第1章%')
AND (sort_order IS NULL OR sort_order < 1000 OR sort_order >= 2000);

-- 如果属于第二篇
UPDATE public.outlines 
SET sort_order = 2100
WHERE (title ILIKE '%第一章%' OR title ILIKE '%第1章%')
AND EXISTS (
    SELECT 1 FROM public.outlines o2 
    WHERE o2.project_id = public.outlines.project_id 
    AND o2.title ILIKE '%第二篇%'
    AND o2.sort_order = 2000
)
AND (sort_order IS NULL OR sort_order < 2000 OR sort_order >= 3000);

-- 如果属于第三篇
UPDATE public.outlines 
SET sort_order = 3100
WHERE (title ILIKE '%第一章%' OR title ILIKE '%第1章%')
AND EXISTS (
    SELECT 1 FROM public.outlines o2 
    WHERE o2.project_id = public.outlines.project_id 
    AND o2.title ILIKE '%第三篇%'
    AND o2.sort_order = 3000
)
AND (sort_order IS NULL OR sort_order < 3000 OR sort_order >= 4000);

-- 10. 第二章
UPDATE public.outlines 
SET sort_order = 1200
WHERE (title ILIKE '%第二章%' OR title ILIKE '%第2章%')
AND (sort_order IS NULL OR sort_order < 1000 OR sort_order >= 2000);

UPDATE public.outlines 
SET sort_order = 2200
WHERE (title ILIKE '%第二章%' OR title ILIKE '%第2章%')
AND EXISTS (
    SELECT 1 FROM public.outlines o2 
    WHERE o2.project_id = public.outlines.project_id 
    AND o2.title ILIKE '%第二篇%'
)
AND (sort_order IS NULL OR sort_order < 2000 OR sort_order >= 3000);

UPDATE public.outlines 
SET sort_order = 3200
WHERE (title ILIKE '%第二章%' OR title ILIKE '%第2章%')
AND EXISTS (
    SELECT 1 FROM public.outlines o2 
    WHERE o2.project_id = public.outlines.project_id 
    AND o2.title ILIKE '%第三篇%'
)
AND (sort_order IS NULL OR sort_order < 3000 OR sort_order >= 4000);

-- 11. 第三章
UPDATE public.outlines 
SET sort_order = 1300
WHERE (title ILIKE '%第三章%' OR title ILIKE '%第3章%')
AND (sort_order IS NULL OR sort_order < 1000 OR sort_order >= 2000);

UPDATE public.outlines 
SET sort_order = 2300
WHERE (title ILIKE '%第三章%' OR title ILIKE '%第3章%')
AND EXISTS (
    SELECT 1 FROM public.outlines o2 
    WHERE o2.project_id = public.outlines.project_id 
    AND o2.title ILIKE '%第二篇%'
)
AND (sort_order IS NULL OR sort_order < 2000 OR sort_order >= 3000);

UPDATE public.outlines 
SET sort_order = 3300
WHERE (title ILIKE '%第三章%' OR title ILIKE '%第3章%')
AND EXISTS (
    SELECT 1 FROM public.outlines o2 
    WHERE o2.project_id = public.outlines.project_id 
    AND o2.title ILIKE '%第三篇%'
)
AND (sort_order IS NULL OR sort_order < 3000 OR sort_order >= 4000);

-- 12. 第四章及以后的章节（类似模式，这里简化处理）
UPDATE public.outlines 
SET sort_order = 1400 + (
    CASE 
        WHEN title ILIKE '%第四章%' OR title ILIKE '%第4章%' THEN 0
        WHEN title ILIKE '%第五章%' OR title ILIKE '%第5章%' THEN 100
        WHEN title ILIKE '%第六章%' OR title ILIKE '%第6章%' THEN 200
        WHEN title ILIKE '%第七章%' OR title ILIKE '%第7章%' THEN 300
        WHEN title ILIKE '%第八章%' OR title ILIKE '%第8章%' THEN 400
        WHEN title ILIKE '%第九章%' OR title ILIKE '%第9章%' THEN 500
        WHEN title ILIKE '%第十章%' OR title ILIKE '%第10章%' THEN 600
        ELSE 0
    END
)
WHERE (title ILIKE '%第四章%' OR title ILIKE '%第4章%' OR 
       title ILIKE '%第五章%' OR title ILIKE '%第5章%' OR
       title ILIKE '%第六章%' OR title ILIKE '%第6章%' OR
       title ILIKE '%第七章%' OR title ILIKE '%第7章%' OR
       title ILIKE '%第八章%' OR title ILIKE '%第8章%' OR
       title ILIKE '%第九章%' OR title ILIKE '%第9章%' OR
       title ILIKE '%第十章%' OR title ILIKE '%第10章%')
AND (sort_order IS NULL OR sort_order < 1000 OR sort_order >= 2000);

-- 13. 小节排序（X.Y格式）
-- 1.1, 1.2, 1.3 等小节
UPDATE public.outlines 
SET sort_order = 1110 + (
    CASE 
        WHEN title ILIKE '%1.1%' THEN 1
        WHEN title ILIKE '%1.2%' THEN 2
        WHEN title ILIKE '%1.3%' THEN 3
        WHEN title ILIKE '%1.4%' THEN 4
        WHEN title ILIKE '%1.5%' THEN 5
        ELSE 0
    END
)
WHERE (title ILIKE '%1.1%' OR title ILIKE '%1.2%' OR title ILIKE '%1.3%' OR 
       title ILIKE '%1.4%' OR title ILIKE '%1.5%')
AND (sort_order IS NULL OR sort_order < 1110 OR sort_order > 1120);

-- 2.1, 2.2, 2.3 等小节
UPDATE public.outlines 
SET sort_order = 1210 + (
    CASE 
        WHEN title ILIKE '%2.1%' THEN 1
        WHEN title ILIKE '%2.2%' THEN 2
        WHEN title ILIKE '%2.3%' THEN 3
        WHEN title ILIKE '%2.4%' THEN 4
        WHEN title ILIKE '%2.5%' THEN 5
        ELSE 0
    END
)
WHERE (title ILIKE '%2.1%' OR title ILIKE '%2.2%' OR title ILIKE '%2.3%' OR 
       title ILIKE '%2.4%' OR title ILIKE '%2.5%')
AND (sort_order IS NULL OR sort_order < 1210 OR sort_order > 1220);

-- 14. 附录部分 (9000+)
UPDATE public.outlines 
SET sort_order = 9000 + (
    CASE 
        WHEN title ILIKE '%附录A%' OR title ILIKE '%附录a%' THEN 0
        WHEN title ILIKE '%附录B%' OR title ILIKE '%附录b%' THEN 10
        WHEN title ILIKE '%附录C%' OR title ILIKE '%附录c%' THEN 20
        WHEN title ILIKE '%附录D%' OR title ILIKE '%附录d%' THEN 30
        ELSE 0
    END
)
WHERE (title ILIKE '%附录%' OR title ILIKE '%appendix%')
AND (sort_order IS NULL OR sort_order != 9000);

-- 15. 参考文献
UPDATE public.outlines 
SET sort_order = 9100
WHERE (title ILIKE '%参考文献%' OR title ILIKE '%references%')
AND (sort_order IS NULL OR sort_order != 9100);

-- 16. 索引
UPDATE public.outlines 
SET sort_order = 9200
WHERE (title ILIKE '%索引%' OR title ILIKE '%index%')
AND (sort_order IS NULL OR sort_order != 9200);

-- 17. 后记和致谢
UPDATE public.outlines 
SET sort_order = 9300
WHERE (title ILIKE '%后记%' OR title ILIKE '%致谢%' OR title ILIKE '%acknowledgments%')
AND (sort_order IS NULL OR sort_order != 9300);

-- ========================================
-- 第三步：同样修复章节排序（chapters表）
-- ========================================

-- 应用相同的排序逻辑到chapters表
-- （这里简化，实际可以复制上面的逻辑，将sort_order改为order_index）

-- 前言
UPDATE public.chapters SET order_index = 0
WHERE (title ILIKE '%前言%' OR title ILIKE '%序%') AND (order_index IS NULL OR order_index != 0);

-- 篇级别
UPDATE public.chapters SET order_index = 1000 WHERE title ILIKE '%第一篇%' OR title ILIKE '%第1篇%';
UPDATE public.chapters SET order_index = 2000 WHERE title ILIKE '%第二篇%' OR title ILIKE '%第2篇%';
UPDATE public.chapters SET order_index = 3000 WHERE title ILIKE '%第三篇%' OR title ILIKE '%第3篇%';
UPDATE public.chapters SET order_index = 4000 WHERE title ILIKE '%第四篇%' OR title ILIKE '%第4篇%';

-- 章级别（简化处理，实际应该根据篇来分配）
UPDATE public.chapters SET order_index = 1100 WHERE title ILIKE '%第一章%' OR title ILIKE '%第1章%';
UPDATE public.chapters SET order_index = 1200 WHERE title ILIKE '%第二章%' OR title ILIKE '%第2章%';
UPDATE public.chapters SET order_index = 1300 WHERE title ILIKE '%第三章%' OR title ILIKE '%第3章%';

-- 附录
UPDATE public.chapters SET order_index = 9000 WHERE title ILIKE '%附录%';
UPDATE public.chapters SET order_index = 9100 WHERE title ILIKE '%参考文献%';
UPDATE public.chapters SET order_index = 9200 WHERE title ILIKE '%索引%';

-- ========================================
-- 第四步：处理NULL值和默认排序
-- ========================================

-- 为没有排序值的大纲项设置默认值（根据层级）
UPDATE public.outlines 
SET sort_order = 
    CASE 
        WHEN level = 0 THEN 6000  -- 篇级别
        WHEN level = 1 THEN 6100  -- 章级别
        WHEN level = 2 THEN 6200  -- 节级别
        ELSE 6300 + (level * 100)
    END + (EXTRACT(EPOCH FROM created_at)::int % 50)
WHERE sort_order IS NULL;

-- 为没有排序值的章节设置默认值
UPDATE public.chapters 
SET order_index = 6000 + (EXTRACT(EPOCH FROM created_at)::int % 1000)
WHERE order_index IS NULL;

-- ========================================
-- 第五步：验证修复结果
-- ========================================

-- 查看修复后的完整层级结构
SELECT 
    o.project_id,
    o.title,
    o.level,
    o.sort_order,
    CASE 
        WHEN o.sort_order < 100 THEN '前言部分'
        WHEN o.sort_order >= 1000 AND o.sort_order < 2000 THEN '第一篇'
        WHEN o.sort_order >= 2000 AND o.sort_order < 3000 THEN '第二篇'
        WHEN o.sort_order >= 3000 AND o.sort_order < 4000 THEN '第三篇'
        WHEN o.sort_order >= 4000 AND o.sort_order < 5000 THEN '第四篇'
        WHEN o.sort_order >= 9000 THEN '附录部分'
        ELSE '其他'
    END as section_type,
    o.created_at
FROM public.outlines o
ORDER BY o.project_id, o.sort_order;

-- 检查层级一致性
SELECT 
    project_id,
    COUNT(*) as total_items,
    COUNT(CASE WHEN sort_order < 100 THEN 1 END) as preface_items,
    COUNT(CASE WHEN sort_order >= 1000 AND sort_order < 2000 THEN 1 END) as part1_items,
    COUNT(CASE WHEN sort_order >= 2000 AND sort_order < 3000 THEN 1 END) as part2_items,
    COUNT(CASE WHEN sort_order >= 3000 AND sort_order < 4000 THEN 1 END) as part3_items,
    COUNT(CASE WHEN sort_order >= 9000 THEN 1 END) as appendix_items
FROM public.outlines 
GROUP BY project_id
ORDER BY project_id;
