-- 分步清理空项目和重复项目
-- 请按顺序执行每个步骤，确认结果后再进行下一步

-- ========================================
-- 步骤1: 查看所有项目的基本信息
-- ========================================
SELECT 
    p.id,
    p.title,
    p.description,
    p.status,
    p.created_at,
    up.full_name as owner_name
FROM projects p
LEFT JOIN user_profiles up ON p.owner_id = up.id
ORDER BY p.created_at DESC;

-- ========================================
-- 步骤2: 查看每个项目的内容统计
-- ========================================
SELECT 
    p.id,
    p.title,
    p.created_at,
    (SELECT COUNT(*) FROM chapters WHERE project_id = p.id) as chapter_count,
    (SELECT COUNT(*) FROM outlines WHERE project_id = p.id) as outline_count,
    (SELECT COUNT(*) FROM chapter_assignments WHERE project_id = p.id) as assignment_count
FROM projects p
ORDER BY p.created_at DESC;

-- ========================================
-- 步骤3: 识别完全空的项目（没有任何内容）
-- ========================================
SELECT 
    p.id,
    p.title,
    p.created_at,
    '空项目-建议删除' as recommendation
FROM projects p
WHERE NOT EXISTS (SELECT 1 FROM chapters WHERE project_id = p.id)
  AND NOT EXISTS (SELECT 1 FROM outlines WHERE project_id = p.id)
  AND NOT EXISTS (SELECT 1 FROM chapter_assignments WHERE project_id = p.id)
ORDER BY p.created_at;

-- ========================================
-- 步骤4: 识别重复标题的项目
-- ========================================
SELECT 
    p1.id,
    p1.title,
    p1.created_at,
    p1.status,
    CASE 
        WHEN p1.created_at = (
            SELECT MAX(p2.created_at) 
            FROM projects p2 
            WHERE TRIM(LOWER(p2.title)) = TRIM(LOWER(p1.title))
        ) THEN '保留（最新）'
        ELSE '删除（重复）'
    END as recommendation
FROM projects p1
WHERE EXISTS (
    SELECT 1 
    FROM projects p2 
    WHERE p2.id != p1.id 
      AND TRIM(LOWER(p2.title)) = TRIM(LOWER(p1.title))
)
ORDER BY p1.title, p1.created_at DESC;

-- ========================================
-- 步骤5: 删除指定的空项目（请替换具体的项目ID）
-- ========================================
-- 示例：删除特定的空项目
-- DELETE FROM projects WHERE id = 'your-empty-project-id-here';

-- 或者删除所有空项目（谨慎使用）
/*
DELETE FROM projects 
WHERE id IN (
    SELECT p.id
    FROM projects p
    WHERE NOT EXISTS (SELECT 1 FROM chapters WHERE project_id = p.id)
      AND NOT EXISTS (SELECT 1 FROM outlines WHERE project_id = p.id)
      AND NOT EXISTS (SELECT 1 FROM chapter_assignments WHERE project_id = p.id)
);
*/

-- ========================================
-- 步骤6: 删除指定的重复项目（请替换具体的项目ID）
-- ========================================
-- 示例：删除特定的重复项目
-- DELETE FROM projects WHERE id = 'your-duplicate-project-id-here';

-- ========================================
-- 步骤7: 清理孤立的相关记录
-- ========================================
-- 清理孤立的项目成员记录
DELETE FROM project_members 
WHERE project_id NOT IN (SELECT id FROM projects);

-- 清理孤立的章节分配记录
DELETE FROM chapter_assignments 
WHERE project_id NOT IN (SELECT id FROM projects);

-- 清理孤立的章节记录
DELETE FROM chapters 
WHERE project_id NOT IN (SELECT id FROM projects);

-- 清理孤立的大纲记录
DELETE FROM outlines 
WHERE project_id NOT IN (SELECT id FROM projects);

-- ========================================
-- 步骤8: 验证清理结果
-- ========================================
SELECT 
    p.id,
    p.title,
    p.created_at,
    up.full_name as owner_name,
    (SELECT COUNT(*) FROM chapters WHERE project_id = p.id) as chapter_count,
    (SELECT COUNT(*) FROM outlines WHERE project_id = p.id) as outline_count,
    (SELECT COUNT(*) FROM chapter_assignments WHERE project_id = p.id) as assignment_count,
    (SELECT COUNT(*) FROM project_members WHERE project_id = p.id) as member_count
FROM projects p
LEFT JOIN user_profiles up ON p.owner_id = up.id
ORDER BY p.created_at DESC;

-- ========================================
-- 步骤9: 检查是否还有重复项目
-- ========================================
SELECT 
    title,
    COUNT(*) as count
FROM projects 
GROUP BY TRIM(LOWER(title))
HAVING COUNT(*) > 1
ORDER BY count DESC;
