-- 简化版数据库修复脚本
-- 修复多用户协作功能所需的数据库结构

-- 1. 更新用户配置表结构
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS global_role VARCHAR(20) DEFAULT 'user';

ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP WITH TIME ZONE;

-- 2. 更新项目成员表结构
ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS invited_by UUID;

ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active';

ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS invitation_token VARCHAR(255);

ALTER TABLE public.project_members 
ADD COLUMN IF NOT EXISTS invitation_expires_at TIMESTAMP WITH TIME ZONE;

-- 3. 创建章节分配表
CREATE TABLE IF NOT EXISTS public.chapter_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID,
    user_id UUID,
    role VARCHAR(20) NOT NULL,
    assigned_by UUID,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deadline TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'assigned',
    notes TEXT
);

-- 4. 创建审核流程表
CREATE TABLE IF NOT EXISTS public.review_processes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID,
    reviewer_id UUID,
    status VARCHAR(20) DEFAULT 'pending',
    review_notes TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建用户邀请表
CREATE TABLE IF NOT EXISTS public.user_invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    project_id UUID,
    invited_by UUID,
    role VARCHAR(20) NOT NULL,
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 更新评论系统表
CREATE TABLE IF NOT EXISTS public.comments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chapter_id UUID,
    parent_id UUID,
    user_id UUID,
    content TEXT NOT NULL,
    position_start INTEGER,
    position_end INTEGER,
    type VARCHAR(20) DEFAULT 'general',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 创建索引
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_chapter ON public.chapter_assignments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_user ON public.chapter_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_review_processes_chapter ON public.review_processes(chapter_id);
CREATE INDEX IF NOT EXISTS idx_review_processes_reviewer ON public.review_processes(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_email ON public.user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_user_invitations_token ON public.user_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_comments_chapter ON public.comments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_comments_user ON public.comments(user_id);

-- 8. 启用行级安全
ALTER TABLE public.chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_processes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- 9. 创建基本的RLS策略
-- 用户邀请表的RLS策略
DROP POLICY IF EXISTS "Users can manage invitations in their projects" ON public.user_invitations;
CREATE POLICY "Users can manage invitations in their projects" ON public.user_invitations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            WHERE pm.project_id = user_invitations.project_id 
            AND pm.user_id = auth.uid()
        )
    );

-- 章节分配表的RLS策略
DROP POLICY IF EXISTS "Users can view assignments in their projects" ON public.chapter_assignments;
CREATE POLICY "Users can view assignments in their projects" ON public.chapter_assignments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = chapter_assignments.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
        )
    );

-- 评论表的RLS策略
DROP POLICY IF EXISTS "Users can view comments in their projects" ON public.comments;
CREATE POLICY "Users can view comments in their projects" ON public.comments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            JOIN public.chapters c ON c.id = comments.chapter_id
            WHERE pm.project_id = c.project_id 
            AND pm.user_id = auth.uid()
        )
    );

-- 10. 确保项目所有者在project_members表中
INSERT INTO public.project_members (project_id, user_id, role, status, joined_at)
SELECT 
    p.id as project_id,
    p.owner_id as user_id,
    'owner' as role,
    'active' as status,
    p.created_at as joined_at
FROM public.projects p
WHERE NOT EXISTS (
    SELECT 1 FROM public.project_members pm 
    WHERE pm.project_id = p.id AND pm.user_id = p.owner_id
)
ON CONFLICT (project_id, user_id) DO NOTHING;

-- 11. 添加约束（如果不存在）
DO $$ 
BEGIN
    -- 检查并添加用户角色约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'user_profiles_global_role_check'
    ) THEN
        ALTER TABLE public.user_profiles 
        ADD CONSTRAINT user_profiles_global_role_check 
        CHECK (global_role IN ('system_admin', 'user'));
    END IF;

    -- 检查并添加项目成员状态约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'project_members_status_check'
    ) THEN
        ALTER TABLE public.project_members 
        ADD CONSTRAINT project_members_status_check 
        CHECK (status IN ('active', 'inactive', 'pending', 'invited'));
    END IF;

    -- 检查并添加邀请状态约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'user_invitations_status_check'
    ) THEN
        ALTER TABLE public.user_invitations 
        ADD CONSTRAINT user_invitations_status_check 
        CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled'));
    END IF;

    -- 检查并添加邀请角色约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'user_invitations_role_check'
    ) THEN
        ALTER TABLE public.user_invitations 
        ADD CONSTRAINT user_invitations_role_check 
        CHECK (role IN ('admin', 'editor', 'author', 'reviewer'));
    END IF;
END $$;

-- 完成提示
SELECT 'Database fix completed successfully!' as result;
