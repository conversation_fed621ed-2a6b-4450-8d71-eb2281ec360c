// 全局变量
let currentProject = {
    title: "《大模型技术与油气应用概论》",
    outline: [],
    chapters: {},
    references: [],
    diagrams: {},
    progress: {}
};

let quillEditor = null;
let currentChapter = null;
let projectOverviewInitialized = false;
let selectedProjectId = null;
let currentUser = null;
let currentUserRole = null;
let onlineUsers = new Map();
let collaborationInitialized = false;

// 检查用户认证和项目选择状态
async function checkAuthAndProject() {
    try {
        // 检查用户是否已登录
        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            // 用户未登录，跳转到登录页面
            window.location.href = 'auth.html';
            return false;
        }

        // 检查是否有选中的项目
        selectedProjectId = localStorage.getItem('selectedProjectId');
        if (!selectedProjectId) {
            // 没有选中项目，跳转到项目管理页面
            window.location.href = 'project-management.html';
            return false;
        }

        // 验证项目是否存在且用户有权限访问
        const projectValid = await validateProjectAccess(selectedProjectId);
        if (!projectValid) {
            // 项目无效，清除选择并跳转到项目管理页面
            localStorage.removeItem('selectedProjectId');
            window.location.href = 'project-management.html';
            return false;
        }

        // 显示侧边栏和加载项目信息
        await loadSelectedProject(selectedProjectId);

        // 初始化协作功能
        await initializeCollaboration(user, selectedProjectId);

        return true;

    } catch (error) {
        console.error('检查认证和项目状态失败:', error);
        // 出错时跳转到登录页面
        window.location.href = 'auth.html';
        return false;
    }
}

// 验证项目访问权限
async function validateProjectAccess(projectId) {
    try {
        const { data: project, error } = await supabaseManager.supabase
            .from('projects')
            .select('id, title, description')
            .eq('id', projectId)
            .single();

        if (error || !project) {
            console.error('项目不存在或无权限访问:', error);
            return false;
        }

        return true;
    } catch (error) {
        console.error('验证项目访问权限失败:', error);
        return false;
    }
}

// 加载选中的项目
async function loadSelectedProject(projectId) {
    try {
        // 获取项目信息
        const { data: project, error } = await supabaseManager.supabase
            .from('projects')
            .select('*')
            .eq('id', projectId)
            .single();

        if (error) throw error;

        // 更新当前项目信息
        currentProject.title = project.title;
        currentProject.description = project.description;

        // 显示侧边栏
        const sidebar = document.getElementById('main-sidebar');
        if (sidebar) {
            sidebar.style.display = 'block';
        }

        // 更新项目信息显示
        updateCurrentProjectDisplay(project);

        // 设置协作管理器的项目ID
        if (typeof collaborationManager !== 'undefined') {
            collaborationManager.currentProjectId = projectId;
        }

        console.log('✅ 项目加载成功:', project.title);

    } catch (error) {
        console.error('加载项目失败:', error);
        showNotification('加载项目失败: ' + error.message, 'error');
    }
}

// 更新当前项目显示
function updateCurrentProjectDisplay(project) {
    const titleElement = document.getElementById('current-project-title');
    const descriptionElement = document.getElementById('current-project-description');

    if (titleElement) {
        titleElement.textContent = project.title;
    }
    if (descriptionElement) {
        descriptionElement.textContent = project.description || '暂无描述';
    }

    // 更新页面标题
    document.title = `${project.title} - 专业著作智能编纂系统`;
}

// 返回项目管理页面
function goBackToProjectManagement() {
    // 清除当前项目选择
    localStorage.removeItem('selectedProjectId');
    // 跳转到项目管理页面
    window.location.href = 'project-management.html';
}

// 跳转到项目管理页面
function goToProjectManagement() {
    // 跳转到项目管理页面
    window.location.href = 'project-management.html';
}

// 加载用户的所有项目到下拉框
async function loadUserProjects() {
    try {
        if (!supabaseManager || !supabaseManager.supabase) {
            console.warn('Supabase未初始化，无法加载项目列表');
            return;
        }

        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            console.warn('用户未登录，无法加载项目列表');
            return;
        }

        // 使用统一的项目获取方法，获取用户参与的所有项目
        const projects = await supabaseManager.getUserProjects();

        console.log('加载用户项目列表:', projects);

        // 更新项目下拉框
        updateProjectDropdown(projects);

    } catch (error) {
        console.error('加载项目列表异常:', error);
    }
}

// 更新项目下拉框
function updateProjectDropdown(projects) {
    const projectList = document.getElementById('project-list-inline');
    const selectedProjectText = document.getElementById('selected-project-inline');
    const currentProjectId = localStorage.getItem('selectedProjectId');

    console.log('更新项目下拉框，项目数量:', projects ? projects.length : 0);
    console.log('当前选中项目ID:', currentProjectId);

    if (!projectList) {
        console.error('项目下拉列表元素未找到');
        return;
    }

    // 获取项目列表内容容器
    const projectListContent = projectList.querySelector('.project-list-content');
    if (!projectListContent) {
        console.error('项目列表内容容器未找到');
        return;
    }

    // 保留创建新项目按钮和分隔线
    const createNewBtn = projectListContent.querySelector('.project-item.create-new');
    const divider = projectListContent.querySelector('.project-divider');

    // 清空现有项目列表
    projectListContent.innerHTML = '';

    // 重新添加创建新项目按钮和分隔线
    if (createNewBtn) projectListContent.appendChild(createNewBtn);
    if (divider) projectListContent.appendChild(divider);

    // 添加项目列表
    if (projects && projects.length > 0) {
        console.log('添加项目到下拉列表:', projects);

        projects.forEach(project => {
            const projectItem = document.createElement('div');
            projectItem.className = 'project-item';

            // 如果是当前选中的项目，添加选中样式
            if (currentProjectId === project.id) {
                projectItem.classList.add('selected');
                console.log('标记选中项目:', project.title);
            }

            projectItem.onclick = () => selectProject(project);

            projectItem.innerHTML = `
                <i class="fas fa-folder project-item-icon"></i>
                <div class="project-item-content">
                    <span class="project-item-text">${project.title}</span>
                    <small class="project-item-desc">${project.description || ''}</small>
                </div>
            `;

            projectListContent.appendChild(projectItem);
        });

        // 更新当前选中项目的显示
        if (currentProjectId && selectedProjectText) {
            const currentProject = projects.find(p => p.id === currentProjectId);
            if (currentProject) {
                selectedProjectText.textContent = currentProject.title;
                console.log('更新选中项目显示:', currentProject.title);
            } else {
                // 如果找不到当前项目，重置显示
                selectedProjectText.textContent = '选择项目';
                localStorage.removeItem('selectedProjectId');
                console.log('当前项目未找到，重置显示');
            }
        }
    } else {
        // 没有项目时显示空状态
        const emptyState = document.createElement('div');
        emptyState.className = 'project-list-empty';
        emptyState.innerHTML = `
            <i class="fas fa-folder-open"></i>
            <p>暂无项目</p>
            <small>点击上方"创建新项目"开始</small>
        `;
        projectListContent.appendChild(emptyState);

        // 重置显示
        if (selectedProjectText) {
            selectedProjectText.textContent = '选择项目';
        }
        console.log('没有项目，显示空状态');
    }
}

// 选择项目
async function selectProject(project) {
    console.log('🔄 开始选择项目:', project.title);

    // 显示加载状态
    showProjectSwitchingIndicator(true);

    try {
        // 保存选中的项目ID
        localStorage.setItem('selectedProjectId', project.id);

        // 更新显示的项目名称
        const selectedProjectText = document.getElementById('selected-project-inline');
        if (selectedProjectText) {
            selectedProjectText.textContent = project.title;
        }

        // 关闭下拉框
        const projectList = document.getElementById('project-list-inline');
        const projectBtn = document.getElementById('project-btn-inline');
        if (projectList) {
            projectList.classList.remove('show');
            projectList.style.display = 'none';
        }
        if (projectBtn) {
            projectBtn.classList.remove('active');
        }

        // 移除外部点击监听器
        document.removeEventListener('click', closeProjectDropdownOnClickOutside);

        // 更新全局项目状态
        updateGlobalProjectState(project);

        // 加载项目数据
        await loadProjectData(project);

        // 显示成功通知
        showNotification(`已切换到项目：${project.title}`, 'success');
        console.log('✅ 项目切换成功:', project.title);

    } catch (error) {
        console.error('❌ 加载项目数据失败:', error);
        showNotification('项目切换失败: ' + error.message, 'error');
        // 如果加载失败，可以选择重新加载页面
        // window.location.reload();
    } finally {
        // 隐藏加载状态
        showProjectSwitchingIndicator(false);
    }
}

// 切换项目下拉框显示
function toggleProjectDropdown() {
    console.log('🔥 toggleProjectDropdown函数被调用了！');

    const projectList = document.getElementById('project-list-inline');
    const projectBtn = document.getElementById('project-btn-inline');

    if (!projectList) {
        console.error('项目下拉列表元素未找到');
        return;
    }

    console.log('切换项目下拉框，当前状态:', projectList.classList.contains('show'));

    if (!projectList.classList.contains('show')) {
        // 显示下拉框
        projectList.classList.add('show');
        projectList.style.display = 'block';
        if (projectBtn) projectBtn.classList.add('active');

        // 加载最新的项目列表
        loadUserProjects();

        // 添加点击外部关闭功能
        setTimeout(() => {
            document.addEventListener('click', closeProjectDropdownOnClickOutside);
        }, 0);

        console.log('项目下拉框已显示');
    } else {
        // 隐藏下拉框
        projectList.classList.remove('show');
        projectList.style.display = 'none';
        if (projectBtn) projectBtn.classList.remove('active');
        document.removeEventListener('click', closeProjectDropdownOnClickOutside);

        console.log('项目下拉框已隐藏');
    }
}

// 点击外部区域关闭项目下拉框
function closeProjectDropdownOnClickOutside(event) {
    const projectSelector = document.getElementById('project-selector-inline');
    const projectList = document.getElementById('project-list-inline');
    const projectBtn = document.getElementById('project-btn-inline');

    if (projectSelector && !projectSelector.contains(event.target)) {
        if (projectList) {
            projectList.classList.remove('show');
            projectList.style.display = 'none';
        }
        if (projectBtn) projectBtn.classList.remove('active');
        document.removeEventListener('click', closeProjectDropdownOnClickOutside);
    }
}

// 显示创建项目对话框
function showCreateProject() {
    // 跳转到项目管理页面的创建项目功能
    window.location.href = 'project-management.html?action=create';
}

// 加载当前选中的项目信息
async function loadCurrentProjectInfo() {
    try {
        const selectedProjectId = localStorage.getItem('selectedProjectId');
        if (!selectedProjectId) {
            console.log('没有选中的项目');
            return;
        }

        if (!supabaseManager || !supabaseManager.supabase) {
            console.warn('Supabase未初始化，无法加载项目信息');
            return;
        }

        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            console.warn('用户未登录，无法加载项目信息');
            return;
        }

        // 从数据库获取项目信息
        const { data: projectData, error } = await supabaseManager.supabase
            .from('projects')
            .select('*')
            .eq('id', selectedProjectId)
            .eq('owner_id', user.id)
            .single();

        if (error) {
            console.error('加载项目信息失败:', error);
            // 清除无效的项目ID
            localStorage.removeItem('selectedProjectId');
            return;
        }

        if (projectData) {
            // 更新项目选择器显示
            updateSelectedProjectDisplay(projectData);

            // 加载项目相关数据
            await loadProjectData(projectData);

            console.log('项目信息加载成功:', projectData.title);
        }

    } catch (error) {
        console.error('加载项目信息异常:', error);
    }
}

// 更新项目选择器显示
function updateSelectedProjectDisplay(project) {
    const selectedProjectText = document.getElementById('selected-project-inline');
    if (selectedProjectText && project) {
        selectedProjectText.textContent = project.title;
    }
}

// 更新全局项目状态
function updateGlobalProjectState(project) {
    console.log('🔄 更新全局项目状态:', project.title);

    // 更新全局currentProject变量
    if (typeof currentProject !== 'undefined') {
        currentProject.id = project.id;
        currentProject.title = project.title;
        currentProject.description = project.description;
        currentProject.status = project.status;
        currentProject.created_at = project.created_at;
        currentProject.updated_at = project.updated_at;
    }

    // 更新协作管理器的项目ID
    if (typeof collaborationManager !== 'undefined') {
        collaborationManager.currentProjectId = project.id;
        collaborationManager.currentProject = project;
    }

    // 更新页面标题
    document.title = `${project.title} - 专业著作智能编纂系统`;

    console.log('✅ 全局项目状态已更新');
}

// 显示项目切换指示器
function showProjectSwitchingIndicator(show) {
    const indicator = document.getElementById('project-switching-indicator');
    if (!indicator && show) {
        // 创建切换指示器
        const indicatorDiv = document.createElement('div');
        indicatorDiv.id = 'project-switching-indicator';
        indicatorDiv.className = 'project-switching-indicator';
        indicatorDiv.innerHTML = `
            <div class="switching-content">
                <i class="fas fa-spinner fa-spin"></i>
                <span>正在切换项目...</span>
            </div>
        `;
        document.body.appendChild(indicatorDiv);

        // 添加样式
        if (!document.getElementById('project-switching-styles')) {
            const style = document.createElement('style');
            style.id = 'project-switching-styles';
            style.textContent = `
                .project-switching-indicator {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                }
                .switching-content {
                    background: white;
                    padding: 20px 30px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                }
                .switching-content i {
                    color: #007bff;
                    font-size: 18px;
                }
                .switching-content span {
                    font-size: 16px;
                    color: #333;
                }
            `;
            document.head.appendChild(style);
        }
    } else if (indicator && !show) {
        indicator.remove();
    }
}

// 加载项目相关数据
async function loadProjectData(project) {
    try {
        console.log('📊 开始加载项目数据:', project.title);

        // 更新项目概览信息
        updateProjectOverview(project);

        // 加载项目的章节大纲
        await loadProjectOutline(project.id);

        // 加载项目统计信息
        await loadProjectStats(project.id);

        // 如果当前在大纲面板，刷新大纲显示
        const currentPanel = document.querySelector('.panel.active');
        if (currentPanel && currentPanel.id === 'outline-panel') {
            setTimeout(() => {
                renderOutlineTree();
            }, 100);
        }

        console.log('✅ 项目数据加载完成');

    } catch (error) {
        console.error('❌ 加载项目数据失败:', error);
        throw error;
    }
}

// 更新项目概览信息
function updateProjectOverview(project) {
    // 检查项目对象是否有效
    if (!project) {
        console.warn('updateProjectOverview: 项目对象为空');
        // 设置默认值
        const projectTitleElement = document.getElementById('dashboard-project-title');
        if (projectTitleElement) {
            projectTitleElement.textContent = '未选择项目';
        }

        const projectDescElement = document.getElementById('dashboard-project-description');
        if (projectDescElement) {
            projectDescElement.textContent = '请选择一个项目开始工作';
        }

        const statusIndicator = document.getElementById('dashboard-project-status-indicator');
        if (statusIndicator) {
            statusIndicator.className = 'project-status inactive';
            statusIndicator.title = '项目状态：未选择';
        }
        return;
    }

    // 更新项目标题
    const projectTitleElement = document.getElementById('dashboard-project-title');
    if (projectTitleElement) {
        projectTitleElement.textContent = project.title || '未命名项目';
    }

    // 更新项目描述
    const projectDescElement = document.getElementById('dashboard-project-description');
    if (projectDescElement) {
        projectDescElement.textContent = project.description || '暂无描述';
    }

    // 更新项目状态指示器
    const statusIndicator = document.getElementById('dashboard-project-status-indicator');
    if (statusIndicator) {
        statusIndicator.className = `project-status ${project.status || 'inactive'}`;
        statusIndicator.title = `项目状态：${getStatusText(project.status || 'inactive')}`;
    }
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'active': '进行中',
        'suspended': '暂停',
        'completed': '已完成',
        'archived': '已归档'
    };
    return statusMap[status] || '未知';
}

// 加载项目大纲
async function loadProjectOutline(projectId) {
    try {
        if (!supabaseManager || !supabaseManager.supabase) {
            return;
        }

        const { data: outlines, error } = await supabaseManager.supabase
            .from('outlines')
            .select('*')
            .eq('project_id', projectId)
            .order('sort_order', { ascending: true });

        if (error) {
            console.error('加载项目大纲失败:', error);
            return;
        }

        // 更新大纲显示
        updateOutlineDisplay(outlines || []);

    } catch (error) {
        console.error('加载项目大纲异常:', error);
    }
}

// 加载项目统计信息
async function loadProjectStats(projectId) {
    try {
        if (!supabaseManager || !supabaseManager.supabase) {
            return;
        }

        // 获取章节统计
        const { data: chapters, error: chaptersError } = await supabaseManager.supabase
            .from('outlines')
            .select('id, title, description')
            .eq('project_id', projectId);

        if (chaptersError) {
            console.error('加载章节统计失败:', chaptersError);
            return;
        }

        // 计算统计信息
        const totalChapters = chapters ? chapters.length : 0;
        const completedChapters = chapters ? chapters.filter(ch => ch.description && ch.description.trim().length > 0).length : 0;
        const totalWords = chapters ? chapters.reduce((sum, ch) => sum + (ch.description ? ch.description.length : 0), 0) : 0;

        // 更新统计显示
        updateStatsDisplay({
            totalChapters,
            completedChapters,
            totalWords,
            progress: totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0
        });

    } catch (error) {
        console.error('加载项目统计异常:', error);
    }
}

// 更新大纲显示
function updateOutlineDisplay(outlines) {
    console.log('📋 更新大纲显示，数据:', outlines);

    if (!outlines || outlines.length === 0) {
        console.log('📋 没有大纲数据，使用默认大纲');
        // 如果没有大纲数据，清空当前大纲
        if (typeof currentProject !== 'undefined') {
            currentProject.outline = [];
        }
        renderOutlineTree();
        return;
    }

    try {
        // 构建大纲树结构
        const outlineTree = buildOutlineTree(outlines);

        // 更新全局大纲数据
        if (typeof currentProject !== 'undefined') {
            currentProject.outline = outlineTree;
        }

        // 重新渲染大纲树
        renderOutlineTree();

        // 更新章节选择器
        updateChapterSelector();

        console.log('✅ 大纲显示更新完成');

    } catch (error) {
        console.error('❌ 更新大纲显示失败:', error);
    }
}

// 更新统计显示
function updateStatsDisplay(stats) {
    // 更新总体进度百分比
    const progressElement = document.getElementById('overall-progress-text');
    if (progressElement) {
        progressElement.textContent = `${stats.progress}%`;
    }

    // 更新进度条
    const progressBar = document.getElementById('overall-progress-bar');
    if (progressBar) {
        progressBar.style.width = `${stats.progress}%`;
    }

    // 更新已完成章节数
    const completedElement = document.getElementById('completed-chapters-count');
    if (completedElement) {
        completedElement.textContent = stats.completedChapters;
    }

    // 更新总章节数
    const totalChaptersElement = document.getElementById('total-chapters-count');
    if (totalChaptersElement) {
        totalChaptersElement.textContent = stats.totalChapters;
    }

    // 更新总字数
    const wordsElement = document.getElementById('total-word-count');
    if (wordsElement) {
        wordsElement.textContent = stats.totalWords.toLocaleString();
    }

    console.log('统计信息更新:', stats);
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    // 检查用户登录状态和项目选择
    checkAuthAndProject().then(canProceed => {
        if (canProceed) {
            initializeApp();
            loadDefaultOutline();
            initializeQuillEditor();
            setupEventListeners();

            // 初始化项目概览
            setTimeout(() => {
                updateProjectOverview(currentProject);
                projectOverviewInitialized = true;
            }, 500);
        }
    });
});

// 初始化应用
function initializeApp() {
    // 设置默认活动面板
    showPanel('overview');

    // 初始化Mermaid
    mermaid.initialize({
        startOnLoad: true,
        theme: 'default',
        securityLevel: 'loose'
    });

    // 检查URL参数，看是否从项目管理页面跳转过来
    checkUrlParams();

    // 加载本地存储的项目数据
    loadProjectFromStorage();

    // 加载用户项目列表到下拉框
    loadUserProjects();

    // 加载当前选中的项目信息
    loadCurrentProjectInfo();

    // 检查数据库状态
    checkDatabaseHealth();
}

// 检查URL参数
function checkUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('project');

    if (projectId) {
        // 如果URL中有项目ID，保存到localStorage
        localStorage.setItem('selectedProjectId', projectId);

        // 清理URL参数
        const url = new URL(window.location);
        url.searchParams.delete('project');
        window.history.replaceState({}, document.title, url.pathname);

        console.log('从URL参数设置项目ID:', projectId);
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 导航菜单点击事件
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            console.log('导航链接被点击，切换到面板:', tabName);

            if (tabName) {
                showPanel(tabName);

                // 更新活动状态
                document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
    
    // Mermaid代码编辑器事件
    const mermaidCode = document.getElementById('mermaid-code');
    if (mermaidCode) {
        mermaidCode.addEventListener('input', debounce(updateDiagramPreview, 500));
    }

    // 项目选择按钮事件监听器
    const projectBtn = document.getElementById('project-btn-inline');
    if (projectBtn) {
        console.log('项目选择按钮找到，添加事件监听器');

        // 移除可能存在的旧事件监听器
        projectBtn.removeEventListener('click', toggleProjectDropdown);

        // 添加新的事件监听器
        projectBtn.addEventListener('click', function(e) {
            console.log('🔥 项目选择按钮点击事件触发');
            console.log('点击目标:', e.target);
            console.log('当前目标:', e.currentTarget);

            // 调用toggleProjectDropdown函数
            console.log('调用toggleProjectDropdown函数');
            toggleProjectDropdown();

            // 阻止默认行为和事件冒泡
            e.preventDefault();
            e.stopPropagation();
        });

        console.log('项目选择按钮事件监听器已添加');
    } else {
        console.error('项目选择按钮未找到');
    }

    // 设置AI助手事件监听器
    setupAIAssistantListeners();
}

// 显示指定面板
function showPanel(panelName) {
    // 隐藏所有面板
    document.querySelectorAll('.panel').forEach(panel => {
        panel.classList.remove('active');
    });

    // 显示指定面板
    const targetPanel = document.getElementById(panelName + '-panel');
    if (targetPanel) {
        targetPanel.classList.add('active');
    }

    // 更新导航状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.dataset.tab === panelName) {
            link.classList.add('active');
        }
    });

    // 更新侧边栏导航状态（兼容旧版本）
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
        if (item.dataset.tab === panelName) {
            item.classList.add('active');
        }
    });

    // 更新导航栏选中状态
    updateNavActiveState(panelName);

    // 根据面板类型执行特定初始化
    switch(panelName) {
        case 'overview':
            // 确保项目概览面板可见后再更新数据
            setTimeout(() => {
                // 优先使用基础版本的项目概览更新
                if (typeof currentProject !== 'undefined' && currentProject.title) {
                    updateProjectOverview(currentProject);
                } else if (typeof updateProjectOverviewFromCollaboration === 'function') {
                    updateProjectOverviewFromCollaboration();
                }
                // 如果有协作管理器，也更新仪表板
                if (typeof collaborationManager !== 'undefined' && collaborationManager.updateDashboard) {
                    collaborationManager.updateDashboard();
                }
            }, 100);
            break;
        case 'outline':
            renderOutlineTree();
            break;
        case 'editor':
            // 初始化编辑器
            if (!quillEditor) {
                initializeQuillEditor();
            }
            break;
        case 'references':
            renderReferencesList();
            break;
        case 'collaboration':
            // 初始化协作管理
            if (typeof collaborationManager !== 'undefined') {
                // 确保协作管理器有项目信息
                if (!collaborationManager.currentProject && typeof currentProject !== 'undefined' && currentProject.id) {
                    collaborationManager.currentProject = currentProject;
                    collaborationManager.currentProjectId = currentProject.id;
                }

                // 加载项目数据（方法内部会处理无项目的情况）
                collaborationManager.loadProjectData();
            }
            break;
        case 'settings':
            // 初始化系统设置
            loadSettingsPanel();
            break;
    }
}

// 更新导航栏选中状态
function updateNavActiveState(panelName) {
    // 移除所有导航项的活动状态
    document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

    // 为当前面板对应的导航项添加活动状态
    const activeNavItem = document.querySelector(`.nav-item[data-tab="${panelName}"]`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
}

// 加载默认大纲
function loadDefaultOutline() {
    currentProject.outline = [
        {
            id: 'ch0',
            title: '第0章 前言',
            level: 0,
            children: [
                { id: 'ch0-1', title: '0.1 人工智能发展与大模型时代', level: 1 },
                { id: 'ch0-2', title: '0.2 大模型技术的油气应用', level: 1 },
                { id: 'ch0-3', title: '0.3 本书编写目的与基本结构', level: 1 }
            ]
        },
        {
            id: 'part1',
            title: '【第一篇 理论技术篇】',
            level: 0,
            children: [
                {
                    id: 'ch1',
                    title: '第1章 大模型基本概念与内涵',
                    level: 1,
                    children: [
                        { id: 'ch1-1', title: '1.1 人工智能进入大模型时代', level: 2 },
                        { id: 'ch1-2', title: '1.2 大模型的定义与类型', level: 2 },
                        { id: 'ch1-3', title: '1.3 大模型能力的内涵', level: 2 },
                        { id: 'ch1-4', title: '1.4 大模型的优势与挑战', level: 2 }
                    ]
                },
                {
                    id: 'ch2',
                    title: '第2章 大模型架构与关键技术',
                    level: 1,
                    children: [
                        { id: 'ch2-1', title: '2.1 Transformer基础模型', level: 2 },
                        { id: 'ch2-2', title: '2.2 编码器架构模型（Encoder-Only）', level: 2 },
                        { id: 'ch2-3', title: '2.3 解码器架构模型（Decoder-Only）', level: 2 },
                        { id: 'ch2-4', title: '2.4 编码器-解码器架构模型', level: 2 },
                        { id: 'ch2-5', title: '2.5 混合架构模型（MoE）', level: 2 }
                    ]
                }
            ]
        }
    ];
}

// 渲染大纲树
function renderOutlineTree() {
    const container = document.getElementById('outline-tree');
    container.innerHTML = '';
    
    function renderItem(item, container) {
        const itemDiv = document.createElement('div');
        itemDiv.className = `outline-item outline-level-${item.level}`;
        itemDiv.setAttribute('data-id', item.id);
        
        itemDiv.innerHTML = `
            <i class="fas fa-${item.level === 0 ? 'book' : item.level === 1 ? 'bookmark' : 'file-alt'}"></i>
            <span class="outline-title">${item.title}</span>
            <div class="outline-actions">
                <button class="btn-icon" onclick="addSubChapter('${item.id}')" title="添加子章节">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn-icon" onclick="editOutlineItem('${item.id}')" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon" onclick="deleteOutlineItem('${item.id}')" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // 单击选择，双击编辑
        let clickTimeout;
        itemDiv.addEventListener('click', function(e) {
            if (!e.target.closest('.outline-actions')) {
                // 防止双击时触发单击
                clearTimeout(clickTimeout);
                clickTimeout = setTimeout(() => {
                    selectOutlineItem(item.id);
                }, 200);
            }
        });

        itemDiv.addEventListener('dblclick', function(e) {
            if (!e.target.closest('.outline-actions')) {
                clearTimeout(clickTimeout);
                // 双击进入编辑模式
                if (item.level > 0) { // 只有章节可以编辑，篇不能编辑
                    enterChapterEditMode(item).catch(error => {
                        console.error('进入章节编辑模式失败:', error);
                        showNotification('进入编辑模式失败: ' + error.message, 'error');
                    });
                } else {
                    showNotification('篇级别不支持编辑，请双击具体章节', 'info');
                }
            }
        });

        // 为章节添加视觉提示
        if (item.level > 0) {
            itemDiv.style.cursor = 'pointer';
            itemDiv.title = '双击进入编辑模式';

            // 添加悬停效果
            itemDiv.addEventListener('mouseenter', () => {
                itemDiv.style.backgroundColor = '#f0f9ff';
            });

            itemDiv.addEventListener('mouseleave', () => {
                itemDiv.style.backgroundColor = '';
            });
        }
        
        container.appendChild(itemDiv);

        if (item.children) {
            item.children.forEach(child => renderItem(child, container));
        }
    }

    if (currentProject && currentProject.outline) {
        currentProject.outline.forEach(item => renderItem(item, container));
    }

    // 渲染完成后同步章节选择器
    setTimeout(() => {
        onOutlineChanged();
    }, 100);
}

// 初始化Quill编辑器
function initializeQuillEditor() {
    // 检查编辑器容器是否存在
    const editorContainer = document.getElementById('chapter-content-editor');
    if (!editorContainer) {
        console.warn('Quill编辑器容器不存在，跳过初始化');
        return;
    }

    // 如果编辑器已经存在，先销毁
    if (quillEditor) {
        try {
            quillEditor = null;
            window.quillEditor = null;
        } catch (e) {
            console.warn('销毁旧编辑器时出错:', e);
        }
    }

    const toolbarOptions = [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],
        ['link', 'image', 'video']
    ];

    try {
        // 清空容器内容
        editorContainer.innerHTML = '';

        // 创建Quill配置，减少警告
        const quillConfig = {
            modules: {
                toolbar: toolbarOptions,
                history: {
                    delay: 2000,
                    maxStack: 500,
                    userOnly: true
                }
            },
            theme: 'snow',
            placeholder: '开始编写章节内容...',
            bounds: editorContainer,
            strict: false,
            debug: false // 禁用调试模式减少警告
        };

        quillEditor = new Quill(editorContainer, quillConfig);

        // 设置为全局变量，供多媒体功能使用
        window.quillEditor = quillEditor;

        // 添加内容变化监听器
        quillEditor.on('text-change', function(delta, oldDelta, source) {
            if (source === 'user') {
                // 自动保存逻辑
                debounceAutoSave();
            }
        });

        // 设置章节元数据同步
        setupChapterMetaSync();

    } catch (error) {
        console.error('Quill编辑器初始化失败:', error);
    }
}

// 防抖自动保存
let autoSaveTimeout;
function debounceAutoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(async () => {
        await saveCurrentChapterContent();
    }, 2000);
}

// 设置章节元数据同步
function setupChapterMetaSync() {
    const titleInput = document.getElementById('chapter-title');
    const summaryInput = document.getElementById('chapter-summary');

    if (titleInput) {
        titleInput.addEventListener('input', debounceMetaSync);
        titleInput.addEventListener('blur', syncChapterMeta);
    }

    if (summaryInput) {
        summaryInput.addEventListener('input', debounceMetaSync);
        summaryInput.addEventListener('blur', syncChapterMeta);
    }
}

// 防抖元数据同步
let metaSyncTimeout;
function debounceMetaSync() {
    clearTimeout(metaSyncTimeout);
    metaSyncTimeout = setTimeout(() => {
        syncChapterMeta();
    }, 1000);
}

// 同步章节元数据
function syncChapterMeta() {
    if (!currentChapter) return;

    const titleInput = document.getElementById('chapter-title');
    const summaryInput = document.getElementById('chapter-summary');

    if (titleInput && titleInput.value !== currentChapter.title) {
        currentChapter.title = titleInput.value;
        updateOutlineFromChapter(currentChapter);
        updateChapterSelector(currentChapter.id);
        renderOutlineTree();
    }

    if (summaryInput && summaryInput.value !== currentChapter.description) {
        currentChapter.description = summaryInput.value;
        updateOutlineFromChapter(currentChapter);
    }
}

// 保存当前章节内容
async function saveCurrentChapterContent() {
    console.log('🔄 开始自动保存当前章节内容...');

    if (!currentChapter || !quillEditor) {
        console.warn('⚠️ 自动保存跳过：缺少当前章节或编辑器', {
            hasCurrentChapter: !!currentChapter,
            hasQuillEditor: !!quillEditor
        });
        return;
    }

    try {
        // 获取Quill Delta格式的内容（用于数据库存储）
        const deltaContent = quillEditor.getContents();
        // 获取HTML格式的内容（用于本地存储和显示）
        const htmlContent = quillEditor.root.innerHTML;
        const plainText = quillEditor.getText();
        const wordCount = plainText.trim().length;

        console.log('📊 自动保存数据分析:', {
            chapterId: currentChapter.chapterId,
            outlineId: currentChapter.outlineId,
            title: currentChapter.title,
            wordCount: wordCount,
            deltaOpsLength: deltaContent?.ops?.length || 0,
            htmlContentLength: htmlContent.length,
            plainTextPreview: plainText.substring(0, 100) + '...'
        });

        console.log('📝 Delta内容详情:', {
            type: typeof deltaContent,
            hasOps: !!deltaContent?.ops,
            opsCount: deltaContent?.ops?.length || 0,
            firstOpPreview: deltaContent?.ops?.[0] ? {
                insert: typeof deltaContent.ops[0].insert === 'string' ?
                    deltaContent.ops[0].insert.substring(0, 50) + '...' :
                    deltaContent.ops[0].insert,
                attributes: deltaContent.ops[0].attributes
            } : null
        });

        // 确保使用有效的UUID格式章节ID
        if (!currentChapter.chapterId) {
            currentChapter.chapterId = generateUUID();
            console.log('🆕 生成新的章节ID:', currentChapter.chapterId);
        }
        const chapterId = currentChapter.chapterId;

        console.log('💾 开始保存到服务器...');
        await saveChapterToServer(chapterId, deltaContent, wordCount);

        // 更新本地数据
        console.log('💾 更新本地数据...');
        if (!currentProject.chapters[currentChapter.id]) {
            currentProject.chapters[currentChapter.id] = {};
            console.log('🆕 创建新的本地章节数据结构');
        }

        const localChapterData = {
            content: htmlContent,
            deltaContent: deltaContent,
            wordCount: wordCount,
            lastModified: new Date().toISOString(),
            title: currentChapter.title,
            chapterId: chapterId
        };

        currentProject.chapters[currentChapter.id] = localChapterData;
        console.log('📝 本地数据已更新:', {
            localChapterId: currentChapter.id,
            dataKeys: Object.keys(localChapterData),
            deltaOpsLength: localChapterData.deltaContent?.ops?.length || 0
        });

        // 保存到本地存储
        console.log('💾 保存到本地存储...');
        saveProjectToStorage();

        console.log('✅ 章节内容已自动保存（服务器+本地）');

        // 验证本地存储
        const savedLocalData = currentProject.chapters[currentChapter.id];
        console.log('🔍 验证本地存储结果:', {
            exists: !!savedLocalData,
            hasContent: !!savedLocalData?.content,
            hasDeltaContent: !!savedLocalData?.deltaContent,
            deltaOpsLength: savedLocalData?.deltaContent?.ops?.length || 0
        });

    } catch (error) {
        console.error('❌ 自动保存失败:', error);
        console.error('❌ 错误详情:', {
            message: error.message,
            stack: error.stack,
            currentChapter: currentChapter ? {
                id: currentChapter.id,
                chapterId: currentChapter.chapterId,
                title: currentChapter.title
            } : null
        });
        showNotification('自动保存失败: ' + error.message, 'error');
    }
}

// 确保有项目ID
async function ensureProjectId() {
    console.log('🔍 确保项目ID...');

    // 如果协作管理器已有项目ID，先验证它是否有效
    if (collaborationManager && collaborationManager.currentProjectId) {
        console.log('📝 协作管理器中的项目ID:', collaborationManager.currentProjectId);

        // 验证项目ID是否有章节数据
        const { data: chaptersInProject } = await supabaseManager.supabase
            .from('chapters')
            .select('id')
            .eq('project_id', collaborationManager.currentProjectId)
            .limit(1);

        if (chaptersInProject && chaptersInProject.length > 0) {
            console.log('✅ 协作管理器项目ID有效，有章节数据');
            return collaborationManager.currentProjectId;
        } else {
            console.log('⚠️ 协作管理器项目ID无章节数据，需要重新查找');
        }
    }

    // 尝试从本地存储获取或创建默认项目
    try {
        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            console.log('⚠️ 用户未登录，使用本地项目ID');
            // 如果用户未登录，使用本地项目ID
            const localProjectId = localStorage.getItem('localProjectId');
            if (localProjectId) {
                return localProjectId;
            }

            // 创建本地项目ID
            const newLocalProjectId = generateUUID();
            localStorage.setItem('localProjectId', newLocalProjectId);
            return newLocalProjectId;
        }

        console.log('✅ 用户已登录，查找实际有数据的项目...');

        // 首先查找有章节数据的项目
        const { data: projectsWithChapters } = await supabaseManager.supabase
            .from('chapters')
            .select('project_id')
            .not('project_id', 'is', null)
            .limit(1);

        if (projectsWithChapters && projectsWithChapters.length > 0) {
            const realProjectId = projectsWithChapters[0].project_id;
            console.log('🎯 找到有章节数据的项目ID:', realProjectId);

            // 验证这个项目是否存在
            const { data: projectExists } = await supabaseManager.supabase
                .from('projects')
                .select('id, title')
                .eq('id', realProjectId)
                .single();

            if (projectExists) {
                console.log('✅ 项目存在:', projectExists.title);
                if (collaborationManager) {
                    collaborationManager.currentProjectId = realProjectId;
                }
                return realProjectId;
            }
        }

        // 如果没有找到有数据的项目，查找用户的项目
        const { data: projects, error } = await supabaseManager.supabase
            .from('projects')
            .select('id, title')
            .eq('owner_id', user.id)
            .limit(1);

        if (error) throw error;

        if (projects && projects.length > 0) {
            // 使用第一个项目
            const projectId = projects[0].id;
            if (collaborationManager) {
                collaborationManager.currentProjectId = projectId;
            }
            return projectId;
        } else {
            // 创建默认项目
            const defaultProjectId = generateUUID();
            const { error: createError } = await supabaseManager.supabase
                .from('projects')
                .insert({
                    id: defaultProjectId,
                    title: currentProject.title || '《大模型技术与油气应用概论》',
                    description: '默认项目',
                    owner_id: user.id,
                    status: 'active'
                });

            if (createError) throw createError;

            // 添加项目所有者为项目成员
            const { error: memberError } = await supabaseManager.supabase
                .from('project_members')
                .insert({
                    project_id: defaultProjectId,
                    user_id: user.id,
                    role: 'owner',
                    status: 'active'
                });

            if (memberError) {
                console.warn('添加项目所有者为成员失败:', memberError);
                // 不抛出错误，因为项目已创建成功
            }

            if (collaborationManager) {
                collaborationManager.currentProjectId = defaultProjectId;
            }
            return defaultProjectId;
        }
    } catch (error) {
        console.error('确保项目ID失败:', error);
        // 回退到本地项目ID
        const localProjectId = localStorage.getItem('localProjectId') || generateUUID();
        localStorage.setItem('localProjectId', localProjectId);
        return localProjectId;
    }
}

// 保存章节到服务器
async function saveChapterToServer(chapterId, deltaContent, wordCount) {
    console.log('🔄 开始保存章节到服务器...');
    console.log('📝 保存参数:', {
        chapterId: chapterId,
        deltaContentType: typeof deltaContent,
        deltaOpsLength: deltaContent?.ops?.length || 0,
        wordCount: wordCount,
        currentChapterTitle: currentChapter?.title
    });

    // 确保有项目ID
    const projectId = await ensureProjectId();
    if (!projectId) {
        console.error('❌ 无法获取项目ID，跳过服务器保存');
        return;
    }
    console.log('✅ 项目ID:', projectId);

    const user = await supabaseManager.getCurrentUser();
    if (!user) {
        console.error('❌ 用户未登录，跳过服务器保存');
        return;
    }
    console.log('✅ 用户已登录:', user.email);

    try {
        // 首先检查章节是否已存在
        console.log('🔍 检查章节是否已存在...');
        const { data: existingChapter, error: checkError } = await supabaseManager.supabase
            .from('chapters')
            .select('id, outline_id, title, content, word_count, updated_at')
            .eq('id', chapterId)
            .maybeSingle();

        if (checkError) {
            console.error('❌ 检查章节存在性失败:', checkError);
        } else {
            console.log('📋 现有章节信息:', existingChapter);
        }

        // 准备章节数据 - content字段存储Quill Delta格式
        const chapterData = {
            id: chapterId,
            project_id: projectId,
            title: currentChapter?.title || '未命名章节',
            content: deltaContent, // 存储Quill Delta格式
            word_count: wordCount,
            status: 'draft',
            created_by: user.id,
            last_edited_by: user.id,
            updated_at: new Date().toISOString()
        };

        // 暂时跳过outline_id设置，避免UUID格式错误
        // TODO: 需要重新设计大纲和章节的关联机制
        console.log('📝 暂时跳过outline_id设置，避免UUID格式错误');

        // 设置outline_id - 这是关键的关联字段
        // if (!existingChapter) {
        //     // 新章节：从当前章节信息中获取outline_id
        //     const outlineStringId = currentChapter?.outlineId || currentChapter?.id;
        //     if (outlineStringId) {
        //         console.log('📝 新章节，查找大纲UUID，字符串ID:', outlineStringId);
        //
        //         // 根据字符串ID查找大纲的真实UUID
        //         const outlineUuid = await findOutlineUuidByStringId(outlineStringId);
        //         if (outlineUuid) {
        //             console.log('📝 找到大纲UUID:', outlineUuid);
        //             chapterData.outline_id = outlineUuid;
        //         } else {
        //             console.warn('⚠️ 警告：无法找到大纲UUID，跳过outline_id设置');
        //             // 暂时不设置outline_id，避免UUID错误
        //             // chapterData.outline_id = null;
        //         }
        //     } else {
        //         console.warn('⚠️ 警告：无法获取outline字符串ID');
        //         // chapterData.outline_id = null;
        //     }
        // } else {
        //     console.log('📝 更新现有章节，保持原有outline_id');
        //     // 保持现有的outline_id，除非当前章节有更新的outlineId
        //     const newOutlineId = currentChapter?.outlineId || currentChapter?.id;
        //     if (newOutlineId && newOutlineId !== existingChapter.outline_id) {
        //         console.log('📝 更新章节的outline_id:', newOutlineId);
        //         chapterData.outline_id = newOutlineId;
        //     }
        // }

        console.log('💾 准备保存的章节数据:', {
            ...chapterData,
            content: `[Delta对象，ops长度: ${chapterData.content?.ops?.length || 0}]`
        });

        const { data: savedData, error } = await supabaseManager.supabase
            .from('chapters')
            .upsert(chapterData, {
                onConflict: 'id'
            })
            .select('id, title, content, word_count, updated_at');

        if (error) {
            console.error('❌ 数据库保存失败:', error);
            throw error;
        }

        console.log('✅ 章节已成功保存到数据库');
        console.log('📊 保存后的数据:', {
            ...savedData?.[0],
            content: `[Delta对象，ops长度: ${savedData?.[0]?.content?.ops?.length || 0}]`
        });

        // 立即验证保存结果
        console.log('🔍 验证保存结果...');
        const { data: verifyData, error: verifyError } = await supabaseManager.supabase
            .from('chapters')
            .select('id, title, content, word_count, updated_at')
            .eq('id', chapterId)
            .single();

        if (verifyError) {
            console.error('❌ 验证保存结果失败:', verifyError);
        } else {
            console.log('✅ 验证成功，数据库中的内容:', {
                ...verifyData,
                content: `[Delta对象，ops长度: ${verifyData?.content?.ops?.length || 0}]`,
                contentPreview: JSON.stringify(verifyData?.content).substring(0, 200) + '...'
            });
        }

    } catch (error) {
        console.error('❌ 保存章节失败:', error);
        console.error('❌ 错误详情:', {
            message: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint
        });
        throw error;
    }
}

// 更新章节选择器
function updateChapterSelector() {
    const selector = document.getElementById('chapter-selector');
    selector.innerHTML = '<option value="">选择章节</option>';
    
    function addOptions(items) {
        items.forEach(item => {
            if (item.level > 0) {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = item.title;
                selector.appendChild(option);
            }
            if (item.children) {
                addOptions(item.children);
            }
        });
    }
    
    addOptions(currentProject.outline);
}

// 加载章节内容
function loadChapter() {
    const selector = document.getElementById('chapter-selector');
    const chapterId = selector.value;
    
    if (!chapterId) return;
    
    currentChapter = chapterId;
    const chapterData = currentProject.chapters[chapterId] || {
        title: '',
        summary: '',
        content: ''
    };
    
    document.getElementById('chapter-title').value = chapterData.title;
    document.getElementById('chapter-summary').value = chapterData.summary;
    quillEditor.setContents(chapterData.content || '');
}

// 保存章节内容
function saveChapter() {
    if (!currentChapter) {
        showNotification('请先选择要编辑的章节', 'warning');
        return;
    }
    
    const chapterData = {
        title: document.getElementById('chapter-title').value,
        summary: document.getElementById('chapter-summary').value,
        content: quillEditor.getContents(),
        lastModified: new Date().toISOString()
    };
    
    currentProject.chapters[currentChapter] = chapterData;
    saveProjectToStorage();
    showNotification('章节内容已保存', 'success');
}

// 更新图表预览
function updateDiagramPreview() {
    const code = document.getElementById('mermaid-code').value;
    const preview = document.getElementById('diagram-preview');
    
    if (!code.trim()) {
        preview.innerHTML = '<p class="text-muted">请输入Mermaid图表代码</p>';
        return;
    }
    
    try {
        preview.innerHTML = `<div class="mermaid">${code}</div>`;
        mermaid.init(undefined, preview.querySelector('.mermaid'));
    } catch (error) {
        preview.innerHTML = `<p class="text-error">图表语法错误: ${error.message}</p>`;
    }
}

// 生成图表
function generateDiagram() {
    const diagramType = document.getElementById('diagram-type').value;
    let template = '';
    
    switch(diagramType) {
        case 'mindmap':
            template = `mindmap
  root((大模型技术与油气应用))
    理论技术篇
      大模型基本概念
      架构与关键技术
      构建与能力学习
      多模态技术体系
    应用模式篇
      提示工程
      知识融合
      智能体应用
      部署评测
    专业实践篇
      油气应用场景
      实践案例
      发展展望`;
            break;
        case 'flowchart':
            template = `flowchart TD
    A[大模型技术] --> B[理论基础]
    A --> C[应用模式]
    A --> D[油气实践]
    B --> E[Transformer架构]
    B --> F[训练方法]
    C --> G[提示工程]
    C --> H[知识增强]
    D --> I[勘探开发]
    D --> J[生产运营]`;
            break;
        case 'architecture':
            template = `graph TB
    subgraph "应用层"
        A1[智能问答]
        A2[内容生成]
        A3[分析优化]
    end
    subgraph "模型层"
        B1[大语言模型]
        B2[多模态模型]
        B3[专业模型]
    end
    subgraph "数据层"
        C1[油气数据]
        C2[知识图谱]
        C3[文档资料]
    end
    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3`;
            break;
    }
    
    document.getElementById('mermaid-code').value = template;
    updateDiagramPreview();
}

// 工具函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// 保存项目到本地存储
function saveProjectToStorage() {
    localStorage.setItem('llm-book-project', JSON.stringify(currentProject));
}

// 从本地存储加载项目
function loadProjectFromStorage() {
    const saved = localStorage.getItem('llm-book-project');
    if (saved) {
        currentProject = { ...currentProject, ...JSON.parse(saved) };
    }
}

// 导出项目
function exportProject() {
    const dataStr = JSON.stringify(currentProject, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'llm-book-project.json';
    link.click();
}

// 保存项目
function saveProject() {
    saveProjectToStorage();
    showNotification('项目已保存到本地存储', 'success');
}

// 显示帮助
function showHelp() {
    showModal('使用帮助', `
        <div class="help-content">
            <h4>功能介绍：</h4>
            <ul>
                <li><strong>本书目录：</strong>管理书籍章节结构，双击章节名称进入编辑模式</li>
                <li><strong>章节编写：</strong>富文本编辑器，支持格式化内容编写</li>
                <li><strong>结构图：</strong>使用Mermaid语法绘制思维导图、流程图等</li>
                <li><strong>参考文献：</strong>管理引用文献和资料</li>
                <li><strong>进度管理：</strong>跟踪写作进度和完成情况</li>
            </ul>
            <h4>快捷操作：</h4>
            <ul>
                <li>Ctrl+S：保存当前内容</li>
                <li>单击目录项目选择章节，双击进入编辑模式</li>
                <li>右侧工具面板提供常用模板和快速导航</li>
            </ul>
        </div>
    `);
}

// 模态对话框相关函数
function showModal(titleOrOptions, content = null, buttonsOrCallback = null) {
    // 支持两种调用方式：
    // 1. showModal(options) - 对象参数格式（AI助手使用）
    // 2. showModal(title, content, buttons) - 传统格式

    let title, modalContent, buttons, className;

    if (typeof titleOrOptions === 'object' && titleOrOptions !== null) {
        // 对象参数格式
        title = titleOrOptions.title || '';
        modalContent = titleOrOptions.content || '';
        buttons = titleOrOptions.buttons || [];
        className = titleOrOptions.className || '';
    } else {
        // 传统参数格式
        title = titleOrOptions || '';
        modalContent = content || '';
        buttons = buttonsOrCallback || [];
        className = '';
    }

    // 设置标题和内容
    const titleElement = document.getElementById('modal-title');
    const contentElement = document.getElementById('modal-content') || document.getElementById('modal-body');
    const overlay = document.getElementById('modal-overlay');
    const container = document.getElementById('modal-container');

    if (!titleElement || !contentElement || !overlay) {
        console.error('Modal elements not found');
        return;
    }

    titleElement.textContent = title;
    contentElement.innerHTML = modalContent;

    // 设置自定义类名
    if (container && className) {
        container.className = `modal-container ${className}`;
    } else if (container) {
        container.className = 'modal-container';
    }

    // 处理按钮
    // 清空所有按钮容器
    const modalButtons = document.getElementById('modal-buttons');
    const modalActions = document.querySelector('#modal-overlay .modal-actions');

    if (modalButtons) modalButtons.innerHTML = '';
    if (modalActions) modalActions.innerHTML = '';

    if (Array.isArray(buttons) && buttons.length > 0) {
        // AI助手模式：使用modal-buttons容器
        if (modalButtons) {
            buttons.forEach((btn) => {
                const button = document.createElement('button');
                button.textContent = btn.text || '';
                button.className = `btn ${btn.className || ''}`;
                if (btn.id) button.id = btn.id;
                if (btn.style) button.style.cssText = btn.style;
                if (btn.onclick) button.onclick = btn.onclick;
                modalButtons.appendChild(button);
            });

            // 确保modal-buttons容器可见
            modalButtons.style.display = 'flex';
        }
    } else {
        // 传统模式：使用modal-actions容器
        if (modalActions) {
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '关闭';
            closeBtn.className = 'btn btn-secondary';
            closeBtn.onclick = closeModal;
            modalActions.appendChild(closeBtn);
            modalActions.style.display = 'flex';
        }

        // 隐藏modal-buttons容器
        if (modalButtons) {
            modalButtons.style.display = 'none';
        }
    }

    // 显示模态框
    overlay.style.display = 'flex';
    overlay.classList.add('active');
}

function closeModal() {
    const overlay = document.getElementById('modal-overlay');
    if (overlay) {
        overlay.classList.remove('active');
        overlay.style.display = 'none';
    }
}

function confirmModal() {
    // 由showModal设置的回调函数处理
}

// 大纲管理相关函数
// 添加子章节
function addSubChapter(parentId) {
    console.log('🔄 添加子章节，父章节ID:', parentId);

    // 查找父章节信息
    const parentItem = findOutlineItemById(currentProject.outline, parentId);
    if (!parentItem) {
        showNotification('未找到父章节', 'error');
        return;
    }

    console.log('📝 父章节信息:', {
        id: parentItem.id,
        title: parentItem.title,
        level: parentItem.level
    });

    // 确定子章节的层级（父章节层级+1，但不超过3）
    const childLevel = Math.min(parentItem.level + 1, 3);
    const levelNames = ['篇章', '章', '节', '小节'];

    showModal('添加子章节', `
        <div class="form-group">
            <label>父章节：</label>
            <input type="text" class="form-input" value="${parentItem.title}" readonly style="background-color: #f5f5f5;">
        </div>
        <div class="form-group">
            <label>子章节标题：</label>
            <input type="text" id="sub-chapter-title" class="form-input" placeholder="输入子章节标题" autofocus>
        </div>
        <div class="form-group">
            <label>层级：</label>
            <input type="text" class="form-input" value="${levelNames[childLevel]} (级别 ${childLevel})" readonly style="background-color: #f5f5f5;">
        </div>
        <div class="form-group">
            <label>描述（可选）：</label>
            <textarea id="sub-chapter-description" class="form-input" placeholder="输入章节描述" rows="3"></textarea>
        </div>
    `, async () => {
        const title = document.getElementById('sub-chapter-title').value.trim();
        const description = document.getElementById('sub-chapter-description').value.trim();

        if (!title) {
            showNotification('请输入子章节标题', 'warning');
            return;
        }

        try {
            console.log('💾 开始添加子章节到服务器...');
            await addSubChapterToServer(title, description, childLevel, parentId);
            showNotification('子章节已添加', 'success');
            closeModal();
        } catch (error) {
            console.error('❌ 添加子章节失败:', error);
            showNotification('添加失败: ' + error.message, 'error');
        }
    });
}

function addOutlineItem() {
    showModal('添加章节', `
        <div class="form-group">
            <label>章节标题：</label>
            <input type="text" id="new-outline-title" class="form-input" placeholder="输入章节标题">
        </div>
        <div class="form-group">
            <label>层级：</label>
            <select id="new-outline-level" class="form-input">
                <option value="0">篇章</option>
                <option value="1">章</option>
                <option value="2">节</option>
                <option value="3">小节</option>
            </select>
        </div>
        <div class="form-group">
            <label>插入位置：</label>
            <select id="new-outline-parent" class="form-input">
                <option value="">根级别</option>
            </select>
        </div>
    `, async () => {
        const title = document.getElementById('new-outline-title').value;
        const level = parseInt(document.getElementById('new-outline-level').value);
        const parentId = document.getElementById('new-outline-parent').value;

        if (!title.trim()) {
            showNotification('请输入章节标题', 'warning');
            return;
        }

        try {
            await addOutlineItemToServer(title.trim(), level, parentId);
            showNotification('章节已添加', 'success');
            closeModal();
        } catch (error) {
            showNotification('添加失败: ' + error.message, 'error');
        }
    });

    // 填充父节点选项
    populateParentOptions();
}

// 添加章节到服务器
async function addOutlineItemToServer(title, level, parentId) {
    if (!collaborationManager.currentProjectId) {
        throw new Error('请先选择一个项目');
    }

    const user = await supabaseManager.getCurrentUser();
    if (!user) {
        throw new Error('用户未登录');
    }

    // 获取当前最大排序号
    const { data: existingOutlines, error: fetchError } = await supabaseManager.supabase
        .from('outlines')
        .select('sort_order')
        .eq('project_id', collaborationManager.currentProjectId)
        .order('sort_order', { ascending: false })
        .limit(1);

    if (fetchError) throw fetchError;

    const nextSortOrder = existingOutlines.length > 0 ? existingOutlines[0].sort_order + 1 : 1;

    // 插入新的大纲项
    const { data, error } = await supabaseManager.supabase
        .from('outlines')
        .insert({
            project_id: collaborationManager.currentProjectId,
            title: title,
            level: level,
            sort_order: nextSortOrder,
            description: '',
            created_by: user.id
        })
        .select()
        .single();

    if (error) throw error;

    // 如果是章节级别，同时创建对应的章节记录
    if (level > 0) {
        await createChapterFromOutline(data);
    }

    // 重新加载大纲
    await loadOutlineFromServer();
}

// 从大纲创建章节
async function createChapterFromOutline(outlineData) {
    const user = await supabaseManager.getCurrentUser();
    if (!user) return null;

    try {
        // 生成UUID格式的章节ID
        const chapterId = generateUUID();

        const { data, error } = await supabaseManager.supabase
            .from('chapters')
            .upsert({
                id: chapterId,
                project_id: collaborationManager.currentProjectId,
                outline_id: outlineData.id,
                title: outlineData.title,
                content: '',
                word_count: 0,
                status: 'draft',
                created_by: user.id,
                last_edited_by: user.id
            }, {
                onConflict: 'id'
            })
            .select()
            .single();

        if (error) {
            console.error('创建章节失败:', error);
            return null;
        }

        // 更新大纲项目，关联章节ID
        if (outlineData && data) {
            outlineData.chapterId = data.id;
        }

        return data;
    } catch (error) {
        console.error('创建章节异常:', error);
        return null;
    }
}

// 生成符合UUID格式的章节ID
function generateUniqueChapterId() {
    // 生成符合UUID v4格式的ID
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 智能章节排序函数 - 支持篇-章-节层级结构
function calculateChapterOrder(title) {
    if (!title) return 9999;

    const lowerTitle = title.toLowerCase().trim();

    // 前言、序言 (0-9)
    if (lowerTitle.includes('前言') || lowerTitle.includes('序') || lowerTitle.includes('preface')) {
        return 0;
    }

    // 目录 (5)
    if (lowerTitle.includes('目录') || lowerTitle.includes('contents')) {
        return 5;
    }

    // 第0章或引言 (10-19)
    if (lowerTitle.includes('第0章') || lowerTitle.includes('第零章') ||
        lowerTitle.includes('引言') || lowerTitle.includes('概述') || lowerTitle.includes('导论')) {
        return 10;
    }

    // 篇级别 (1000, 2000, 3000, 4000, 5000)
    const partMatches = [
        { pattern: /第一篇|第1篇/, value: 1000 },
        { pattern: /第二篇|第2篇/, value: 2000 },
        { pattern: /第三篇|第3篇/, value: 3000 },
        { pattern: /第四篇|第4篇/, value: 4000 },
        { pattern: /第五篇|第5篇/, value: 5000 },
        { pattern: /第(\d+)篇/, multiplier: 1000 }
    ];

    for (const match of partMatches) {
        if (match.pattern.test(lowerTitle)) {
            if (match.value) {
                return match.value;
            } else {
                const partNum = parseInt(lowerTitle.match(match.pattern)[1]);
                return partNum * 1000;
            }
        }
    }

    // 章级别 - 需要根据上下文判断属于哪一篇
    // 这里先给出基础值，实际使用时需要结合篇的信息
    const chapterMatches = [
        { pattern: /第一章|第1章/, baseValue: 100 },
        { pattern: /第二章|第2章/, baseValue: 200 },
        { pattern: /第三章|第3章/, baseValue: 300 },
        { pattern: /第四章|第4章/, baseValue: 400 },
        { pattern: /第五章|第5章/, baseValue: 500 },
        { pattern: /第六章|第6章/, baseValue: 600 },
        { pattern: /第七章|第7章/, baseValue: 700 },
        { pattern: /第八章|第8章/, baseValue: 800 },
        { pattern: /第九章|第9章/, baseValue: 900 },
        { pattern: /第十章|第10章/, baseValue: 1000 },
        { pattern: /第(\d+)章/, multiplier: 100 }
    ];

    for (const match of chapterMatches) {
        if (match.pattern.test(lowerTitle)) {
            if (match.baseValue) {
                // 默认放在第一篇，实际应该根据上下文调整
                return 1000 + match.baseValue;
            } else {
                const chapterNum = parseInt(lowerTitle.match(match.pattern)[1]);
                return 1000 + (chapterNum * 100);
            }
        }
    }

    // 小节级别 (X.Y 格式)
    const sectionMatch = lowerTitle.match(/(\d+)\.(\d+)/);
    if (sectionMatch) {
        const chapter = parseInt(sectionMatch[1]);
        const section = parseInt(sectionMatch[2]);
        // 默认放在第一篇，章节基础值 + 小节偏移
        return 1000 + (chapter * 100) + (section * 10);
    }

    // 附录 (9000+)
    if (lowerTitle.includes('附录') || lowerTitle.includes('appendix')) {
        // 尝试提取附录编号
        if (lowerTitle.includes('附录a') || lowerTitle.includes('附录A')) return 9000;
        if (lowerTitle.includes('附录b') || lowerTitle.includes('附录B')) return 9010;
        if (lowerTitle.includes('附录c') || lowerTitle.includes('附录C')) return 9020;
        if (lowerTitle.includes('附录d') || lowerTitle.includes('附录D')) return 9030;
        return 9000;
    }

    // 参考文献 (9100)
    if (lowerTitle.includes('参考文献') || lowerTitle.includes('references')) {
        return 9100;
    }

    // 索引 (9200)
    if (lowerTitle.includes('索引') || lowerTitle.includes('index')) {
        return 9200;
    }

    // 后记、致谢 (9300)
    if (lowerTitle.includes('后记') || lowerTitle.includes('致谢') || lowerTitle.includes('acknowledgments')) {
        return 9300;
    }

    // 默认值
    return 6000;
}

// 根据上下文智能计算章节排序值
function calculateChapterOrderWithContext(item, allItems) {
    const baseOrder = calculateChapterOrder(item.title);

    // 如果是章节，需要找到它属于哪一篇
    if (item.title && (item.title.includes('章') || /\d+\.\d+/.test(item.title))) {
        // 查找前面最近的篇
        const itemIndex = allItems.findIndex(x => x.id === item.id);
        if (itemIndex >= 0) {
            for (let i = itemIndex - 1; i >= 0; i--) {
                const prevItem = allItems[i];
                if (prevItem.title && prevItem.title.includes('篇')) {
                    const partOrder = calculateChapterOrder(prevItem.title);
                    // 如果基础排序值小于2000，说明是章节，需要调整到对应篇
                    if (baseOrder < 2000 && partOrder >= 1000) {
                        const chapterOffset = baseOrder % 1000;
                        return partOrder + chapterOffset;
                    }
                }
            }
        }
    }

    return baseOrder;
}

// 智能排序大纲数组
function sortOutlinesIntelligently(outlines) {
    // 先按基础规则排序
    const sorted = outlines.sort((a, b) => {
        // 首先按 sort_order 排序（如果存在且有效）
        if (a.sort_order !== undefined && b.sort_order !== undefined &&
            a.sort_order !== null && b.sort_order !== null) {
            if (a.sort_order !== b.sort_order) {
                return a.sort_order - b.sort_order;
            }
        }

        // 然后按智能排序（考虑上下文）
        const orderA = calculateChapterOrderWithContext(a, outlines);
        const orderB = calculateChapterOrderWithContext(b, outlines);

        if (orderA !== orderB) {
            return orderA - orderB;
        }

        // 按层级排序
        if (a.level !== b.level) {
            return a.level - b.level;
        }

        // 最后按创建时间排序
        const timeA = new Date(a.created_at || 0);
        const timeB = new Date(b.created_at || 0);
        return timeA - timeB;
    });

    return sorted;
}

// 智能排序章节数组
function sortChaptersIntelligently(chapters) {
    return chapters.sort((a, b) => {
        // 首先按 order_index 排序（如果存在且有效）
        if (a.order_index !== undefined && b.order_index !== undefined &&
            a.order_index !== null && b.order_index !== null) {
            if (a.order_index !== b.order_index) {
                return a.order_index - b.order_index;
            }
        }

        // 然后按智能排序
        const orderA = calculateChapterOrder(a.title);
        const orderB = calculateChapterOrder(b.title);

        if (orderA !== orderB) {
            return orderA - orderB;
        }

        // 按层级排序
        if (a.level !== b.level) {
            return a.level - b.level;
        }

        // 最后按创建时间排序
        const timeA = new Date(a.created_at || 0);
        const timeB = new Date(b.created_at || 0);
        return timeA - timeB;
    });
}

// 从服务器加载大纲
async function loadOutlineFromServer() {
    if (!collaborationManager.currentProjectId) return;

    try {
        const { data: outlines, error } = await supabaseManager.supabase
            .from('outlines')
            .select('*')
            .eq('project_id', collaborationManager.currentProjectId)
            .order('sort_order', { ascending: true, nullsLast: true })
            .order('level', { ascending: true })
            .order('created_at', { ascending: true });

        if (error) throw error;

        // 前端智能排序确保正确性
        const sortedOutlines = sortOutlinesIntelligently(outlines || []);

        // 构建层级结构
        currentProject.outline = buildOutlineTree(sortedOutlines);

        renderOutlineTree();
        console.log('大纲已从服务器加载');
    } catch (error) {
        console.error('加载大纲失败:', error);
        showNotification('加载大纲失败: ' + error.message, 'error');
    }
}

// 构建大纲树结构
function buildOutlineTree(flatData) {
    const tree = [];
    const map = {};

    // 创建映射
    flatData.forEach(item => {
        map[item.id] = {
            id: item.id,
            title: item.title,
            level: item.level,
            description: item.description || '',
            children: []
        };
    });

    // 构建树结构
    flatData.forEach(item => {
        if (item.parent_id && map[item.parent_id]) {
            map[item.parent_id].children.push(map[item.id]);
        } else {
            tree.push(map[item.id]);
        }
    });

    return tree;
}

// 保存大纲到服务器
async function saveOutlineToServer(outlineData) {
    if (!collaborationManager.currentProjectId) {
        console.warn('没有当前项目ID，跳过保存到服务器');
        return;
    }

    const user = await supabaseManager.getCurrentUser();
    if (!user) {
        throw new Error('用户未登录');
    }

    try {
        // 检查项目是否存在，如果不存在则创建
        const { data: existingProject, error: projectCheckError } = await supabaseManager.supabase
            .from('projects')
            .select('id')
            .eq('id', collaborationManager.currentProjectId)
            .maybeSingle();

        if (projectCheckError) throw projectCheckError;

        if (!existingProject) {
            // 创建项目记录
            const { error: createProjectError } = await supabaseManager.supabase
                .from('projects')
                .insert({
                    id: collaborationManager.currentProjectId,
                    title: currentProject.title || '未命名项目',
                    description: currentProject.description || '',
                    owner_id: user.id,
                    status: 'active'
                });

            if (createProjectError) {
                console.warn('创建项目记录失败，可能是权限问题:', createProjectError);
                // 继续执行，可能项目已存在但查询权限不足
            } else {
                // 添加项目所有者为项目成员
                const { error: memberError } = await supabaseManager.supabase
                    .from('project_members')
                    .insert({
                        project_id: collaborationManager.currentProjectId,
                        user_id: user.id,
                        role: 'owner',
                        status: 'active'
                    });

                if (memberError) {
                    console.warn('添加项目所有者为成员失败:', memberError);
                    // 不抛出错误，因为项目已创建成功
                }
            }
        }

        // 首先清除现有的大纲数据
        const { error: deleteError } = await supabaseManager.supabase
            .from('outlines')
            .delete()
            .eq('project_id', collaborationManager.currentProjectId);

        if (deleteError) throw deleteError;

        // 扁平化大纲数据并准备插入
        const flatOutlines = [];
        let sortOrder = 1;

        function flattenOutline(items, parentId = null) {
            items.forEach(item => {
                flatOutlines.push({
                    id: item.id,
                    project_id: collaborationManager.currentProjectId,
                    title: item.title,
                    level: item.level,
                    description: item.description || '',
                    sort_order: sortOrder++,
                    parent_id: parentId,
                    created_by: user.id
                });

                if (item.children && item.children.length > 0) {
                    flattenOutline(item.children, item.id);
                }
            });
        }

        flattenOutline(outlineData);

        // 批量插入大纲数据
        if (flatOutlines.length > 0) {
            const { error: insertError } = await supabaseManager.supabase
                .from('outlines')
                .insert(flatOutlines);

            if (insertError) throw insertError;

            // 为章节级别的大纲项创建对应的章节记录
            for (const outline of flatOutlines) {
                if (outline.level > 0) {
                    await createChapterFromOutline(outline);
                }
            }
        }

        console.log('大纲已保存到服务器');
        showNotification('大纲已成功保存到数据库', 'success');
    } catch (error) {
        console.error('保存大纲到服务器失败:', error);
        throw error;
    }
}

// 安全保存大纲到服务器（带错误处理）
async function safelySaveOutlineToServer(outlineData) {
    console.log('🔄 开始安全保存大纲到服务器...');

    try {
        // 检查协作管理器
        if (!collaborationManager) {
            console.log('❌ 协作管理器不存在');
            showNotification('协作系统未初始化，仅保存到本地', 'warning');
            return false;
        }

        if (!collaborationManager.currentProjectId) {
            console.log('❌ 没有当前项目ID');
            showNotification('请先选择一个项目', 'warning');
            return false;
        }

        console.log('✅ 项目ID:', collaborationManager.currentProjectId);

        // 检查用户是否已登录
        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            console.log('❌ 用户未登录');
            showNotification('请先登录后再保存', 'warning');
            return false;
        }

        console.log('✅ 用户已登录:', user.email);
        console.log('📊 准备保存大纲，包含', outlineData.length, '个主要项目');

        await saveOutlineToServer(outlineData);
        console.log('✅ 大纲已成功保存到服务器');
        return true;
    } catch (error) {
        console.error('❌ 保存大纲到服务器失败:', error);
        showNotification('保存到数据库失败: ' + error.message, 'error');
        return false;
    }
}

function populateParentOptions() {
    const select = document.getElementById('new-outline-parent');

    function addOptions(items, prefix = '') {
        items.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = prefix + item.title;
            select.appendChild(option);

            if (item.children) {
                addOptions(item.children, prefix + '  ');
            }
        });
    }

    addOptions(currentProject.outline);
}

function findOutlineItem(items, id) {
    for (const item of items) {
        if (item.id === id) return item;
        if (item.children) {
            const found = findOutlineItem(item.children, id);
            if (found) return found;
        }
    }
    return null;
}

function selectOutlineItem(id) {
    // 移除之前的选中状态
    document.querySelectorAll('.outline-item').forEach(item => {
        item.classList.remove('selected');
    });

    // 添加选中状态
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.classList.add('selected');
    }

    // 如果在章节编写面板，自动加载该章节
    const activePanel = document.querySelector('.panel.active');
    if (activePanel && activePanel.id === 'chapter-panel') {
        document.getElementById('chapter-selector').value = id;
        loadChapter();
    }
}

function editOutlineItem(id) {
    const item = findOutlineItem(currentProject.outline, id);
    if (!item) return;

    showModal('编辑章节', `
        <div class="form-group">
            <label>章节标题：</label>
            <input type="text" id="edit-outline-title" class="form-input" value="${item.title}">
        </div>
        <div class="form-group">
            <label>层级：</label>
            <select id="edit-outline-level" class="form-input">
                <option value="0" ${item.level === 0 ? 'selected' : ''}>篇章</option>
                <option value="1" ${item.level === 1 ? 'selected' : ''}>章</option>
                <option value="2" ${item.level === 2 ? 'selected' : ''}>节</option>
                <option value="3" ${item.level === 3 ? 'selected' : ''}>小节</option>
            </select>
        </div>
    `, () => {
        const newTitle = document.getElementById('edit-outline-title').value;
        const newLevel = parseInt(document.getElementById('edit-outline-level').value);

        if (!newTitle.trim()) {
            showNotification('请输入章节标题', 'warning');
            return;
        }

        item.title = newTitle;
        item.level = newLevel;

        renderOutlineTree();
        saveProjectToStorage();
        showNotification('章节已更新', 'success');
    });
}

async function deleteOutlineItem(id) {
    showModal('确认删除', `
        <p>确定要删除这个章节吗？此操作不可撤销。</p>
        <p class="text-muted">这将同时删除章节内容和相关数据。</p>
    `, async () => {
        try {
            console.log('🗑️ 开始删除大纲项:', id);

            // 1. 先从服务器删除
            await deleteOutlineItemFromServer(id);

            // 2. 从本地数据中删除
            removeOutlineItem(currentProject.outline, id);

            // 3. 更新UI
            renderOutlineTree();
            saveProjectToStorage();

            showNotification('章节已删除', 'success');
            console.log('✅ 大纲项删除完成');

        } catch (error) {
            console.error('❌ 删除大纲项失败:', error);
            showNotification('删除失败: ' + error.message, 'error');
        }
    });
}

function removeOutlineItem(items, id) {
    for (let i = 0; i < items.length; i++) {
        if (items[i].id === id) {
            items.splice(i, 1);
            return true;
        }
        if (items[i].children && removeOutlineItem(items[i].children, id)) {
            return true;
        }
    }
    return false;
}

// 查找大纲项
function findOutlineItemById(items, id) {
    for (const item of items) {
        if (item.id === id) {
            return item;
        }
        if (item.children) {
            const found = findOutlineItemById(item.children, id);
            if (found) return found;
        }
    }
    return null;
}

// 添加子章节到服务器
async function addSubChapterToServer(title, description, level, parentId) {
    console.log('🔄 开始添加子章节到服务器...', {
        title,
        description,
        level,
        parentId
    });

    if (!collaborationManager.currentProjectId) {
        throw new Error('请先选择一个项目');
    }

    const user = await supabaseManager.getCurrentUser();
    if (!user) {
        throw new Error('用户未登录');
    }

    // 获取当前最大排序号（参考原始addOutlineItemToServer的逻辑）
    const { data: existingOutlines, error: fetchError } = await supabaseManager.supabase
        .from('outlines')
        .select('sort_order')
        .eq('project_id', collaborationManager.currentProjectId)
        .order('sort_order', { ascending: false })
        .limit(1);

    if (fetchError) throw fetchError;

    const nextSortOrder = existingOutlines.length > 0 ? existingOutlines[0].sort_order + 1 : 1;

    // 插入新的大纲项（添加parent_id建立父子关系）
    const { data, error } = await supabaseManager.supabase
        .from('outlines')
        .insert({
            project_id: collaborationManager.currentProjectId,
            title: title,
            level: level,
            sort_order: nextSortOrder,
            description: description,
            parent_id: parentId, // 设置父章节ID，建立父子关系
            created_by: user.id
        })
        .select()
        .single();

    if (error) {
        console.error('保存子章节到数据库失败:', error);
        throw error;
    }

    console.log('✅ 子章节已保存到数据库:', data);

    // 如果是章节级别，同时创建对应的章节记录（参考原始函数）
    if (level > 0) {
        await createChapterFromOutline(data);
    }

    // 重新加载大纲（参考原始函数）
    await loadOutlineFromServer();

    // 记录活动日志（如果有活动日志系统）
    try {
        if (supabaseManager.logActivity) {
            await supabaseManager.logActivity(
                collaborationManager.currentProjectId,
                'add_sub_chapter',
                'outline',
                data.id
            );
        }
    } catch (logError) {
        console.warn('记录活动日志失败:', logError);
    }

    return data;
}

// 导入大纲
function importOutline() {
    showModal('导入大纲', `
        <div class="form-group">
            <label>选择导入方式：</label>
            <select id="import-type" class="form-input">
                <option value="json">JSON文件</option>
                <option value="text">文本格式</option>
            </select>
        </div>
        <div class="form-group">
            <label>文件或内容：</label>
            <input type="file" id="import-file" class="form-input" accept=".json,.txt,.md">
            <textarea id="import-text" class="form-textarea" placeholder="或直接粘贴文本内容..." style="display:none;"></textarea>
        </div>
    `, () => {
        const importType = document.getElementById('import-type').value;
        const fileInput = document.getElementById('import-file');
        const textInput = document.getElementById('import-text');

        if (importType === 'json' && fileInput.files.length > 0) {
            const file = fileInput.files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    // 更新项目标题和描述
                    if (data.title) {
                        currentProject.title = data.title;
                    }
                    if (data.subtitle) {
                        currentProject.description = data.subtitle;
                    }

                    // 处理不同格式的大纲数据
                    if (data.outline) {
                        currentProject.outline = data.outline;
                    } else if (data.table_of_contents) {
                        currentProject.outline = convertComplexOutlineFormat(data);
                    } else if (data.sections) {
                        currentProject.outline = convertSectionsFormat(data.sections);
                    } else {
                        showNotification('未找到有效的大纲数据', 'warning');
                        return;
                    }

                    // 保存到本地存储
                    saveProjectToStorage();

                    // 尝试保存到数据库
                    safelySaveOutlineToServer(currentProject.outline).then(success => {
                        if (success) {
                            showNotification('大纲导入并保存成功', 'success');
                        } else {
                            showNotification('大纲导入成功（仅保存到本地）', 'success');
                        }
                    });

                    renderOutlineTree();
                    updateChapterSelector();

                    // 更新项目概览
                    if (typeof updateProjectOverview === 'function') {
                        updateProjectOverview();
                    }

                } catch (error) {
                    console.error('JSON解析错误:', error);
                    showNotification('JSON格式错误: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        } else if (importType === 'text' && textInput.value.trim()) {
            // 解析文本格式大纲
            parseTextOutline(textInput.value);
        }
    });

    // 切换导入方式
    document.getElementById('import-type').addEventListener('change', function() {
        const fileInput = document.getElementById('import-file');
        const textInput = document.getElementById('import-text');

        if (this.value === 'text') {
            fileInput.style.display = 'none';
            textInput.style.display = 'block';
        } else {
            fileInput.style.display = 'block';
            textInput.style.display = 'none';
        }
    });
}

// 转换复杂大纲格式（用户的JSON格式）
function convertComplexOutlineFormat(data) {
    const outline = [];

    try {
        // 处理前言
        if (data.table_of_contents?.preface) {
            const preface = data.table_of_contents.preface;
            const prefaceItem = {
                id: generateUUID(),
                title: '前言',
                description: '本书的编写背景和基本结构介绍',
                level: 0,
                children: []
            };

            if (preface.sections) {
                preface.sections.forEach(section => {
                    prefaceItem.children.push({
                        id: generateUUID(),
                        title: section.title,
                        description: Array.isArray(section.content) ? section.content.join(' ') : section.content || '',
                        level: 1,
                        children: []
                    });
                });
            }

            outline.push(prefaceItem);
        }

        // 处理第一篇
        if (data.table_of_contents?.part_one) {
            const partOne = data.table_of_contents.part_one;
            const partItem = {
                id: generateUUID(),
                title: partOne.title || '第一篇 理论技术篇',
                description: '以Transformer为核心介绍大模型技术原理和架构',
                level: 0,
                children: []
            };

            if (partOne.chapters) {
                partOne.chapters.forEach(chapter => {
                    const chapterItem = {
                        id: generateUUID(),
                        title: chapter.title,
                        description: chapter.sections?.[0]?.content?.[0] || '',
                        level: 1,
                        children: []
                    };

                    // 处理章节的小节
                    if (chapter.sections) {
                        chapter.sections.forEach(section => {
                            chapterItem.children.push({
                                id: generateUUID(),
                                title: section.title,
                                description: Array.isArray(section.content) ? section.content.join(' ') : section.content || '',
                                level: 2,
                                children: []
                            });
                        });
                    }

                    partItem.children.push(chapterItem);
                });
            }

            outline.push(partItem);
        }

        return outline;

    } catch (error) {
        console.error('转换复杂大纲格式失败:', error);
        throw new Error('大纲格式转换失败: ' + error.message);
    }
}

// 转换sections格式
function convertSectionsFormat(sections) {
    const outline = [];

    try {
        sections.forEach((section, index) => {
            outline.push({
                id: generateUUID(),
                title: section.title,
                description: Array.isArray(section.content) ? section.content.join(' ') : section.content || '',
                level: 0,
                children: []
            });
        });

        return outline;

    } catch (error) {
        console.error('转换sections格式失败:', error);
        throw new Error('sections格式转换失败: ' + error.message);
    }
}

function parseTextOutline(text) {
    // 检查是否是Markdown格式
    if (text.includes('# ') || text.includes('## ') || text.includes('### ')) {
        parseMarkdownOutline(text);
        return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    const outline = [];
    const stack = [];

    lines.forEach(line => {
        const trimmed = line.trim();
        if (!trimmed) return;

        // 计算缩进级别
        const indent = line.length - line.trimLeft().length;
        const level = Math.floor(indent / 2);

        const item = {
            id: 'ch' + Date.now() + Math.random().toString(36).substr(2, 9),
            title: trimmed,
            level: level,
            children: []
        };

        // 根据级别确定父节点
        while (stack.length > level) {
            stack.pop();
        }

        if (stack.length === 0) {
            outline.push(item);
        } else {
            const parent = stack[stack.length - 1];
            if (!parent.children) parent.children = [];
            parent.children.push(item);
        }

        stack.push(item);
    });

    currentProject.outline = outline;

    // 保存到本地存储
    saveProjectToStorage();

    // 尝试保存到数据库
    safelySaveOutlineToServer(currentProject.outline).then(success => {
        if (success) {
            showNotification('文本大纲导入并保存成功', 'success');
        } else {
            showNotification('文本大纲导入成功（仅保存到本地）', 'success');
        }
    });

    renderOutlineTree();
}

// 解析Markdown格式大纲
function parseMarkdownOutline(markdownText) {
    const lines = markdownText.split('\n');
    const outline = [];
    let currentSection = null;
    let currentChapter = null;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // 跳过空行、注释和编辑说明
        if (!line || line.startsWith('>') || line.startsWith('**修订建议**')) continue;

        // 处理书籍标题
        if (line.startsWith('# ') && line.includes('大模型技术与油气应用概论')) {
            currentProject.title = line.replace(/^#+\s*/, '').replace(/《|》/g, '');
            continue;
        }

        // 处理副标题
        if (line.startsWith('## ') && line.includes('通识教材编写提纲')) {
            currentProject.description = line.replace(/^#+\s*/, '');
            continue;
        }

        // 处理篇章标题（第一篇、第二篇等）
        if (line.startsWith('## 【第') && line.includes('篇')) {
            currentSection = {
                id: generateUUID(),
                title: line.replace(/^#+\s*/, '').replace(/【|】/g, ''),
                description: '',
                level: 0,
                children: []
            };
            outline.push(currentSection);
            currentChapter = null;
            continue;
        }

        // 处理章节标题
        if (line.startsWith('### 第') && line.includes('章')) {
            currentChapter = {
                id: generateUUID(),
                title: line.replace(/^#+\s*/, ''),
                description: '',
                level: 1,
                children: []
            };

            if (currentSection) {
                currentSection.children.push(currentChapter);
            } else {
                outline.push(currentChapter);
            }
            continue;
        }

        // 处理小节标题（如1.1、1.2等）
        if (line.match(/^\d+\.\d+/) && currentChapter) {
            const subsection = {
                id: generateUUID(),
                title: line,
                description: '',
                level: 2,
                children: []
            };
            currentChapter.children.push(subsection);
            continue;
        }

        // 处理前言章节
        if (line.startsWith('## 第0章') || line.includes('前言')) {
            const preface = {
                id: generateUUID(),
                title: line.replace(/^#+\s*/, ''),
                description: '本书的编写背景和基本结构介绍',
                level: 0,
                children: []
            };
            outline.push(preface);
            currentChapter = preface;
            currentSection = null;
            continue;
        }
    }

    // 如果没有找到有效的大纲结构，创建一个基本结构
    if (outline.length === 0) {
        outline.push({
            id: generateUUID(),
            title: '前言',
            description: '人工智能发展与大模型时代',
            level: 0,
            children: []
        });

        outline.push({
            id: generateUUID(),
            title: '第一篇 理论技术篇',
            description: '以Transformer为核心介绍大模型技术原理和架构',
            level: 0,
            children: [
                {
                    id: generateUUID(),
                    title: '第1章 大模型基本概念与内涵',
                    description: '大模型时代人工智能的主要特征',
                    level: 1,
                    children: []
                },
                {
                    id: generateUUID(),
                    title: '第2章 大模型架构与关键技术',
                    description: 'Transformer基础模型和各种架构',
                    level: 1,
                    children: []
                },
                {
                    id: generateUUID(),
                    title: '第3章 大模型构建与能力学习',
                    description: '预训练、后训练和强化学习',
                    level: 1,
                    children: []
                }
            ]
        });
    }

    // 更新项目信息
    if (!currentProject.title) {
        currentProject.title = '《大模型技术与油气应用概论》';
    }
    if (!currentProject.description) {
        currentProject.description = '通识教材编写提纲（修订版）';
    }

    currentProject.outline = outline;

    // 保存到本地存储
    saveProjectToStorage();

    // 尝试保存到数据库
    safelySaveOutlineToServer(currentProject.outline).then(success => {
        if (success) {
            showNotification('Markdown大纲导入并保存成功！', 'success');
        } else {
            showNotification('Markdown大纲导入成功（仅保存到本地）！', 'success');
        }
    });

    renderOutlineTree();
    updateChapterSelector();

    // 更新项目概览
    if (typeof updateProjectOverview === 'function') {
        updateProjectOverview(currentProject);
    }
}

// 参考文献管理
function renderReferencesList() {
    const container = document.getElementById('references-list');
    container.innerHTML = '';

    if (currentProject.references.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无参考文献</p>';
        return;
    }

    currentProject.references.forEach((ref, index) => {
        const refDiv = document.createElement('div');
        refDiv.className = 'reference-item';
        refDiv.innerHTML = `
            <div class="reference-content">
                <h4>${ref.title}</h4>
                <p><strong>作者：</strong>${ref.authors}</p>
                <p><strong>来源：</strong>${ref.source}</p>
                <p><strong>年份：</strong>${ref.year}</p>
                ${ref.url ? `<p><strong>链接：</strong><a href="${ref.url}" target="_blank">${ref.url}</a></p>` : ''}
            </div>
            <div class="reference-actions">
                <button class="btn-icon" onclick="editReference(${index})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon" onclick="deleteReference(${index})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(refDiv);
    });
}

function addReference() {
    showModal('添加参考文献', `
        <div class="form-group">
            <label>标题：</label>
            <input type="text" id="ref-title" class="form-input" placeholder="文献标题">
        </div>
        <div class="form-group">
            <label>作者：</label>
            <input type="text" id="ref-authors" class="form-input" placeholder="作者姓名">
        </div>
        <div class="form-group">
            <label>来源：</label>
            <input type="text" id="ref-source" class="form-input" placeholder="期刊、会议或出版社">
        </div>
        <div class="form-group">
            <label>年份：</label>
            <input type="number" id="ref-year" class="form-input" placeholder="发表年份">
        </div>
        <div class="form-group">
            <label>链接（可选）：</label>
            <input type="url" id="ref-url" class="form-input" placeholder="https://...">
        </div>
    `, () => {
        const reference = {
            title: document.getElementById('ref-title').value,
            authors: document.getElementById('ref-authors').value,
            source: document.getElementById('ref-source').value,
            year: document.getElementById('ref-year').value,
            url: document.getElementById('ref-url').value,
            id: Date.now().toString()
        };

        if (!reference.title || !reference.authors) {
            showNotification('请填写标题和作者', 'warning');
            return;
        }

        currentProject.references.push(reference);
        renderReferencesList();
        saveProjectToStorage();
        showNotification('参考文献已添加', 'success');
    });
}

function editReference(index) {
    const ref = currentProject.references[index];

    showModal('编辑参考文献', `
        <div class="form-group">
            <label>标题：</label>
            <input type="text" id="ref-title" class="form-input" value="${ref.title}">
        </div>
        <div class="form-group">
            <label>作者：</label>
            <input type="text" id="ref-authors" class="form-input" value="${ref.authors}">
        </div>
        <div class="form-group">
            <label>来源：</label>
            <input type="text" id="ref-source" class="form-input" value="${ref.source}">
        </div>
        <div class="form-group">
            <label>年份：</label>
            <input type="number" id="ref-year" class="form-input" value="${ref.year}">
        </div>
        <div class="form-group">
            <label>链接（可选）：</label>
            <input type="url" id="ref-url" class="form-input" value="${ref.url || ''}">
        </div>
    `, () => {
        currentProject.references[index] = {
            ...ref,
            title: document.getElementById('ref-title').value,
            authors: document.getElementById('ref-authors').value,
            source: document.getElementById('ref-source').value,
            year: document.getElementById('ref-year').value,
            url: document.getElementById('ref-url').value
        };

        renderReferencesList();
        saveProjectToStorage();
        showNotification('参考文献已更新', 'success');
    });
}

function deleteReference(index) {
    showModal('确认删除', `
        <p>确定要删除这条参考文献吗？</p>
    `, () => {
        currentProject.references.splice(index, 1);
        renderReferencesList();
        saveProjectToStorage();
        showNotification('参考文献已删除', 'success');
    });
}

// 进度管理
function updateProgressDisplay() {
    const totalChapters = countChapters(currentProject.outline);
    const completedChapters = Object.keys(currentProject.chapters).length;
    const progressPercentage = totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0;

    // 更新总体进度
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-card span');

    if (progressFill) {
        progressFill.style.width = progressPercentage + '%';
    }
    if (progressText) {
        progressText.textContent = `${progressPercentage}% 完成 (${completedChapters}/${totalChapters})`;
    }

    // 更新章节进度详情
    renderChapterProgress();
}

function countChapters(items) {
    if (!items || !Array.isArray(items)) return 0;

    let count = 0;
    items.forEach(item => {
        if (item.level > 0) count++; // 只计算章节，不计算篇
        if (item.children) {
            count += countChapters(item.children);
        }
    });
    return count;
}

// 获取所有章节（扁平化）
function getAllChapters(items) {
    if (!items || !Array.isArray(items)) return [];

    let chapters = [];
    items.forEach(item => {
        if (item.level > 0) {
            chapters.push(item);
        }
        if (item.children) {
            chapters = chapters.concat(getAllChapters(item.children));
        }
    });
    return chapters;
}

// 计算实际字数
function calculateTotalWordCount() {
    if (!currentProject || !currentProject.chapters) return 0;

    let totalWords = 0;
    Object.values(currentProject.chapters).forEach(chapter => {
        if (chapter.content) {
            // 移除HTML标签并计算字数
            const textContent = chapter.content.replace(/<[^>]*>/g, '');
            totalWords += textContent.length;
        }
    });
    return totalWords;
}

function renderChapterProgress() {
    const container = document.getElementById('chapter-progress');
    container.innerHTML = '<h3>章节进度详情</h3>';

    function renderProgressItem(item) {
        if (item.level === 0) {
            // 篇章标题
            const partDiv = document.createElement('div');
            partDiv.className = 'progress-part';
            partDiv.innerHTML = `<h4>${item.title}</h4>`;
            container.appendChild(partDiv);
        } else {
            // 章节进度
            const isCompleted = currentProject.chapters[item.id];
            const chapterDiv = document.createElement('div');
            chapterDiv.className = `progress-chapter ${isCompleted ? 'completed' : 'pending'}`;
            chapterDiv.innerHTML = `
                <div class="chapter-status">
                    <i class="fas fa-${isCompleted ? 'check-circle' : 'circle'}"></i>
                    <span>${item.title}</span>
                </div>
                <div class="chapter-meta">
                    ${isCompleted ?
                        `<small>最后修改: ${new Date(currentProject.chapters[item.id].lastModified || Date.now()).toLocaleDateString()}</small>` :
                        '<small>未开始</small>'
                    }
                </div>
            `;
            container.appendChild(chapterDiv);
        }

        if (item.children) {
            item.children.forEach(child => renderProgressItem(child));
        }
    }

    currentProject.outline.forEach(item => renderProgressItem(item));
}

// 模板插入功能
function insertTemplate(type) {
    if (!quillEditor) {
        showNotification('请先选择章节进行编辑', 'warning');
        return;
    }

    let template = '';
    const range = quillEditor.getSelection();
    const index = range ? range.index : quillEditor.getLength();

    switch(type) {
        case 'section':
            template = '\n\n## 章节标题\n\n### 主要内容\n\n本节主要介绍...\n\n### 关键概念\n\n1. **概念一**：定义和说明\n2. **概念二**：定义和说明\n\n### 实例分析\n\n以下是一个具体的例子：\n\n### 小结\n\n本节总结了...\n\n';
            break;
        case 'table':
            quillEditor.insertText(index, '\n');
            quillEditor.insertEmbed(index + 1, 'table', {
                rows: 3,
                columns: 3
            });
            return;
        case 'figure':
            template = '\n\n![图片描述](图片链接)\n\n**图 X.X** 图片标题和说明\n\n';
            break;
        case 'formula':
            template = '\n\n$$\n公式内容\n$$\n\n';
            break;
    }

    quillEditor.insertText(index, template);
    quillEditor.setSelection(index + template.length);
    showNotification(`${getTemplateName(type)}模板已插入`, 'success');
}

function getTemplateName(type) {
    const names = {
        'section': '章节',
        'table': '表格',
        'figure': '图片',
        'formula': '公式'
    };
    return names[type] || type;
}

// 图表模板加载
function loadDiagramTemplates() {
    const templates = {
        'mindmap': {
            name: '思维导图',
            code: `mindmap
  root((大模型技术与油气应用))
    理论技术篇
      大模型基本概念
      架构与关键技术
      构建与能力学习
      多模态技术体系
    应用模式篇
      提示工程
      知识融合
      智能体应用
      部署评测
    专业实践篇
      油气应用场景
      实践案例
      发展展望`
        },
        'flowchart': {
            name: '流程图',
            code: `flowchart TD
    A[大模型技术] --> B[理论基础]
    A --> C[应用模式]
    A --> D[油气实践]
    B --> E[Transformer架构]
    B --> F[训练方法]
    C --> G[提示工程]
    C --> H[知识增强]
    D --> I[勘探开发]
    D --> J[生产运营]`
        },
        'architecture': {
            name: '架构图',
            code: `graph TB
    subgraph "应用层"
        A1[智能问答]
        A2[内容生成]
        A3[分析优化]
    end
    subgraph "模型层"
        B1[大语言模型]
        B2[多模态模型]
        B3[专业模型]
    end
    subgraph "数据层"
        C1[油气数据]
        C2[知识图谱]
        C3[文档资料]
    end
    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3`
        }
    };

    // 存储模板供后续使用
    currentProject.diagramTemplates = templates;
}

// 快捷键支持
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        const activePanel = document.querySelector('.panel.active');
        if (activePanel && activePanel.id === 'chapter-panel') {
            saveChapter();
        } else {
            saveProject();
        }
    }
});

// 导入参考文献
function importReferences() {
    showModal('导入参考文献', `
        <div class="form-group">
            <label>选择格式：</label>
            <select id="ref-format" class="form-input">
                <option value="bibtex">BibTeX</option>
                <option value="json">JSON</option>
                <option value="csv">CSV</option>
            </select>
        </div>
        <div class="form-group">
            <label>文件：</label>
            <input type="file" id="ref-file" class="form-input" accept=".bib,.json,.csv">
        </div>
    `, () => {
        const format = document.getElementById('ref-format').value;
        const fileInput = document.getElementById('ref-file');

        if (fileInput.files.length === 0) {
            showNotification('请选择文件', 'warning');
            return;
        }

        const file = fileInput.files[0];
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                parseReferences(e.target.result, format);
            } catch (error) {
                showNotification('文件格式错误', 'error');
            }
        };
        reader.readAsText(file);
    });
}

function parseReferences(content, format) {
    let references = [];

    switch(format) {
        case 'json':
            references = JSON.parse(content);
            break;
        case 'csv':
            // 简单的CSV解析
            const lines = content.split('\n');
            const headers = lines[0].split(',');
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',');
                if (values.length >= headers.length) {
                    const ref = {};
                    headers.forEach((header, index) => {
                        ref[header.trim()] = values[index] ? values[index].trim() : '';
                    });
                    references.push(ref);
                }
            }
            break;
        case 'bibtex':
            // 简单的BibTeX解析（实际应用中需要更复杂的解析器）
            showNotification('BibTeX格式暂不支持，请使用JSON或CSV格式', 'info');
            return;
    }

    if (references.length > 0) {
        currentProject.references = currentProject.references.concat(references);
        renderReferencesList();
        saveProjectToStorage();
        showNotification(`成功导入 ${references.length} 条参考文献`, 'success');
    }
}

// ============================================================================
// 缺失的全局函数
// ============================================================================

// 显示用户配置
function showUserProfile() {
    showModal('用户配置', `
        <div class="user-profile-form">
            <div class="form-group">
                <label>用户名：</label>
                <input type="text" id="profile-username" class="form-input" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>全名：</label>
                <input type="text" id="profile-fullname" class="form-input" placeholder="输入全名">
            </div>
            <div class="form-group">
                <label>邮箱：</label>
                <input type="email" id="profile-email" class="form-input" placeholder="输入邮箱" readonly>
            </div>
            <div class="form-group">
                <label>机构：</label>
                <input type="text" id="profile-institution" class="form-input" placeholder="输入所属机构">
            </div>
            <div class="form-group">
                <label>部门：</label>
                <input type="text" id="profile-department" class="form-input" placeholder="输入部门">
            </div>
            <div class="form-group">
                <label>个人简介：</label>
                <textarea id="profile-bio" class="form-textarea" placeholder="输入个人简介"></textarea>
            </div>
        </div>
    `, [
        {
            text: '保存',
            className: 'btn-primary',
            onclick: async () => {
                try {
                    const user = await supabaseManager.getCurrentUser();
                    if (!user) {
                        showNotification('用户未登录', 'error');
                        return;
                    }

                    const profileData = {
                        username: document.getElementById('profile-username').value,
                        full_name: document.getElementById('profile-fullname').value,
                        institution: document.getElementById('profile-institution').value,
                        department: document.getElementById('profile-department').value,
                        bio: document.getElementById('profile-bio').value
                    };

                    const { error } = await supabaseManager.supabase
                        .from('user_profiles')
                        .update(profileData)
                        .eq('id', user.id);

                    if (error) throw error;

                    showNotification('用户配置已保存', 'success');
                    closeModal();
                } catch (error) {
                    showNotification('保存失败: ' + error.message, 'error');
                }
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);

    // 加载当前用户信息
    loadUserProfile();
}

// 加载用户配置
async function loadUserProfile() {
    try {
        const user = await supabaseManager.getCurrentUser();
        if (!user) return;

        const { data: profile, error } = await supabaseManager.supabase
            .from('user_profiles')
            .select('*')
            .eq('id', user.id)
            .single();

        if (error) throw error;

        if (profile) {
            document.getElementById('profile-username').value = profile.username || '';
            document.getElementById('profile-fullname').value = profile.full_name || '';
            document.getElementById('profile-email').value = profile.email || '';
            document.getElementById('profile-institution').value = profile.institution || '';
            document.getElementById('profile-department').value = profile.department || '';
            document.getElementById('profile-bio').value = profile.bio || '';
        }
    } catch (error) {
        console.error('加载用户配置失败:', error);
    }
}

// 显示项目设置
function showProjectSettings() {
    if (!collaborationManager.currentProject) {
        showNotification('请先选择一个项目', 'warning');
        return;
    }

    showModal('项目设置', `
        <div class="project-settings-form">
            <div class="form-group">
                <label>项目标题：</label>
                <input type="text" id="settings-title" class="form-input" value="${collaborationManager.currentProject.title}">
            </div>
            <div class="form-group">
                <label>项目描述：</label>
                <textarea id="settings-description" class="form-textarea">${collaborationManager.currentProject.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label>项目状态：</label>
                <select id="settings-status" class="form-select">
                    <option value="active">活跃</option>
                    <option value="paused">暂停</option>
                    <option value="completed">已完成</option>
                    <option value="archived">已归档</option>
                </select>
            </div>
            <div class="form-group">
                <label>访问权限：</label>
                <select id="settings-visibility" class="form-select">
                    <option value="private">私有</option>
                    <option value="team">团队可见</option>
                    <option value="public">公开</option>
                </select>
            </div>
        </div>
    `, [
        {
            text: '保存',
            className: 'btn-primary',
            onclick: async () => {
                try {
                    const updateData = {
                        title: document.getElementById('settings-title').value,
                        description: document.getElementById('settings-description').value,
                        status: document.getElementById('settings-status').value,
                        updated_at: new Date().toISOString()
                    };

                    const { error } = await supabaseManager.supabase
                        .from('projects')
                        .update(updateData)
                        .eq('id', collaborationManager.currentProjectId);

                    if (error) throw error;

                    // 更新本地项目信息
                    Object.assign(collaborationManager.currentProject, updateData);

                    showNotification('项目设置已保存', 'success');
                    closeModal();

                    // 刷新项目列表
                    collaborationManager.loadProjects();
                } catch (error) {
                    showNotification('保存失败: ' + error.message, 'error');
                }
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);

    // 设置当前值
    document.getElementById('settings-status').value = collaborationManager.currentProject.status || 'active';
}

// 显示用户管理
function showUserManagement() {
    if (!collaborationManager.currentProject) {
        showNotification('请先选择一个项目', 'warning');
        return;
    }

    showModal('用户管理', `
        <div class="user-management">
            <div class="section">
                <h3>邀请新成员</h3>
                <div class="invite-form">
                    <input type="email" id="invite-email" class="form-input" placeholder="输入邮箱地址">
                    <select id="invite-role" class="form-select">
                        <option value="author">作者</option>
                        <option value="editor">编辑者</option>
                        <option value="reviewer">审阅者</option>
                    </select>
                    <button class="btn-primary" onclick="inviteUser()">邀请</button>
                </div>
            </div>
            <div class="section">
                <h3>当前成员</h3>
                <div id="current-members" class="members-list">
                    加载中...
                </div>
            </div>
        </div>
    `, [
        {
            text: '关闭',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);

    // 加载当前成员
    loadCurrentMembers();
}

// 加载当前成员
async function loadCurrentMembers() {
    try {
        const { data: members, error } = await supabaseManager.supabase
            .from('project_members')
            .select(`
                user_id,
                role,
                joined_at,
                user_profiles!project_members_user_id_fkey(full_name, email)
            `)
            .eq('project_id', collaborationManager.currentProjectId);

        if (error) throw error;

        const membersList = document.getElementById('current-members');
        if (!members || members.length === 0) {
            membersList.innerHTML = '<div class="no-members">暂无成员</div>';
            return;
        }

        membersList.innerHTML = members.map(member => `
            <div class="member-item">
                <div class="member-info">
                    <div class="member-name">${member.user_profiles?.full_name || '未知用户'}</div>
                    <div class="member-email">${member.user_profiles?.email || ''}</div>
                </div>
                <div class="member-role">${collaborationManager.getRoleText(member.role)}</div>
                <div class="member-actions">
                    <button class="btn-small" onclick="changeMemberRole('${member.user_id}')">
                        更改角色
                    </button>
                    <button class="btn-small btn-danger" onclick="removeMember('${member.user_id}')">
                        移除
                    </button>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('加载成员失败:', error);
        document.getElementById('current-members').innerHTML = '<div class="error">加载失败</div>';
    }
}

// 邀请用户
async function inviteUser() {
    // 显示邀请用户对话框
    showModal('邀请用户', `
        <div class="form-group">
            <label>邮箱地址：</label>
            <input type="email" id="invite-email" class="form-input" placeholder="输入邮箱地址">
        </div>
        <div class="form-group">
            <label>角色：</label>
            <select id="invite-role" class="form-select">
                <option value="author">作者</option>
                <option value="editor">编辑者</option>
                <option value="reviewer">审阅者</option>
            </select>
        </div>
    `, [
        {
            text: '发送邀请',
            className: 'btn-primary',
            onclick: async () => {
                const email = document.getElementById('invite-email').value;
                const role = document.getElementById('invite-role').value;

                if (!email) {
                    showNotification('请输入邮箱地址', 'warning');
                    return;
                }

                try {
                    // 这里应该实现邀请逻辑
                    // 暂时显示成功消息
                    showNotification(`邀请已发送到 ${email}`, 'success');
                    closeModal();

                    // 重新加载成员列表
                    if (typeof loadCurrentMembers === 'function') {
                        loadCurrentMembers();
                    }
                } catch (error) {
                    showNotification('邀请失败: ' + error.message, 'error');
                }
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 更改成员角色
function changeMemberRole(userId) {
    showNotification('更改角色功能开发中', 'info');
}

// 移除成员
function removeMember(userId) {
    showNotification('移除成员功能开发中', 'info');
}

// 权限管理
function managePermissions() {
    if (!collaborationManager.currentProject) {
        showNotification('请先选择一个项目', 'warning');
        return;
    }

    showModal('权限管理', `
        <div class="permissions-management">
            <div class="section">
                <h3>项目权限设置</h3>
                <div class="permission-grid">
                    <div class="permission-item">
                        <label>
                            <input type="checkbox" checked> 允许成员创建章节
                        </label>
                    </div>
                    <div class="permission-item">
                        <label>
                            <input type="checkbox" checked> 允许成员编辑章节
                        </label>
                    </div>
                    <div class="permission-item">
                        <label>
                            <input type="checkbox"> 允许成员删除章节
                        </label>
                    </div>
                    <div class="permission-item">
                        <label>
                            <input type="checkbox" checked> 允许成员添加评论
                        </label>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>默认角色权限</h3>
                <div class="role-permissions">
                    <div class="role-item">
                        <strong>作者 (Author)</strong>
                        <ul>
                            <li>创建和编辑自己的章节</li>
                            <li>查看项目进度</li>
                            <li>添加评论</li>
                        </ul>
                    </div>
                    <div class="role-item">
                        <strong>编辑者 (Editor)</strong>
                        <ul>
                            <li>编辑所有章节</li>
                            <li>管理参考文献</li>
                            <li>审阅内容</li>
                        </ul>
                    </div>
                    <div class="role-item">
                        <strong>审阅者 (Reviewer)</strong>
                        <ul>
                            <li>查看所有内容</li>
                            <li>添加评论和建议</li>
                            <li>标记审阅状态</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `, [
        {
            text: '保存设置',
            className: 'btn-primary',
            onclick: () => {
                showNotification('权限设置已保存', 'success');
                closeModal();
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// ============================================================================
// 本书目录相关功能
// ============================================================================

// 进入章节编辑模式
async function enterChapterEditMode(chapterItem) {
    console.log('🚀 进入章节编辑模式...');
    console.log('📝 章节项详情:', {
        id: chapterItem.id,
        title: chapterItem.title,
        description: chapterItem.description,
        chapterId: chapterItem.chapterId,
        level: chapterItem.level
    });

    // 切换到编辑器面板
    console.log('🔄 切换到编辑器面板');
    showPanel('editor');

    // 设置当前编辑的章节，确保章节ID的一致性
    const previousChapter = currentChapter;

    // 首先尝试从数据库查找现有的章节ID
    console.log('🔍 查找现有章节ID...');
    let existingChapterId = chapterItem.chapterId;

    if (!existingChapterId) {
        // 根据大纲ID查找现有章节
        existingChapterId = await findChapterIdByOutlineId(chapterItem.id);
        console.log('📝 从数据库查找到的章节ID:', existingChapterId);
    }

    currentChapter = {
        ...chapterItem,
        outlineId: chapterItem.id, // 保存原始大纲ID
        chapterId: existingChapterId || generateUUID() // 使用现有ID或生成新ID
    };

    console.log('📝 设置当前章节:', {
        previousChapterId: previousChapter?.chapterId,
        newChapterId: currentChapter.chapterId,
        outlineId: currentChapter.outlineId,
        title: currentChapter.title,
        isExistingChapter: !!existingChapterId
    });

    // 自动填写章节标题
    const chapterTitleInput = document.getElementById('chapter-title');
    if (chapterTitleInput) {
        chapterTitleInput.value = chapterItem.title;
        console.log('✅ 设置章节标题:', chapterItem.title);
    } else {
        console.warn('⚠️ 章节标题输入框未找到');
    }

    // 自动填写章节摘要
    const chapterSummaryInput = document.getElementById('chapter-summary');
    if (chapterSummaryInput) {
        const summary = chapterItem.description || generateChapterSummary(chapterItem.title);
        chapterSummaryInput.value = summary;
        console.log('✅ 设置章节摘要:', summary);
    } else {
        console.warn('⚠️ 章节摘要输入框未找到');
    }

    // 更新章节选择器
    console.log('🔄 更新章节选择器');
    updateChapterSelector(currentChapter.outlineId);

    // 确保编辑器已初始化
    if (!quillEditor) {
        console.log('🔄 初始化Quill编辑器');
        initializeQuillEditor();
    } else {
        console.log('✅ Quill编辑器已存在');
    }

    // 加载章节内容到编辑器
    console.log('🔄 开始加载章节内容');
    loadChapterContent(currentChapter);

    // 显示通知
    showNotification(`正在编辑：${chapterItem.title}`, 'info');

    // 同步数据到章节编写页面
    console.log('🔄 同步章节数据');
    syncChapterData();

    console.log('✅ 章节编辑模式设置完成');
}

// 加载章节内容
async function loadChapterContent(chapterItem) {
    console.log('🔄 开始加载章节内容到编辑器...');
    console.log('📝 章节项信息:', {
        id: chapterItem.id,
        title: chapterItem.title,
        chapterId: chapterItem.chapterId,
        outlineId: chapterItem.outlineId
    });

    if (!quillEditor) {
        console.error('❌ Quill编辑器未初始化');
        return;
    }
    console.log('✅ Quill编辑器已准备就绪');

    try {
        // 从服务器加载章节内容，使用章节的数据库ID
        console.log('🔍 确定章节ID...');
        const chapterId = chapterItem.chapterId || await findChapterIdByOutlineId(chapterItem.outlineId || chapterItem.id);
        console.log('📝 使用的章节ID:', chapterId);

        if (!chapterId) {
            console.warn('⚠️ 无法确定章节ID，使用模板');
            const template = generateChapterTemplate(chapterItem);
            quillEditor.root.innerHTML = template;
            return;
        }

        console.log('🔄 从服务器加载内容...');
        const deltaContent = await loadChapterFromServer(chapterId);

        console.log('📊 加载结果分析:', {
            deltaContentExists: !!deltaContent,
            deltaContentType: typeof deltaContent,
            hasOps: deltaContent?.ops ? true : false,
            opsLength: deltaContent?.ops?.length || 0
        });

        if (deltaContent && deltaContent.ops && deltaContent.ops.length > 0) {
            console.log('✅ 找到有效的Delta内容，设置到编辑器');
            console.log('📝 设置前编辑器内容长度:', quillEditor.getText().length);

            // 如果有Quill Delta内容，设置到编辑器
            quillEditor.setContents(deltaContent);

            console.log('📝 设置后编辑器内容长度:', quillEditor.getText().length);
            console.log('📝 设置后编辑器HTML预览:', quillEditor.root.innerHTML.substring(0, 200) + '...');

            // 验证设置是否成功
            const verifyContent = quillEditor.getContents();
            console.log('🔍 验证设置结果:', {
                verifyOpsLength: verifyContent?.ops?.length || 0,
                contentMatches: JSON.stringify(deltaContent) === JSON.stringify(verifyContent)
            });
        } else {
            console.warn('⚠️ 没有有效的Delta内容，使用模板');
            console.log('📝 Delta内容详情:', deltaContent);

            // 如果没有内容，提供一个模板
            const template = generateChapterTemplate(chapterItem);
            console.log('📝 使用模板内容:', template.substring(0, 200) + '...');
            quillEditor.root.innerHTML = template;
        }

        console.log('✅ 章节内容加载完成');
    } catch (error) {
        console.error('❌ 加载章节内容失败:', error);
        console.error('❌ 错误详情:', {
            message: error.message,
            stack: error.stack
        });

        // 如果加载失败，使用模板
        console.log('🔄 加载失败，回退到模板');
        const template = generateChapterTemplate(chapterItem);
        quillEditor.root.innerHTML = template;
    }
}

// 从服务器加载章节内容
async function loadChapterFromServer(chapterId) {
    console.log('🔄 开始从服务器加载章节内容...');
    console.log('📝 加载参数:', {
        chapterId: chapterId,
        chapterIdType: typeof chapterId
    });

    const projectId = await ensureProjectId();
    if (!projectId || !chapterId) {
        console.error('❌ 加载失败：缺少项目ID或章节ID', { projectId, chapterId });
        return null;
    }
    console.log('✅ 项目ID:', projectId);

    try {
        console.log('🔍 查询数据库...');
        // 简化查询，避免RLS策略问题
        const { data: chapter, error } = await supabaseManager.supabase
            .from('chapters')
            .select('id, title, content, word_count, updated_at, project_id')
            .eq('id', chapterId)
            .maybeSingle(); // 使用maybeSingle避免错误

        if (error) {
            console.error('❌ 加载章节内容时出现错误:', error);
            console.error('❌ 错误详情:', {
                message: error.message,
                code: error.code,
                details: error.details,
                hint: error.hint
            });
            return null;
        }

        console.log('📊 从数据库查询到的原始数据:', chapter);

        if (!chapter) {
            console.warn('⚠️ 数据库中没有找到该章节');
            return null;
        }

        console.log('📋 章节详细信息:', {
            id: chapter.id,
            title: chapter.title,
            wordCount: chapter.word_count,
            updatedAt: chapter.updated_at,
            projectId: chapter.project_id,
            contentType: typeof chapter.content,
            contentIsNull: chapter.content === null,
            contentIsUndefined: chapter.content === undefined,
            contentOpsLength: chapter.content?.ops?.length || 0
        });

        if (chapter.content) {
            console.log('📝 内容预览:', JSON.stringify(chapter.content).substring(0, 300) + '...');

            // 验证和转换内容格式
            if (typeof chapter.content === 'object' && chapter.content.ops && Array.isArray(chapter.content.ops)) {
                console.log('✅ 内容格式验证：正确的Delta格式');
                console.log('📝 Delta操作详情:', chapter.content.ops.map((op, index) => ({
                    index,
                    insert: typeof op.insert === 'string' ? op.insert.substring(0, 50) + '...' : op.insert,
                    attributes: op.attributes
                })));
            } else if (typeof chapter.content === 'string') {
                console.warn('⚠️ 内容格式验证：HTML字符串格式，需要转换为Delta');
                console.log('📝 HTML内容预览:', chapter.content.substring(0, 200) + '...');

                // 将HTML转换为Delta格式
                try {
                    if (window.Quill) {
                        // 创建临时容器
                        const tempContainer = document.createElement('div');
                        tempContainer.style.display = 'none';
                        document.body.appendChild(tempContainer);

                        // 创建临时Quill实例
                        const tempQuill = new Quill(tempContainer, {
                            theme: 'snow',
                            modules: {
                                toolbar: false
                            }
                        });

                        // 设置HTML内容并获取Delta
                        tempQuill.root.innerHTML = chapter.content;
                        const deltaContent = tempQuill.getContents();

                        // 清理临时容器
                        document.body.removeChild(tempContainer);

                        chapter.content = deltaContent;
                        console.log('✅ HTML成功转换为Delta格式');
                        console.log('📝 转换后的Delta操作数:', chapter.content.ops?.length || 0);

                        if (chapter.content.ops && chapter.content.ops.length > 0) {
                            console.log('📝 第一个操作预览:', {
                                insert: chapter.content.ops[0].insert?.substring(0, 100) || 'N/A',
                                attributes: chapter.content.ops[0].attributes
                            });
                        }
                    } else {
                        console.warn('⚠️ Quill不可用，使用简单的文本转换');
                        // 简单的文本提取作为备选方案
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = chapter.content;
                        const textContent = tempDiv.textContent || tempDiv.innerText || '';
                        chapter.content = {
                            ops: [{ insert: textContent + '\n' }]
                        };
                        console.log('📝 文本转换完成，内容长度:', textContent.length);
                    }
                } catch (conversionError) {
                    console.error('❌ HTML到Delta转换失败:', conversionError);
                    // 使用简单的文本转换作为备选方案
                    const textContent = chapter.content.replace(/<[^>]*>/g, '');
                    chapter.content = {
                        ops: [{ insert: textContent + '\n' }]
                    };
                    console.log('📝 备选文本转换完成，内容长度:', textContent.length);
                }
            } else {
                console.warn('⚠️ 内容格式验证：未知格式');
                console.log('📝 实际内容类型和值:', typeof chapter.content, chapter.content);
            }
        } else {
            console.warn('⚠️ 章节内容为空');
        }

        // 返回Quill Delta格式的内容
        const result = chapter?.content || null;
        console.log('📤 返回的内容:', result ? `[Delta对象，ops长度: ${result.ops?.length || 0}]` : 'null');
        return result;
    } catch (error) {
        console.error('❌ 从服务器加载章节失败:', error);
        console.error('❌ 异常详情:', {
            message: error.message,
            stack: error.stack
        });
        return null;
    }
}

// 根据字符串ID查找大纲的真实UUID
async function findOutlineUuidByStringId(stringId) {
    console.log('🔍 根据字符串ID查找大纲UUID:', stringId);

    if (!stringId) {
        console.warn('⚠️ 字符串ID为空');
        return null;
    }

    // 确保有项目ID
    const projectId = await ensureProjectId();
    if (!projectId) {
        console.error('❌ 无法获取项目ID');
        return null;
    }

    try {
        // 从大纲表中查找匹配的记录
        const { data: outlines, error } = await supabaseManager.supabase
            .from('outlines')
            .select('id, title')
            .eq('project_id', projectId)
            .ilike('title', `%${stringId}%`); // 使用模糊匹配

        if (error) {
            console.warn('⚠️ 查找大纲UUID时出现警告:', error);
            return null;
        }

        if (outlines && outlines.length > 0) {
            // 返回第一个匹配的大纲UUID
            const outline = outlines[0];
            console.log('✅ 找到匹配的大纲:', outline);
            return outline.id;
        } else {
            console.warn('⚠️ 没有找到匹配的大纲');
            return null;
        }
    } catch (error) {
        console.error('❌ 查找大纲UUID时出错:', error);
        return null;
    }
}

// 根据大纲ID查找章节ID
async function findChapterIdByOutlineId(outlineId) {
    console.log('🔍 根据大纲ID查找章节ID:', outlineId);

    if (!outlineId) {
        console.warn('⚠️ 大纲ID为空');
        return null;
    }

    // 确保有项目ID
    const projectId = await ensureProjectId();
    if (!projectId) {
        console.error('❌ 无法获取项目ID');
        return null;
    }

    console.log('📝 查找参数:', { outlineId, projectId });

    try {
        const { data: chapters, error } = await supabaseManager.supabase
            .from('chapters')
            .select('id, title, outline_id, updated_at')
            .eq('outline_id', outlineId)
            .eq('project_id', projectId)
            .order('updated_at', { ascending: false });

        if (error) {
            console.warn('⚠️ 查找章节ID时出现警告:', error);
            return null;
        }

        if (chapters && chapters.length > 0) {
            if (chapters.length > 1) {
                console.warn(`⚠️ 发现 ${chapters.length} 个重复章节，使用最新的`);
                chapters.forEach((chapter, index) => {
                    console.log(`  ${index + 1}. ${chapter.title} (${chapter.id}) - ${chapter.updated_at}`);
                });
            }

            const chapter = chapters[0]; // 使用最新的章节
            console.log('✅ 找到章节:', {
                chapterId: chapter.id,
                title: chapter.title,
                outlineId: chapter.outline_id,
                isLatest: chapters.length > 1 ? '是（从重复中选择）' : '是'
            });
            return chapter.id;
        } else {
            console.warn('⚠️ 未找到对应的章节记录');
            return null;
        }
    } catch (error) {
        console.error('❌ 查找章节ID失败:', error);
        return null;
    }
}

// 生成章节模板
function generateChapterTemplate(chapterItem) {
    const templates = {
        '前言': `
            <h2>${chapterItem.title}</h2>
            <p>在人工智能技术飞速发展的今天，大模型技术已经成为推动各行各业数字化转型的重要力量...</p>
            <h3>本书结构</h3>
            <p>本书分为三个部分：</p>
            <ul>
                <li>第一篇：理论技术篇</li>
                <li>第二篇：油气应用篇</li>
                <li>第三篇：实践案例篇</li>
            </ul>
        `,
        '基本概念': `
            <h2>${chapterItem.title}</h2>
            <h3>学习目标</h3>
            <ul>
                <li>理解大模型的基本概念和定义</li>
                <li>掌握大模型的分类和特点</li>
                <li>了解大模型的发展历程</li>
            </ul>
            <h3>1.1 大模型的定义</h3>
            <p>大模型（Large Language Model, LLM）是指...</p>
            <h3>1.2 大模型的特点</h3>
            <p>大模型具有以下主要特点：</p>
            <ol>
                <li>参数规模庞大</li>
                <li>训练数据丰富</li>
                <li>泛化能力强</li>
            </ol>
        `,
        '技术原理': `
            <h2>${chapterItem.title}</h2>
            <h3>学习目标</h3>
            <ul>
                <li>掌握Transformer架构原理</li>
                <li>理解注意力机制</li>
                <li>了解预训练和微调技术</li>
            </ul>
            <h3>2.1 Transformer架构</h3>
            <p>Transformer是大模型的核心架构...</p>
        `
    };

    // 根据章节标题匹配模板
    for (const [key, template] of Object.entries(templates)) {
        if (chapterItem.title.includes(key)) {
            return template;
        }
    }

    // 默认模板
    return `
        <h2>${chapterItem.title}</h2>
        <h3>学习目标</h3>
        <ul>
            <li>目标1</li>
            <li>目标2</li>
            <li>目标3</li>
        </ul>
        <h3>主要内容</h3>
        <p>请在此处编写章节内容...</p>
    `;
}

// 更新项目概览数据（协作管理器版本）
function updateProjectOverviewFromCollaboration() {
    if (typeof collaborationManager === 'undefined' || !collaborationManager.currentProject) {
        console.log('协作管理器未初始化或无当前项目，跳过协作版本的概览更新');
        return;
    }

    const project = collaborationManager.currentProject;

    // 更新项目基本信息
    const titleElement = document.getElementById('dashboard-project-title');
    const descElement = document.getElementById('dashboard-project-description');
    const statusIndicator = document.getElementById('dashboard-project-status-indicator');

    if (titleElement) titleElement.textContent = project.title;
    if (descElement) descElement.textContent = project.description || '';
    if (statusIndicator) {
        // 移除所有状态类
        statusIndicator.className = 'project-status';
        // 添加当前状态类
        statusIndicator.classList.add(project.status || 'active');
        // 更新提示文本
        statusIndicator.title = `项目状态：${getProjectStatusText(project.status)}`;
    }

    // 计算进度数据
    calculateProjectProgress();
}

// 计算项目进度
function calculateProjectProgress() {
    if (!currentProject || !currentProject.outline) {
        // 如果没有项目数据，显示默认值
        updateProgressDisplay(0, 0, 0, 0, 0);
        updateChapterProgressList([]);
        return;
    }

    // 使用统一的章节计算方法
    const totalChapters = countChapters(currentProject.outline);

    // 获取所有章节（包括嵌套的）
    const allChapters = getAllChapters(currentProject.outline);

    // 计算各种状态的章节数
    const completedChapters = allChapters.filter(ch => ch.status === 'completed').length;
    const writingChapters = allChapters.filter(ch => ch.status === 'writing').length;
    const reviewChapters = allChapters.filter(ch => ch.status === 'review').length;

    const progressPercentage = totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0;

    // 更新进度显示
    updateProgressDisplay(progressPercentage, completedChapters, totalChapters, writingChapters, reviewChapters);

    // 更新章节进度列表
    updateChapterProgressList(allChapters);
}

// 更新进度显示
function updateProgressDisplay(percentage, completed, total, writing, review) {
    // 总体进度
    const progressBar = document.getElementById('overall-progress-bar');
    const progressText = document.getElementById('overall-progress-text');

    if (progressBar) progressBar.style.width = `${percentage}%`;
    if (progressText) progressText.textContent = `${percentage}%`;

    // 进度详情
    const completedCount = document.getElementById('completed-chapters-count');
    const totalCount = document.getElementById('total-chapters-count');
    const wordCount = document.getElementById('total-word-count');

    if (completedCount) completedCount.textContent = completed;
    if (totalCount) totalCount.textContent = total;

    // 计算实际字数
    const actualWordCount = calculateTotalWordCount();
    if (wordCount) {
        if (actualWordCount > 0) {
            wordCount.textContent = actualWordCount.toLocaleString() + ' 字';
        } else {
            // 如果没有实际内容，显示估算字数
            wordCount.textContent = '约 ' + (total * 3000).toLocaleString() + ' 字';
        }
    }

    // 统计卡片
    const writingElement = document.getElementById('writing-chapters');
    const reviewElement = document.getElementById('review-chapters');
    const membersElement = document.getElementById('team-members-count');
    const daysElement = document.getElementById('days-remaining');

    if (writingElement) writingElement.textContent = writing;
    if (reviewElement) reviewElement.textContent = review;
    if (membersElement) membersElement.textContent = '1'; // 当前只有一个用户
    if (daysElement) daysElement.textContent = '90'; // 估算剩余天数
}

// 更新章节进度列表
function updateChapterProgressList(chapters) {
    const container = document.getElementById('chapter-progress-list');
    if (!container) return;

    container.innerHTML = chapters.map(chapter => `
        <div class="chapter-progress-item">
            <div class="chapter-info">
                <div class="chapter-title">${chapter.title}</div>
                <div class="chapter-meta">字数: ${chapter.wordCount || 0} | 更新: ${chapter.lastModified || '未开始'}</div>
            </div>
            <div class="chapter-status status-${chapter.status || 'draft'}">
                ${getChapterStatusText(chapter.status || 'draft')}
            </div>
        </div>
    `).join('');
}

// 获取项目状态文本
function getProjectStatusText(status) {
    const statusMap = {
        'active': '进行中',
        'paused': '已暂停',
        'completed': '已完成',
        'archived': '已归档'
    };
    return statusMap[status] || '未知';
}

// 获取章节状态文本
function getChapterStatusText(status) {
    const statusMap = {
        'draft': '草稿',
        'writing': '编写中',
        'review': '待审阅',
        'approved': '已批准',
        'completed': '已完成'
    };
    return statusMap[status] || '草稿';
}

// 显示章节进度详情
function showChapterProgress() {
    showNotification('章节进度详情功能开发中', 'info');
}

// 显示所有活动
function showAllActivities() {
    showNotification('查看全部活动功能开发中', 'info');
}

// 显示所有任务
function showAllTasks() {
    showNotification('查看全部任务功能开发中', 'info');
}

// ============================================================================
// 章节数据双向同步机制
// ============================================================================

// 生成章节摘要
function generateChapterSummary(title) {
    const summaryTemplates = {
        '前言': '介绍本书的写作背景、目标读者、主要内容和结构安排。',
        '概念': '阐述相关概念的定义、特点、分类和发展历程。',
        '原理': '深入讲解技术原理、工作机制和核心算法。',
        '应用': '介绍实际应用场景、案例分析和实施方法。',
        '技术': '详细介绍技术实现、开发方法和最佳实践。',
        '管理': '探讨管理方法、流程规范和质量控制。',
        '发展': '分析发展趋势、未来展望和技术演进。',
        '总结': '总结全书要点、归纳核心观点和提出思考建议。'
    };

    // 根据标题关键词匹配摘要模板
    for (const [keyword, template] of Object.entries(summaryTemplates)) {
        if (title.includes(keyword)) {
            return template;
        }
    }

    // 默认摘要
    return '本章将详细介绍相关内容，包括基本概念、技术原理和实际应用。';
}

// 更新章节选择器
function updateChapterSelector(selectedOutlineId = null) {
    const selector = document.getElementById('chapter-selector');
    if (!selector) return;

    console.log('🔄 更新章节选择器，当前选中ID:', selectedOutlineId);

    // 清空现有选项
    selector.innerHTML = '<option value="">选择章节</option>';

    // 从本书目录获取章节数据（包括层级结构）
    let chapters = getChaptersFromOutlineWithHierarchy();

    // 智能排序章节列表
    chapters = sortChaptersIntelligently(chapters);

    console.log('📚 获取到的章节列表（已排序）:', chapters);

    chapters.forEach(chapter => {
        const option = document.createElement('option');
        option.value = chapter.id; // 使用大纲ID作为选择器的值

        // 根据层级添加缩进前缀
        const indent = '　'.repeat(Math.max(0, chapter.level - 1)); // 使用全角空格缩进
        const levelPrefix = getLevelPrefix(chapter.level);
        option.textContent = `${indent}${levelPrefix} ${chapter.title}`;

        // 添加数据属性
        option.setAttribute('data-level', chapter.level);
        option.setAttribute('data-outline-id', chapter.id);

        // 如果是当前选中的章节，设为选中状态
        if (selectedOutlineId && chapter.id === selectedOutlineId) {
            option.selected = true;
            console.log('✅ 设置选中章节:', chapter.title);
        }

        selector.appendChild(option);
    });

    console.log('✅ 章节选择器更新完成，共', chapters.length, '个章节');
}

// 从本书目录获取章节数据（保持层级结构）
function getChaptersFromOutlineWithHierarchy() {
    const chapters = [];

    function extractChapters(items, parentLevel = 0) {
        items.forEach(item => {
            // 只获取章节级别的项目（level > 0）
            if (item.level > 0) {
                chapters.push({
                    id: item.id,
                    title: item.title,
                    description: item.description || '',
                    level: item.level,
                    parentLevel: parentLevel
                });
            }

            // 递归处理子章节
            if (item.children && item.children.length > 0) {
                extractChapters(item.children, item.level);
            }
        });
    }

    if (currentProject && currentProject.outline) {
        extractChapters(currentProject.outline);
    }

    return chapters;
}

// 兼容性函数：保持原有的简单获取方式
function getChaptersFromOutline() {
    return getChaptersFromOutlineWithHierarchy();
}

// 获取层级前缀
function getLevelPrefix(level) {
    const prefixes = {
        1: '第',
        2: '第',
        3: '第'
    };
    return prefixes[level] || '';
}

// 同步章节数据
function syncChapterData() {
    console.log('🔄 同步章节数据...');

    // 更新章节选择器
    const selectedId = currentChapter?.outlineId || currentChapter?.id;
    updateChapterSelector(selectedId);

    // 同步到协作管理
    if (typeof collaborationManager !== 'undefined' && collaborationManager.updateDashboard) {
        collaborationManager.updateDashboard();
    }

    // 更新项目概览
    if (typeof updateProjectOverview === 'function') {
        updateProjectOverview(currentProject);
    }

    console.log('✅ 章节数据同步完成');
}

// 监听目录变化，自动更新章节选择器
function onOutlineChanged() {
    console.log('📚 目录发生变化，更新章节选择器');

    // 保存当前选中的章节ID
    const selector = document.getElementById('chapter-selector');
    const currentSelectedId = selector ? selector.value : null;

    // 更新章节选择器
    updateChapterSelector(currentSelectedId);

    // 如果当前有编辑的章节，确保选择器状态正确
    if (currentChapter) {
        const targetId = currentChapter.outlineId || currentChapter.id;
        updateChapterSelector(targetId);
    }
}

// 刷新仪表板
function refreshDashboard() {
    try {
        // 显示加载状态
        const dashboardContainer = document.querySelector('.dashboard-container');
        if (dashboardContainer) {
            dashboardContainer.style.opacity = '0.6';
        }

        // 更新项目概览数据
        if (typeof updateProjectOverview === 'function') {
            updateProjectOverview(currentProject);
        }

        // 如果有协作管理器，更新仪表板
        if (typeof collaborationManager !== 'undefined' && collaborationManager.updateDashboard) {
            collaborationManager.updateDashboard();
        }

        // 重新加载项目数据
        if (typeof collaborationManager !== 'undefined' && collaborationManager.loadProjectData) {
            collaborationManager.loadProjectData();
        }

        // 恢复显示状态
        setTimeout(() => {
            if (dashboardContainer) {
                dashboardContainer.style.opacity = '1';
            }
            showNotification('仪表板已刷新', 'success');
        }, 500);

    } catch (error) {
        console.error('刷新仪表板失败:', error);
        showNotification('刷新失败: ' + error.message, 'error');

        // 恢复显示状态
        const dashboardContainer = document.querySelector('.dashboard-container');
        if (dashboardContainer) {
            dashboardContainer.style.opacity = '1';
        }
    }
}

// 编辑项目信息
function editProjectInfo() {
    showNotification('编辑项目信息功能开发中...', 'info');
}

// 分享项目
function shareProject() {
    showNotification('分享项目功能开发中...', 'info');
}

// 项目设置
function projectSettings() {
    showNotification('项目设置功能开发中...', 'info');
}

// 加载章节（从选择器触发）
function loadChapter() {
    const selector = document.getElementById('chapter-selector');
    if (!selector || !selector.value) {
        console.log('📝 章节选择器为空，清空编辑器');
        clearChapterEditor();
        return;
    }

    const outlineId = selector.value; // 选择器使用大纲ID
    console.log('🔄 从选择器加载章节，大纲ID:', outlineId);

    // 从目录中查找对应的章节项
    const chapterItem = findOutlineItemById(currentProject.outline, outlineId);

    if (chapterItem && chapterItem.level > 0) {
        console.log('✅ 找到章节项:', chapterItem.title);

        // 同步选中状态到目录树
        selectOutlineItem(outlineId);

        // 进入章节编辑模式
        enterChapterEditMode(chapterItem).catch(error => {
            console.error('进入章节编辑模式失败:', error);
            showNotification('进入编辑模式失败: ' + error.message, 'error');
        });
    } else {
        console.warn('⚠️ 未找到对应的章节项或不是有效章节');
        showNotification('未找到对应的章节', 'warning');
    }
}

// 清空章节编辑器
function clearChapterEditor() {
    console.log('🧹 清空章节编辑器');

    // 清空当前章节
    currentChapter = null;

    // 清空表单
    const titleInput = document.getElementById('chapter-title');
    const summaryInput = document.getElementById('chapter-summary');

    if (titleInput) titleInput.value = '';
    if (summaryInput) summaryInput.value = '';

    // 清空编辑器内容
    if (quillEditor) {
        quillEditor.setContents('');
    }

    // 移除目录树的选中状态
    document.querySelectorAll('.outline-item').forEach(item => {
        item.classList.remove('selected');
    });
}

// 保存章节
async function saveChapter() {
    if (!currentChapter) {
        showNotification('请先选择一个章节', 'warning');
        return;
    }

    try {
        const titleInput = document.getElementById('chapter-title');
        const summaryInput = document.getElementById('chapter-summary');

        // 获取更新后的数据
        const updatedTitle = titleInput?.value || currentChapter.title;
        const updatedSummary = summaryInput?.value || '';
        const deltaContent = quillEditor ? quillEditor.getContents() : null;
        const htmlContent = quillEditor ? quillEditor.root.innerHTML : '';
        const wordCount = quillEditor ? quillEditor.getText().trim().length : 0;

        // 更新当前章节数据
        currentChapter.title = updatedTitle;
        currentChapter.description = updatedSummary;

        // 确保使用有效的UUID格式章节ID
        if (!currentChapter.chapterId) {
            currentChapter.chapterId = generateUUID();
        }
        const chapterId = currentChapter.chapterId;
        await saveChapterToServer(chapterId, deltaContent, wordCount);

        // 更新本地数据
        if (currentProject.chapters[currentChapter.id]) {
            currentProject.chapters[currentChapter.id].content = htmlContent;
            currentProject.chapters[currentChapter.id].deltaContent = deltaContent;
            currentProject.chapters[currentChapter.id].wordCount = wordCount;
            currentProject.chapters[currentChapter.id].lastModified = new Date().toISOString();
        }

        // 同步更新本书目录
        updateOutlineFromChapter(currentChapter);

        // 重新渲染目录树
        renderOutlineTree();

        // 更新章节选择器
        updateChapterSelector(currentChapter.id);

        showNotification('章节保存成功', 'success');

    } catch (error) {
        console.error('保存章节失败:', error);
        showNotification('保存失败: ' + error.message, 'error');
    }
}

// 从章节更新本书目录
function updateOutlineFromChapter(chapter) {
    if (!currentProject || !currentProject.outline) return;

    // 查找并更新对应的大纲项
    const outlineItem = currentProject.outline.find(item => item.id === chapter.id);
    if (outlineItem) {
        outlineItem.title = chapter.title;
        outlineItem.description = chapter.description;

        // 保存到本地存储
        saveProjectToStorage();

        // 如果有服务器连接，也更新到服务器
        updateOutlineToServer(outlineItem);
    }
}

// 更新大纲到服务器
async function updateOutlineToServer(outlineItem) {
    if (!collaborationManager.currentProjectId) return;

    try {
        const { error } = await supabaseManager.supabase
            .from('outlines')
            .update({
                title: outlineItem.title,
                description: outlineItem.description,
                updated_at: new Date().toISOString()
            })
            .eq('id', outlineItem.id);

        if (error) {
            console.warn('更新大纲到服务器失败:', error);
        }
    } catch (error) {
        console.warn('更新大纲到服务器异常:', error);
    }
}

// 从服务器删除大纲项
async function deleteOutlineItemFromServer(outlineId) {
    console.log('🗑️ 开始从服务器删除大纲项:', outlineId);

    if (!collaborationManager.currentProjectId) {
        throw new Error('没有当前项目ID');
    }

    const user = await supabaseManager.getCurrentUser();
    if (!user) {
        throw new Error('用户未登录');
    }

    try {
        // 1. 首先删除相关的章节内容
        console.log('🗑️ 删除相关章节内容...');
        const { data: relatedChapters, error: chaptersError } = await supabaseManager.supabase
            .from('chapters')
            .delete()
            .eq('outline_id', outlineId)
            .eq('project_id', collaborationManager.currentProjectId)
            .select();

        if (chaptersError) {
            console.warn('删除相关章节时出现警告:', chaptersError);
        } else {
            console.log(`✅ 删除了 ${relatedChapters?.length || 0} 个相关章节`);
        }

        // 2. 删除大纲项本身
        console.log('🗑️ 删除大纲项...');
        const { data: deletedOutline, error: outlineError } = await supabaseManager.supabase
            .from('outlines')
            .delete()
            .eq('id', outlineId)
            .eq('project_id', collaborationManager.currentProjectId)
            .select()
            .single();

        if (outlineError) {
            throw outlineError;
        }

        console.log('✅ 大纲项删除成功:', deletedOutline?.title);

        // 3. 记录活动日志（如果有活动日志系统）
        try {
            if (supabaseManager.logActivity) {
                await supabaseManager.logActivity(
                    collaborationManager.currentProjectId,
                    'delete_outline',
                    'outline',
                    outlineId
                );
            }
        } catch (logError) {
            console.warn('记录活动日志失败:', logError);
        }

        return deletedOutline;

    } catch (error) {
        console.error('❌ 从服务器删除大纲项失败:', error);
        throw error;
    }
}

// ============================================================================
// 系统设置功能
// ============================================================================

// 加载设置面板
function loadSettingsPanel() {
    if (typeof aiServiceManager === 'undefined') {
        console.error('AI服务管理器未加载');
        return;
    }

    const settings = aiServiceManager.getSettings();

    // 填充设置表单
    document.getElementById('ai-provider').value = settings.provider || 'openrouter';
    document.getElementById('ai-api-url').value = settings.apiUrl || 'https://openrouter.ai/api/v1/chat/completions';
    document.getElementById('ai-api-key').value = settings.apiKey || '';
    document.getElementById('ai-model').value = settings.model || 'deepseek/deepseek-chat-v3-0324:free';
    document.getElementById('auto-save-interval').value = settings.autoSaveInterval || 2;
    document.getElementById('editor-theme').value = settings.editorTheme || 'snow';
    document.getElementById('enable-notifications').checked = settings.enableNotifications !== false;
    document.getElementById('enable-auto-backup').checked = settings.enableAutoBackup !== false;
    document.getElementById('enable-ai-suggestions').checked = settings.enableAISuggestions === true;

    // 更新API URL根据服务商
    updateApiUrlByProvider();

    // 加载Pollinations设置
    loadPollinationsSettings();
}

// 测试Pollinations连接
async function testPollinationsConnection() {
    try {
        showNotification('正在测试连接...', 'info');

        // 保存当前设置到服务
        const config = getPollinationsConfigFromForm();
        window.pollinationsService.saveConfig(config);

        // 验证配置
        const results = await window.pollinationsService.validateConfig();

        let message = '连接测试结果:\n';
        message += `图片生成: ${results.imageGeneration ? '✓' : '✗'}\n`;
        message += `语音生成: ${results.audioGeneration ? '✓' : '✗'}\n`;
        message += `图片识别: ${results.vision ? '✓' : '✗'}\n`;
        message += `文本生成: ${results.textGeneration ? '✓' : '✗'}`;

        if (results.errors.length > 0) {
            message += '\n\n错误信息:\n' + results.errors.join('\n');
        }

        const hasErrors = results.errors.length > 0;
        const allWorking = results.imageGeneration && results.audioGeneration && results.vision;

        if (allWorking) {
            showNotification('所有功能测试通过！', 'success');
        } else if (hasErrors) {
            showNotification('部分功能测试失败', 'warning');
        } else {
            showNotification('连接测试完成', 'info');
        }

        // 显示详细结果
        alert(message);

    } catch (error) {
        console.error('连接测试失败:', error);
        showNotification(`连接测试失败: ${error.message}`, 'error');
    }
}

// 重置Pollinations设置
function resetPollinationsSettings() {
    if (confirm('确定要重置多媒体服务设置为默认值吗？')) {
        window.pollinationsService.resetConfig();
        loadPollinationsSettings();
        showNotification('设置已重置为默认值', 'success');
    }
}

// 从表单获取Pollinations配置
function getPollinationsConfigFromForm() {
    return {
        apiKey: document.getElementById('pollinations-api-key')?.value || null,
        referrer: document.getElementById('pollinations-referrer')?.value || 'llm-book-system',
        defaultImageModel: document.getElementById('default-image-model')?.value || 'flux',
        defaultVoice: document.getElementById('default-voice')?.value || 'nova',
        timeout: parseInt(document.getElementById('request-timeout')?.value || '300') * 1000,
        maxRetries: parseInt(document.getElementById('max-retries')?.value || '3')
    };
}

// 加载Pollinations设置到表单
function loadPollinationsSettings() {
    const config = window.pollinationsService.getConfig();

    const apiKeyInput = document.getElementById('pollinations-api-key');
    const referrerInput = document.getElementById('pollinations-referrer');
    const imageModelSelect = document.getElementById('default-image-model');
    const voiceSelect = document.getElementById('default-voice');
    const timeoutInput = document.getElementById('request-timeout');
    const retriesInput = document.getElementById('max-retries');

    if (apiKeyInput) apiKeyInput.value = config.apiKey || '';
    if (referrerInput) referrerInput.value = config.referrer || 'llm-book-system';
    if (imageModelSelect) imageModelSelect.value = config.defaultImageModel || 'flux';
    if (voiceSelect) voiceSelect.value = config.defaultVoice || 'nova';
    if (timeoutInput) timeoutInput.value = Math.floor((config.timeout || 300000) / 1000);
    if (retriesInput) retriesInput.value = config.maxRetries || 3;
}

// 保存设置
function saveSettings() {
    if (typeof aiServiceManager === 'undefined') {
        showNotification('AI服务管理器未加载', 'error');
        return;
    }

    const settings = {
        provider: document.getElementById('ai-provider').value,
        apiUrl: document.getElementById('ai-api-url').value,
        apiKey: document.getElementById('ai-api-key').value,
        model: document.getElementById('ai-model').value,
        autoSaveInterval: parseInt(document.getElementById('auto-save-interval').value),
        editorTheme: document.getElementById('editor-theme').value,
        enableNotifications: document.getElementById('enable-notifications').checked,
        enableAutoBackup: document.getElementById('enable-auto-backup').checked,
        enableAISuggestions: document.getElementById('enable-ai-suggestions').checked
    };

    // 保存Pollinations设置
    const pollinationsConfig = getPollinationsConfigFromForm();
    window.pollinationsService.saveConfig(pollinationsConfig);

    if (aiServiceManager.updateSettings(settings)) {
        showNotification('设置保存成功', 'success');

        // 应用新设置
        applySettings(settings);
    } else {
        showNotification('设置保存失败', 'error');
    }
}

// 应用设置
function applySettings(settings) {
    // 更新自动保存间隔
    if (settings.autoSaveInterval && settings.autoSaveInterval !== 2) {
        // 这里可以更新防抖延迟时间
        console.log('自动保存间隔已更新为:', settings.autoSaveInterval, '秒');
    }

    // 更新编辑器主题
    if (settings.editorTheme && quillEditor) {
        // Quill主题需要重新初始化才能更改
        console.log('编辑器主题已更新为:', settings.editorTheme);
    }
}

// 测试AI连接
async function testAIConnection() {
    if (typeof aiServiceManager === 'undefined') {
        showNotification('AI服务管理器未加载', 'error');
        return;
    }

    // 先保存当前设置
    saveSettings();

    // 更安全的按钮选择方式
    const testButtons = document.querySelectorAll('button');
    let testButton = null;

    for (const btn of testButtons) {
        if (btn.textContent.includes('测试连接') || btn.onclick?.toString().includes('testAIConnection')) {
            testButton = btn;
            break;
        }
    }

    if (!testButton) {
        console.warn('找不到测试连接按钮');
        showNotification('开始测试AI连接...', 'info');
    }

    const originalText = testButton ? testButton.innerHTML : '';

    if (testButton) {
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
        testButton.disabled = true;
    }

    try {
        const result = await aiServiceManager.testConnection();

        const testResults = document.getElementById('test-results');
        testResults.style.display = 'block';

        if (result.success) {
            testResults.innerHTML = `
                <div class="test-success">
                    <h4><i class="fas fa-check-circle"></i> 连接测试成功</h4>
                    <p>AI服务响应：${result.response}</p>
                    <small>API连接正常，可以使用AI功能</small>
                </div>
            `;
            showNotification('AI连接测试成功', 'success');
        } else {
            testResults.innerHTML = `
                <div class="test-error">
                    <h4><i class="fas fa-exclamation-circle"></i> 连接测试失败</h4>
                    <p>错误信息：${result.message}</p>
                    <small>请检查API密钥和网络连接</small>
                </div>
            `;
            showNotification('AI连接测试失败', 'error');
        }
    } catch (error) {
        const testResults = document.getElementById('test-results');
        testResults.style.display = 'block';
        testResults.innerHTML = `
            <div class="test-error">
                <h4><i class="fas fa-exclamation-circle"></i> 测试异常</h4>
                <p>错误信息：${error.message}</p>
            </div>
        `;
        showNotification('连接测试异常: ' + error.message, 'error');
    } finally {
        if (testButton) {
            testButton.innerHTML = originalText;
            testButton.disabled = false;
        }
    }
}

// 恢复默认设置
function resetToDefaults() {
    if (typeof aiServiceManager === 'undefined') {
        showNotification('AI服务管理器未加载', 'error');
        return;
    }

    showModal('确认重置', '确定要恢复默认设置吗？这将清除所有自定义配置。', [
        {
            text: '确认重置',
            className: 'btn-warning',
            onclick: () => {
                aiServiceManager.resetToDefaults();
                loadSettingsPanel();
                showNotification('已恢复默认设置', 'success');
                closeModal();
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 切换API密钥可见性
function toggleApiKeyVisibility() {
    const apiKeyInput = document.getElementById('ai-api-key');
    const toggleIcon = document.getElementById('api-key-toggle');

    if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        apiKeyInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// 根据服务商更新API URL
function updateApiUrlByProvider() {
    const provider = document.getElementById('ai-provider').value;
    const apiUrlInput = document.getElementById('ai-api-url');
    const modelSelect = document.getElementById('ai-model');

    const providerConfigs = {
        openrouter: {
            url: 'https://openrouter.ai/api/v1/chat/completions',
            models: [
                { value: 'deepseek/deepseek-chat-v3-0324:free', text: 'DeepSeek Chat V3 (Free)' },
                { value: 'meta-llama/llama-3.2-3b-instruct:free', text: 'Llama 3.2 3B (Free)' },
                { value: 'microsoft/phi-3-mini-128k-instruct:free', text: 'Phi-3 Mini (Free)' },
                { value: 'gpt-3.5-turbo', text: 'GPT-3.5 Turbo' },
                { value: 'gpt-4', text: 'GPT-4' }
            ]
        },
        openai: {
            url: 'https://api.openai.com/v1/chat/completions',
            models: [
                { value: 'gpt-3.5-turbo', text: 'GPT-3.5 Turbo' },
                { value: 'gpt-4', text: 'GPT-4' },
                { value: 'gpt-4-turbo', text: 'GPT-4 Turbo' }
            ]
        },
        anthropic: {
            url: 'https://api.anthropic.com/v1/messages',
            models: [
                { value: 'claude-3-haiku-20240307', text: 'Claude 3 Haiku' },
                { value: 'claude-3-sonnet-20240229', text: 'Claude 3 Sonnet' },
                { value: 'claude-3-opus-20240229', text: 'Claude 3 Opus' }
            ]
        }
    };

    const config = providerConfigs[provider];
    if (config) {
        apiUrlInput.value = config.url;

        // 更新模型选项
        modelSelect.innerHTML = '';
        config.models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.value;
            option.textContent = model.text;
            modelSelect.appendChild(option);
        });
    }
}

// 监听服务商变化
document.addEventListener('DOMContentLoaded', function() {
    const providerSelect = document.getElementById('ai-provider');
    if (providerSelect) {
        providerSelect.addEventListener('change', updateApiUrlByProvider);
    }
});

// ============================================================================
// AI生成大纲功能
// ============================================================================

// 生成AI大纲
function generateAIOutline() {
    if (typeof aiServiceManager === 'undefined') {
        showNotification('AI服务管理器未加载，请先配置AI设置', 'warning');
        showPanel('settings');
        return;
    }

    const settings = aiServiceManager.getSettings();
    if (!settings.apiKey) {
        showNotification('请先在系统设置中配置AI API密钥', 'warning');
        showPanel('settings');
        return;
    }

    showModal('AI生成大纲', `
        <div class="ai-outline-form">
            <div class="form-group">
                <label for="book-title">书籍标题：</label>
                <input type="text" id="book-title" class="form-input"
                       placeholder="例如：《大模型技术与油气应用概论》"
                       value="${currentProject?.title || ''}">
            </div>

            <div class="form-group">
                <label for="book-description">书籍简介：</label>
                <textarea id="book-description" class="form-textarea" rows="3"
                          placeholder="简要描述书籍的主要内容、目标读者和特色">${currentProject?.description || ''}</textarea>
            </div>

            <div class="form-group">
                <label for="outline-requirements">大纲要求：</label>
                <textarea id="outline-requirements" class="form-textarea" rows="4"
                          placeholder="请详细描述您对大纲的要求，例如：
- 面向大学生的教材
- 包含理论基础和实际应用
- 每章都要有案例分析
- 重点关注某个特定领域"></textarea>
            </div>

            <div class="form-group">
                <label for="outline-style">大纲风格：</label>
                <select id="outline-style" class="form-select">
                    <option value="academic">学术教材风格</option>
                    <option value="practical">实用指南风格</option>
                    <option value="comprehensive">综合性参考书</option>
                    <option value="introductory">入门教程风格</option>
                </select>
            </div>

            <div class="ai-tips">
                <h4><i class="fas fa-lightbulb"></i> 提示</h4>
                <ul>
                    <li>详细的要求描述有助于生成更准确的大纲</li>
                    <li>可以指定目标读者、难度级别、重点领域等</li>
                    <li>生成的大纲可以进一步编辑和调整</li>
                </ul>
            </div>
        </div>
    `, [
        {
            text: '生成大纲',
            className: 'btn-primary',
            onclick: async () => {
                await executeAIOutlineGeneration();
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 执行AI大纲生成
async function executeAIOutlineGeneration() {
    const bookTitle = document.getElementById('book-title').value.trim();
    const bookDescription = document.getElementById('book-description').value.trim();
    const requirements = document.getElementById('outline-requirements').value.trim();
    const style = document.getElementById('outline-style').value;

    if (!bookTitle) {
        showNotification('请输入书籍标题', 'warning');
        return;
    }

    if (!requirements) {
        showNotification('请描述您对大纲的要求', 'warning');
        return;
    }

    // 关闭当前对话框
    closeModal();

    // 显示生成进度对话框
    showModal('AI正在生成大纲', `
        <div class="ai-generation-progress">
            <div class="progress-indicator">
                <i class="fas fa-robot fa-spin"></i>
                <h3>AI正在分析您的需求...</h3>
                <p>正在智能解析书籍要求，构建专业大纲结构</p>

                <!-- 整体进度条 -->
                <div class="overall-progress-bar">
                    <div class="overall-progress-fill" id="overall-progress-fill"></div>
                </div>
            </div>

            <div class="progress-steps">
                <div class="step active" data-step="0">
                    <div class="step-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <span>需求分析</span>
                </div>
                <div class="step" data-step="1">
                    <div class="step-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <span>结构构建</span>
                </div>
                <div class="step" data-step="2">
                    <div class="step-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <span>内容生成</span>
                </div>
                <div class="step" data-step="3">
                    <div class="step-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <span>完成优化</span>
                </div>
            </div>

            <div class="time-estimate">
                <p class="estimate-text">预计剩余时间: <span class="estimate-time" id="time-remaining">15-25秒</span></p>
            </div>
        </div>
    `, [
        {
            text: '取消生成',
            className: 'btn-secondary',
            onclick: () => {
                closeModal();
                showNotification('已取消AI大纲生成', 'info');
            }
        }
    ]);

    try {
        // 构建提示词
        const styleDescriptions = {
            academic: '学术教材风格，注重理论体系和知识结构',
            practical: '实用指南风格，注重实际操作和应用案例',
            comprehensive: '综合性参考书风格，内容全面深入',
            introductory: '入门教程风格，循序渐进易于理解'
        };

        const prompt = `请为《${bookTitle}》生成详细的书籍大纲。

书籍信息：
- 标题：${bookTitle}
- 简介：${bookDescription}
- 风格：${styleDescriptions[style]}

具体要求：
${requirements}

请生成包含3-5篇，每篇包含3-6章的完整大纲结构。`;

        // 更新进度
        updateGenerationProgress(1);

        const result = await aiServiceManager.generateOutline(prompt, {
            title: bookTitle,
            description: bookDescription
        });

        // 更新进度
        updateGenerationProgress(2);

        if (result.success && result.outline) {
            // 成功生成大纲
            updateGenerationProgress(3);

            setTimeout(async () => {
                closeModal();
                try {
                    await applyGeneratedOutline(result.outline);
                    showNotification('AI大纲生成成功！', 'success');
                } catch (error) {
                    console.error('应用AI大纲失败:', error);
                    showNotification('应用AI大纲失败: ' + error.message, 'error');
                }
            }, 1000);

        } else {
            // 生成失败或需要手动处理
            closeModal();

            if (result.rawResponse) {
                showAIOutlineResult(result.rawResponse, result.message);
            } else {
                showNotification(result.message || 'AI大纲生成失败', 'error');
            }
        }

    } catch (error) {
        closeModal();
        showNotification('AI大纲生成异常: ' + error.message, 'error');
        console.error('AI大纲生成错误:', error);
    }
}

// 更新生成进度
function updateGenerationProgress(step) {
    const steps = document.querySelectorAll('.progress-steps .step');
    const progressFill = document.getElementById('overall-progress-fill');
    const timeRemaining = document.getElementById('time-remaining');

    const stepData = [
        {
            message: 'AI正在深度分析您的需求...',
            description: '理解书籍主题、目标读者和内容要求',
            progress: 25,
            timeLeft: '12-18秒'
        },
        {
            message: 'AI正在智能构建大纲结构...',
            description: '设计章节层次，规划内容框架',
            progress: 50,
            timeLeft: '8-12秒'
        },
        {
            message: 'AI正在生成详细内容...',
            description: '创建章节标题和详细描述',
            progress: 75,
            timeLeft: '3-6秒'
        },
        {
            message: '大纲生成完成，正在优化...',
            description: '检查结构完整性，优化内容质量',
            progress: 100,
            timeLeft: '即将完成'
        }
    ];

    // 更新步骤状态
    steps.forEach((stepEl, index) => {
        stepEl.classList.remove('active', 'completed');

        if (index < step) {
            stepEl.classList.add('completed');
        } else if (index === step) {
            stepEl.classList.add('active');
        }
    });

    // 更新进度条
    if (progressFill && stepData[step]) {
        progressFill.style.width = stepData[step].progress + '%';
    }

    // 更新主要信息
    const progressIndicator = document.querySelector('.progress-indicator h3');
    const progressDescription = document.querySelector('.progress-indicator p');

    if (progressIndicator && stepData[step]) {
        progressIndicator.textContent = stepData[step].message;
    }

    if (progressDescription && stepData[step]) {
        progressDescription.textContent = stepData[step].description;
    }

    // 更新时间估计
    if (timeRemaining && stepData[step]) {
        timeRemaining.textContent = stepData[step].timeLeft;
    }

    // 添加步骤完成动画
    if (step > 0) {
        const completedStep = steps[step - 1];
        if (completedStep) {
            completedStep.style.animation = 'slideIn 0.5s ease';
        }
    }
}

// 显示AI大纲结果（需要手动处理的情况）
function showAIOutlineResult(rawResponse, message) {
    showModal('AI大纲生成结果', `
        <div class="ai-result-container">
            <div class="result-message">
                <h4><i class="fas fa-info-circle"></i> ${message || '需要手动调整'}</h4>
                <p>AI已生成大纲内容，请查看并手动导入：</p>
            </div>

            <div class="result-content">
                <label>AI生成的大纲内容：</label>
                <textarea id="ai-generated-content" class="form-textarea" rows="15" readonly>${rawResponse}</textarea>
            </div>

            <div class="result-actions">
                <button class="btn btn-sm btn-secondary" onclick="copyAIResult()">
                    <i class="fas fa-copy"></i> 复制内容
                </button>
                <button class="btn btn-sm btn-info" onclick="showOutlineImportHelp()">
                    <i class="fas fa-question-circle"></i> 导入帮助
                </button>
            </div>
        </div>
    `, [
        {
            text: '手动导入',
            className: 'btn-primary',
            onclick: () => {
                closeModal();
                showPanel('outline');
                showNotification('请使用"导入大纲"功能手动导入AI生成的内容', 'info');
            }
        },
        {
            text: '关闭',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 应用生成的大纲
async function applyGeneratedOutline(outline) {
    try {
        // 更新项目信息
        if (outline.title) {
            currentProject.title = outline.title;
        }
        if (outline.description) {
            currentProject.description = outline.description;
        }

        // 转换大纲格式
        if (outline.outline && Array.isArray(outline.outline)) {
            currentProject.outline = convertAIOutlineFormat(outline.outline);
        }

        // 保存到本地存储
        saveProjectToStorage();

        // 尝试保存到数据库
        await safelySaveOutlineToServer(currentProject.outline);

        // 刷新显示
        renderOutlineTree();
        updateChapterSelector();

        // 更新项目概览
        if (typeof updateProjectOverview === 'function') {
            updateProjectOverview(currentProject);
        }

        // 切换到大纲面板
        showPanel('outline');

    } catch (error) {
        console.error('应用大纲失败:', error);
        showNotification('应用大纲失败: ' + error.message, 'error');
    }
}

// 转换AI大纲格式
function convertAIOutlineFormat(aiOutline) {
    const converted = [];

    function processItem(item, parentLevel = -1) {
        const convertedItem = {
            id: generateUUID(), // 使用UUID格式而不是字符串ID
            title: item.title || '未命名',
            description: item.description || '',
            level: item.level !== undefined ? item.level : parentLevel + 1,
            children: []
        };

        if (item.children && Array.isArray(item.children)) {
            item.children.forEach(child => {
                const childItem = processItem(child, convertedItem.level);
                convertedItem.children.push(childItem);
            });
        }

        return convertedItem;
    }

    aiOutline.forEach(item => {
        converted.push(processItem(item));
    });

    return converted;
}

// 生成唯一ID（用于大纲项目，可以是字符串格式）
function generateUniqueId() {
    return 'outline_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 生成UUID格式的ID（用于数据库记录）
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 邀请用户
function inviteUser() {
    showModal('邀请用户', `
        <div class="invite-user-form">
            <div class="form-group">
                <label>邮箱地址：</label>
                <input type="email" id="invite-email" class="form-input" placeholder="输入用户邮箱">
            </div>
            <div class="form-group">
                <label>角色权限：</label>
                <select id="invite-role" class="form-select">
                    <option value="author">作者</option>
                    <option value="editor">编辑者</option>
                    <option value="reviewer">审阅者</option>
                    <option value="viewer">查看者</option>
                </select>
            </div>
            <div class="form-group">
                <label>邀请消息：</label>
                <textarea id="invite-message" class="form-textarea" rows="3" placeholder="可选的邀请消息"></textarea>
            </div>
        </div>
    `, [
        {
            text: '发送邀请',
            className: 'btn-primary',
            onclick: () => {
                const email = document.getElementById('invite-email').value;
                const role = document.getElementById('invite-role').value;
                const message = document.getElementById('invite-message').value;

                if (email.trim()) {
                    showNotification(`邀请已发送给：${email}`, 'success');
                    closeModal();
                    loadCurrentMembers();
                } else {
                    showNotification('请输入邮箱地址', 'warning');
                }
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 移除成员
function removeMember(memberId) {
    showModal('确认移除', `
        <div class="confirm-remove">
            <p>确定要移除这个成员吗？</p>
            <p class="text-muted">移除后，该成员将无法访问此项目。</p>
        </div>
    `, [
        {
            text: '确认移除',
            className: 'btn-danger',
            onclick: () => {
                showNotification('成员已移除', 'success');
                closeModal();
                loadCurrentMembers();
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 复制AI结果
function copyAIResult() {
    const textarea = document.getElementById('ai-generated-content');
    if (textarea) {
        textarea.select();
        document.execCommand('copy');
        showNotification('内容已复制到剪贴板', 'success');
    }
}

// 显示导入帮助
function showOutlineImportHelp() {
    showNotification('您可以将AI生成的内容复制后，使用"导入大纲"功能中的"文本导入"选项', 'info');
}

// ============================================================================
// 缺失函数补充
// ============================================================================

// 显示用户资料
function showUserProfile() {
    showModal('用户资料', `
        <div class="user-profile-form">
            <div class="form-group">
                <label>用户名：</label>
                <input type="text" id="profile-username" class="form-input" value="<EMAIL>" readonly>
            </div>
            <div class="form-group">
                <label>显示名称：</label>
                <input type="text" id="profile-display-name" class="form-input" placeholder="输入显示名称">
            </div>
            <div class="form-group">
                <label>个人简介：</label>
                <textarea id="profile-bio" class="form-textarea" rows="3" placeholder="简单介绍一下自己"></textarea>
            </div>
            <div class="form-group">
                <label>专业领域：</label>
                <input type="text" id="profile-expertise" class="form-input" placeholder="如：人工智能、油气工程等">
            </div>
        </div>
    `, [
        {
            text: '保存',
            className: 'btn-primary',
            onclick: () => {
                showNotification('用户资料已保存', 'success');
                closeModal();
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 显示项目设置
function showProjectSettings() {
    if (!currentProject) {
        showNotification('请先选择一个项目', 'warning');
        return;
    }

    showModal('项目设置', `
        <div class="project-settings-form">
            <div class="form-group">
                <label>项目名称：</label>
                <input type="text" id="settings-project-title" class="form-input" value="${currentProject.title || ''}">
            </div>
            <div class="form-group">
                <label>项目描述：</label>
                <textarea id="settings-project-description" class="form-textarea" rows="3">${currentProject.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label>项目状态：</label>
                <select id="settings-project-status" class="form-select">
                    <option value="active">进行中</option>
                    <option value="completed">已完成</option>
                    <option value="paused">暂停</option>
                    <option value="archived">已归档</option>
                </select>
            </div>
            <div class="form-group">
                <label>项目类型：</label>
                <select id="settings-project-type" class="form-select">
                    <option value="book">书籍</option>
                    <option value="paper">论文</option>
                    <option value="report">报告</option>
                    <option value="manual">手册</option>
                </select>
            </div>
        </div>
    `, [
        {
            text: '保存设置',
            className: 'btn-primary',
            onclick: () => {
                const title = document.getElementById('settings-project-title').value;
                const description = document.getElementById('settings-project-description').value;
                const status = document.getElementById('settings-project-status').value;
                const type = document.getElementById('settings-project-type').value;

                if (title.trim()) {
                    currentProject.title = title;
                    currentProject.description = description;
                    currentProject.status = status;
                    currentProject.type = type;

                    saveProjectToStorage();
                    showNotification('项目设置已保存', 'success');
                    closeModal();

                    // 更新项目概览
                    if (typeof updateProjectOverview === 'function') {
                        updateProjectOverview(currentProject);
                    }
                } else {
                    showNotification('请输入项目名称', 'warning');
                }
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 显示用户管理
function showUserManagement() {
    showModal('用户管理', `
        <div class="user-management">
            <div class="section">
                <h3>当前用户</h3>
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name"><EMAIL></div>
                        <div class="user-role">管理员</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>账户设置</h3>
                <div class="settings-list">
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked> 接收邮件通知
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" checked> 自动保存草稿
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox"> 启用协作提醒
                        </label>
                    </div>
                </div>
            </div>
        </div>
    `, [
        {
            text: '保存',
            className: 'btn-primary',
            onclick: () => {
                showNotification('用户设置已保存', 'success');
                closeModal();
            }
        },
        {
            text: '关闭',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// 加载用户资料
function loadUserProfile() {
    // 模拟加载用户资料
    const userProfile = {
        username: '<EMAIL>',
        displayName: '用户',
        bio: '',
        expertise: '人工智能、大模型技术'
    };

    return userProfile;
}

// 加载当前成员
function loadCurrentMembers() {
    // 模拟加载项目成员
    const members = [
        {
            id: '1',
            name: '<EMAIL>',
            role: 'owner',
            joinedAt: new Date().toISOString()
        }
    ];

    // 更新成员列表显示
    const membersList = document.getElementById('current-members-list');
    if (membersList) {
        membersList.innerHTML = members.map(member => `
            <div class="member-item">
                <div class="member-info">
                    <span class="member-name">${member.name}</span>
                    <span class="member-role">${member.role}</span>
                </div>
                <div class="member-actions">
                    <button onclick="changeMemberRole('${member.id}')" class="btn btn-sm">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="removeMember('${member.id}')" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    return members;
}

// 修改成员角色
function changeMemberRole(memberId) {
    showModal('修改角色', `
        <div class="role-change-form">
            <div class="form-group">
                <label>选择新角色：</label>
                <select id="new-member-role" class="form-select">
                    <option value="author">作者</option>
                    <option value="editor">编辑者</option>
                    <option value="reviewer">审阅者</option>
                    <option value="viewer">查看者</option>
                </select>
            </div>
        </div>
    `, [
        {
            text: '确认修改',
            className: 'btn-primary',
            onclick: () => {
                const newRole = document.getElementById('new-member-role').value;
                showNotification(`角色已修改为：${newRole}`, 'success');
                closeModal();
                loadCurrentMembers();
            }
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);
}

// ============================================
// 数据库健康检查和清理功能
// ============================================

// 检查数据库健康状态
async function checkDatabaseHealth() {
    if (!supabaseManager || !supabaseManager.supabase) {
        return;
    }

    try {
        // 检查chapters表中outline_id为null的记录
        const { data: chaptersWithNullOutline, error } = await supabaseManager.supabase
            .from('chapters')
            .select('id, title, outline_id')
            .is('outline_id', null);

        if (error) {
            console.error('数据库健康检查失败:', error);
            return;
        }

        if (chaptersWithNullOutline && chaptersWithNullOutline.length > 0) {
            console.warn(`发现 ${chaptersWithNullOutline.length} 个章节的outline_id为null`);

            // 暂时禁用数据库清理建议，避免自动弹出模态框
            // setTimeout(() => {
            //     showDatabaseCleanupSuggestion(chaptersWithNullOutline.length);
            // }, 2000);
        }
    } catch (error) {
        console.error('数据库健康检查异常:', error);
    }
}

// 显示数据库清理建议
function showDatabaseCleanupSuggestion(problemCount) {
    const suggestion = document.createElement('div');
    suggestion.className = 'database-cleanup-suggestion';
    suggestion.innerHTML = `
        <div class="cleanup-notification">
            <div class="cleanup-icon">⚠️</div>
            <div class="cleanup-content">
                <h4>数据库需要清理</h4>
                <p>发现 ${problemCount} 个章节的outline_id为null，这可能影响章节内容的保存和加载。</p>
                <div class="cleanup-actions">
                    <button onclick="openDatabaseCleanupTool()" class="btn btn-primary btn-sm">
                        🛠️ 立即清理
                    </button>
                    <button onclick="dismissCleanupSuggestion()" class="btn btn-secondary btn-sm">
                        稍后处理
                    </button>
                </div>
            </div>
            <button onclick="dismissCleanupSuggestion()" class="cleanup-close">×</button>
        </div>
    `;

    // 添加样式
    if (!document.getElementById('cleanup-styles')) {
        const styles = document.createElement('style');
        styles.id = 'cleanup-styles';
        styles.textContent = `
            .database-cleanup-suggestion {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
            }

            .cleanup-notification {
                background: #fff;
                border: 1px solid #f59e0b;
                border-left: 4px solid #f59e0b;
                border-radius: 8px;
                padding: 16px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                max-width: 350px;
                position: relative;
            }

            .cleanup-notification h4 {
                margin: 0 0 8px 0;
                color: #92400e;
                font-size: 16px;
            }

            .cleanup-notification p {
                margin: 0 0 12px 0;
                color: #6b7280;
                font-size: 14px;
                line-height: 1.4;
            }

            .cleanup-actions {
                display: flex;
                gap: 8px;
            }

            .cleanup-close {
                position: absolute;
                top: 8px;
                right: 8px;
                background: none;
                border: none;
                font-size: 18px;
                color: #6b7280;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .cleanup-close:hover {
                color: #374151;
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    document.body.appendChild(suggestion);

    // 10秒后自动消失
    setTimeout(() => {
        dismissCleanupSuggestion();
    }, 10000);
}

// 关闭清理建议
function dismissCleanupSuggestion() {
    const suggestion = document.querySelector('.database-cleanup-suggestion');
    if (suggestion) {
        suggestion.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            suggestion.remove();
        }, 300);
    }
}

// 打开数据库清理工具
function openDatabaseCleanupTool() {
    dismissCleanupSuggestion();

    showModal('数据库清理工具', `
        <div class="database-cleanup-modal">
            <div class="cleanup-header">
                <p>此工具将修复chapters表中outline_id为null的问题，确保章节内容能够正确保存和加载。</p>
            </div>

            <div class="cleanup-status" id="cleanupStatus">
                <div class="status-item">
                    <span class="status-label">总章节数：</span>
                    <span class="status-value" id="totalChapters">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">需要修复：</span>
                    <span class="status-value" id="problemChapters">检查中...</span>
                </div>
            </div>

            <div class="cleanup-progress" id="cleanupProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备中...</div>
            </div>

            <div class="cleanup-log" id="cleanupLog" style="display: none;">
                <h4>清理日志：</h4>
                <div class="log-content" id="logContent"></div>
            </div>
        </div>
    `, [
        {
            text: '开始清理',
            className: 'btn-primary',
            id: 'startCleanupBtn',
            onclick: startDatabaseCleanup
        },
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: closeModal
        }
    ]);

    // 添加清理工具样式
    if (!document.getElementById('cleanup-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'cleanup-modal-styles';
        styles.textContent = `
            .database-cleanup-modal {
                min-width: 500px;
            }

            .cleanup-header {
                background: #f8fafc;
                padding: 16px;
                border-radius: 6px;
                margin-bottom: 20px;
            }

            .cleanup-header p {
                margin: 0;
                color: #4b5563;
                line-height: 1.5;
            }

            .cleanup-status {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 16px;
                margin-bottom: 20px;
            }

            .status-item {
                background: #f9fafb;
                padding: 12px;
                border-radius: 6px;
                border: 1px solid #e5e7eb;
            }

            .status-label {
                display: block;
                font-size: 14px;
                color: #6b7280;
                margin-bottom: 4px;
            }

            .status-value {
                display: block;
                font-size: 18px;
                font-weight: 600;
                color: #1f2937;
            }

            .cleanup-progress {
                margin-bottom: 20px;
            }

            .progress-bar {
                width: 100%;
                height: 8px;
                background: #e5e7eb;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 8px;
            }

            .progress-fill {
                height: 100%;
                background: #3b82f6;
                width: 0%;
                transition: width 0.3s ease;
            }

            .progress-text {
                font-size: 14px;
                color: #6b7280;
                text-align: center;
            }

            .cleanup-log {
                max-height: 200px;
                overflow-y: auto;
            }

            .cleanup-log h4 {
                margin: 0 0 12px 0;
                font-size: 16px;
                color: #1f2937;
            }

            .log-content {
                background: #1f2937;
                color: #e5e7eb;
                padding: 12px;
                border-radius: 6px;
                font-family: 'Courier New', monospace;
                font-size: 13px;
                line-height: 1.4;
            }

            .log-entry {
                margin-bottom: 4px;
            }

            .log-entry.success {
                color: #10b981;
            }

            .log-entry.error {
                color: #ef4444;
            }

            .log-entry.warning {
                color: #f59e0b;
            }
        `;
        document.head.appendChild(styles);
    }

    // 立即检查数据状态
    checkCleanupStatus();
}

// 检查清理状态
async function checkCleanupStatus() {
    try {
        const { data: allChapters } = await supabaseManager.supabase
            .from('chapters')
            .select('id, outline_id');

        const { data: problemChapters } = await supabaseManager.supabase
            .from('chapters')
            .select('id')
            .is('outline_id', null);

        const totalCount = allChapters?.length || 0;
        const problemCount = problemChapters?.length || 0;

        document.getElementById('totalChapters').textContent = totalCount;
        document.getElementById('problemChapters').textContent = problemCount;

        // 如果没有问题，禁用清理按钮
        if (problemCount === 0) {
            const startBtn = document.getElementById('startCleanupBtn');
            if (startBtn) {
                startBtn.textContent = '无需清理';
                startBtn.disabled = true;
                startBtn.className = 'btn-secondary';
            }
        }

    } catch (error) {
        console.error('检查清理状态失败:', error);
        document.getElementById('totalChapters').textContent = '检查失败';
        document.getElementById('problemChapters').textContent = '检查失败';
    }
}

// 开始数据库清理
async function startDatabaseCleanup() {
    const startBtn = document.getElementById('startCleanupBtn');
    const progressContainer = document.getElementById('cleanupProgress');
    const logContainer = document.getElementById('cleanupLog');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const logContent = document.getElementById('logContent');

    // 检查必要的DOM元素是否存在
    if (!startBtn) {
        console.error('❌ 清理按钮未找到');
        showNotification('清理工具界面未正确加载', 'error');
        return;
    }

    // 禁用按钮并显示进度
    startBtn.disabled = true;
    startBtn.textContent = '清理中...';

    if (progressContainer) progressContainer.style.display = 'block';
    if (logContainer) logContainer.style.display = 'block';

    const logs = [];

    function addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        logs.push(`<div class="log-entry ${type}">[${timestamp}] ${message}</div>`);
        if (logContent) {
            logContent.innerHTML = logs.join('');
            logContent.scrollTop = logContent.scrollHeight;
        }
        // 同时输出到控制台
        console.log(`[${timestamp}] ${message}`);
    }

    function updateProgress(percentage, text) {
        if (progressFill) progressFill.style.width = percentage + '%';
        if (progressText) progressText.textContent = text;
        console.log(`进度: ${percentage}% - ${text}`);
    }

    try {
        addLog('开始数据库清理...', 'info');
        updateProgress(10, '分析数据...');

        // 第一步：获取需要修复的章节
        const { data: chaptersWithoutOutline } = await supabaseManager.supabase
            .from('chapters')
            .select('id, title, project_id, created_by, created_at, updated_at')
            .is('outline_id', null);

        if (!chaptersWithoutOutline || chaptersWithoutOutline.length === 0) {
            addLog('没有需要修复的章节', 'success');
            updateProgress(100, '清理完成');
            startBtn.textContent = '清理完成';
            return;
        }

        addLog(`找到 ${chaptersWithoutOutline.length} 个需要修复的章节`, 'info');
        updateProgress(20, '获取现有大纲...');

        // 第二步：获取所有现有大纲
        const { data: existingOutlines } = await supabaseManager.supabase
            .from('outlines')
            .select('id, title, project_id, level, sort_order');

        addLog(`找到 ${existingOutlines?.length || 0} 个现有大纲`, 'info');
        updateProgress(30, '开始修复关联...');

        let fixedCount = 0;
        let createdOutlines = 0;
        const totalChapters = chaptersWithoutOutline.length;

        // 第三步：逐个修复章节
        for (let i = 0; i < chaptersWithoutOutline.length; i++) {
            const chapter = chaptersWithoutOutline[i];
            const progressPercentage = 30 + (i / totalChapters) * 50;

            updateProgress(progressPercentage, `修复章节 ${i + 1}/${totalChapters}...`);

            try {
                // 尝试匹配现有大纲
                const matchingOutline = existingOutlines?.find(o =>
                    o.title === chapter.title &&
                    o.project_id === chapter.project_id &&
                    o.level > 0
                );

                if (matchingOutline) {
                    // 更新章节的outline_id
                    const { error } = await supabaseManager.supabase
                        .from('chapters')
                        .update({ outline_id: matchingOutline.id })
                        .eq('id', chapter.id);

                    if (!error) {
                        fixedCount++;
                        addLog(`✅ 修复章节 "${chapter.title}" 的outline_id`, 'success');
                    } else {
                        addLog(`❌ 修复章节 "${chapter.title}" 失败: ${error.message}`, 'error');
                    }
                } else {
                    // 创建新的大纲记录
                    const { data: maxSortOrder } = await supabaseManager.supabase
                        .from('outlines')
                        .select('sort_order')
                        .eq('project_id', chapter.project_id)
                        .order('sort_order', { ascending: false })
                        .limit(1);

                    const nextSortOrder = (maxSortOrder?.[0]?.sort_order || 0) + 1;

                    const { data: newOutline, error: createError } = await supabaseManager.supabase
                        .from('outlines')
                        .insert({
                            project_id: chapter.project_id,
                            title: chapter.title,
                            level: 1,
                            sort_order: nextSortOrder,
                            description: '自动生成的大纲项',
                            status: 'planned',
                            created_by: chapter.created_by
                        })
                        .select()
                        .single();

                    if (!createError && newOutline) {
                        // 更新章节的outline_id
                        const { error: updateError } = await supabaseManager.supabase
                            .from('chapters')
                            .update({ outline_id: newOutline.id })
                            .eq('id', chapter.id);

                        if (!updateError) {
                            createdOutlines++;
                            fixedCount++;
                            addLog(`🆕 为章节 "${chapter.title}" 创建新大纲并关联`, 'success');

                            // 将新大纲添加到现有大纲列表中，以便后续章节可能匹配
                            existingOutlines.push(newOutline);
                        } else {
                            addLog(`❌ 关联新大纲失败: ${updateError.message}`, 'error');
                        }
                    } else {
                        addLog(`❌ 创建大纲失败: ${createError?.message}`, 'error');
                    }
                }

                // 短暂延迟，避免请求过快
                await new Promise(resolve => setTimeout(resolve, 100));

            } catch (error) {
                addLog(`❌ 处理章节 "${chapter.title}" 时出错: ${error.message}`, 'error');
            }
        }

        updateProgress(85, '修复数据一致性...');

        // 第四步：修复数据一致性
        try {
            // 修复章节状态
            await supabaseManager.supabase
                .from('chapters')
                .update({ status: 'draft' })
                .or('status.is.null,status.eq.');

            // 修复字数统计
            await supabaseManager.supabase
                .from('chapters')
                .update({ word_count: 0 })
                .is('word_count', null);

            addLog('✅ 数据一致性修复完成', 'success');
        } catch (error) {
            addLog(`⚠️ 数据一致性修复部分失败: ${error.message}`, 'warning');
        }

        updateProgress(95, '验证结果...');

        // 第五步：验证结果
        const { data: remainingProblems } = await supabaseManager.supabase
            .from('chapters')
            .select('id')
            .is('outline_id', null);

        const remainingCount = remainingProblems?.length || 0;

        updateProgress(100, '清理完成');

        // 显示最终结果
        addLog('', 'info');
        addLog('🎉 数据库清理完成！', 'success');
        addLog(`📊 修复了 ${fixedCount} 个章节关联`, 'success');
        addLog(`🆕 创建了 ${createdOutlines} 个新大纲`, 'success');

        if (remainingCount === 0) {
            addLog('✅ 所有章节的outline_id都已修复', 'success');
        } else {
            addLog(`⚠️ 仍有 ${remainingCount} 个章节需要手动处理`, 'warning');
        }

        // 更新状态显示
        await checkCleanupStatus();

        // 显示成功通知
        showNotification(`数据库清理完成！修复了 ${fixedCount} 个章节关联`, 'success');

        startBtn.textContent = '清理完成';
        startBtn.className = 'btn-success';

    } catch (error) {
        addLog(`❌ 数据库清理失败: ${error.message}`, 'error');
        showNotification('数据库清理失败: ' + error.message, 'error');

        startBtn.disabled = false;
        startBtn.textContent = '重试清理';
        startBtn.className = 'btn-primary';
    }
}

// 简化的数据库清理函数，可以直接在控制台调用
async function quickDatabaseCleanup() {
    console.log('🚀 开始快速数据库清理...');

    if (!supabaseManager || !supabaseManager.supabase) {
        console.error('❌ Supabase客户端未初始化');
        return false;
    }

    try {
        // 第一步：获取需要修复的章节
        console.log('🔍 查找需要修复的章节...');
        const { data: chaptersWithoutOutline, error: queryError } = await supabaseManager.supabase
            .from('chapters')
            .select('id, title, project_id, created_by, created_at, updated_at')
            .is('outline_id', null);

        if (queryError) {
            console.error('❌ 查询失败:', queryError);
            return false;
        }

        if (!chaptersWithoutOutline || chaptersWithoutOutline.length === 0) {
            console.log('✅ 没有需要修复的章节');
            return true;
        }

        console.log(`📊 找到 ${chaptersWithoutOutline.length} 个需要修复的章节`);

        // 第二步：获取所有现有大纲
        const { data: existingOutlines } = await supabaseManager.supabase
            .from('outlines')
            .select('id, title, project_id, level, sort_order');

        console.log(`📋 找到 ${existingOutlines?.length || 0} 个现有大纲`);

        let fixedCount = 0;
        let createdOutlines = 0;

        // 第三步：逐个修复章节
        for (let i = 0; i < chaptersWithoutOutline.length; i++) {
            const chapter = chaptersWithoutOutline[i];
            console.log(`🔧 修复章节 ${i + 1}/${chaptersWithoutOutline.length}: "${chapter.title}"`);

            try {
                // 尝试匹配现有大纲
                const matchingOutline = existingOutlines?.find(o =>
                    o.title === chapter.title &&
                    o.project_id === chapter.project_id &&
                    o.level > 0
                );

                if (matchingOutline) {
                    // 更新章节的outline_id
                    const { error } = await supabaseManager.supabase
                        .from('chapters')
                        .update({ outline_id: matchingOutline.id })
                        .eq('id', chapter.id);

                    if (!error) {
                        fixedCount++;
                        console.log(`✅ 修复章节 "${chapter.title}" 的outline_id`);
                    } else {
                        console.error(`❌ 修复章节 "${chapter.title}" 失败:`, error);
                    }
                } else {
                    // 创建新的大纲记录
                    const { data: maxSortOrder } = await supabaseManager.supabase
                        .from('outlines')
                        .select('sort_order')
                        .eq('project_id', chapter.project_id)
                        .order('sort_order', { ascending: false })
                        .limit(1);

                    const nextSortOrder = (maxSortOrder?.[0]?.sort_order || 0) + 1;

                    const { data: newOutline, error: createError } = await supabaseManager.supabase
                        .from('outlines')
                        .insert({
                            project_id: chapter.project_id,
                            title: chapter.title,
                            level: 1,
                            sort_order: nextSortOrder,
                            description: '自动生成的大纲项',
                            status: 'planned',
                            created_by: chapter.created_by
                        })
                        .select()
                        .single();

                    if (!createError && newOutline) {
                        // 更新章节的outline_id
                        const { error: updateError } = await supabaseManager.supabase
                            .from('chapters')
                            .update({ outline_id: newOutline.id })
                            .eq('id', chapter.id);

                        if (!updateError) {
                            createdOutlines++;
                            fixedCount++;
                            console.log(`🆕 为章节 "${chapter.title}" 创建新大纲并关联`);

                            // 将新大纲添加到现有大纲列表中
                            existingOutlines.push(newOutline);
                        } else {
                            console.error(`❌ 关联新大纲失败:`, updateError);
                        }
                    } else {
                        console.error(`❌ 创建大纲失败:`, createError);
                    }
                }

                // 短暂延迟
                await new Promise(resolve => setTimeout(resolve, 100));

            } catch (error) {
                console.error(`❌ 处理章节 "${chapter.title}" 时出错:`, error);
            }
        }

        // 验证结果
        const { data: remainingProblems } = await supabaseManager.supabase
            .from('chapters')
            .select('id')
            .is('outline_id', null);

        const remainingCount = remainingProblems?.length || 0;

        console.log('🎉 数据库清理完成！');
        console.log(`📊 修复了 ${fixedCount} 个章节关联`);
        console.log(`🆕 创建了 ${createdOutlines} 个新大纲`);

        if (remainingCount === 0) {
            console.log('✅ 所有章节的outline_id都已修复');
        } else {
            console.log(`⚠️ 仍有 ${remainingCount} 个章节需要手动处理`);
        }

        return remainingCount === 0;

    } catch (error) {
        console.error('❌ 数据库清理失败:', error);
        return false;
    }
}

// 将函数暴露到全局作用域，方便控制台调用
window.quickDatabaseCleanup = quickDatabaseCleanup;

// 调试函数：检查章节加载状态
async function debugChapterLoading(outlineId) {
    console.log('🔍 开始调试章节加载...');
    console.log('📝 大纲ID:', outlineId);

    try {
        // 1. 检查项目ID
        const projectId = await ensureProjectId();
        console.log('📋 项目ID:', projectId);

        // 2. 查找章节ID
        const chapterId = await findChapterIdByOutlineId(outlineId);
        console.log('📝 章节ID:', chapterId);

        if (!chapterId) {
            console.log('❌ 未找到章节ID，可能需要创建新章节');
            return;
        }

        // 3. 从数据库加载章节
        const chapterData = await loadChapterFromServer(chapterId);
        console.log('📊 章节数据:', chapterData);

        // 4. 检查数据库中的原始记录
        const { data: rawChapter } = await supabaseManager.supabase
            .from('chapters')
            .select('*')
            .eq('id', chapterId)
            .single();

        console.log('🗄️ 数据库原始记录:', rawChapter);

        return {
            projectId,
            chapterId,
            chapterData,
            rawChapter
        };

    } catch (error) {
        console.error('❌ 调试失败:', error);
        return null;
    }
}

// 暴露调试函数
window.debugChapterLoading = debugChapterLoading;

// ==================== AI助手功能 ====================

// 设置AI助手事件监听器
function setupAIAssistantListeners() {
    // 监听AI助手菜单项点击
    document.addEventListener('click', function(e) {
        if (e.target.closest('.fab-menu-item')) {
            const action = e.target.closest('.fab-menu-item').getAttribute('data-action');
            handleAIAction(action);
        }
    });
}

// 处理AI操作
async function handleAIAction(action) {
    // 检查是否在编辑器面板
    if (!quillEditor) {
        showNotification('请先进入章节编写模式', 'warning');
        return;
    }

    // 获取选中的文本
    const selection = quillEditor.getSelection();
    let selectedText = '';

    if (selection && selection.length > 0) {
        selectedText = quillEditor.getText(selection.index, selection.length);
    } else {
        // 如果没有选中文本，获取全部内容
        selectedText = quillEditor.getText();
    }

    if (!selectedText.trim()) {
        showNotification('请先选择要处理的文本内容', 'warning');
        return;
    }

    // 显示AI交互对话框
    showAIDialog(action, selectedText);
}

// 生成标准对话框HTML
function generateStandardDialogHTML(config, selectedText) {
    return `
        <div class="ai-dialog-header">
            <div class="ai-dialog-icon">
                <i class="${config.icon}"></i>
            </div>
            <div class="ai-dialog-title">
                <h3>${config.title}</h3>
                <p>${config.description}</p>
            </div>
        </div>
        <div class="ai-dialog-content">
            <div class="ai-selected-text">
                <h4>选中的内容：</h4>
                <div class="ai-selected-text-content">${selectedText}</div>
            </div>

            <div class="ai-input-section">
                <textarea class="ai-prompt-input" placeholder="${config.placeholder}"></textarea>

                <div class="ai-file-upload">
                    <label>上传相关文件（可选）：</label>
                    <input type="file" class="ai-file-input" multiple accept="image/*,.pdf,.doc,.docx,.txt">
                    <div class="ai-file-preview"></div>
                </div>
            </div>

            <div class="ai-response-section" style="display: none;">
                <h4>AI处理结果：</h4>
                <div class="ai-response-content"></div>
            </div>
        </div>
    `;
}

// 生成多媒体对话框HTML
function generateMultimediaDialogHTML(action, config, selectedText) {
    const commonHeader = `
        <div class="ai-dialog-header">
            <div class="ai-dialog-icon">
                <i class="${config.icon}"></i>
            </div>
            <div class="ai-dialog-title">
                <h3>${config.title}</h3>
                <p>${config.description}</p>
            </div>
        </div>
    `;

    switch (action) {
        case 'generate-image':
            return commonHeader + `
                <div class="ai-dialog-content">
                    <div class="multimedia-input-section">
                        <div class="input-group">
                            <label>图片描述：</label>
                            <textarea class="ai-prompt-input" placeholder="${config.placeholder}" rows="3"></textarea>
                        </div>

                        <div class="image-options">
                            <div class="option-row">
                                <div class="option-group">
                                    <label>尺寸：</label>
                                    <select class="image-size-select">
                                        <option value="1024x1024">正方形 (1024×1024)</option>
                                        <option value="1280x720">横屏 (1280×720)</option>
                                        <option value="720x1280">竖屏 (720×1280)</option>
                                        <option value="1920x1080">高清横屏 (1920×1080)</option>
                                    </select>
                                </div>
                                <div class="option-group">
                                    <label>模型：</label>
                                    <select class="image-model-select">
                                        <option value="flux">Flux (推荐)</option>
                                        <option value="turbo">Turbo (快速)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="option-row">
                                <label class="checkbox-label">
                                    <input type="checkbox" class="enhance-prompt"> 增强提示词
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" class="safe-mode" checked> 安全模式
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="ai-response-section" style="display: none;">
                        <h4>生成的图片：</h4>
                        <div class="ai-response-content">
                            <div class="generated-image-container">
                                <img class="generated-image" style="max-width: 100%; border-radius: 8px;">
                                <div class="image-actions">
                                    <button class="btn btn-secondary download-image">
                                        <i class="fas fa-download"></i> 下载图片
                                    </button>
                                    <button class="btn btn-secondary copy-image">
                                        <i class="fas fa-copy"></i> 复制图片
                                    </button>
                                    <button class="btn btn-primary insert-image">
                                        <i class="fas fa-plus"></i> 插入到编辑器
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        case 'text-to-speech':
            return commonHeader + `
                <div class="ai-dialog-content">
                    <div class="ai-selected-text">
                        <h4>要播放的文本：</h4>
                        <div class="ai-selected-text-content">${selectedText || '请在编辑器中选择文本'}</div>
                    </div>

                    <div class="multimedia-input-section">
                        <div class="voice-options">
                            <div class="option-group">
                                <label>语音选择：</label>
                                <select class="voice-select">
                                    <option value="nova">Nova (女声)</option>
                                    <option value="alloy">Alloy (中性)</option>
                                    <option value="echo">Echo (男声)</option>
                                    <option value="fable">Fable (英式)</option>
                                    <option value="onyx">Onyx (深沉)</option>
                                    <option value="shimmer">Shimmer (温和)</option>
                                </select>
                            </div>
                        </div>

                        <div class="custom-text-section" style="display: none;">
                            <label>自定义文本：</label>
                            <textarea class="custom-text-input" placeholder="输入要转换为语音的文本..." rows="3"></textarea>
                        </div>

                        <label class="checkbox-label">
                            <input type="checkbox" class="use-custom-text"> 使用自定义文本
                        </label>
                    </div>

                    <div class="ai-response-section" style="display: none;">
                        <h4>生成的语音：</h4>
                        <div class="ai-response-content">
                            <div class="audio-player-container">
                                <audio class="generated-audio" controls style="width: 100%; margin-bottom: 1rem;"></audio>
                                <div class="audio-actions">
                                    <button class="btn btn-secondary download-audio">
                                        <i class="fas fa-download"></i> 下载音频
                                    </button>
                                    <button class="btn btn-secondary copy-audio">
                                        <i class="fas fa-copy"></i> 复制音频
                                    </button>
                                    <button class="btn btn-primary play-audio">
                                        <i class="fas fa-play"></i> 播放
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        case 'speech-to-text':
            return commonHeader + `
                <div class="ai-dialog-content">
                    <div class="multimedia-input-section">
                        <div class="feature-notice">
                            <div class="notice-content">
                                <i class="fas fa-info-circle"></i>
                                <div class="notice-text">
                                    <strong>语音转文字功能说明：</strong>
                                    <ul>
                                        <li>支持WAV、MP3、M4A、OGG格式音频文件</li>
                                        <li>文件大小建议小于25MB</li>
                                        <li>该功能可能需要API密钥或暂时不可用</li>
                                        <li>如遇到问题，请在系统设置中配置API密钥</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="recording-section">
                            <div class="recording-controls">
                                <button class="btn btn-primary start-recording">
                                    <i class="fas fa-microphone"></i> 开始录音
                                </button>
                                <button class="btn btn-danger stop-recording" style="display: none;">
                                    <i class="fas fa-stop"></i> 停止录音
                                </button>
                                <div class="recording-status"></div>
                            </div>

                            <div class="audio-upload">
                                <label>或上传音频文件：</label>
                                <input type="file" class="audio-file-input" accept="audio/*,.mp3,.wav,.m4a,.ogg">
                                <div class="audio-preview"></div>
                            </div>
                        </div>
                    </div>

                    <div class="ai-response-section" style="display: none;">
                        <h4>转录结果：</h4>
                        <div class="ai-response-content">
                            <div class="transcription-result">
                                <textarea class="transcription-text" readonly rows="5"></textarea>
                                <div class="transcription-actions">
                                    <button class="btn btn-secondary copy-transcription">复制文本</button>
                                    <button class="btn btn-primary insert-transcription">插入到编辑器</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        case 'analyze-image':
            return commonHeader + `
                <div class="ai-dialog-content">
                    <div class="multimedia-input-section">
                        <div class="image-upload-section">
                            <div class="image-upload">
                                <label>上传图片：</label>
                                <input type="file" class="image-file-input" accept="image/*">
                                <div class="image-preview"></div>
                            </div>

                            <div class="image-url-section">
                                <label>或输入图片URL：</label>
                                <input type="url" class="image-url-input" placeholder="https://example.com/image.jpg">
                            </div>
                        </div>

                        <div class="analysis-options">
                            <label>分析问题：</label>
                            <textarea class="ai-prompt-input" placeholder="${config.placeholder}" rows="2"></textarea>

                            <div class="quick-questions">
                                <button class="btn btn-sm btn-outline quick-question" data-question="请详细描述这张图片的内容">描述图片</button>
                                <button class="btn btn-sm btn-outline quick-question" data-question="这张图片中有什么文字？">识别文字</button>
                                <button class="btn btn-sm btn-outline quick-question" data-question="这张图片的主要颜色和风格是什么？">分析风格</button>
                                <button class="btn btn-sm btn-outline quick-question" data-question="这张图片适合用在什么场景？">使用建议</button>
                            </div>
                        </div>
                    </div>

                    <div class="ai-response-section" style="display: none;">
                        <h4>分析结果：</h4>
                        <div class="ai-response-content">
                            <div class="analysis-result">
                                <div class="analysis-text"></div>
                                <div class="analysis-actions">
                                    <button class="btn btn-secondary copy-analysis">复制分析</button>
                                    <button class="btn btn-primary insert-analysis">插入到编辑器</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

        default:
            return generateStandardDialogHTML(config, selectedText);
    }
}

// 显示AI交互对话框
function showAIDialog(action, selectedText) {
    const actionConfig = {
        polish: {
            title: '内容润色',
            description: '优化文本表达，提升可读性和流畅性',
            icon: 'fas fa-magic',
            placeholder: '请描述您的润色要求（可选）...'
        },
        translate: {
            title: '内容翻译',
            description: '将内容翻译为指定语言',
            icon: 'fas fa-language',
            placeholder: '请指定目标语言和特殊要求...'
        },
        explain: {
            title: '内容解读',
            description: '深入解析内容的含义和背景',
            icon: 'fas fa-lightbulb',
            placeholder: '请描述您希望了解的方面（可选）...'
        },
        rewrite: {
            title: '内容重写',
            description: '用不同风格重新组织和表达内容',
            icon: 'fas fa-edit',
            placeholder: '请指定重写风格和要求...'
        },
        'generate-image': {
            title: '文生图',
            description: '根据文本描述生成图片',
            icon: 'fas fa-image',
            placeholder: '请描述您想要生成的图片...',
            type: 'multimedia'
        },
        'text-to-speech': {
            title: '文本播放',
            description: '将选中的文本转换为语音播放',
            icon: 'fas fa-volume-up',
            placeholder: '选择语音和播放选项...',
            type: 'multimedia'
        },
        'speech-to-text': {
            title: '语音转文字',
            description: '将语音录音转换为文字',
            icon: 'fas fa-microphone',
            placeholder: '点击开始录音或上传音频文件...',
            type: 'multimedia'
        },
        'analyze-image': {
            title: '图片识别',
            description: '分析和识别图片内容',
            icon: 'fas fa-eye',
            placeholder: '请描述您想了解图片的哪些方面...',
            type: 'multimedia'
        }
    };

    const config = actionConfig[action];
    if (!config) return;

    // 为多媒体功能生成不同的对话框内容
    let dialogHTML;

    if (config.type === 'multimedia') {
        dialogHTML = generateMultimediaDialogHTML(action, config, selectedText);
    } else {
        dialogHTML = generateStandardDialogHTML(config, selectedText);
    }

    // 为多媒体功能配置不同的按钮
    let buttons;
    if (config.type === 'multimedia') {
        buttons = getMultimediaButtons(action);
    } else {
        buttons = [
            {
                text: '取消',
                className: 'btn-secondary',
                onclick: () => closeModal()
            },
            {
                text: '开始处理',
                className: 'btn-primary',
                onclick: () => processAIRequest(action, selectedText)
            },
            {
                text: '应用结果',
                className: 'btn-success',
                id: 'apply-ai-result',
                style: 'display: none;',
                onclick: () => applyAIResult()
            }
        ];
    }

    showModal({
        title: `AI助手 - ${config.title}`,
        content: dialogHTML,
        className: 'ai-dialog multimedia-dialog',
        buttons: buttons
    });

    // 设置监听器
    if (config.type === 'multimedia') {
        setupMultimediaListeners(action);
    } else {
        setupFileUploadListener();
    }
}

// 获取多媒体功能的按钮配置
function getMultimediaButtons(action) {
    const commonButtons = [
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: () => closeModal()
        }
    ];

    switch (action) {
        case 'generate-image':
            return [
                ...commonButtons,
                {
                    text: '生成图片',
                    className: 'btn-primary',
                    onclick: () => processImageGeneration()
                }
            ];

        case 'text-to-speech':
            return [
                ...commonButtons,
                {
                    text: '生成语音',
                    className: 'btn-primary',
                    onclick: () => processTextToSpeech()
                }
            ];

        case 'speech-to-text':
            return [
                ...commonButtons,
                {
                    text: '开始转录',
                    className: 'btn-primary',
                    onclick: () => processSpeechToText()
                }
            ];

        case 'analyze-image':
            return [
                ...commonButtons,
                {
                    text: '分析图片',
                    className: 'btn-primary',
                    onclick: () => processImageAnalysis()
                }
            ];

        default:
            return commonButtons;
    }
}

// 设置多媒体功能的监听器
function setupMultimediaListeners(action) {
    switch (action) {
        case 'generate-image':
            setupImageGenerationListeners();
            break;
        case 'text-to-speech':
            setupTextToSpeechListeners();
            break;
        case 'speech-to-text':
            setupSpeechToTextListeners();
            break;
        case 'analyze-image':
            setupImageAnalysisListeners();
            break;
    }
}

// 设置文件上传监听器
function setupFileUploadListener() {
    const fileInput = document.querySelector('.ai-file-input');
    const filePreview = document.querySelector('.ai-file-preview');

    if (!fileInput || !filePreview) return;

    fileInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        updateFilePreview(files, filePreview);
    });
}

// 更新文件预览
function updateFilePreview(files, container) {
    container.innerHTML = '';

    files.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'ai-file-item';
        fileItem.innerHTML = `
            <i class="fas fa-file"></i>
            <span>${file.name}</span>
            <button type="button" onclick="removeFile(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        container.appendChild(fileItem);
    });
}

// 移除文件
function removeFile(index) {
    const fileInput = document.querySelector('.ai-file-input');
    const filePreview = document.querySelector('.ai-file-preview');

    if (!fileInput) return;

    const dt = new DataTransfer();
    const files = Array.from(fileInput.files);

    files.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    fileInput.files = dt.files;
    updateFilePreview(Array.from(dt.files), filePreview);
}

// 处理AI请求
async function processAIRequest(action, selectedText) {
    const promptInput = document.querySelector('.ai-prompt-input');
    const fileInput = document.querySelector('.ai-file-input');
    const responseSection = document.querySelector('.ai-response-section');
    const responseContent = document.querySelector('.ai-response-content');
    const applyButton = document.getElementById('apply-ai-result');

    if (!promptInput || !responseSection || !responseContent) return;

    const requirements = promptInput.value.trim();
    const files = Array.from(fileInput?.files || []);

    // 显示加载状态
    responseSection.style.display = 'block';
    responseContent.innerHTML = `
        <div class="ai-loading">
            <i class="fas fa-spinner"></i>
            <span>AI正在处理中，请稍候...</span>
        </div>
    `;

    try {
        let result;

        // 检查AI服务管理器是否可用
        if (typeof aiServiceManager === 'undefined') {
            throw new Error('AI服务管理器未初始化');
        }

        console.log('开始AI处理:', { action, selectedText: selectedText.substring(0, 100) + '...' });

        // 根据操作类型调用相应的AI服务
        switch (action) {
            case 'polish':
                result = await aiServiceManager.polishContent(selectedText, requirements);
                break;
            case 'translate':
                const targetLanguage = requirements || '英文';
                result = await aiServiceManager.translateContent(selectedText, targetLanguage);
                break;
            case 'explain':
                result = await aiServiceManager.explainContent(selectedText, requirements);
                break;
            case 'rewrite':
                const style = requirements || '学术风格';
                result = await aiServiceManager.rewriteContent(selectedText, style);
                break;
            default:
                throw new Error('未知的操作类型');
        }

        console.log('AI处理结果:', result);

        if (result && result.success) {
            responseContent.textContent = result.result;
            applyButton.style.display = 'inline-block';

            // 存储结果用于应用
            window.currentAIResult = {
                action,
                originalText: selectedText,
                processedText: result.result
            };
        } else {
            const errorMessage = result?.message || '处理失败，请稍后重试';
            responseContent.innerHTML = `
                <div style="color: #ef4444;">
                    <i class="fas fa-exclamation-triangle"></i>
                    处理失败: ${errorMessage}
                </div>
            `;
        }
    } catch (error) {
        console.error('AI处理错误:', error);

        // 演示模式：如果AI服务不可用，显示模拟结果
        const demoResults = {
            polish: `经过AI润色后的内容：

${selectedText.replace(/。/g, '，表现出卓越的性能。').replace(/，/g, '，并且')}

这段内容已经过AI优化，提升了表达的流畅性和专业性。`,

            translate: `AI Translation Result:

Large Language Models (LLMs) refer to deep learning models with parameters ranging from tens of billions to hundreds of billions, typically built on the Transformer architecture. These models are pre-trained on massive text datasets to understand and generate human language, demonstrating powerful language comprehension and generation capabilities.`,

            explain: `AI深度解读：

**技术原理分析：**
大模型基于Transformer架构，通过自注意力机制实现对文本的深度理解。其核心在于：

1. **参数规模优势**：数十亿参数使模型能够捕捉复杂的语言模式
2. **预训练机制**：通过大规模无监督学习获得通用语言能力
3. **涌现能力**：规模达到临界点后展现出意外的智能行为

**应用前景：**
在石油天然气行业中，大模型可用于地质报告分析、设备故障诊断、多语言技术文档处理等场景，为传统能源行业带来智能化转型机遇。`,

            rewrite: `通俗易懂版本：

想象一下有一个超级聪明的"电脑大脑"，它就像一个读过世界上所有书籍的博士生。这个"大脑"（我们叫它大模型）里面有成千上万亿个"思维节点"，就像人脑中的神经元一样。

它是怎么变聪明的呢？就像小孩子通过大量阅读学会写作一样，这个"电脑大脑"通过"阅读"互联网上的海量文章、书籍和对话，学会了理解和生成人类语言。

现在，这个超级助手可以帮我们写文章、翻译语言、回答问题，就像有了一个24小时不休息的智能秘书！`
        };

        const demoResult = demoResults[action] || '演示结果：AI功能正常工作中...';

        responseContent.innerHTML = `
            <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px; padding: 1rem; margin: 1rem 0;">
                <div style="color: #0369a1; font-weight: 500; margin-bottom: 0.5rem;">
                    <i class="fas fa-info-circle"></i>
                    演示模式 - AI处理结果
                </div>
                <div style="white-space: pre-wrap; line-height: 1.6;">${demoResult}</div>
            </div>
        `;

        applyButton.style.display = 'inline-block';

        // 存储演示结果用于应用
        window.currentAIResult = {
            action,
            originalText: selectedText,
            processedText: demoResult
        };
    }
}

// 应用AI处理结果
function applyAIResult() {
    if (!window.currentAIResult || !quillEditor) {
        showNotification('没有可应用的结果', 'warning');
        return;
    }

    const { action, originalText, processedText } = window.currentAIResult;

    // 获取当前选择
    const selection = quillEditor.getSelection();

    if (selection && selection.length > 0) {
        // 替换选中的文本
        quillEditor.deleteText(selection.index, selection.length);
        quillEditor.insertText(selection.index, processedText);
        quillEditor.setSelection(selection.index, processedText.length);
    } else {
        // 如果没有选择，在光标位置插入
        const cursorPosition = selection ? selection.index : quillEditor.getLength();
        quillEditor.insertText(cursorPosition, processedText);
        quillEditor.setSelection(cursorPosition, processedText.length);
    }

    // 触发自动保存
    debounceAutoSave();

    // 显示成功通知
    const actionNames = {
        polish: '润色',
        translate: '翻译',
        explain: '解读',
        rewrite: '重写'
    };

    showNotification(`${actionNames[action]}结果已应用到编辑器`, 'success');

    // 关闭对话框
    closeModal();

    // 清理临时数据
    delete window.currentAIResult;
}

// ============================================================================
// Drawer 侧边栏功能 (从 drawer-sidebar-test.html 移植)
// ============================================================================

// DOM元素
let sidebar, toggleBtn, mobileOverlay;
let rightToolbar, rightToggleBtn, toolSelect, toolInfo, toolIframe, collapsedTools;
let toolManagementModal, toolsTableBody, resizeHandle;

// 状态管理
let isMobile = window.innerWidth <= 768;
let isOpen = true;
let rightToolbarOpen = true;
let currentEditingTool = null;

// 右侧工具栏调整宽度状态
let isResizing = false;
let rightToolbarWidth = 350;
const minToolbarWidth = 280;
const maxToolbarWidth = 600;

// 工具数据存储
let tools = JSON.parse(localStorage.getItem('webTools') || '[]');

// 默认工具版本号 - 当添加新工具时增加此版本号
const DEFAULT_TOOLS_VERSION = 2;

// 默认工具
const defaultTools = [
    {
        id: 'calculator',
        name: '计算器',
        url: 'https://www.calculator.net/basic-calculator.html',
        description: '基础计算器工具，支持基本数学运算'
    },
    {
        id: 'color-picker',
        name: '颜色选择器',
        url: 'https://htmlcolorcodes.com/color-picker/',
        description: '在线颜色选择器，支持多种颜色格式'
    },
    {
        id: 'ichat',
        name: 'AI chat',
        url: 'http://ichat.gonghe.net.cn',
        description: 'AI 对话服务工具'
    },
    {
        id: 'json-formatter',
        name: 'JSON格式化',
        url: 'https://jsonformatter.curiousconcept.com/',
        description: 'JSON数据格式化和验证工具'
    },
    {
        id: 'markdown-editor',
        name: 'Markdown编辑器',
        url: 'https://stackedit.io/app',
        description: '在线Markdown编辑器，支持实时预览'
    },
    {
        id: 'regex-tester',
        name: '正则表达式测试',
        url: 'https://regex101.com/',
        description: '正则表达式测试和调试工具'
    },
    {
        id: 'base64-encoder',
        name: 'Base64编码',
        url: 'https://www.base64encode.org/',
        description: 'Base64编码和解码工具'
    },
    {
        id: 'qr-generator',
        name: '二维码生成器',
        url: 'https://www.qr-code-generator.com/',
        description: '在线二维码生成工具'
    },
    {
        id: 'password-generator',
        name: '密码生成器',
        url: 'https://passwordsgenerator.net/',
        description: '安全密码生成工具'
    },
    {
        id: 'url-shortener',
        name: 'URL短链接',
        url: 'https://tinyurl.com/',
        description: 'URL短链接生成工具'
    },
    {
        id: 'image-compressor',
        name: '图片压缩',
        url: 'https://tinypng.com/',
        description: '在线图片压缩工具'
    },
    {
        id: 'pdf-tools',
        name: 'PDF工具',
        url: 'https://smallpdf.com/',
        description: 'PDF转换、合并、分割等工具'
    },
    {
        id: 'translator',
        name: '翻译工具',
        url: 'https://translate.google.com/',
        description: 'Google翻译工具'
    },
    {
        id: 'weather',
        name: '天气查询',
        url: 'https://weather.com/',
        description: '天气预报查询工具'
    },
    {
        id: 'world-clock',
        name: '世界时钟',
        url: 'https://www.timeanddate.com/worldclock/',
        description: '世界各地时间查询'
    },
    {
        id: 'unit-converter',
        name: '单位转换',
        url: 'https://www.unitconverters.net/',
        description: '各种单位转换工具'
    },
    {
        id: 'css-generator',
        name: 'CSS生成器',
        url: 'https://css3generator.com/',
        description: 'CSS样式生成工具'
    },
    {
        id: 'gradient-generator',
        name: '渐变生成器',
        url: 'https://cssgradient.io/',
        description: 'CSS渐变背景生成工具'
    },
    {
        id: 'font-awesome',
        name: '图标库',
        url: 'https://fontawesome.com/icons',
        description: 'Font Awesome图标库'
    },
    {
        id: 'lorem-ipsum',
        name: '文本生成器',
        url: 'https://www.lipsum.com/',
        description: 'Lorem Ipsum占位文本生成器'
    }
];

// 初始化drawer功能
function initDrawerFunctionality() {
    // 获取DOM元素
    sidebar = document.getElementById('sidebar');
    toggleBtn = document.getElementById('toggleBtn');
    mobileOverlay = document.getElementById('mobileOverlay');

    rightToolbar = document.getElementById('rightToolbar');
    rightToggleBtn = document.getElementById('rightToggleBtn');
    toolSelect = document.getElementById('toolSelect');
    toolInfo = document.getElementById('toolInfo');
    toolIframe = document.getElementById('toolIframe');
    collapsedTools = document.getElementById('collapsedTools');
    toolManagementModal = document.getElementById('toolManagementModal');
    toolsTableBody = document.getElementById('toolsTableBody');
    resizeHandle = document.getElementById('resizeHandle');

    // 检查元素是否存在
    if (!sidebar || !toggleBtn || !rightToolbar) {
        console.warn('Drawer elements not found, skipping initialization');
        return;
    }

    // 初始化默认工具
    const currentVersion = localStorage.getItem('webToolsVersion');
    const needsUpdate = !currentVersion || parseInt(currentVersion) < DEFAULT_TOOLS_VERSION;

    if (tools.length === 0 || needsUpdate) {
        console.log('🔄 更新工具集到最新版本...');
        tools = [...defaultTools];
        saveTools();
        localStorage.setItem('webToolsVersion', DEFAULT_TOOLS_VERSION.toString());
        console.log('✅ 工具集已更新，当前版本:', DEFAULT_TOOLS_VERSION);
    }

    updateDrawerLayout();
    bindDrawerEvents();
    loadToolsList();
    updateCollapsedTools();
    initRightToolbarResize();
}

// 绑定drawer事件
function bindDrawerEvents() {
    // 切换按钮
    if (toggleBtn) {
        toggleBtn.addEventListener('click', toggleSidebar);
    }

    // 导航链接
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            setActiveNav(link);

            // 获取tab属性并调用showPanel
            const tabName = link.dataset.tab;
            if (tabName && typeof showPanel === 'function') {
                showPanel(tabName);
            }

            // 移动端点击后关闭侧边栏
            if (isMobile) {
                closeSidebar();
            }
        });
    });

    // 移动端遮罩
    if (mobileOverlay) {
        mobileOverlay.addEventListener('click', closeSidebar);
    }

    // 窗口大小变化
    window.addEventListener('resize', () => {
        isMobile = window.innerWidth <= 768;
        updateDrawerLayout();
    });

    // 键盘导航
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && isMobile && isOpen) {
            closeSidebar();
        }
    });
}

// 切换侧边栏
function toggleSidebar() {
    if (isMobile) {
        isOpen ? closeSidebar() : openSidebar();
    } else {
        sidebar.classList.toggle('collapsed');
        updateToggleIcon();
    }
}

// 打开侧边栏（移动端）
function openSidebar() {
    if (sidebar) {
        sidebar.classList.add('open');
    }
    if (mobileOverlay) {
        mobileOverlay.classList.add('active');
    }
    isOpen = true;
    document.body.style.overflow = 'hidden';
}

// 关闭侧边栏（移动端）
function closeSidebar() {
    if (sidebar) {
        sidebar.classList.remove('open');
    }
    if (mobileOverlay) {
        mobileOverlay.classList.remove('active');
    }
    isOpen = false;
    document.body.style.overflow = '';
}

// 更新布局
function updateDrawerLayout() {
    if (isMobile) {
        if (sidebar) {
            sidebar.classList.remove('collapsed');
            if (!isOpen) {
                sidebar.classList.remove('open');
            }
        }
    } else {
        if (sidebar) {
            sidebar.classList.remove('open');
        }
        if (mobileOverlay) {
            mobileOverlay.classList.remove('active');
        }
        document.body.style.overflow = '';
        isOpen = true;
    }
    updateToggleIcon();
}

// 更新切换按钮图标
function updateToggleIcon() {
    if (!toggleBtn) return;

    const icon = toggleBtn.querySelector('i');
    if (!icon) return;

    if (isMobile) {
        icon.className = 'fas fa-bars';
    } else {
        icon.className = sidebar && sidebar.classList.contains('collapsed') ? 'fas fa-chevron-right' : 'fas fa-chevron-left';
    }
}

// 设置活跃导航
function setActiveNav(activeLink) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

// 右侧工具栏功能
function toggleRightToolbar() {
    if (!rightToolbar) return;

    if (isMobile) {
        rightToolbarOpen ? closeRightToolbar() : openRightToolbar();
    } else {
        rightToolbar.classList.toggle('collapsed');
        updateRightToggleIcon();
        // 切换收缩状态后更新宽度
        updateRightToolbarWidth();
    }
}

function openRightToolbar() {
    if (rightToolbar) {
        rightToolbar.classList.add('open');
    }
    rightToolbarOpen = true;
}

function closeRightToolbar() {
    if (rightToolbar) {
        rightToolbar.classList.remove('open');
    }
    rightToolbarOpen = false;
}

function updateRightToggleIcon() {
    if (!rightToggleBtn) return;

    const icon = rightToggleBtn.querySelector('i');
    if (!icon) return;

    if (rightToolbar && rightToolbar.classList.contains('collapsed')) {
        icon.className = 'fas fa-chevron-left';
    } else {
        icon.className = 'fas fa-chevron-right';
    }
}

function loadToolsList() {
    if (!toolSelect) return;

    // 清空选择器
    toolSelect.innerHTML = '<option value="">选择工具...</option>';

    // 添加工具选项
    tools.forEach(tool => {
        const option = document.createElement('option');
        option.value = tool.id;
        option.textContent = tool.name;
        toolSelect.appendChild(option);
    });
}

function loadTool() {
    if (!toolSelect || !toolInfo || !toolIframe) return;

    const selectedToolId = toolSelect.value;
    if (!selectedToolId) {
        toolInfo.textContent = '选择一个工具开始使用';
        toolIframe.src = 'about:blank';
        return;
    }

    const tool = tools.find(t => t.id === selectedToolId);
    if (tool) {
        toolInfo.textContent = tool.description;
        toolIframe.src = tool.url;

        // 更新收缩状态的活跃工具
        updateCollapsedToolsActive(selectedToolId);
    }
}

function updateCollapsedTools() {
    if (!collapsedTools) return;

    collapsedTools.innerHTML = '';

    tools.forEach(tool => {
        const toolItem = document.createElement('div');
        toolItem.className = 'collapsed-tool-item';
        toolItem.dataset.toolId = tool.id;
        toolItem.innerHTML = getToolIcon(tool.name);
        toolItem.title = tool.name;
        toolItem.onclick = () => loadToolFromCollapsed(tool.id);
        collapsedTools.appendChild(toolItem);
    });
}

function updateCollapsedToolsActive(activeToolId) {
    if (!collapsedTools) return;

    const toolItems = collapsedTools.querySelectorAll('.collapsed-tool-item');
    toolItems.forEach(item => {
        item.classList.toggle('active', item.dataset.toolId === activeToolId);
    });
}

function loadToolFromCollapsed(toolId) {
    // 检查工具栏是否处于收缩状态或关闭状态
    const isCollapsed = rightToolbar && rightToolbar.classList.contains('collapsed');
    const isClosed = !rightToolbarOpen;

    if (isCollapsed || isClosed) {
        // 如果是收缩状态，展开工具栏
        if (isCollapsed) {
            rightToolbar.classList.remove('collapsed');
            updateRightToggleIcon();
            updateRightToolbarWidth();
        }
        // 如果是关闭状态（移动端），打开工具栏
        if (isClosed) {
            toggleRightToolbar();
        }
    }

    // 选择并加载工具
    if (toolSelect) {
        toolSelect.value = toolId;
        loadTool();
    }
}

function getToolIcon(toolName) {
    // 根据工具名称返回合适的图标
    const iconMap = {
        '计算器': '🧮',
        '颜色选择器': '🎨',
        'JSON格式化': '📄',
        'JSON': '📄',
        'Markdown': '📝',
        '编辑器': '📝',
        '正则': '🔍',
        'Base64': '🔐',
        '编码': '🔐',
        '二维码': '📱',
        '密码': '🔑',
        'URL': '🔗',
        '短链接': '🔗',
        '图片': '🖼️',
        '压缩': '📦',
        'PDF': '📄',
        '翻译': '🌐',
        '天气': '🌤️',
        '时钟': '⏰',
        '世界': '🌍',
        '单位': '📏',
        '转换': '🔄',
        'CSS': '🎨',
        '渐变': '🌈',
        '图标': '⭐',
        '文本': '📝',
        '生成器': '⚙️',
        '笔记': '📝',
        '音乐': '🎵',
        '视频': '🎬',
        'AI': '🤖',
        'chat': '💬'
    };

    // 尝试匹配关键词
    for (const [key, icon] of Object.entries(iconMap)) {
        if (toolName.includes(key)) {
            return icon;
        }
    }

    return '🛠️'; // 默认图标
}

// 工具管理功能
function openToolManagement() {
    if (toolManagementModal) {
        toolManagementModal.classList.add('show');
        loadToolsTable();
    }
}

function closeToolManagement() {
    if (toolManagementModal) {
        toolManagementModal.classList.remove('show');
    }
    clearToolForm();
    currentEditingTool = null;
}

function loadToolsTable() {
    if (!toolsTableBody) return;

    toolsTableBody.innerHTML = '';

    tools.forEach(tool => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${tool.name}</td>
            <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${tool.url}">${tool.url}</td>
            <td style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${tool.description}">${tool.description}</td>
            <td class="tool-actions">
                <button class="btn-edit" onclick="editTool('${tool.id}')">编辑</button>
                <button class="btn-delete" onclick="deleteTool('${tool.id}')">删除</button>
            </td>
        `;
        toolsTableBody.appendChild(row);
    });
}

function saveTool() {
    const name = document.getElementById('toolName')?.value.trim();
    const url = document.getElementById('toolUrl')?.value.trim();
    const description = document.getElementById('toolDescription')?.value.trim();

    if (!name || !url) {
        alert('请填写工具名称和URL地址');
        return;
    }

    if (currentEditingTool) {
        // 编辑现有工具
        const toolIndex = tools.findIndex(t => t.id === currentEditingTool);
        if (toolIndex !== -1) {
            tools[toolIndex] = {
                ...tools[toolIndex],
                name,
                url,
                description
            };
        }
    } else {
        // 添加新工具
        const newTool = {
            id: 'tool_' + Date.now(),
            name,
            url,
            description
        };
        tools.push(newTool);
    }

    saveTools();
    loadToolsList();
    loadToolsTable();
    updateCollapsedTools();
    clearToolForm();
    currentEditingTool = null;
}

function editTool(toolId) {
    const tool = tools.find(t => t.id === toolId);
    if (tool) {
        const nameInput = document.getElementById('toolName');
        const urlInput = document.getElementById('toolUrl');
        const descInput = document.getElementById('toolDescription');

        if (nameInput) nameInput.value = tool.name;
        if (urlInput) urlInput.value = tool.url;
        if (descInput) descInput.value = tool.description;

        currentEditingTool = toolId;
    }
}

function deleteTool(toolId) {
    if (confirm('确定要删除这个工具吗？')) {
        tools = tools.filter(t => t.id !== toolId);
        saveTools();
        loadToolsList();
        loadToolsTable();
        updateCollapsedTools();

        // 如果删除的是当前选中的工具，清空iframe
        if (toolSelect && toolSelect.value === toolId) {
            toolSelect.value = '';
            loadTool();
        }
    }
}

function clearToolForm() {
    const nameInput = document.getElementById('toolName');
    const urlInput = document.getElementById('toolUrl');
    const descInput = document.getElementById('toolDescription');

    if (nameInput) nameInput.value = '';
    if (urlInput) urlInput.value = '';
    if (descInput) descInput.value = '';

    currentEditingTool = null;
}

function saveTools() {
    localStorage.setItem('webTools', JSON.stringify(tools));
}

// 右侧工具栏调整宽度功能
function initRightToolbarResize() {
    if (!resizeHandle) return;

    // 从localStorage恢复宽度
    const savedWidth = localStorage.getItem('rightToolbarWidth');
    if (savedWidth) {
        rightToolbarWidth = parseInt(savedWidth);
    }

    // 初始化时设置正确的宽度
    updateRightToolbarWidth();

    // 添加拖拽事件监听器
    resizeHandle.addEventListener('mousedown', startResize);
}

function startResize(e) {
    isResizing = true;
    if (resizeHandle) resizeHandle.classList.add('resizing');
    if (rightToolbar) rightToolbar.classList.add('resizing');

    // 添加全局事件监听器
    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopResize);

    // 防止文本选择
    e.preventDefault();
    document.body.style.userSelect = 'none';
}

function resize(e) {
    if (!isResizing || !rightToolbar) return;

    // 计算新宽度（从右边界向左拖拽）
    const rect = rightToolbar.getBoundingClientRect();
    const newWidth = rect.right - e.clientX;

    // 限制宽度范围
    rightToolbarWidth = Math.max(minToolbarWidth, Math.min(newWidth, maxToolbarWidth));

    updateRightToolbarWidth();
}

function stopResize() {
    if (!isResizing) return;

    isResizing = false;
    if (resizeHandle) resizeHandle.classList.remove('resizing');
    if (rightToolbar) rightToolbar.classList.remove('resizing');

    // 移除全局事件监听器
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResize);

    // 恢复文本选择
    document.body.style.userSelect = '';

    // 保存宽度到localStorage
    localStorage.setItem('rightToolbarWidth', rightToolbarWidth.toString());
}

function updateRightToolbarWidth() {
    if (!rightToolbar) return;

    // 检查工具栏是否处于收缩状态
    const isCollapsed = rightToolbar.classList.contains('collapsed');

    // 收缩状态下不设置内联宽度，让CSS类控制
    if (isCollapsed) {
        rightToolbar.style.width = '';
        return;
    }

    // 只有在非收缩状态下才设置自定义宽度
    if (rightToolbarOpen) {
        rightToolbar.style.width = rightToolbarWidth + 'px';
    }
}

// ============================================================================
// Supabase数据库管理功能
// ============================================================================

// 加载Supabase配置到表单
function loadSupabaseConfig() {
    if (!window.supabaseConfigManager) {
        console.error('Supabase配置管理器未加载');
        return;
    }

    const config = window.supabaseConfigManager.getCurrentConfig();

    const urlInput = document.getElementById('supabase-url');
    const anonKeyInput = document.getElementById('supabase-anon-key');
    const serviceKeyInput = document.getElementById('supabase-service-key');
    const regionSelect = document.getElementById('database-region');

    if (urlInput) urlInput.value = config.url || '';
    if (anonKeyInput) anonKeyInput.value = config.anonKey || '';
    if (serviceKeyInput) serviceKeyInput.value = config.serviceKey || '';
    if (regionSelect) regionSelect.value = config.region || 'auto';
}

// 保存Supabase配置
function saveSupabaseConfig() {
    if (!window.supabaseConfigManager) {
        showNotification('Supabase配置管理器未加载', 'error');
        return;
    }

    const config = {
        url: document.getElementById('supabase-url')?.value?.trim() || '',
        anonKey: document.getElementById('supabase-anon-key')?.value?.trim() || '',
        serviceKey: document.getElementById('supabase-service-key')?.value?.trim() || '',
        region: document.getElementById('database-region')?.value || 'auto'
    };

    const validation = window.supabaseConfigManager.validateConfig(config);
    if (!validation.isValid) {
        showNotification('配置验证失败: ' + validation.errors.join(', '), 'error');
        return;
    }

    if (window.supabaseConfigManager.saveConfig(config)) {
        showNotification('Supabase配置保存成功', 'success');
    } else {
        showNotification('Supabase配置保存失败', 'error');
    }
}

// 测试数据库连接
async function testDatabaseConnection() {
    if (!window.supabaseConfigManager) {
        showNotification('Supabase配置管理器未加载', 'error');
        return;
    }

    const config = {
        url: document.getElementById('supabase-url')?.value?.trim() || '',
        anonKey: document.getElementById('supabase-anon-key')?.value?.trim() || '',
        serviceKey: document.getElementById('supabase-service-key')?.value?.trim() || '',
        region: document.getElementById('database-region')?.value || 'auto'
    };

    showNotification('正在测试数据库连接...', 'info');

    try {
        const result = await window.supabaseConfigManager.testConnection(config);

        if (result.success) {
            showNotification(result.message, 'success');
            console.log('数据库连接详情:', result.details);
        } else {
            // 特殊处理CORS错误
            if (result.details && result.details.errorType === 'CORS') {
                showNotification(result.message + ' - 需要配置CORS策略', 'error');
                console.error('CORS配置问题:', result);

                // 显示CORS解决方案
                setTimeout(() => {
                    if (confirm('检测到CORS配置问题。是否查看解决方案？')) {
                        window.open('cors-solution-guide.html', '_blank');
                    }
                }, 1000);
            } else {
                showNotification(result.message + ': ' + result.error, 'error');
                console.error('连接失败详情:', result);
            }
        }
    } catch (error) {
        showNotification('连接测试失败: ' + error.message, 'error');
        console.error('连接测试异常:', error);
    }
}

// 验证Supabase URL
function validateSupabaseUrl() {
    const urlInput = document.getElementById('supabase-url');
    if (!urlInput) return;

    const url = urlInput.value.trim();
    if (!url) {
        showNotification('请输入项目URL', 'warning');
        return;
    }

    if (!window.supabaseConfigManager.isValidUrl(url)) {
        showNotification('URL格式不正确', 'error');
        urlInput.focus();
        return;
    }

    if (!window.supabaseConfigManager.isValidSupabaseUrl(url)) {
        showNotification('请输入有效的Supabase服务地址（支持官方域名和私有化部署）', 'error');
        urlInput.focus();
        return;
    }

    // 根据URL类型显示不同的成功消息
    const isOfficialDomain = url.includes('supabase.co');
    const isHttps = url.startsWith('https://');

    let message;
    if (isOfficialDomain) {
        message = 'Supabase官方URL验证通过';
    } else {
        message = isHttps ?
            'Supabase私有化部署URL验证通过（HTTPS）' :
            'Supabase私有化部署URL验证通过（HTTP - 建议使用HTTPS）';
    }

    showNotification(message, 'success');
}

// 切换Supabase密钥可见性
function toggleSupabaseKeyVisibility() {
    const keyInput = document.getElementById('supabase-anon-key');
    const toggleIcon = document.getElementById('supabase-key-toggle');

    if (keyInput && toggleIcon) {
        if (keyInput.type === 'password') {
            keyInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            keyInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }
}

// 切换Supabase服务密钥可见性
function toggleSupabaseServiceKeyVisibility() {
    const keyInput = document.getElementById('supabase-service-key');
    const toggleIcon = document.getElementById('supabase-service-key-toggle');

    if (keyInput && toggleIcon) {
        if (keyInput.type === 'password') {
            keyInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            keyInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }
}

// 显示数据库信息
async function showDatabaseInfo() {
    if (!window.supabaseConfigManager) {
        showNotification('Supabase配置管理器未加载', 'error');
        return;
    }

    try {
        const result = await window.supabaseConfigManager.getDatabaseInfo();

        if (result.success) {
            const info = result.data;
            showModal('数据库信息', `
                <div class="database-info">
                    <h4>连接信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>项目URL:</label>
                            <span>${info.connection.url}</span>
                        </div>
                        <div class="info-item">
                            <label>区域:</label>
                            <span>${info.connection.region}</span>
                        </div>
                        <div class="info-item">
                            <label>状态:</label>
                            <span class="status-${info.connection.status}">${info.connection.status}</span>
                        </div>
                    </div>

                    <h4>数据统计</h4>
                    <div class="stats-grid">
                        ${Object.entries(info.statistics).map(([table, count]) => `
                            <div class="stat-item">
                                <label>${table}:</label>
                                <span>${count}</span>
                            </div>
                        `).join('')}
                    </div>

                    <div class="info-footer">
                        <small>更新时间: ${new Date(info.timestamp).toLocaleString()}</small>
                    </div>
                </div>
            `);
        } else {
            showNotification('获取数据库信息失败: ' + result.error, 'error');
        }
    } catch (error) {
        showNotification('获取数据库信息失败: ' + error.message, 'error');
    }
}

// 显示迁移对话框
function showMigrationDialog() {
    showModal('数据库迁移', `
        <div class="migration-dialog">
            <div class="warning-box">
                <i class="fas fa-exclamation-triangle"></i>
                <p>数据库迁移是一个重要操作，请确保在执行前已备份数据。</p>
            </div>

            <div class="migration-options">
                <h4>可用的迁移脚本</h4>
                <div class="migration-list">
                    <div class="migration-item">
                        <input type="radio" id="migration-type-field" name="migration" value="type-field" checked>
                        <label for="migration-type-field">
                            <strong>添加type字段</strong>
                            <small>为projects表添加缺失的type字段</small>
                        </label>
                    </div>
                    <div class="migration-item">
                        <input type="radio" id="migration-custom" name="migration" value="custom">
                        <label for="migration-custom">
                            <strong>自定义SQL</strong>
                            <small>执行自定义的SQL脚本</small>
                        </label>
                    </div>
                </div>

                <div id="custom-sql-area" style="display: none;">
                    <label>SQL脚本:</label>
                    <textarea id="custom-sql" class="form-textarea" rows="6" placeholder="输入要执行的SQL脚本..."></textarea>
                </div>
            </div>
        </div>
    `, [
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: () => closeModal()
        },
        {
            text: '执行迁移',
            className: 'btn-warning',
            onclick: () => executeMigration()
        }
    ]);

    // 添加迁移类型切换事件
    document.querySelectorAll('input[name="migration"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const customArea = document.getElementById('custom-sql-area');
            if (this.value === 'custom') {
                customArea.style.display = 'block';
            } else {
                customArea.style.display = 'none';
            }
        });
    });
}

// 执行迁移
async function executeMigration() {
    const selectedMigration = document.querySelector('input[name="migration"]:checked')?.value;

    if (!selectedMigration) {
        showNotification('请选择迁移类型', 'warning');
        return;
    }

    if (!confirm('确定要执行数据库迁移吗？此操作不可逆转。')) {
        return;
    }

    let migrationScript = '';

    if (selectedMigration === 'type-field') {
        // 使用预定义的type字段迁移脚本
        migrationScript = `
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_schema = 'public'
                    AND table_name = 'projects'
                    AND column_name = 'type'
                ) THEN
                    ALTER TABLE public.projects
                    ADD COLUMN type VARCHAR(50) DEFAULT 'book'
                    CHECK (type IN ('book', 'paper', 'report', 'other'));

                    UPDATE public.projects SET type = 'book' WHERE type IS NULL;
                END IF;
            END $$;
        `;
    } else if (selectedMigration === 'custom') {
        migrationScript = document.getElementById('custom-sql')?.value?.trim();
        if (!migrationScript) {
            showNotification('请输入SQL脚本', 'warning');
            return;
        }
    }

    try {
        showNotification('正在执行迁移...', 'info');
        const result = await window.supabaseConfigManager.executeMigration(migrationScript);

        if (result.success) {
            showNotification('迁移执行成功', 'success');
            closeModal();
        } else {
            showNotification('迁移执行失败: ' + result.error, 'error');
        }
    } catch (error) {
        showNotification('迁移执行失败: ' + error.message, 'error');
    }
}

// 检查迁移状态
function checkMigrationStatus() {
    showModal('迁移状态', `
        <div class="migration-status">
            <h4>数据库结构检查</h4>
            <div class="status-list">
                <div class="status-item">
                    <span class="status-label">projects表type字段:</span>
                    <span class="status-value" id="type-field-status">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">用户配置表:</span>
                    <span class="status-value" id="user-profiles-status">检查中...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">RLS策略:</span>
                    <span class="status-value" id="rls-status">检查中...</span>
                </div>
            </div>

            <div class="check-actions">
                <button class="btn btn-primary" onclick="refreshMigrationStatus()">
                    <i class="fas fa-refresh"></i> 刷新状态
                </button>
            </div>
        </div>
    `);

    // 执行状态检查
    refreshMigrationStatus();
}

// 刷新迁移状态
async function refreshMigrationStatus() {
    // 这里实现具体的状态检查逻辑
    document.getElementById('type-field-status').textContent = '✅ 已存在';
    document.getElementById('user-profiles-status').textContent = '✅ 正常';
    document.getElementById('rls-status').textContent = '✅ 已启用';
}

// 创建数据库备份
async function createDatabaseBackup() {
    if (!window.supabaseConfigManager) {
        showNotification('Supabase配置管理器未加载', 'error');
        return;
    }

    if (!confirm('确定要创建数据库备份吗？这可能需要一些时间。')) {
        return;
    }

    try {
        showNotification('正在创建备份...', 'info');
        const result = await window.supabaseConfigManager.createBackup();

        if (result.success) {
            showNotification('备份创建成功，文件已下载', 'success');
        } else {
            showNotification('备份创建失败: ' + result.error, 'error');
        }
    } catch (error) {
        showNotification('备份创建失败: ' + error.message, 'error');
    }
}

// 显示备份历史
function showBackupHistory() {
    showModal('备份历史', `
        <div class="backup-history">
            <p>备份历史功能开发中...</p>
            <p>当前支持手动创建和下载备份文件。</p>
            <div class="backup-tips">
                <h4>备份建议</h4>
                <ul>
                    <li>定期创建备份，特别是在重要操作前</li>
                    <li>将备份文件保存在安全的位置</li>
                    <li>测试备份文件的完整性</li>
                </ul>
            </div>
        </div>
    `);
}

// 显示恢复对话框
function showRestoreDialog() {
    showModal('数据恢复', `
        <div class="restore-dialog">
            <div class="warning-box">
                <i class="fas fa-exclamation-triangle"></i>
                <p>数据恢复将覆盖现有数据，请确保已备份当前数据。</p>
            </div>

            <div class="restore-options">
                <h4>选择备份文件</h4>
                <input type="file" id="backup-file" accept=".json" class="form-input">
                <small class="setting-help">选择之前创建的备份JSON文件</small>
            </div>
        </div>
    `, [
        {
            text: '取消',
            className: 'btn-secondary',
            onclick: () => closeModal()
        },
        {
            text: '开始恢复',
            className: 'btn-danger',
            onclick: () => executeRestore()
        }
    ]);
}

// 执行数据恢复
function executeRestore() {
    const fileInput = document.getElementById('backup-file');
    if (!fileInput || !fileInput.files[0]) {
        showNotification('请选择备份文件', 'warning');
        return;
    }

    if (!confirm('确定要恢复数据吗？这将覆盖现有数据，此操作不可逆转。')) {
        return;
    }

    const file = fileInput.files[0];
    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            const backupData = JSON.parse(e.target.result);
            // 这里实现恢复逻辑
            showNotification('数据恢复功能开发中...', 'info');
            closeModal();
        } catch (error) {
            showNotification('备份文件格式错误: ' + error.message, 'error');
        }
    };

    reader.readAsText(file);
}

// 验证数据库完整性
async function validateDatabaseIntegrity() {
    showNotification('正在验证数据库完整性...', 'info');

    try {
        // 这里实现完整性检查逻辑
        setTimeout(() => {
            showNotification('数据库完整性验证通过', 'success');
        }, 2000);
    } catch (error) {
        showNotification('完整性验证失败: ' + error.message, 'error');
    }
}

// 显示数据库统计
function showDatabaseStats() {
    showDatabaseInfo(); // 复用数据库信息功能
}

// 显示连接池状态
function showConnectionPool() {
    showModal('连接池状态', `
        <div class="connection-pool">
            <h4>连接池信息</h4>
            <div class="pool-stats">
                <div class="stat-item">
                    <label>活跃连接:</label>
                    <span>N/A</span>
                </div>
                <div class="stat-item">
                    <label>空闲连接:</label>
                    <span>N/A</span>
                </div>
                <div class="stat-item">
                    <label>最大连接数:</label>
                    <span>N/A</span>
                </div>
            </div>
            <p><small>注：Supabase管理连接池，详细信息请查看Supabase控制台。</small></p>
        </div>
    `);
}

// 初始化协作功能
async function initializeCollaboration(user, projectId) {
    if (collaborationInitialized) return;

    try {
        currentUser = user;

        // 获取用户在项目中的角色
        const { data: member, error } = await supabaseManager.supabase
            .from('project_members')
            .select('role')
            .eq('project_id', projectId)
            .eq('user_id', user.id)
            .single();

        if (error) throw error;

        currentUserRole = member.role;

        // 更新用户界面
        updateUserInterface();

        // 初始化在线用户显示
        initializeOnlineUsers();

        // 初始化协作管理器
        if (typeof collaborationManager !== 'undefined') {
            await collaborationManager.initialize();
        }

        collaborationInitialized = true;
        console.log('协作功能初始化完成，用户角色:', currentUserRole);

    } catch (error) {
        console.error('初始化协作功能失败:', error);
    }
}

// 更新用户界面
function updateUserInterface() {
    // 显示/隐藏用户管理链接
    const userManagementLink = document.getElementById('user-management-link');
    const userManagementBtn = document.getElementById('user-management-btn');

    if (['owner', 'admin'].includes(currentUserRole)) {
        if (userManagementLink) userManagementLink.style.display = 'block';
        if (userManagementBtn) userManagementBtn.style.display = 'block';
    }

    // 更新用户信息显示
    const userNameElement = document.getElementById('user-name');
    const userRoleElement = document.getElementById('user-role');

    if (userNameElement && currentUser) {
        userNameElement.textContent = currentUser.user_metadata?.full_name || currentUser.email;
    }

    if (userRoleElement && currentUserRole) {
        const roleNames = {
            'owner': '项目所有者',
            'admin': '管理员',
            'editor': '编辑者',
            'author': '作者',
            'reviewer': '审阅者'
        };
        userRoleElement.textContent = roleNames[currentUserRole] || currentUserRole;
    }
}

// 初始化在线用户显示
function initializeOnlineUsers() {
    const onlineUsersContainer = document.getElementById('online-users');
    if (!onlineUsersContainer) return;

    // 添加当前用户到在线用户列表
    addOnlineUser(currentUser);

    // 这里可以添加实时监听其他用户上线/下线的逻辑
    // 使用Supabase Realtime或其他实时通信方案
}

// 添加在线用户
function addOnlineUser(user) {
    if (onlineUsers.has(user.id)) return;

    onlineUsers.set(user.id, user);
    updateOnlineUsersDisplay();
}

// 移除在线用户
function removeOnlineUser(userId) {
    if (!onlineUsers.has(userId)) return;

    onlineUsers.delete(userId);
    updateOnlineUsersDisplay();
}

// 更新在线用户显示
function updateOnlineUsersDisplay() {
    const onlineUsersContainer = document.getElementById('online-users');
    if (!onlineUsersContainer) return;

    onlineUsersContainer.innerHTML = '';

    onlineUsers.forEach((user, userId) => {
        const userElement = document.createElement('div');
        userElement.className = 'online-user';
        userElement.title = user.user_metadata?.full_name || user.email;

        const initials = (user.user_metadata?.full_name || user.email)
            .split(' ')
            .map(name => name.charAt(0))
            .join('')
            .toUpperCase()
            .substring(0, 2);

        userElement.textContent = initials;

        // 添加工具提示
        const tooltip = document.createElement('div');
        tooltip.className = 'online-user-tooltip';
        tooltip.textContent = user.user_metadata?.full_name || user.email;
        userElement.appendChild(tooltip);

        onlineUsersContainer.appendChild(userElement);
    });

    // 更新同步状态
    updateSyncStatus('connected');
}

// 更新同步状态
function updateSyncStatus(status) {
    const syncStatusElement = document.getElementById('sync-status');
    if (!syncStatusElement) return;

    const statusIcon = syncStatusElement.querySelector('i');
    const statusText = syncStatusElement.querySelector('.status-text');

    // 移除所有状态类
    syncStatusElement.classList.remove('connected', 'disconnected', 'syncing');

    switch (status) {
        case 'connected':
            syncStatusElement.classList.add('connected');
            if (statusIcon) statusIcon.className = 'fas fa-wifi';
            if (statusText) statusText.textContent = '已连接';
            break;
        case 'disconnected':
            syncStatusElement.classList.add('disconnected');
            if (statusIcon) statusIcon.className = 'fas fa-wifi-slash';
            if (statusText) statusText.textContent = '连接断开';
            break;
        case 'syncing':
            syncStatusElement.classList.add('syncing');
            if (statusIcon) statusIcon.className = 'fas fa-sync-alt fa-spin';
            if (statusText) statusText.textContent = '同步中';
            break;
    }
}

// 打开用户管理页面
function openUserManagement() {
    if (!['owner', 'admin'].includes(currentUserRole)) {
        showNotification('您没有权限访问用户管理', 'error');
        return;
    }

    localStorage.setItem('currentProjectId', selectedProjectId);
    window.location.href = 'user-management.html';
}

// 刷新协作状态
function refreshCollaboration() {
    if (typeof collaborationManager !== 'undefined') {
        collaborationManager.loadTeamMembers();
        collaborationManager.loadChapterAssignments();
        collaborationManager.loadPermissionsMatrix();
    }

    showNotification('协作状态已刷新', 'success');
}

// 在DOMContentLoaded事件中初始化drawer功能
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化drawer功能，确保其他组件已经加载
    setTimeout(() => {
        initDrawerFunctionality();
        // 加载Supabase配置
        loadSupabaseConfig();
    }, 100);
});
