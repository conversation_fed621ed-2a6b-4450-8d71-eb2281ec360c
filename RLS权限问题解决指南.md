# RLS 权限问题解决指南

## 🚨 问题描述

错误信息：`new row violates row-level security policy for table "user_profiles"`

这个错误表示 Supabase 的行级安全策略（Row Level Security, RLS）阻止了向 `user_profiles` 表插入新记录。

## 🔍 问题原因

### 1. RLS 策略限制
- Supabase 默认启用了 RLS 来保护数据安全
- 当前的 RLS 策略不允许直接插入用户配置
- 需要适当的策略来允许创建用户功能

### 2. 认证上下文问题
- 创建用户时可能缺少适当的认证上下文
- RLS 策略需要识别当前用户的权限

## ✅ 解决方案

### 方案1：执行快速修复脚本（推荐）

1. **复制快速修复脚本**
   ```
   打开：quick-fix-rls.sql
   复制全部内容
   ```

2. **在 Supabase 控制台执行**
   ```
   1. 登录 Supabase 控制台
   2. 进入 SQL 编辑器
   3. 粘贴并执行脚本
   ```

3. **验证修复结果**
   ```
   应该看到：Quick RLS fix applied successfully!
   ```

### 方案2：执行完整的 RLS 策略（生产环境推荐）

1. **使用完整策略脚本**
   ```
   打开：fix-rls-policies.sql
   在 Supabase 控制台执行
   ```

2. **更安全的权限控制**
   - 细粒度的权限管理
   - 基于项目成员关系的访问控制
   - 符合安全最佳实践

## 🚀 立即修复步骤

### 步骤1：登录 Supabase 控制台
```
访问：https://supabase.com/dashboard
选择您的项目：bigzfjlaypptochqpxzu
```

### 步骤2：打开 SQL 编辑器
```
左侧菜单 → SQL Editor → New query
```

### 步骤3：执行快速修复脚本
```sql
-- 复制以下内容到 SQL 编辑器

-- 为 user_profiles 表添加宽松的插入策略
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON public.user_profiles;
CREATE POLICY "Allow insert for authenticated users" 
ON public.user_profiles
FOR INSERT 
TO authenticated
WITH CHECK (true);

-- 为 user_profiles 表添加宽松的查询策略
DROP POLICY IF EXISTS "Allow select for authenticated users" ON public.user_profiles;
CREATE POLICY "Allow select for authenticated users" 
ON public.user_profiles
FOR SELECT 
TO authenticated
USING (true);

-- 为 project_members 表添加宽松的插入策略
DROP POLICY IF EXISTS "Allow member insert for authenticated users" ON public.project_members;
CREATE POLICY "Allow member insert for authenticated users" 
ON public.project_members
FOR INSERT 
TO authenticated
WITH CHECK (true);

-- 为 project_members 表添加宽松的查询策略
DROP POLICY IF EXISTS "Allow member select for authenticated users" ON public.project_members;
CREATE POLICY "Allow member select for authenticated users" 
ON public.project_members
FOR SELECT 
TO authenticated
USING (true);

-- 完成提示
SELECT 'Quick RLS fix applied successfully!' as result;
```

### 步骤4：执行脚本
```
点击 "Run" 按钮执行
等待执行完成
```

### 步骤5：验证修复
```
返回测试页面：test-create-user.html
重新尝试创建用户
应该可以成功创建
```

## 🔍 验证修复结果

### 1. 检查策略是否创建成功
```sql
SELECT 
    tablename,
    policyname,
    cmd,
    permissive
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('user_profiles', 'project_members')
ORDER BY tablename, policyname;
```

### 2. 测试创建用户功能
```
1. 访问 test-create-user.html
2. 填写测试数据
3. 点击"测试创建用户"
4. 应该显示成功消息
```

## ⚠️ 注意事项

### 安全考虑
- **快速修复方案**：使用宽松的策略，适合开发和测试
- **完整策略方案**：更安全，适合生产环境
- **定期审查**：建议定期检查和更新 RLS 策略

### 权限范围
修复后的权限：
- ✅ 已认证用户可以创建用户配置
- ✅ 已认证用户可以查看用户配置
- ✅ 已认证用户可以添加项目成员
- ✅ 已认证用户可以查看项目成员

## 🔧 故障排除

### 如果修复后仍有问题

1. **检查用户认证状态**
   ```sql
   SELECT 
       auth.uid() as current_user_id,
       auth.role() as current_role;
   ```

2. **检查策略是否生效**
   ```sql
   SELECT * FROM pg_policies 
   WHERE tablename = 'user_profiles';
   ```

3. **清除浏览器缓存**
   ```
   按 Ctrl+Shift+R 强制刷新页面
   ```

4. **重新登录**
   ```
   退出登录后重新登录
   确保认证状态正确
   ```

## 📞 技术支持

如果问题持续存在：

1. **提供错误信息**：完整的错误消息
2. **提供执行结果**：SQL 脚本的执行结果
3. **提供用户状态**：当前登录用户信息
4. **提供操作步骤**：详细的操作过程

## 🎯 预期结果

修复完成后：
- ✅ 创建用户功能正常工作
- ✅ 不再出现 RLS 权限错误
- ✅ 用户可以被正确添加到项目
- ✅ 用户管理页面正常显示

---

**执行快速修复脚本，让创建用户功能立即可用！** 🚀
