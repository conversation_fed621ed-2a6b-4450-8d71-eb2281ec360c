-- 清理章节为0的空项目
-- 这个脚本会识别并删除没有实际内容的测试项目

-- 1. 查看所有项目及其章节统计
SELECT 
    p.id,
    p.title,
    p.description,
    p.status,
    p.created_at,
    p.owner_id,
    up.full_name as owner_name,
    COALESCE(chapter_count.count, 0) as chapter_count,
    COALESCE(outline_count.count, 0) as outline_count,
    COALESCE(assignment_count.count, 0) as assignment_count
FROM projects p
LEFT JOIN user_profiles up ON p.owner_id = up.id
LEFT JOIN (
    SELECT project_id, COUNT(*) as count 
    FROM chapters 
    GROUP BY project_id
) chapter_count ON p.id = chapter_count.project_id
LEFT JOIN (
    SELECT project_id, COUNT(*) as count 
    FROM outlines 
    GROUP BY project_id
) outline_count ON p.id = outline_count.project_id
LEFT JOIN (
    SELECT project_id, COUNT(*) as count 
    FROM chapter_assignments 
    GROUP BY project_id
) assignment_count ON p.id = assignment_count.project_id
ORDER BY p.created_at DESC;

-- 2. 识别空项目（没有章节、大纲和分配的项目）
WITH empty_projects AS (
    SELECT 
        p.id,
        p.title,
        p.created_at,
        COALESCE(chapter_count.count, 0) as chapter_count,
        COALESCE(outline_count.count, 0) as outline_count,
        COALESCE(assignment_count.count, 0) as assignment_count
    FROM projects p
    LEFT JOIN (
        SELECT project_id, COUNT(*) as count 
        FROM chapters 
        GROUP BY project_id
    ) chapter_count ON p.id = chapter_count.project_id
    LEFT JOIN (
        SELECT project_id, COUNT(*) as count 
        FROM outlines 
        GROUP BY project_id
    ) outline_count ON p.id = outline_count.project_id
    LEFT JOIN (
        SELECT project_id, COUNT(*) as count 
        FROM chapter_assignments 
        GROUP BY project_id
    ) assignment_count ON p.id = assignment_count.project_id
    WHERE 
        COALESCE(chapter_count.count, 0) = 0 
        AND COALESCE(outline_count.count, 0) = 0 
        AND COALESCE(assignment_count.count, 0) = 0
)
SELECT 
    id,
    title,
    created_at,
    '待删除' as action
FROM empty_projects
ORDER BY created_at;

-- 3. 识别重复项目（相同标题的项目，保留最新的）
WITH duplicate_projects AS (
    SELECT
        p.id,
        p.title,
        p.created_at,
        ROW_NUMBER() OVER (
            PARTITION BY TRIM(LOWER(p.title))
            ORDER BY p.created_at DESC
        ) as row_num
    FROM projects p
),
duplicate_titles AS (
    SELECT TRIM(LOWER(title)) as normalized_title
    FROM projects
    GROUP BY TRIM(LOWER(title))
    HAVING COUNT(*) > 1
)
SELECT
    dp.id,
    dp.title,
    dp.created_at,
    dp.row_num,
    CASE
        WHEN dp.row_num = 1 THEN '保留'
        ELSE '删除（重复）'
    END as action
FROM duplicate_projects dp
WHERE TRIM(LOWER(dp.title)) IN (
    SELECT normalized_title FROM duplicate_titles
)
ORDER BY dp.title, dp.created_at DESC;

-- 4. 删除空项目（没有任何内容的项目）
-- 注意：这会删除数据，请先备份！
DELETE FROM projects 
WHERE id IN (
    SELECT p.id
    FROM projects p
    LEFT JOIN chapters c ON p.id = c.project_id
    LEFT JOIN outlines o ON p.id = o.project_id
    LEFT JOIN chapter_assignments ca ON p.id = ca.project_id
    WHERE c.id IS NULL 
      AND o.id IS NULL 
      AND ca.id IS NULL
);

-- 5. 删除重复项目（保留最新的）
-- 注意：这会删除数据，请先备份！
DELETE FROM projects
WHERE id IN (
    WITH duplicate_projects AS (
        SELECT
            p.id,
            p.title,
            p.created_at,
            ROW_NUMBER() OVER (
                PARTITION BY TRIM(LOWER(p.title))
                ORDER BY p.created_at DESC
            ) as row_num
        FROM projects p
    )
    SELECT id
    FROM duplicate_projects
    WHERE row_num > 1
);

-- 6. 清理孤立的项目成员记录
DELETE FROM project_members 
WHERE project_id NOT IN (SELECT id FROM projects);

-- 7. 清理孤立的章节分配记录
DELETE FROM chapter_assignments 
WHERE project_id NOT IN (SELECT id FROM projects);

-- 8. 验证清理结果
SELECT 
    p.id,
    p.title,
    p.description,
    p.status,
    p.created_at,
    up.full_name as owner_name,
    COALESCE(chapter_count.count, 0) as chapter_count,
    COALESCE(outline_count.count, 0) as outline_count,
    COALESCE(assignment_count.count, 0) as assignment_count
FROM projects p
LEFT JOIN user_profiles up ON p.owner_id = up.id
LEFT JOIN (
    SELECT project_id, COUNT(*) as count 
    FROM chapters 
    GROUP BY project_id
) chapter_count ON p.id = chapter_count.project_id
LEFT JOIN (
    SELECT project_id, COUNT(*) as count 
    FROM outlines 
    GROUP BY project_id
) outline_count ON p.id = outline_count.project_id
LEFT JOIN (
    SELECT project_id, COUNT(*) as count 
    FROM chapter_assignments 
    GROUP BY project_id
) assignment_count ON p.id = assignment_count.project_id
ORDER BY p.created_at DESC;
