<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户Supabase配置测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .config-section h3 {
            margin-top: 0;
            color: #555;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="url"], input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            font-family: monospace;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.success {
            background-color: #28a745;
        }
        button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .pre-filled {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .pre-filled h4 {
            margin-top: 0;
            color: #0056b3;
        }
        .config-item {
            font-family: monospace;
            background-color: white;
            padding: 8px;
            border-radius: 3px;
            margin: 5px 0;
            word-break: break-all;
        }
        .toggle-btn {
            background-color: #6c757d;
            font-size: 12px;
            padding: 6px 12px;
        }
        .toggle-btn:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 用户Supabase配置测试</h1>
        
        <div class="pre-filled">
            <h4>您的配置信息：</h4>
            <div class="config-item"><strong>URL:</strong> https://bigzfjlaypptochqpxzu.supabase.co</div>
            <div class="config-item"><strong>API Key:</strong> eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpZ3pmamxheXBwdG9jaHFweHp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MDc1MjUsImV4cCI6MjA2ODI4MzUyNX0.02clbd9p8TCBqsYEkKJlHnbnU60cRSVxgJo0bfNtq9M</div>
        </div>

        <div class="config-section">
            <h3>📝 配置输入</h3>
            <div class="input-group">
                <label for="supabase-url">Supabase URL:</label>
                <input type="url" id="supabase-url" value="http://superbase.ailer.ltd" placeholder="http://superbase.ailer.ltd">
            </div>
            <div class="input-group">
                <label for="supabase-key">API Key (匿名密钥):</label>
                <input type="password" id="supabase-key" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE">
                <button type="button" class="toggle-btn" onclick="toggleKeyVisibility()">显示/隐藏</button>
            </div>
            <div class="input-group">
                <label for="service-key">服务密钥 (可选):</label>
                <input type="password" id="service-key" placeholder="服务密钥（可选）">
                <button type="button" class="toggle-btn" onclick="toggleServiceKeyVisibility()">显示/隐藏</button>
            </div>
            
            <button onclick="validateConfiguration()">验证配置</button>
            <button onclick="testConnection()" class="success">测试连接</button>
            <button onclick="saveConfiguration()" class="warning">保存配置</button>
            <button onclick="clearForm()">清空表单</button>
            
            <div id="validation-result"></div>
        </div>

        <div class="config-section">
            <h3>🧪 详细测试</h3>
            <button onclick="testUrlValidation()">URL格式验证</button>
            <button onclick="testJwtValidation()">JWT格式验证</button>
            <button onclick="testProtocolSecurity()">协议安全检查</button>
            <button onclick="runAllTests()">运行所有测试</button>
            
            <div id="detailed-results"></div>
        </div>

        <div class="config-section">
            <h3>📋 配置说明</h3>
            <div class="info">
                <p><strong>关于HTTP协议：</strong></p>
                <ul>
                    <li>✅ 您的URL使用HTTP协议，系统已支持私有化部署的HTTP连接</li>
                    <li>⚠️ 建议在生产环境中使用HTTPS以确保数据安全</li>
                    <li>🔒 官方Supabase服务仍然要求HTTPS协议</li>
                </ul>
                
                <p><strong>API Key说明：</strong></p>
                <ul>
                    <li>📝 您提供的API Key格式正确（JWT格式）</li>
                    <li>🔑 这是匿名密钥，用于客户端访问</li>
                    <li>🛡️ 服务密钥是可选的，用于管理员操作</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 引入Supabase配置管理器 -->
    <script src="supabase-config-manager.js"></script>

    <script>
        function validateConfiguration() {
            const url = document.getElementById('supabase-url').value.trim();
            const anonKey = document.getElementById('supabase-key').value.trim();
            const serviceKey = document.getElementById('service-key').value.trim();
            
            const config = { url, anonKey, serviceKey };
            const validation = window.supabaseConfigManager.validateConfig(config);
            
            const resultDiv = document.getElementById('validation-result');
            
            if (validation.isValid) {
                const isHttps = url.startsWith('https://');
                const protocolNote = isHttps ? '' : ' (HTTP协议 - 建议升级到HTTPS)';
                showResult(resultDiv, 'success', `✅ 配置验证通过${protocolNote}`);
            } else {
                showResult(resultDiv, 'error', `❌ 配置验证失败: ${validation.errors.join(', ')}`);
            }
        }

        async function testConnection() {
            const url = document.getElementById('supabase-url').value.trim();
            const anonKey = document.getElementById('supabase-key').value.trim();
            const serviceKey = document.getElementById('service-key').value.trim();
            
            const config = { url, anonKey, serviceKey };
            const resultDiv = document.getElementById('validation-result');
            
            showResult(resultDiv, 'info', '🔄 正在测试连接...');
            
            try {
                const result = await window.supabaseConfigManager.testConnection(config);
                
                if (result.success) {
                    showResult(resultDiv, 'success', `✅ ${result.message}`);
                    console.log('连接详情:', result.details);
                } else {
                    showResult(resultDiv, 'error', `❌ ${result.message}: ${result.error}`);
                    console.error('连接失败:', result);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `❌ 连接测试异常: ${error.message}`);
                console.error('连接测试异常:', error);
            }
        }

        function saveConfiguration() {
            const url = document.getElementById('supabase-url').value.trim();
            const anonKey = document.getElementById('supabase-key').value.trim();
            const serviceKey = document.getElementById('service-key').value.trim();
            
            const config = { url, anonKey, serviceKey, region: 'auto' };
            
            if (window.supabaseConfigManager.saveConfig(config)) {
                showResult(document.getElementById('validation-result'), 'success', '✅ 配置保存成功');
            } else {
                showResult(document.getElementById('validation-result'), 'error', '❌ 配置保存失败');
            }
        }

        function testUrlValidation() {
            const url = document.getElementById('supabase-url').value.trim();
            const resultDiv = document.getElementById('detailed-results');
            
            const isValidUrl = window.supabaseConfigManager.isValidUrl(url);
            const isValidSupabase = isValidUrl ? window.supabaseConfigManager.isValidSupabaseUrl(url) : false;
            
            let message = `URL: ${url}\n`;
            message += `基本格式: ${isValidUrl ? '✅ 有效' : '❌ 无效'}\n`;
            message += `Supabase格式: ${isValidSupabase ? '✅ 有效' : '❌ 无效'}\n`;
            message += `协议: ${url.startsWith('https://') ? 'HTTPS ✅' : 'HTTP ⚠️'}`;
            
            showResult(resultDiv, isValidSupabase ? 'success' : 'error', message);
        }

        function testJwtValidation() {
            const anonKey = document.getElementById('supabase-key').value.trim();
            const serviceKey = document.getElementById('service-key').value.trim();
            const resultDiv = document.getElementById('detailed-results');
            
            const isValidAnon = window.supabaseConfigManager.isValidJWT(anonKey);
            const isValidService = serviceKey ? window.supabaseConfigManager.isValidJWT(serviceKey) : true;
            
            let message = `匿名密钥: ${isValidAnon ? '✅ 有效JWT格式' : '❌ 无效JWT格式'}\n`;
            if (serviceKey) {
                message += `服务密钥: ${isValidService ? '✅ 有效JWT格式' : '❌ 无效JWT格式'}`;
            } else {
                message += `服务密钥: 未提供（可选）`;
            }
            
            showResult(resultDiv, (isValidAnon && isValidService) ? 'success' : 'error', message);
        }

        function testProtocolSecurity() {
            const url = document.getElementById('supabase-url').value.trim();
            const resultDiv = document.getElementById('detailed-results');
            
            const isHttps = url.startsWith('https://');
            const isHttp = url.startsWith('http://');
            const isOfficial = url.includes('supabase.co');
            
            let message = `协议安全检查:\n`;
            message += `使用协议: ${isHttps ? 'HTTPS ✅' : isHttp ? 'HTTP ⚠️' : '未知 ❌'}\n`;
            message += `域名类型: ${isOfficial ? '官方域名' : '私有化部署'}\n`;
            
            if (isOfficial && !isHttps) {
                message += `⚠️ 官方域名必须使用HTTPS`;
                showResult(resultDiv, 'error', message);
            } else if (!isOfficial && !isHttps) {
                message += `⚠️ 建议私有化部署也使用HTTPS`;
                showResult(resultDiv, 'warning', message);
            } else {
                message += `✅ 协议配置合适`;
                showResult(resultDiv, 'success', message);
            }
        }

        function runAllTests() {
            testUrlValidation();
            setTimeout(() => testJwtValidation(), 500);
            setTimeout(() => testProtocolSecurity(), 1000);
        }

        function clearForm() {
            document.getElementById('supabase-url').value = '';
            document.getElementById('supabase-key').value = '';
            document.getElementById('service-key').value = '';
            document.getElementById('validation-result').innerHTML = '';
            document.getElementById('detailed-results').innerHTML = '';
        }

        function toggleKeyVisibility() {
            const keyInput = document.getElementById('supabase-key');
            keyInput.type = keyInput.type === 'password' ? 'text' : 'password';
        }

        function toggleServiceKeyVisibility() {
            const keyInput = document.getElementById('service-key');
            keyInput.type = keyInput.type === 'password' ? 'text' : 'password';
        }

        function showResult(element, type, message) {
            element.innerHTML = `<div class="${type}">${message.replace(/\n/g, '<br>')}</div>`;
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('用户Supabase配置测试页面已加载');
            console.log('配置管理器状态:', window.supabaseConfigManager ? '已加载' : '未加载');
            
            // 自动运行一次验证
            setTimeout(() => {
                validateConfiguration();
            }, 1000);
        });
    </script>
</body>
</html>
