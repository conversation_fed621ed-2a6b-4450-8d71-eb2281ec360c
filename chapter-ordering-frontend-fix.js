// 前端章节排序优化脚本
// 确保章节和大纲按正确顺序显示

// ========================================
// 排序工具函数
// ========================================

/**
 * 智能章节排序函数
 * @param {string} title - 章节标题
 * @returns {number} - 排序权重
 */
function calculateChapterOrder(title) {
    if (!title) return 9999;
    
    const lowerTitle = title.toLowerCase().trim();
    
    // 前言、序言
    if (lowerTitle.includes('前言') || lowerTitle.includes('序') || lowerTitle.includes('preface')) {
        return 0;
    }
    
    // 目录
    if (lowerTitle.includes('目录') || lowerTitle.includes('contents')) {
        return 5;
    }
    
    // 第0章或引言
    if (lowerTitle.includes('第0章') || lowerTitle.includes('第零章') || 
        lowerTitle.includes('引言') || lowerTitle.includes('概述')) {
        return 10;
    }
    
    // 提取章节号码
    const chapterMatches = [
        { pattern: /第(\d+)章/, multiplier: 100 },
        { pattern: /第一章/, value: 100 },
        { pattern: /第二章/, value: 200 },
        { pattern: /第三章/, value: 300 },
        { pattern: /第四章/, value: 400 },
        { pattern: /第五章/, value: 500 },
        { pattern: /第六章/, value: 600 },
        { pattern: /第七章/, value: 700 },
        { pattern: /第八章/, value: 800 },
        { pattern: /第九章/, value: 900 },
        { pattern: /第十章/, value: 1000 }
    ];
    
    for (const match of chapterMatches) {
        if (match.pattern.test(lowerTitle)) {
            if (match.value) {
                return match.value;
            } else {
                const chapterNum = parseInt(lowerTitle.match(match.pattern)[1]);
                return 100 + (chapterNum * 100);
            }
        }
    }
    
    // 小节编号 (X.Y 格式)
    const sectionMatch = lowerTitle.match(/(\d+)\.(\d+)/);
    if (sectionMatch) {
        const chapter = parseInt(sectionMatch[1]);
        const section = parseInt(sectionMatch[2]);
        return 100 + (chapter * 100) + (section * 10);
    }
    
    // 附录
    if (lowerTitle.includes('附录') || lowerTitle.includes('appendix')) {
        return 9000;
    }
    
    // 参考文献
    if (lowerTitle.includes('参考文献') || lowerTitle.includes('references')) {
        return 9100;
    }
    
    // 索引
    if (lowerTitle.includes('索引') || lowerTitle.includes('index')) {
        return 9200;
    }
    
    // 后记、致谢
    if (lowerTitle.includes('后记') || lowerTitle.includes('致谢') || 
        lowerTitle.includes('acknowledgments')) {
        return 9300;
    }
    
    // 默认值
    return 5000;
}

/**
 * 排序章节数组
 * @param {Array} chapters - 章节数组
 * @returns {Array} - 排序后的章节数组
 */
function sortChapters(chapters) {
    return chapters.sort((a, b) => {
        // 首先按 order_index 排序（如果存在）
        if (a.order_index !== undefined && b.order_index !== undefined) {
            if (a.order_index !== b.order_index) {
                return a.order_index - b.order_index;
            }
        }
        
        // 然后按智能排序
        const orderA = calculateChapterOrder(a.title);
        const orderB = calculateChapterOrder(b.title);
        
        if (orderA !== orderB) {
            return orderA - orderB;
        }
        
        // 最后按创建时间排序
        const timeA = new Date(a.created_at || 0);
        const timeB = new Date(b.created_at || 0);
        return timeA - timeB;
    });
}

/**
 * 排序大纲数组
 * @param {Array} outlines - 大纲数组
 * @returns {Array} - 排序后的大纲数组
 */
function sortOutlines(outlines) {
    return outlines.sort((a, b) => {
        // 首先按 sort_order 排序（如果存在）
        if (a.sort_order !== undefined && b.sort_order !== undefined) {
            if (a.sort_order !== b.sort_order) {
                return a.sort_order - b.sort_order;
            }
        }
        
        // 然后按智能排序
        const orderA = calculateChapterOrder(a.title);
        const orderB = calculateChapterOrder(b.title);
        
        if (orderA !== orderB) {
            return orderA - orderB;
        }
        
        // 按层级排序
        if (a.level !== b.level) {
            return a.level - b.level;
        }
        
        // 最后按创建时间排序
        const timeA = new Date(a.created_at || 0);
        const timeB = new Date(b.created_at || 0);
        return timeA - timeB;
    });
}

// ========================================
// 数据库查询优化
// ========================================

/**
 * 优化的大纲加载函数
 */
async function loadOutlinesWithProperOrdering(projectId) {
    if (!supabaseManager?.supabase) {
        throw new Error('Supabase未初始化');
    }
    
    try {
        const { data: outlines, error } = await supabaseManager.supabase
            .from('outlines')
            .select('*')
            .eq('project_id', projectId)
            .order('sort_order', { ascending: true, nullsLast: true })
            .order('level', { ascending: true })
            .order('created_at', { ascending: true });
        
        if (error) throw error;
        
        // 前端再次排序确保正确性
        return sortOutlines(outlines || []);
        
    } catch (error) {
        console.error('加载大纲失败:', error);
        throw error;
    }
}

/**
 * 优化的章节加载函数
 */
async function loadChaptersWithProperOrdering(projectId) {
    if (!supabaseManager?.supabase) {
        throw new Error('Supabase未初始化');
    }
    
    try {
        const { data: chapters, error } = await supabaseManager.supabase
            .from('chapters')
            .select('*')
            .eq('project_id', projectId)
            .order('order_index', { ascending: true, nullsLast: true })
            .order('created_at', { ascending: true });
        
        if (error) throw error;
        
        // 前端再次排序确保正确性
        return sortChapters(chapters || []);
        
    } catch (error) {
        console.error('加载章节失败:', error);
        throw error;
    }
}

// ========================================
// UI更新函数
// ========================================

/**
 * 更新章节选择器的排序
 */
function updateChapterSelectorOrdering() {
    const selector = document.getElementById('chapter-selector');
    if (!selector) return;
    
    // 获取所有选项
    const options = Array.from(selector.options).slice(1); // 跳过第一个默认选项
    
    // 提取章节数据
    const chapters = options.map(option => ({
        element: option,
        title: option.textContent.trim(),
        order_index: parseInt(option.getAttribute('data-order-index')) || undefined,
        created_at: option.getAttribute('data-created-at') || new Date().toISOString()
    }));
    
    // 排序
    const sortedChapters = sortChapters(chapters);
    
    // 重新排列选项
    const defaultOption = selector.options[0];
    selector.innerHTML = '';
    selector.appendChild(defaultOption);
    
    sortedChapters.forEach(chapter => {
        selector.appendChild(chapter.element);
    });
}

/**
 * 更新大纲树的排序
 */
function updateOutlineTreeOrdering() {
    if (typeof currentProject === 'undefined' || !currentProject.outline) {
        return;
    }
    
    // 排序大纲数据
    currentProject.outline = sortOutlines(currentProject.outline);
    
    // 重新渲染大纲树
    if (typeof renderOutlineTree === 'function') {
        renderOutlineTree();
    }
}

// ========================================
// 初始化和事件监听
// ========================================

/**
 * 初始化排序优化
 */
function initializeChapterOrdering() {
    console.log('🔄 初始化章节排序优化...');
    
    // 监听项目切换事件
    if (typeof collaborationManager !== 'undefined') {
        const originalLoadProject = collaborationManager.loadProject;
        if (originalLoadProject) {
            collaborationManager.loadProject = async function(projectId) {
                const result = await originalLoadProject.call(this, projectId);
                
                // 项目加载后优化排序
                setTimeout(() => {
                    updateOutlineTreeOrdering();
                    updateChapterSelectorOrdering();
                }, 100);
                
                return result;
            };
        }
    }
    
    // 监听大纲更新事件
    const originalUpdateOutlineDisplay = window.updateOutlineDisplay;
    if (originalUpdateOutlineDisplay) {
        window.updateOutlineDisplay = function(outlines) {
            if (outlines && Array.isArray(outlines)) {
                outlines = sortOutlines(outlines);
            }
            return originalUpdateOutlineDisplay.call(this, outlines);
        };
    }
    
    // 监听章节选择器更新事件
    const originalUpdateChapterSelector = window.updateChapterSelector;
    if (originalUpdateChapterSelector) {
        window.updateChapterSelector = function() {
            const result = originalUpdateChapterSelector.call(this);
            setTimeout(updateChapterSelectorOrdering, 50);
            return result;
        };
    }
    
    console.log('✅ 章节排序优化初始化完成');
}

// ========================================
// 导出函数（如果在模块环境中）
// ========================================

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        calculateChapterOrder,
        sortChapters,
        sortOutlines,
        loadOutlinesWithProperOrdering,
        loadChaptersWithProperOrdering,
        updateChapterSelectorOrdering,
        updateOutlineTreeOrdering,
        initializeChapterOrdering
    };
}

// 自动初始化（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    // 等待DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeChapterOrdering);
    } else {
        initializeChapterOrdering();
    }
}
