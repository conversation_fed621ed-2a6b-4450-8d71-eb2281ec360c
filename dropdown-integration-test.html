<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉框集成测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 0;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            text-align: center;
        }
        
        .test-content {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-card h3 {
            margin: 0 0 1rem 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-pass {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-fail {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .test-results {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1><i class="fas fa-vial"></i> 下拉框样式优化集成测试</h1>
        <p>验证项目选择下拉框的样式优化和功能完整性</p>
    </div>
    
    <div class="test-content">
        <div class="test-grid">
            <!-- 内联下拉框测试 -->
            <div class="test-card">
                <h3><i class="fas fa-desktop"></i> 内联项目选择器</h3>
                <div class="panel-header">
                    <div class="panel-header-left">
                        <h4><i class="fas fa-tachometer-alt"></i> 项目概览</h4>
                        <div class="project-selector-inline">
                            <div class="project-dropdown">
                                <button class="project-btn project-btn-inline" onclick="testToggleDropdown('inline')">
                                    <i class="fas fa-folder"></i>
                                    <span id="selected-project-inline">选择项目</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="project-list" id="project-list-inline">
                                    <div class="project-item" onclick="testSelectProject('new', 'inline')">
                                        <i class="fas fa-plus"></i>
                                        <span>创建新项目</span>
                                    </div>
                                    <div class="project-divider"></div>
                                    <div class="project-item" onclick="testSelectProject('book1', 'inline')">
                                        <div class="project-info">
                                            <div class="project-title">大模型技术与油气应用概论</div>
                                            <div class="project-role">项目所有者</div>
                                        </div>
                                        <div class="project-status"></div>
                                    </div>
                                    <div class="project-item" onclick="testSelectProject('book2', 'inline')">
                                        <div class="project-info">
                                            <div class="project-title">人工智能在石油勘探中的应用</div>
                                            <div class="project-role">编辑者</div>
                                        </div>
                                        <div class="project-status suspended"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 侧边栏下拉框测试 -->
            <div class="test-card">
                <h3><i class="fas fa-sidebar"></i> 侧边栏项目选择器</h3>
                <div style="width: 100%; max-width: 280px;">
                    <div class="project-dropdown">
                        <button class="project-btn" onclick="testToggleDropdown('sidebar')">
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <i class="fas fa-folder"></i>
                                <span id="selected-project-sidebar">选择项目</span>
                            </div>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="project-list" id="project-list-sidebar">
                            <div class="project-item" onclick="testSelectProject('new', 'sidebar')">
                                <i class="fas fa-plus"></i>
                                <span>创建新项目</span>
                            </div>
                            <div class="project-divider"></div>
                            <div class="project-item" onclick="testSelectProject('book1', 'sidebar')">
                                <div class="project-info">
                                    <div class="project-title">大模型技术与油气应用概论</div>
                                    <div class="project-role">项目所有者</div>
                                </div>
                                <div class="project-status"></div>
                            </div>
                            <div class="project-item" onclick="testSelectProject('book2', 'sidebar')">
                                <div class="project-info">
                                    <div class="project-title">人工智能在石油勘探中的应用</div>
                                    <div class="project-role">编辑者</div>
                                </div>
                                <div class="project-status suspended"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-results">
            <h3><i class="fas fa-clipboard-check"></i> 测试结果</h3>
            <div id="test-results-list">
                <!-- 测试结果将在这里动态生成 -->
            </div>
            <div style="margin-top: 1rem; text-align: center;">
                <button onclick="runAllTests()" style="background: #3b82f6; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; cursor: pointer; font-weight: 500;">
                    <i class="fas fa-play"></i> 运行所有测试
                </button>
            </div>
        </div>
    </div>

    <script>
        const testResults = [];
        
        function testToggleDropdown(type) {
            const dropdown = document.getElementById(`project-list-${type}`);
            const btn = dropdown.previousElementSibling;
            
            dropdown.classList.toggle('show');
            btn.classList.toggle('active');
            
            // 记录测试结果
            const isOpen = dropdown.classList.contains('show');
            addTestResult(`下拉框切换 (${type})`, isOpen, `下拉框${isOpen ? '打开' : '关闭'}成功`);
        }
        
        function testSelectProject(projectId, type) {
            const projectNames = {
                'new': '创建新项目',
                'book1': '大模型技术与油气应用概论',
                'book2': '人工智能在石油勘探中的应用'
            };
            
            const selectedSpan = document.getElementById(`selected-project-${type}`);
            if (selectedSpan) {
                selectedSpan.textContent = projectNames[projectId] || '选择项目';
            }
            
            // 关闭下拉菜单
            const dropdown = document.getElementById(`project-list-${type}`);
            const btn = dropdown.previousElementSibling;
            dropdown.classList.remove('show');
            btn.classList.remove('active');
            
            addTestResult(`项目选择 (${type})`, true, `成功选择: ${projectNames[projectId]}`);
        }
        
        function addTestResult(testName, passed, message) {
            testResults.push({ testName, passed, message, timestamp: new Date() });
            updateTestResults();
        }
        
        function updateTestResults() {
            const resultsList = document.getElementById('test-results-list');
            resultsList.innerHTML = testResults.map(result => `
                <div class="result-item">
                    <div>
                        <strong>${result.testName}</strong>
                        <div style="font-size: 0.875rem; color: #6b7280;">${result.message}</div>
                    </div>
                    <div class="status-indicator ${result.passed ? 'status-pass' : 'status-fail'}">
                        <i class="fas fa-${result.passed ? 'check' : 'times'}"></i>
                        ${result.passed ? '通过' : '失败'}
                    </div>
                </div>
            `).join('');
        }
        
        function runAllTests() {
            testResults.length = 0; // 清空之前的结果
            
            // 测试样式加载
            const stylesLoaded = document.querySelector('link[href="styles.css"]') !== null;
            addTestResult('样式文件加载', stylesLoaded, stylesLoaded ? 'CSS样式文件加载成功' : 'CSS样式文件加载失败');
            
            // 测试DOM元素存在
            const inlineDropdown = document.getElementById('project-list-inline');
            const sidebarDropdown = document.getElementById('project-list-sidebar');
            addTestResult('DOM元素检查', inlineDropdown && sidebarDropdown, '所有必要的DOM元素都存在');
            
            // 测试CSS类
            const hasProjectListClass = inlineDropdown && inlineDropdown.classList.contains('project-list');
            addTestResult('CSS类检查', hasProjectListClass, 'CSS类应用正确');
            
            // 测试响应式设计
            const inlineBtn = document.querySelector('.project-btn-inline');
            const sidebarBtn = document.querySelector('.project-btn');
            const hasResponsiveClasses = inlineBtn && sidebarBtn;
            addTestResult('响应式设计', hasResponsiveClasses, '响应式CSS类应用正确');
            
            // 测试动画支持
            const hasAnimationSupport = CSS.supports('animation', 'fadeInDown 0.2s ease-out');
            addTestResult('动画支持', hasAnimationSupport, hasAnimationSupport ? '浏览器支持CSS动画' : '浏览器不支持CSS动画');
            
            updateTestResults();
        }
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.project-dropdown')) {
                document.querySelectorAll('.project-list').forEach(list => {
                    list.classList.remove('show');
                    if (list.previousElementSibling) {
                        list.previousElementSibling.classList.remove('active');
                    }
                });
            }
        });
        
        // 页面加载完成后运行初始测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
