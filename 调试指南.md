# 章节保存问题调试指南

## 🎯 问题现状
- Console显示"已保存到数据库"
- 刷新页面后内容恢复到以前的状态
- 说明：**保存成功，但加载失败**

## 🔍 调试步骤

### 第一步：使用实时监控工具
1. 打开 `debug-monitor.html`
2. 点击"连接主应用"
3. 在主应用中进行以下操作：
   - 双击任意章节进入编写模式
   - 输入一些测试内容
   - 等待自动保存或手动保存
4. 观察监控工具中的详细日志

### 第二步：检查保存过程
在监控工具的"保存过程监控"面板中，查找以下关键信息：

#### 🔍 需要确认的保存信息：
- ✅ 项目ID是否正确获取
- ✅ 用户是否已登录
- ✅ Delta内容是否正确格式化
- ✅ 数据库保存是否成功
- ✅ 验证查询是否返回正确数据

#### 🚨 可能的保存问题：
- ❌ 项目ID为空或无效
- ❌ 用户未登录
- ❌ Delta格式错误
- ❌ 数据库权限问题
- ❌ 网络连接问题

### 第三步：检查加载过程
刷新主应用页面，再次进入同一章节，在监控工具的"加载过程监控"面板中查找：

#### 🔍 需要确认的加载信息：
- ✅ 章节ID是否正确
- ✅ 数据库查询是否成功
- ✅ 返回的内容格式是否正确
- ✅ 编辑器是否正确设置内容

#### 🚨 可能的加载问题：
- ❌ 章节ID不匹配
- ❌ 数据库查询失败
- ❌ 返回内容为空
- ❌ Delta格式解析错误
- ❌ 编辑器设置失败

## 📊 关键调试信息

### 保存过程关键日志：
```
🔄 开始保存章节到服务器...
📝 保存参数: {chapterId, deltaContentType, deltaOpsLength, wordCount}
✅ 项目ID: [项目ID]
✅ 用户已登录: [用户邮箱]
📋 现有章节信息: [章节信息]
💾 准备保存的章节数据: [数据详情]
✅ 章节已成功保存到数据库
✅ 验证成功，数据库中的内容: [验证结果]
```

### 加载过程关键日志：
```
🔄 开始从服务器加载章节内容...
📝 加载参数: {chapterId, chapterIdType}
✅ 项目ID: [项目ID]
📊 从数据库查询到的原始数据: [原始数据]
📋 章节详细信息: [详细信息]
✅ 内容格式验证：正确的Delta格式
📤 返回的内容: [返回内容]
```

## 🔧 常见问题和解决方案

### 问题1：项目ID问题
**症状：** 日志显示"无法获取项目ID"
**解决：** 
- 检查用户是否已登录
- 检查协作管理器是否正确初始化
- 确认数据库中是否有项目记录

### 问题2：章节ID不匹配
**症状：** 保存时使用一个ID，加载时使用另一个ID
**解决：**
- 检查`currentChapter.chapterId`和`currentChapter.outlineId`
- 确认章节ID的生成和传递逻辑
- 验证数据库中的章节记录

### 问题3：Delta格式问题
**症状：** 保存的不是标准Delta格式，或加载时解析失败
**解决：**
- 检查`quillEditor.getContents()`返回的格式
- 验证数据库中存储的内容格式
- 确认`quillEditor.setContents()`的调用

### 问题4：数据库权限问题
**症状：** 保存成功但查询失败，或RLS策略阻止访问
**解决：**
- 检查用户权限设置
- 验证RLS策略配置
- 确认项目成员关系

## 🎯 重点检查项

### 1. 数据一致性检查
在保存后立即验证：
```javascript
// 保存后的验证查询
const { data: verifyData } = await supabaseManager.supabase
    .from('chapters')
    .select('id, title, content, word_count, updated_at')
    .eq('id', chapterId)
    .single();
```

### 2. 章节ID映射检查
确认以下ID的对应关系：
- `currentChapter.id` (大纲ID)
- `currentChapter.chapterId` (数据库章节ID)
- `currentChapter.outlineId` (原始大纲ID)

### 3. 编辑器状态检查
在加载前后检查编辑器状态：
```javascript
// 加载前
console.log('加载前编辑器内容:', quillEditor.getText().length);

// 加载后
console.log('加载后编辑器内容:', quillEditor.getText().length);
```

## 📞 获取帮助

如果按照以上步骤仍无法解决问题，请提供以下信息：

1. **监控工具的完整日志**（保存和加载过程）
2. **浏览器控制台的错误信息**
3. **数据库中的实际数据**（可通过Supabase管理界面查看）
4. **用户登录状态和权限信息**

## 🚀 快速测试命令

在浏览器控制台中运行以下命令进行快速测试：

```javascript
// 检查当前状态
console.log('当前章节:', currentChapter);
console.log('编辑器内容:', quillEditor?.getContents());
console.log('项目ID:', collaborationManager?.currentProjectId);

// 手动触发保存
await saveCurrentChapterContent();

// 手动触发加载
await loadChapterContent(currentChapter);

// 检查本地存储
console.log('本地章节数据:', currentProject.chapters[currentChapter?.id]);
```

---

**重要提示：** 请确保在测试过程中保持监控工具开启，这样可以获得最详细的调试信息。
