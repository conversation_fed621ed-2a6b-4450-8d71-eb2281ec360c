<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节保存快速测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-button.success {
            background: #27ae60;
        }
        .test-button.error {
            background: #e74c3c;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>章节保存快速测试</h1>
        <p>快速验证章节保存和加载功能是否正常工作</p>

        <div class="test-section">
            <h3>测试步骤</h3>
            <ol>
                <li>在主应用中双击任意章节进入编写模式</li>
                <li>在编辑器中输入一些测试内容</li>
                <li>点击下面的"测试保存"按钮</li>
                <li>刷新主应用页面</li>
                <li>再次进入同一章节，检查内容是否保持</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="openMainApp()">打开主应用</button>
            <button class="test-button" onclick="testSave()">测试保存</button>
            <button class="test-button" onclick="testLoad()">测试加载</button>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>

        <div class="test-section">
            <h3>测试结果</h3>
            <div id="test-status">
                <span class="status info">等待测试</span>
            </div>
            <div id="test-log" class="test-log">
                测试日志将在这里显示...
            </div>
        </div>
    </div>

    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }

        function updateLogDisplay() {
            const logElement = document.getElementById('test-log');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusElement = document.getElementById('test-status');
            statusElement.innerHTML = `<span class="status ${type}">${message}</span>`;
        }

        function clearLog() {
            testLog = [];
            updateLogDisplay();
            updateStatus('日志已清除', 'info');
            log('测试日志已清除');
        }

        function openMainApp() {
            log('正在打开主应用...');
            window.open('./index.html', '_blank');
            updateStatus('主应用已在新窗口打开', 'success');
        }

        async function testSave() {
            log('开始测试保存功能...');
            updateStatus('正在测试保存...', 'warning');

            try {
                // 检查是否有主应用窗口
                const mainWindow = window.opener || window.parent;
                if (!mainWindow || mainWindow === window) {
                    throw new Error('无法访问主应用窗口，请确保从主应用打开此测试页面');
                }

                // 检查必要的函数和对象
                if (!mainWindow.currentChapter) {
                    throw new Error('没有当前选中的章节，请先在主应用中双击章节进入编写模式');
                }

                if (!mainWindow.quillEditor) {
                    throw new Error('Quill编辑器未初始化');
                }

                log(`当前章节: ${mainWindow.currentChapter.title || '未命名'}`);
                log(`章节ID: ${mainWindow.currentChapter.chapterId || '未设置'}`);

                // 获取编辑器内容
                const deltaContent = mainWindow.quillEditor.getContents();
                const textContent = mainWindow.quillEditor.getText();
                const wordCount = textContent.trim().length;

                log(`内容字数: ${wordCount}`);
                log(`Delta操作数: ${deltaContent?.ops?.length || 0}`);

                if (wordCount === 0) {
                    log('编辑器内容为空，请先输入一些测试内容', 'warning');
                    updateStatus('编辑器内容为空', 'warning');
                    return;
                }

                // 执行保存
                await mainWindow.saveCurrentChapterContent();
                
                log('保存操作已执行');
                updateStatus('保存测试完成', 'success');

                // 验证本地存储
                const localData = mainWindow.currentProject.chapters[mainWindow.currentChapter.id];
                if (localData && localData.content) {
                    log('本地存储验证: 成功');
                } else {
                    log('本地存储验证: 失败', 'error');
                }

            } catch (error) {
                log(`保存测试失败: ${error.message}`, 'error');
                updateStatus('保存测试失败', 'error');
            }
        }

        async function testLoad() {
            log('开始测试加载功能...');
            updateStatus('正在测试加载...', 'warning');

            try {
                const mainWindow = window.opener || window.parent;
                if (!mainWindow || mainWindow === window) {
                    throw new Error('无法访问主应用窗口');
                }

                if (!mainWindow.currentChapter || !mainWindow.currentChapter.chapterId) {
                    throw new Error('没有当前章节或章节ID');
                }

                const chapterId = mainWindow.currentChapter.chapterId;
                log(`正在加载章节: ${chapterId}`);

                // 执行加载
                const loadedContent = await mainWindow.loadChapterFromServer(chapterId);
                
                if (loadedContent) {
                    log('从服务器加载内容: 成功');
                    log(`加载的内容类型: ${typeof loadedContent}`);
                    
                    if (typeof loadedContent === 'object' && loadedContent.ops) {
                        log(`Delta操作数: ${loadedContent.ops.length}`);
                        updateStatus('加载测试完成 - Delta格式正确', 'success');
                    } else {
                        log('警告: 加载的内容不是Delta格式', 'warning');
                        updateStatus('加载测试完成 - 格式异常', 'warning');
                    }
                } else {
                    log('从服务器加载内容: 为空', 'warning');
                    updateStatus('加载测试完成 - 内容为空', 'warning');
                }

            } catch (error) {
                log(`加载测试失败: ${error.message}`, 'error');
                updateStatus('加载测试失败', 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('章节保存快速测试页面已加载');
            log('请按照测试步骤进行操作');
        });

        // 监听来自主应用的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'chapter-save-result') {
                log(`收到保存结果: ${event.data.success ? '成功' : '失败'}`);
                if (event.data.error) {
                    log(`错误信息: ${event.data.error}`, 'error');
                }
            }
        });
    </script>
</body>
</html>
