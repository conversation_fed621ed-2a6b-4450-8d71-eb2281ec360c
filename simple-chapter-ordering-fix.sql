-- 简化版书籍目录排序修复脚本
-- 直接使用UPDATE语句修复章节和大纲的排序问题

-- ========================================
-- 第一步：查看当前排序状况
-- ========================================

-- 查看当前大纲排序
SELECT 
    o.project_id,
    o.title,
    o.level,
    o.sort_order,
    o.created_at
FROM public.outlines o
ORDER BY o.project_id, o.sort_order NULLS LAST, o.created_at;

-- 查看当前章节排序
SELECT 
    c.project_id,
    c.title,
    c.order_index,
    c.created_at
FROM public.chapters c
ORDER BY c.project_id, c.order_index NULLS LAST, c.created_at;

-- ========================================
-- 第二步：修复大纲排序（outlines表）
-- ========================================

-- 为前言和序言设置排序值
UPDATE public.outlines 
SET sort_order = 0
WHERE (title ILIKE '%前言%' OR title ILIKE '%序%' OR title ILIKE '%preface%')
AND sort_order != 0;

-- 为第0章设置排序值
UPDATE public.outlines 
SET sort_order = 10
WHERE (title ILIKE '%第0章%' OR title ILIKE '%第零章%' OR title ILIKE '%概述%' OR title ILIKE '%引言%')
AND sort_order != 10;

-- 为第1章设置排序值
UPDATE public.outlines 
SET sort_order = 100
WHERE (title ILIKE '%第1章%' OR title ILIKE '%第一章%')
AND sort_order != 100;

-- 为第2章设置排序值
UPDATE public.outlines 
SET sort_order = 200
WHERE (title ILIKE '%第2章%' OR title ILIKE '%第二章%')
AND sort_order != 200;

-- 为第3章设置排序值
UPDATE public.outlines 
SET sort_order = 300
WHERE (title ILIKE '%第3章%' OR title ILIKE '%第三章%')
AND sort_order != 300;

-- 为第4章设置排序值
UPDATE public.outlines 
SET sort_order = 400
WHERE (title ILIKE '%第4章%' OR title ILIKE '%第四章%')
AND sort_order != 400;

-- 为第5章设置排序值
UPDATE public.outlines 
SET sort_order = 500
WHERE (title ILIKE '%第5章%' OR title ILIKE '%第五章%')
AND sort_order != 500;

-- 为第6章设置排序值
UPDATE public.outlines 
SET sort_order = 600
WHERE (title ILIKE '%第6章%' OR title ILIKE '%第六章%')
AND sort_order != 600;

-- 为第7章设置排序值
UPDATE public.outlines 
SET sort_order = 700
WHERE (title ILIKE '%第7章%' OR title ILIKE '%第七章%')
AND sort_order != 700;

-- 为第8章设置排序值
UPDATE public.outlines 
SET sort_order = 800
WHERE (title ILIKE '%第8章%' OR title ILIKE '%第八章%')
AND sort_order != 800;

-- 为第9章设置排序值
UPDATE public.outlines 
SET sort_order = 900
WHERE (title ILIKE '%第9章%' OR title ILIKE '%第九章%')
AND sort_order != 900;

-- 为第10章设置排序值
UPDATE public.outlines 
SET sort_order = 1000
WHERE (title ILIKE '%第10章%' OR title ILIKE '%第十章%')
AND sort_order != 1000;

-- 为附录设置排序值
UPDATE public.outlines 
SET sort_order = 9000
WHERE (title ILIKE '%附录%' OR title ILIKE '%appendix%')
AND sort_order != 9000;

-- 为参考文献设置排序值
UPDATE public.outlines 
SET sort_order = 9100
WHERE (title ILIKE '%参考文献%' OR title ILIKE '%references%')
AND sort_order != 9100;

-- 为索引设置排序值
UPDATE public.outlines 
SET sort_order = 9200
WHERE (title ILIKE '%索引%' OR title ILIKE '%index%')
AND sort_order != 9200;

-- 为后记和致谢设置排序值
UPDATE public.outlines 
SET sort_order = 9300
WHERE (title ILIKE '%后记%' OR title ILIKE '%致谢%' OR title ILIKE '%acknowledgments%')
AND sort_order != 9300;

-- ========================================
-- 第三步：修复章节排序（chapters表）
-- ========================================

-- 为前言和序言设置排序值
UPDATE public.chapters 
SET order_index = 0
WHERE (title ILIKE '%前言%' OR title ILIKE '%序%' OR title ILIKE '%preface%')
AND order_index != 0;

-- 为第0章设置排序值
UPDATE public.chapters 
SET order_index = 10
WHERE (title ILIKE '%第0章%' OR title ILIKE '%第零章%' OR title ILIKE '%概述%' OR title ILIKE '%引言%')
AND order_index != 10;

-- 为第1章设置排序值
UPDATE public.chapters 
SET order_index = 100
WHERE (title ILIKE '%第1章%' OR title ILIKE '%第一章%')
AND order_index != 100;

-- 为第2章设置排序值
UPDATE public.chapters 
SET order_index = 200
WHERE (title ILIKE '%第2章%' OR title ILIKE '%第二章%')
AND order_index != 200;

-- 为第3章设置排序值
UPDATE public.chapters 
SET order_index = 300
WHERE (title ILIKE '%第3章%' OR title ILIKE '%第三章%')
AND order_index != 300;

-- 为第4章设置排序值
UPDATE public.chapters 
SET order_index = 400
WHERE (title ILIKE '%第4章%' OR title ILIKE '%第四章%')
AND order_index != 400;

-- 为第5章设置排序值
UPDATE public.chapters 
SET order_index = 500
WHERE (title ILIKE '%第5章%' OR title ILIKE '%第五章%')
AND order_index != 500;

-- 为第6章设置排序值
UPDATE public.chapters 
SET order_index = 600
WHERE (title ILIKE '%第6章%' OR title ILIKE '%第六章%')
AND order_index != 600;

-- 为第7章设置排序值
UPDATE public.chapters 
SET order_index = 700
WHERE (title ILIKE '%第7章%' OR title ILIKE '%第七章%')
AND order_index != 700;

-- 为第8章设置排序值
UPDATE public.chapters 
SET order_index = 800
WHERE (title ILIKE '%第8章%' OR title ILIKE '%第八章%')
AND order_index != 800;

-- 为第9章设置排序值
UPDATE public.chapters 
SET order_index = 900
WHERE (title ILIKE '%第9章%' OR title ILIKE '%第九章%')
AND order_index != 900;

-- 为第10章设置排序值
UPDATE public.chapters 
SET order_index = 1000
WHERE (title ILIKE '%第10章%' OR title ILIKE '%第十章%')
AND order_index != 1000;

-- 为附录设置排序值
UPDATE public.chapters 
SET order_index = 9000
WHERE (title ILIKE '%附录%' OR title ILIKE '%appendix%')
AND order_index != 9000;

-- 为参考文献设置排序值
UPDATE public.chapters 
SET order_index = 9100
WHERE (title ILIKE '%参考文献%' OR title ILIKE '%references%')
AND order_index != 9100;

-- 为索引设置排序值
UPDATE public.chapters 
SET order_index = 9200
WHERE (title ILIKE '%索引%' OR title ILIKE '%index%')
AND order_index != 9200;

-- 为后记和致谢设置排序值
UPDATE public.chapters 
SET order_index = 9300
WHERE (title ILIKE '%后记%' OR title ILIKE '%致谢%' OR title ILIKE '%acknowledgments%')
AND order_index != 9300;

-- ========================================
-- 第四步：处理NULL值和默认排序
-- ========================================

-- 为没有排序值的大纲项设置默认值
UPDATE public.outlines
SET sort_order = 5000 + (level * 100) + (EXTRACT(EPOCH FROM created_at)::int % 100)
WHERE sort_order IS NULL;

-- 为没有排序值的章节设置默认值
UPDATE public.chapters
SET order_index = 5000 + (EXTRACT(EPOCH FROM created_at)::int % 1000)
WHERE order_index IS NULL;

-- ========================================
-- 第五步：同步大纲和章节的排序
-- ========================================

-- 确保关联的大纲和章节具有相同的排序值
UPDATE public.chapters 
SET order_index = o.sort_order
FROM public.outlines o
WHERE chapters.outline_id = o.id
AND chapters.order_index != o.sort_order;

-- ========================================
-- 第六步：验证修复结果
-- ========================================

-- 查看修复后的大纲排序
SELECT 
    o.project_id,
    o.title,
    o.level,
    o.sort_order,
    '大纲' as type
FROM public.outlines o
ORDER BY o.project_id, o.sort_order;

-- 查看修复后的章节排序
SELECT 
    c.project_id,
    c.title,
    c.order_index,
    '章节' as type
FROM public.chapters c
ORDER BY c.project_id, c.order_index;

-- 检查排序一致性
SELECT 
    o.project_id,
    o.title as outline_title,
    o.sort_order,
    c.title as chapter_title,
    c.order_index,
    CASE 
        WHEN o.sort_order = c.order_index THEN '✅ 一致'
        WHEN c.id IS NULL THEN '⚠️ 无对应章节'
        ELSE '❌ 排序不一致'
    END as consistency_status
FROM public.outlines o
LEFT JOIN public.chapters c ON o.id = c.outline_id
ORDER BY o.project_id, o.sort_order;

-- 统计修复结果
SELECT 
    '大纲排序修复统计' as category,
    COUNT(*) as total_items,
    COUNT(CASE WHEN sort_order IS NOT NULL THEN 1 END) as items_with_order,
    COUNT(CASE WHEN sort_order IS NULL THEN 1 END) as items_without_order
FROM public.outlines

UNION ALL

SELECT 
    '章节排序修复统计' as category,
    COUNT(*) as total_items,
    COUNT(CASE WHEN order_index IS NOT NULL THEN 1 END) as items_with_order,
    COUNT(CASE WHEN order_index IS NULL THEN 1 END) as items_without_order
FROM public.chapters;
