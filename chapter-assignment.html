<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节分配管理 - 专业著作智能编纂系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="chapter-assignment.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <h1 class="system-title">
                    <i class="fas fa-book-open"></i>
                    专业著作智能编纂系统
                </h1>
                <div class="breadcrumb">
                    <a href="index.html">首页</a>
                    <i class="fas fa-chevron-right"></i>
                    <span>章节分配管理</span>
                </div>
            </div>
            <div class="header-right">
                <div class="project-selector">
                    <i class="fas fa-project-diagram"></i>
                    <span id="current-project-name">选择项目</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="user-menu">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <span id="user-name">用户</span>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <h3>章节管理</h3>
                    <ul>
                        <li class="nav-item active">
                            <a href="#overview" data-tab="overview">
                                <i class="fas fa-chart-pie"></i>
                                <span>项目概览</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#assignments" data-tab="assignments">
                                <i class="fas fa-tasks"></i>
                                <span>章节分配</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#progress" data-tab="progress">
                                <i class="fas fa-chart-line"></i>
                                <span>进度跟踪</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#reviews" data-tab="reviews">
                                <i class="fas fa-clipboard-check"></i>
                                <span>审核管理</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="nav-section">
                    <h3>协作工具</h3>
                    <ul>
                        <li class="nav-item">
                            <a href="#discussions" data-tab="discussions">
                                <i class="fas fa-comments"></i>
                                <span>讨论区</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#files" data-tab="files">
                                <i class="fas fa-folder"></i>
                                <span>文件管理</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#reports" data-tab="reports">
                                <i class="fas fa-chart-bar"></i>
                                <span>统计报告</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 项目概览面板 -->
            <section id="overview-panel" class="content-panel active">
                <div class="panel-header">
                    <h2>项目概览</h2>
                    <div class="panel-actions">
                        <button id="create-assignment-btn" class="btn btn-primary" onclick="showCreateAssignmentModal()">
                            <i class="fas fa-plus"></i>
                            新建分配
                        </button>
                        <button class="btn btn-secondary" onclick="refreshOverview()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-chapters">0</h3>
                            <p>总章节数</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-authors">0</h3>
                            <p>参与作者</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completed-chapters">0</h3>
                            <p>已完成章节</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pending-reviews">0</h3>
                            <p>待审核</p>
                        </div>
                    </div>
                </div>

                <!-- 进度图表 -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>项目进度</h3>
                        <div class="chart-controls">
                            <select id="chart-period">
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                                <option value="quarter">本季度</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-content">
                        <canvas id="progress-chart"></canvas>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="recent-activity">
                    <h3>最近活动</h3>
                    <div id="activity-list" class="activity-list">
                        <!-- 活动项目将通过JavaScript动态加载 -->
                    </div>
                </div>
            </section>

            <!-- 章节分配面板 -->
            <section id="assignments-panel" class="content-panel">
                <div class="panel-header">
                    <h2>章节分配</h2>
                    <div class="panel-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="assignment-search" placeholder="搜索章节或作者...">
                        </div>
                        <select id="status-filter">
                            <option value="">所有状态</option>
                            <option value="pending">待确认</option>
                            <option value="in_progress">进行中</option>
                            <option value="reviewing">审核中</option>
                            <option value="completed">已完成</option>
                        </select>
                        <button id="create-assignment-btn-2" class="btn btn-primary" onclick="showCreateAssignmentModal()">
                            <i class="fas fa-plus"></i>
                            新建分配
                        </button>
                    </div>
                </div>

                <!-- 分配列表 -->
                <div class="assignments-container">
                    <div class="assignments-header">
                        <div class="header-cell">章节</div>
                        <div class="header-cell">主笔作者</div>
                        <div class="header-cell">协作者</div>
                        <div class="header-cell">状态</div>
                        <div class="header-cell">进度</div>
                        <div class="header-cell">截止日期</div>
                        <div class="header-cell">操作</div>
                    </div>
                    <div id="assignments-list" class="assignments-list">
                        <!-- 分配项目将通过JavaScript动态加载 -->
                    </div>
                </div>
            </section>

            <!-- 进度跟踪面板 -->
            <section id="progress-panel" class="content-panel">
                <div class="panel-header">
                    <h2>进度跟踪</h2>
                    <div class="panel-actions">
                        <select id="progress-view">
                            <option value="timeline">时间线视图</option>
                            <option value="kanban">看板视图</option>
                            <option value="gantt">甘特图</option>
                        </select>
                    </div>
                </div>
                
                <div id="progress-content" class="progress-content">
                    <!-- 进度内容将根据选择的视图动态加载 -->
                </div>
            </section>

            <!-- 审核管理面板 -->
            <section id="reviews-panel" class="content-panel">
                <div class="panel-header">
                    <h2>审核管理</h2>
                    <div class="panel-actions">
                        <select id="review-filter">
                            <option value="">所有审核</option>
                            <option value="pending">待审核</option>
                            <option value="in_progress">审核中</option>
                            <option value="completed">已完成</option>
                        </select>
                    </div>
                </div>
                
                <div id="reviews-content" class="reviews-content">
                    <!-- 审核内容将通过JavaScript动态加载 -->
                </div>
            </section>

            <!-- 讨论区面板 -->
            <section id="discussions-panel" class="content-panel">
                <div class="panel-header">
                    <h2>讨论区</h2>
                    <div class="panel-actions">
                        <button class="btn btn-primary" onclick="showNewDiscussionModal()">
                            <i class="fas fa-plus"></i>
                            新建讨论
                        </button>
                    </div>
                </div>
                
                <div id="discussions-content" class="discussions-content">
                    <!-- 讨论内容将通过JavaScript动态加载 -->
                </div>
            </section>

            <!-- 文件管理面板 -->
            <section id="files-panel" class="content-panel">
                <div class="panel-header">
                    <h2>文件管理</h2>
                    <div class="panel-actions">
                        <button class="btn btn-primary" onclick="showUploadModal()">
                            <i class="fas fa-upload"></i>
                            上传文件
                        </button>
                    </div>
                </div>
                
                <div id="files-content" class="files-content">
                    <!-- 文件内容将通过JavaScript动态加载 -->
                </div>
            </section>

            <!-- 统计报告面板 -->
            <section id="reports-panel" class="content-panel">
                <div class="panel-header">
                    <h2>统计报告</h2>
                    <div class="panel-actions">
                        <button class="btn btn-secondary" onclick="exportReport()">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>
                
                <div id="reports-content" class="reports-content">
                    <!-- 报告内容将通过JavaScript动态加载 -->
                </div>
            </section>
        </div>
    </main>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <!-- 脚本文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="supabase-config.js"></script>
    <script src="role-permission-manager.js"></script>
    <script src="chapter-assignment.js"></script>
</body>
</html>
