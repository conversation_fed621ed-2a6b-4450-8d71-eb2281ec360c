#!/bin/bash

# AI增强学术专著编写系统 - 系统监控脚本
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker服务状态
check_docker_services() {
    echo "=== Docker服务状态 ==="
    
    local services=("postgres" "postgrest" "gotrue" "realtime" "storage" "redis")
    local all_healthy=true
    
    for service in "${services[@]}"; do
        local container_name="llm-book-$service"
        local status=$(docker inspect --format='{{.State.Status}}' "$container_name" 2>/dev/null || echo "not_found")
        
        case $status in
            "running")
                log_success "$service: 运行中"
                ;;
            "exited")
                log_error "$service: 已停止"
                all_healthy=false
                ;;
            "not_found")
                log_error "$service: 容器不存在"
                all_healthy=false
                ;;
            *)
                log_warning "$service: 状态异常 ($status)"
                all_healthy=false
                ;;
        esac
    done
    
    if $all_healthy; then
        log_success "所有服务运行正常"
    else
        log_error "发现服务异常"
    fi
    
    echo ""
}

# 检查系统资源使用情况
check_system_resources() {
    echo "=== 系统资源使用情况 ==="
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo "CPU使用率: ${cpu_usage}%"
    
    # 内存使用情况
    local memory_info=$(free -h | grep "Mem:")
    local memory_used=$(echo $memory_info | awk '{print $3}')
    local memory_total=$(echo $memory_info | awk '{print $2}')
    local memory_percent=$(free | grep "Mem:" | awk '{printf "%.1f", $3/$2 * 100.0}')
    echo "内存使用: $memory_used / $memory_total (${memory_percent}%)"
    
    # 磁盘使用情况
    echo "磁盘使用情况:"
    df -h | grep -E "/$|/opt" | while read line; do
        echo "  $line"
    done
    
    # Docker容器资源使用
    echo ""
    echo "Docker容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | head -10
    
    echo ""
}

# 检查数据库状态
check_database_status() {
    echo "=== 数据库状态 ==="
    
    source .env
    
    # 检查数据库连接
    if docker-compose exec -T postgres pg_isready -U $POSTGRES_USER -d $POSTGRES_DB >/dev/null 2>&1; then
        log_success "数据库连接正常"
        
        # 获取数据库统计信息
        local db_size=$(docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT pg_size_pretty(pg_database_size('$POSTGRES_DB'));" | tr -d ' ')
        echo "数据库大小: $db_size"
        
        local table_count=$(docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema='public';" | tr -d ' ')
        echo "数据表数量: $table_count"
        
        local connection_count=$(docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" | tr -d ' ')
        echo "活跃连接数: $connection_count"
        
        # 检查慢查询
        local slow_queries=$(docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT count(*) FROM pg_stat_statements WHERE mean_time > 1000;" 2>/dev/null | tr -d ' ' || echo "0")
        if [ "$slow_queries" -gt 0 ]; then
            log_warning "发现 $slow_queries 个慢查询"
        fi
        
    else
        log_error "数据库连接失败"
    fi
    
    echo ""
}

# 检查API服务状态
check_api_services() {
    echo "=== API服务状态 ==="
    
    local endpoints=(
        "http://localhost:3001/health:PostgREST API"
        "http://localhost:9999/health:GoTrue 认证"
        "http://localhost:4000/health:Realtime 服务"
        "http://localhost:5000/health:Storage 服务"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local url=$(echo $endpoint | cut -d':' -f1-2)
        local name=$(echo $endpoint | cut -d':' -f3)
        
        if curl -f -s "$url" >/dev/null 2>&1; then
            log_success "$name: 正常"
        else
            log_error "$name: 异常"
        fi
    done
    
    echo ""
}

# 检查日志错误
check_logs_for_errors() {
    echo "=== 日志错误检查 ==="
    
    local services=("postgres" "postgrest" "gotrue" "realtime" "storage")
    local error_found=false
    
    for service in "${services[@]}"; do
        local container_name="llm-book-$service"
        local error_count=$(docker logs "$container_name" --since="1h" 2>&1 | grep -i "error\|exception\|failed" | wc -l)
        
        if [ "$error_count" -gt 0 ]; then
            log_warning "$service: 发现 $error_count 个错误日志"
            error_found=true
        else
            log_success "$service: 无错误日志"
        fi
    done
    
    if ! $error_found; then
        log_success "所有服务日志正常"
    fi
    
    echo ""
}

# 检查备份状态
check_backup_status() {
    echo "=== 备份状态检查 ==="
    
    local backup_dir="/opt/backups/llm-book-system"
    
    if [ -d "$backup_dir" ]; then
        local latest_backup=$(find "$backup_dir" -name "database_*.sql.gz" | sort | tail -1)
        
        if [ -n "$latest_backup" ]; then
            local backup_date=$(stat -c %y "$latest_backup" | cut -d' ' -f1)
            local days_old=$(( ($(date +%s) - $(stat -c %Y "$latest_backup")) / 86400 ))
            
            echo "最新备份: $backup_date ($days_old 天前)"
            
            if [ "$days_old" -le 1 ]; then
                log_success "备份状态正常"
            elif [ "$days_old" -le 7 ]; then
                log_warning "备份较旧，建议尽快备份"
            else
                log_error "备份过旧，请立即备份"
            fi
        else
            log_error "未找到数据库备份文件"
        fi
    else
        log_error "备份目录不存在"
    fi
    
    echo ""
}

# 检查AI服务状态
check_ai_services() {
    echo "=== AI服务状态 ==="
    
    source .env
    
    if [ -n "$OPENROUTER_API_KEY" ] && [ "$OPENROUTER_API_KEY" != "your_openrouter_api_key_here" ]; then
        # 测试API连接
        local api_test=$(curl -s -w "%{http_code}" -o /dev/null \
            -H "Authorization: Bearer $OPENROUTER_API_KEY" \
            -H "Content-Type: application/json" \
            "https://openrouter.ai/api/v1/models" || echo "000")
        
        if [ "$api_test" = "200" ]; then
            log_success "OpenRouter API连接正常"
        else
            log_error "OpenRouter API连接失败 (HTTP: $api_test)"
        fi
    else
        log_warning "OpenRouter API密钥未配置"
    fi
    
    # 检查AI任务统计
    if docker-compose exec -T postgres pg_isready -U $POSTGRES_USER -d $POSTGRES_DB >/dev/null 2>&1; then
        local today_tasks=$(docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT count(*) FROM ai_tasks WHERE created_at >= CURRENT_DATE;" 2>/dev/null | tr -d ' ' || echo "0")
        local failed_tasks=$(docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT count(*) FROM ai_tasks WHERE status = 'failed' AND created_at >= CURRENT_DATE;" 2>/dev/null | tr -d ' ' || echo "0")
        
        echo "今日AI任务: $today_tasks"
        echo "失败任务: $failed_tasks"
        
        if [ "$failed_tasks" -gt 0 ]; then
            log_warning "发现失败的AI任务"
        fi
    fi
    
    echo ""
}

# 生成监控报告
generate_report() {
    local report_file="/tmp/monitor_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "AI增强学术专著编写系统监控报告"
        echo "========================================"
        echo "生成时间: $(date)"
        echo ""
        
        echo "系统信息:"
        echo "  操作系统: $(uname -a)"
        echo "  运行时间: $(uptime)"
        echo ""
        
        echo "Docker版本:"
        docker --version
        docker-compose --version
        echo ""
        
        echo "服务状态:"
        docker-compose ps
        echo ""
        
        echo "资源使用:"
        free -h
        df -h
        echo ""
        
        echo "网络连接:"
        netstat -tlnp | grep -E ":80|:443|:3001|:4000|:5000|:9999"
        
    } > "$report_file"
    
    echo "监控报告已生成: $report_file"
}

# 自动修复常见问题
auto_fix_issues() {
    echo "=== 自动修复检查 ==="
    
    # 检查并重启异常服务
    local services=("postgres" "postgrest" "gotrue" "realtime" "storage" "redis")
    
    for service in "${services[@]}"; do
        local container_name="llm-book-$service"
        local status=$(docker inspect --format='{{.State.Status}}' "$container_name" 2>/dev/null || echo "not_found")
        
        if [ "$status" = "exited" ]; then
            log_info "尝试重启服务: $service"
            docker-compose restart "$service"
            sleep 5
            
            local new_status=$(docker inspect --format='{{.State.Status}}' "$container_name" 2>/dev/null || echo "not_found")
            if [ "$new_status" = "running" ]; then
                log_success "$service 重启成功"
            else
                log_error "$service 重启失败"
            fi
        fi
    done
    
    # 清理过期的缓存
    if docker-compose exec -T postgres pg_isready -U $POSTGRES_USER -d $POSTGRES_DB >/dev/null 2>&1; then
        docker-compose exec -T postgres psql -U $POSTGRES_USER -d $POSTGRES_DB -c "DELETE FROM reference_cache WHERE expires_at < NOW();" >/dev/null 2>&1 || true
        log_info "已清理过期缓存"
    fi
    
    echo ""
}

# 显示使用帮助
show_help() {
    cat << EOF
AI增强学术专著编写系统监控脚本

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -q, --quick         快速检查（仅检查服务状态）
  -r, --report        生成详细监控报告
  -f, --fix           自动修复常见问题
  -w, --watch         持续监控模式

示例:
  $0                  # 完整监控检查
  $0 -q              # 快速检查
  $0 -r              # 生成报告
  $0 -f              # 自动修复
  $0 -w              # 持续监控
EOF
}

# 持续监控模式
watch_mode() {
    echo "进入持续监控模式 (按 Ctrl+C 退出)"
    echo ""
    
    while true; do
        clear
        echo "=== 持续监控 - $(date) ==="
        echo ""
        
        check_docker_services
        check_system_resources
        check_database_status
        
        echo "下次更新: $(date -d '+30 seconds')"
        sleep 30
    done
}

# 快速检查模式
quick_check() {
    echo "=== 快速状态检查 ==="
    echo ""
    
    check_docker_services
    check_api_services
    
    echo "快速检查完成"
}

# 主函数
main() {
    echo "=== AI增强学术专著编写系统监控 ==="
    echo "检查时间: $(date)"
    echo ""
    
    check_docker_services
    check_system_resources
    check_database_status
    check_api_services
    check_logs_for_errors
    check_backup_status
    check_ai_services
    
    echo "=== 监控检查完成 ==="
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -q|--quick)
        quick_check
        exit 0
        ;;
    -r|--report)
        main
        generate_report
        exit 0
        ;;
    -f|--fix)
        auto_fix_issues
        exit 0
        ;;
    -w|--watch)
        watch_mode
        exit 0
        ;;
    "")
        main
        exit 0
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
