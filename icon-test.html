<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标测试页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background: #f8fafc;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .icon-test {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .icon-test i {
            font-size: 1.5rem;
            width: 24px;
            text-align: center;
        }
        
        .icon-test .icon-name {
            font-family: monospace;
            background: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        
        .fab-menu-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            width: 100%;
            padding: 0.75rem 1rem;
            background: white;
            border: none;
            color: #374151;
            font-size: 0.875rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            text-align: left;
            margin-bottom: 0.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .fab-menu-item:hover {
            background: #f3f4f6;
            color: #1f2937;
            transform: translateX(4px);
        }
        
        .fab-menu-item i {
            width: 16px;
            text-align: center;
            color: #6b7280;
        }
        
        .fab-menu-item:hover i {
            color: #3b82f6;
        }
        
        .fab-menu-item span {
            font-weight: 500;
        }
        
        /* 特定功能的颜色 */
        .fab-menu-item[data-action="polish"]:hover {
            background: #fef3c7;
            color: #92400e;
        }
        
        .fab-menu-item[data-action="polish"]:hover i {
            color: #f59e0b;
        }
        
        .alternative-icons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .alternative-icon {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .alternative-icon:hover {
            background: #f3f4f6;
            border-color: #3b82f6;
        }
        
        .alternative-icon i {
            font-size: 1.25rem;
            color: #3b82f6;
        }
        
        .recommended {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .recommended i {
            color: #10b981;
        }
    </style>
</head>
<body>
    <h1>AI助手图标测试页面</h1>
    
    <div class="test-section">
        <h2>当前图标测试</h2>
        <p>测试当前使用的图标是否正常显示：</p>
        
        <div class="icon-test">
            <i class="fas fa-sparkles"></i>
            <span>润色功能图标</span>
            <span class="icon-name">fas fa-sparkles</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-language"></i>
            <span>翻译功能图标</span>
            <span class="icon-name">fas fa-language</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-lightbulb"></i>
            <span>解读功能图标</span>
            <span class="icon-name">fas fa-lightbulb</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-edit"></i>
            <span>重写功能图标</span>
            <span class="icon-name">fas fa-edit</span>
        </div>
    </div>
    
    <div class="test-section">
        <h2>实际按钮效果测试</h2>
        <p>模拟实际的AI助手菜单按钮：</p>
        
        <button class="fab-menu-item" data-action="polish" title="内容润色">
            <i class="fas fa-sparkles"></i>
            <span>润色</span>
        </button>
        
        <button class="fab-menu-item" data-action="translate" title="翻译">
            <i class="fas fa-language"></i>
            <span>翻译</span>
        </button>
        
        <button class="fab-menu-item" data-action="explain" title="解读">
            <i class="fas fa-lightbulb"></i>
            <span>解读</span>
        </button>
        
        <button class="fab-menu-item" data-action="rewrite" title="重写">
            <i class="fas fa-edit"></i>
            <span>重写</span>
        </button>
    </div>
    
    <div class="test-section">
        <h2>润色功能的替代图标选项</h2>
        <p>如果当前图标不显示，以下是一些适合"润色"功能的替代图标：</p>
        
        <div class="alternative-icons">
            <div class="alternative-icon recommended">
                <i class="fas fa-sparkles"></i>
                <span>fa-sparkles (推荐)</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-magic"></i>
                <span>fa-magic</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-wand-magic-sparkles"></i>
                <span>fa-wand-magic-sparkles</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-star"></i>
                <span>fa-star</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-gem"></i>
                <span>fa-gem</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-palette"></i>
                <span>fa-palette</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-brush"></i>
                <span>fa-brush</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-feather"></i>
                <span>fa-feather</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-pen-fancy"></i>
                <span>fa-pen-fancy</span>
            </div>
            
            <div class="alternative-icon">
                <i class="fas fa-highlighter"></i>
                <span>fa-highlighter</span>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>诊断信息</h2>
        <p>检查Font Awesome是否正确加载：</p>
        <div id="diagnostic-info"></div>
    </div>
    
    <script>
        // 检查Font Awesome是否正确加载
        function checkFontAwesome() {
            const diagnosticDiv = document.getElementById('diagnostic-info');
            
            // 检查CSS是否加载
            const links = document.querySelectorAll('link[href*="font-awesome"]');
            diagnosticDiv.innerHTML += `<p>Font Awesome CSS链接数量: ${links.length}</p>`;
            
            // 检查图标元素是否存在
            const icons = document.querySelectorAll('i[class*="fa"]');
            diagnosticDiv.innerHTML += `<p>页面中的图标元素数量: ${icons.length}</p>`;
            
            // 检查特定图标
            const sparklesIcon = document.querySelector('.fas.fa-sparkles');
            if (sparklesIcon) {
                const computedStyle = window.getComputedStyle(sparklesIcon, '::before');
                const content = computedStyle.getPropertyValue('content');
                diagnosticDiv.innerHTML += `<p>fa-sparkles图标内容: ${content}</p>`;
                diagnosticDiv.innerHTML += `<p>fa-sparkles字体族: ${computedStyle.fontFamily}</p>`;
            }
        }
        
        // 页面加载完成后执行检查
        window.addEventListener('load', checkFontAwesome);
        
        // 点击替代图标时的处理
        document.querySelectorAll('.alternative-icon').forEach(icon => {
            icon.addEventListener('click', function() {
                const iconClass = this.querySelector('i').className;
                const iconName = this.querySelector('span').textContent;
                alert(`选择的图标: ${iconName}\n类名: ${iconClass}\n\n您可以将此图标类名替换到代码中的 "fas fa-sparkles"`);
            });
        });
    </script>
</body>
</html>
