// 章节分配管理系统
// 专业学术协作编著平台

class ChapterAssignmentManager {
    constructor() {
        this.currentProject = null;
        this.currentUser = null;
        this.assignments = [];
        this.collaborators = [];
        this.reviews = [];
        this.discussions = [];
        this.workLogs = [];
        
        // UI状态
        this.activeTab = 'overview';
        this.filters = {
            status: '',
            author: '',
            search: ''
        };
        
        // 图表实例
        this.progressChart = null;
        
        this.init();
    }
    
    // 初始化系统
    async init() {
        try {
            await this.checkAuthentication();
            await this.loadCurrentProject();
            this.setupEventListeners();
            this.setupNavigation();
            await this.loadInitialData();
            
            console.log('章节分配管理系统初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showNotification('系统初始化失败', 'error');
        }
    }
    
    // 检查用户认证
    async checkAuthentication() {
        const user = await supabaseManager.getCurrentUser();
        if (!user) {
            window.location.href = 'auth.html';
            return;
        }

        this.currentUser = user;
        await this.loadUserRole();
        this.updateUserDisplay();
    }

    // 加载用户角色
    async loadUserRole() {
        if (!this.currentUser || !this.currentProject) return;

        try {
            // 尝试从数据库获取用户角色
            const { data: member, error } = await supabaseManager.supabase
                .from('project_members')
                .select('role')
                .eq('project_id', this.currentProject.id)
                .eq('user_id', this.currentUser.id)
                .single();

            if (error) {
                console.warn('获取用户角色失败，使用默认角色:', error);
                // 使用默认角色进行演示
                this.currentUserRole = 'chief_editor'; // 默认为主编角色
            } else {
                this.currentUserRole = member.role;
            }

            // 设置权限管理器的当前用户
            if (window.rolePermissionManager) {
                window.rolePermissionManager.setCurrentUser(this.currentUser, this.currentUserRole);
            }

            // 更新UI权限
            this.updateUIPermissions();

            console.log('用户角色已设置:', this.currentUserRole);

        } catch (error) {
            console.error('加载用户角色失败:', error);
            // 使用默认角色
            this.currentUserRole = 'chief_editor';
            if (window.rolePermissionManager) {
                window.rolePermissionManager.setCurrentUser(this.currentUser, this.currentUserRole);
            }
            this.updateUIPermissions();
        }
    }

    // 更新UI权限显示
    updateUIPermissions() {
        if (!window.rolePermissionManager || !this.currentUserRole) return;

        // 检查各种权限并更新UI
        const canAssign = window.rolePermissionManager.canAssignChapters(this.currentUserRole);
        const canManage = window.rolePermissionManager.canManageMembers(this.currentUserRole);
        const canReview = window.rolePermissionManager.canReviewContent(this.currentUserRole);

        // 更新按钮显示
        this.updateButtonVisibility('create-assignment-btn', canAssign);
        this.updateButtonVisibility('manage-members-btn', canManage);
        this.updateButtonVisibility('review-content-btn', canReview);

        // 更新导航菜单
        this.updateNavPermissions();
    }

    // 更新按钮可见性
    updateButtonVisibility(buttonId, hasPermission) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = hasPermission ? 'inline-flex' : 'none';
        }
    }

    // 更新导航权限
    updateNavPermissions() {
        const navItems = {
            'assignments': window.rolePermissionManager.hasPermission('chapter.view', this.currentUserRole),
            'reviews': window.rolePermissionManager.hasPermission('review.view', this.currentUserRole),
            'files': window.rolePermissionManager.hasPermission('file.view', this.currentUserRole),
            'reports': window.rolePermissionManager.hasPermission('report.view', this.currentUserRole)
        };

        for (const [tab, hasPermission] of Object.entries(navItems)) {
            const navItem = document.querySelector(`[data-tab="${tab}"]`);
            if (navItem) {
                const listItem = navItem.closest('.nav-item');
                if (listItem) {
                    listItem.style.display = hasPermission ? 'block' : 'none';
                }
            }
        }
    }
    
    // 加载当前项目
    async loadCurrentProject() {
        // 尝试从URL参数获取项目ID
        const urlParams = new URLSearchParams(window.location.search);
        let projectId = urlParams.get('project');

        // 如果没有，尝试从localStorage获取
        if (!projectId) {
            projectId = localStorage.getItem('currentProjectId');
        }

        // 如果还没有，尝试从全局变量获取
        if (!projectId && typeof currentProject !== 'undefined' && currentProject.id) {
            projectId = currentProject.id;
        }

        // 如果仍然没有项目ID，创建一个模拟项目用于演示
        if (!projectId) {
            console.log('没有找到项目ID，使用模拟项目进行演示');
            this.currentProject = {
                id: 'demo-project-1',
                title: '演示项目 - 学术著作编纂',
                description: '这是一个用于演示章节分配系统的模拟项目',
                status: 'active',
                owner_id: this.currentUser?.id,
                created_at: new Date().toISOString()
            };
            this.updateProjectDisplay();
            return;
        }

        try {
            const { data: project, error } = await supabaseManager.supabase
                .from('projects')
                .select('*')
                .eq('id', projectId)
                .single();

            if (error) {
                console.warn('从数据库加载项目失败，使用模拟项目:', error);
                // 使用模拟项目
                this.currentProject = {
                    id: projectId,
                    title: '演示项目 - 学术著作编纂',
                    description: '这是一个用于演示章节分配系统的模拟项目',
                    status: 'active',
                    owner_id: this.currentUser?.id,
                    created_at: new Date().toISOString()
                };
            } else {
                this.currentProject = project;
            }

            this.updateProjectDisplay();

        } catch (error) {
            console.error('加载项目失败:', error);
            // 使用模拟项目作为后备
            this.currentProject = {
                id: 'demo-project-1',
                title: '演示项目 - 学术著作编纂',
                description: '这是一个用于演示章节分配系统的模拟项目',
                status: 'active',
                owner_id: this.currentUser?.id,
                created_at: new Date().toISOString()
            };
            this.updateProjectDisplay();
        }
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('assignment-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.filterAssignments();
            });
        }
        
        // 状态过滤
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.filterAssignments();
            });
        }
        
        // 图表周期选择
        const chartPeriod = document.getElementById('chart-period');
        if (chartPeriod) {
            chartPeriod.addEventListener('change', (e) => {
                this.updateProgressChart(e.target.value);
            });
        }
        
        // 进度视图切换
        const progressView = document.getElementById('progress-view');
        if (progressView) {
            progressView.addEventListener('change', (e) => {
                this.switchProgressView(e.target.value);
            });
        }
        
        // 审核过滤
        const reviewFilter = document.getElementById('review-filter');
        if (reviewFilter) {
            reviewFilter.addEventListener('change', (e) => {
                this.filterReviews(e.target.value);
            });
        }
        
        // 模态框关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }
    
    // 设置导航
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item a');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = e.target.closest('a').dataset.tab;
                this.switchTab(tab);
            });
        });
    }
    
    // 切换标签页
    switchTab(tabName) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).closest('.nav-item').classList.add('active');
        
        // 更新面板显示
        document.querySelectorAll('.content-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(`${tabName}-panel`).classList.add('active');
        
        this.activeTab = tabName;
        
        // 加载对应数据
        this.loadTabData(tabName);
    }
    
    // 加载标签页数据
    async loadTabData(tabName) {
        switch (tabName) {
            case 'overview':
                await this.loadOverviewData();
                break;
            case 'assignments':
                await this.loadAssignments();
                break;
            case 'progress':
                await this.loadProgressData();
                break;
            case 'reviews':
                await this.loadReviews();
                break;
            case 'discussions':
                await this.loadDiscussions();
                break;
            case 'files':
                await this.loadFiles();
                break;
            case 'reports':
                await this.loadReports();
                break;
        }
    }
    
    // 加载初始数据
    async loadInitialData() {
        await this.loadOverviewData();
    }
    
    // 加载概览数据
    async loadOverviewData() {
        try {
            // 加载统计数据
            await this.loadStatistics();
            
            // 加载进度图表
            await this.loadProgressChart();
            
            // 加载最近活动
            await this.loadRecentActivity();
            
        } catch (error) {
            console.error('加载概览数据失败:', error);
            this.showNotification('加载数据失败', 'error');
        }
    }
    
    // 加载统计数据
    async loadStatistics() {
        if (!this.currentProject) return;
        
        try {
            // 获取章节总数
            const { data: chapters, error: chaptersError } = await supabaseManager.supabase
                .from('chapters')
                .select('id, status')
                .eq('project_id', this.currentProject.id);
                
            if (chaptersError) throw chaptersError;
            
            // 获取分配数据
            const { data: assignments, error: assignmentsError } = await supabaseManager.supabase
                .from('chapter_assignments')
                .select('id, status, lead_author_id')
                .eq('project_id', this.currentProject.id);
                
            if (assignmentsError) throw assignmentsError;
            
            // 获取待审核数量
            const { data: reviews, error: reviewsError } = await supabaseManager.supabase
                .from('chapter_reviews')
                .select('id')
                .eq('status', 'pending');
                
            if (reviewsError) throw reviewsError;
            
            // 计算统计数据
            const totalChapters = chapters ? chapters.length : 0;
            const completedChapters = chapters ? chapters.filter(c => c.status === 'completed').length : 0;
            const uniqueAuthors = assignments ? new Set(assignments.map(a => a.lead_author_id)).size : 0;
            const pendingReviews = reviews ? reviews.length : 0;
            
            // 更新UI
            this.updateStatistics({
                totalChapters,
                totalAuthors: <AUTHORS>
                completedChapters,
                pendingReviews
            });
            
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }
    
    // 更新统计数据显示
    updateStatistics(stats) {
        document.getElementById('total-chapters').textContent = stats.totalChapters;
        document.getElementById('total-authors').textContent = stats.totalAuthors;
        document.getElementById('completed-chapters').textContent = stats.completedChapters;
        document.getElementById('pending-reviews').textContent = stats.pendingReviews;
    }
    
    // 加载进度图表
    async loadProgressChart(period = 'month') {
        const ctx = document.getElementById('progress-chart');
        if (!ctx) return;
        
        try {
            // 这里应该从数据库获取实际的进度数据
            // 暂时使用模拟数据
            const data = this.generateMockProgressData(period);
            
            if (this.progressChart) {
                this.progressChart.destroy();
            }
            
            this.progressChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: '完成章节数',
                        data: data.completed,
                        borderColor: '#1e40af',
                        backgroundColor: 'rgba(30, 64, 175, 0.1)',
                        tension: 0.4
                    }, {
                        label: '分配章节数',
                        data: data.assigned,
                        borderColor: '#059669',
                        backgroundColor: 'rgba(5, 150, 105, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
        } catch (error) {
            console.error('加载进度图表失败:', error);
        }
    }
    
    // 生成模拟进度数据
    generateMockProgressData(period) {
        const now = new Date();
        const data = { labels: [], completed: [], assigned: [] };
        
        let days = 30;
        if (period === 'week') days = 7;
        else if (period === 'quarter') days = 90;
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(now);
            date.setDate(date.getDate() - i);
            
            data.labels.push(date.toLocaleDateString('zh-CN', { 
                month: 'short', 
                day: 'numeric' 
            }));
            
            // 模拟数据
            data.completed.push(Math.floor(Math.random() * 10) + i * 0.2);
            data.assigned.push(Math.floor(Math.random() * 15) + i * 0.3);
        }
        
        return data;
    }
    
    // 加载最近活动
    async loadRecentActivity() {
        try {
            // 这里应该从数据库获取实际的活动数据
            // 暂时使用模拟数据
            const activities = [
                {
                    icon: 'fas fa-user-plus',
                    title: '张三被分配为第一章主笔作者',
                    time: '2小时前',
                    type: 'assignment'
                },
                {
                    icon: 'fas fa-check-circle',
                    title: '第二章初稿已提交审核',
                    time: '4小时前',
                    type: 'submission'
                },
                {
                    icon: 'fas fa-comment',
                    title: '李四在第三章讨论区发表了新评论',
                    time: '6小时前',
                    type: 'discussion'
                },
                {
                    icon: 'fas fa-edit',
                    title: '王五完成了第四章的修改',
                    time: '1天前',
                    type: 'edit'
                }
            ];
            
            this.renderRecentActivity(activities);
            
        } catch (error) {
            console.error('加载最近活动失败:', error);
        }
    }
    
    // 渲染最近活动
    renderRecentActivity(activities) {
        const container = document.getElementById('activity-list');
        if (!container) return;
        
        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-meta">${activity.time}</div>
                </div>
            </div>
        `).join('');
    }
    
    // 更新用户显示
    updateUserDisplay() {
        const userNameElement = document.getElementById('user-name');
        if (userNameElement && this.currentUser) {
            userNameElement.textContent = this.currentUser.email.split('@')[0];
        }
    }
    
    // 更新项目显示
    updateProjectDisplay() {
        const projectNameElement = document.getElementById('current-project-name');
        if (projectNameElement && this.currentProject) {
            projectNameElement.textContent = this.currentProject.title;
        }
    }
    
    // 显示通知
    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        if (!container) return;
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">${this.getNotificationTitle(type)}</div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-message">${message}</div>
        `;
        
        container.appendChild(notification);
        
        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
    
    // 获取通知标题
    getNotificationTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '信息'
        };
        return titles[type] || '通知';
    }
    
    // 显示模态框
    showModal(content) {
        const container = document.getElementById('modal-container');
        if (!container) return;
        
        container.innerHTML = `
            <div class="modal-overlay active">
                <div class="modal">
                    ${content}
                </div>
            </div>
        `;
    }
    
    // 关闭模态框
    closeModal() {
        const overlay = document.querySelector('.modal-overlay');
        if (overlay) {
            overlay.classList.remove('active');
            setTimeout(() => overlay.remove(), 300);
        }
    }

    // 加载章节分配
    async loadAssignments() {
        if (!this.currentProject) return;

        try {
            // 暂时使用模拟数据，因为可能还没有创建相关表
            const mockAssignments = [
                {
                    id: '1',
                    title: '第0章：前言',
                    description: '介绍本书的写作背景、目标读者、主要内容和结构安排',
                    status: 'in_progress',
                    lead_author_id: this.currentUser?.id,
                    lead_author: { email: this.currentUser?.email },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 5000,
                    priority: 'high'
                },
                {
                    id: '2',
                    title: '第1章：大模型基本概念与内涵',
                    description: '阐述大模型的定义、特点、分类和发展历程',
                    status: 'pending',
                    lead_author_id: this.currentUser?.id,
                    lead_author: { email: this.currentUser?.email },
                    assigned_by: this.currentUser?.id,
                    created_at: new Date().toISOString(),
                    due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
                    word_count_target: 8000,
                    priority: 'medium'
                }
            ];

            this.assignments = mockAssignments;
            this.renderAssignments();

        } catch (error) {
            console.error('加载章节分配失败:', error);
            this.showNotification('加载章节分配失败', 'error');
        }
    }

    // 渲染章节分配列表
    renderAssignments() {
        const container = document.getElementById('assignments-list');
        if (!container) return;

        if (this.assignments.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>暂无章节分配</h3>
                    <p>点击"新建分配"开始分配章节给团队成员</p>
                    <button class="btn btn-primary" onclick="showCreateAssignmentModal()">
                        <i class="fas fa-plus"></i>
                        新建分配
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.assignments.map(assignment => this.renderAssignmentItem(assignment)).join('');
    }

    // 渲染单个分配项目
    renderAssignmentItem(assignment) {
        const progress = this.calculateProgress(assignment);
        const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
        const isOverdue = dueDate && dueDate < new Date() && assignment.status !== 'completed';

        // 检查权限
        const isCreator = assignment.assigned_by === this.currentUser.id;
        const canEdit = window.rolePermissionManager?.validateOperation('edit_assignment', { isCreator }) || false;
        const canDelete = window.rolePermissionManager?.validateOperation('delete_assignment', { isCreator }) || false;
        const canView = window.rolePermissionManager?.hasPermission('chapter.view', this.currentUserRole) || false;

        return `
            <div class="assignment-item" data-id="${assignment.id}">
                <div>
                    <div class="assignment-title">${assignment.title}</div>
                    <div class="assignment-subtitle">${assignment.chapters?.title || '未关联章节'}</div>
                </div>
                <div>
                    <div class="user-badge">
                        <i class="fas fa-user"></i>
                        ${assignment.lead_author?.email?.split('@')[0] || '未分配'}
                    </div>
                </div>
                <div>
                    ${this.renderCollaborators(assignment.id)}
                </div>
                <div>
                    <span class="status-badge status-${assignment.status}">
                        ${this.getStatusText(assignment.status)}
                    </span>
                </div>
                <div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <div style="font-size: 11px; color: var(--gray-500); margin-top: 2px;">
                        ${progress}%
                    </div>
                </div>
                <div>
                    <div class="due-date ${isOverdue ? 'overdue' : ''}">
                        ${dueDate ? dueDate.toLocaleDateString('zh-CN') : '无截止日期'}
                    </div>
                </div>
                <div class="actions">
                    ${canEdit ? `
                        <button class="action-btn" onclick="editAssignment('${assignment.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                    ` : ''}
                    ${canView ? `
                        <button class="action-btn" onclick="viewAssignmentDetails('${assignment.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                    ` : ''}
                    ${canDelete ? `
                        <button class="action-btn" onclick="deleteAssignment('${assignment.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                    ${this.renderStatusActions(assignment)}
                </div>
            </div>
        `;
    }

    // 渲染状态操作按钮
    renderStatusActions(assignment) {
        if (!window.rolePermissionManager) return '';

        const actions = [];
        const currentStatus = assignment.status;
        const isAssignee = assignment.lead_author_id === this.currentUser.id;
        const canReview = window.rolePermissionManager.canReviewContent(this.currentUserRole);

        // 根据当前状态和用户权限显示可用操作
        switch (currentStatus) {
            case 'pending':
                if (isAssignee) {
                    actions.push(`
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'accepted')" title="接受任务">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'rejected')" title="拒绝任务">
                            <i class="fas fa-times"></i>
                        </button>
                    `);
                }
                break;

            case 'accepted':
                if (isAssignee) {
                    actions.push(`
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'in_progress')" title="开始编写">
                            <i class="fas fa-play"></i>
                        </button>
                    `);
                }
                break;

            case 'in_progress':
                if (isAssignee) {
                    actions.push(`
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'submitted')" title="提交审核">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    `);
                }
                break;

            case 'submitted':
                if (canReview) {
                    actions.push(`
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'reviewing')" title="开始审核">
                            <i class="fas fa-search"></i>
                        </button>
                    `);
                }
                break;

            case 'reviewing':
                if (canReview) {
                    actions.push(`
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'approved')" title="批准">
                            <i class="fas fa-thumbs-up"></i>
                        </button>
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'revising')" title="需要修改">
                            <i class="fas fa-edit"></i>
                        </button>
                    `);
                }
                break;

            case 'revising':
                if (isAssignee) {
                    actions.push(`
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'submitted')" title="重新提交">
                            <i class="fas fa-redo"></i>
                        </button>
                    `);
                }
                break;

            case 'approved':
                if (canReview) {
                    actions.push(`
                        <button class="action-btn" onclick="updateAssignmentStatus('${assignment.id}', 'completed')" title="标记完成">
                            <i class="fas fa-flag-checkered"></i>
                        </button>
                    `);
                }
                break;
        }

        return actions.join('');
    }

    // 渲染协作者
    renderCollaborators(assignmentId) {
        // 这里应该从协作者数据中获取
        // 暂时返回占位符
        return `
            <div class="user-badge">
                <i class="fas fa-users"></i>
                2 协作者
            </div>
        `;
    }

    // 计算进度
    calculateProgress(assignment) {
        // 根据状态计算进度
        const statusProgress = {
            'pending': 0,
            'accepted': 10,
            'in_progress': 50,
            'submitted': 80,
            'reviewing': 85,
            'revising': 70,
            'approved': 95,
            'completed': 100,
            'rejected': 0
        };

        return statusProgress[assignment.status] || 0;
    }

    // 获取状态文本
    getStatusText(status) {
        const statusTexts = {
            'pending': '待确认',
            'accepted': '已接受',
            'in_progress': '进行中',
            'submitted': '已提交',
            'reviewing': '审核中',
            'revising': '修改中',
            'approved': '已批准',
            'completed': '已完成',
            'rejected': '已拒绝'
        };

        return statusTexts[status] || status;
    }

    // 过滤分配
    filterAssignments() {
        const filteredAssignments = this.assignments.filter(assignment => {
            // 状态过滤
            if (this.filters.status && assignment.status !== this.filters.status) {
                return false;
            }

            // 搜索过滤
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const title = assignment.title.toLowerCase();
                const authorEmail = assignment.lead_author?.email?.toLowerCase() || '';

                if (!title.includes(searchTerm) && !authorEmail.includes(searchTerm)) {
                    return false;
                }
            }

            return true;
        });

        // 重新渲染过滤后的结果
        this.renderFilteredAssignments(filteredAssignments);
    }

    // 渲染过滤后的分配
    renderFilteredAssignments(assignments) {
        const container = document.getElementById('assignments-list');
        if (!container) return;

        if (assignments.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🔍</div>
                    <h3>没有找到匹配的分配</h3>
                    <p>尝试调整搜索条件或过滤器</p>
                </div>
            `;
            return;
        }

        container.innerHTML = assignments.map(assignment => this.renderAssignmentItem(assignment)).join('');
    }

    // 创建新分配
    async createAssignment(assignmentData) {
        // 检查权限
        if (!window.rolePermissionManager.validateOperation('create_assignment')) {
            this.showNotification('您没有权限创建章节分配', 'error');
            return;
        }

        try {
            const { data, error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .insert([{
                    ...assignmentData,
                    project_id: this.currentProject.id,
                    assigned_by: this.currentUser.id,
                    status: 'pending'
                }])
                .select()
                .single();

            if (error) throw error;

            this.showNotification('章节分配创建成功', 'success');
            await this.loadAssignments();
            this.closeModal();

            return data;

        } catch (error) {
            console.error('创建分配失败:', error);
            this.showNotification('创建分配失败: ' + error.message, 'error');
            throw error;
        }
    }

    // 更新分配状态
    async updateAssignmentStatus(assignmentId, newStatus) {
        try {
            const { error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .update({
                    status: newStatus,
                    updated_at: new Date().toISOString()
                })
                .eq('id', assignmentId);

            if (error) throw error;

            this.showNotification('状态更新成功', 'success');
            await this.loadAssignments();

        } catch (error) {
            console.error('更新状态失败:', error);
            this.showNotification('更新状态失败: ' + error.message, 'error');
        }
    }

    // 删除分配
    async deleteAssignment(assignmentId) {
        // 检查权限
        const assignment = this.assignments.find(a => a.id === assignmentId);
        const isCreator = assignment && assignment.assigned_by === this.currentUser.id;

        if (!window.rolePermissionManager.validateOperation('delete_assignment', { isCreator })) {
            this.showNotification('您没有权限删除此分配', 'error');
            return;
        }

        if (!confirm('确定要删除这个章节分配吗？此操作不可撤销。')) {
            return;
        }

        try {
            const { error } = await supabaseManager.supabase
                .from('chapter_assignments')
                .delete()
                .eq('id', assignmentId);

            if (error) throw error;

            this.showNotification('分配删除成功', 'success');
            await this.loadAssignments();

        } catch (error) {
            console.error('删除分配失败:', error);
            this.showNotification('删除分配失败: ' + error.message, 'error');
        }
    }

    // 加载进度数据
    async loadProgressData() {
        try {
            const viewType = document.getElementById('progress-view')?.value || 'timeline';

            switch (viewType) {
                case 'timeline':
                    await this.renderTimelineView();
                    break;
                case 'kanban':
                    await this.renderKanbanView();
                    break;
                case 'gantt':
                    await this.renderGanttView();
                    break;
            }
        } catch (error) {
            console.error('加载进度数据失败:', error);
        }
    }

    // 渲染时间线视图
    async renderTimelineView() {
        const container = document.getElementById('progress-content');
        if (!container) return;

        const assignments = this.assignments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

        container.innerHTML = `
            <div class="timeline-container">
                <div class="timeline-header">
                    <h3>项目时间线</h3>
                    <div class="timeline-legend">
                        <span class="legend-item"><span class="legend-dot status-pending"></span>待确认</span>
                        <span class="legend-item"><span class="legend-dot status-in-progress"></span>进行中</span>
                        <span class="legend-item"><span class="legend-dot status-completed"></span>已完成</span>
                    </div>
                </div>
                <div class="timeline">
                    ${assignments.map(assignment => this.renderTimelineItem(assignment)).join('')}
                </div>
            </div>
        `;
    }

    // 渲染时间线项目
    renderTimelineItem(assignment) {
        const createdDate = new Date(assignment.created_at).toLocaleDateString('zh-CN');
        const dueDate = assignment.due_date ? new Date(assignment.due_date).toLocaleDateString('zh-CN') : null;

        return `
            <div class="timeline-item">
                <div class="timeline-marker status-${assignment.status}"></div>
                <div class="timeline-content">
                    <div class="timeline-title">${assignment.title}</div>
                    <div class="timeline-meta">
                        <span>创建: ${createdDate}</span>
                        ${dueDate ? `<span>截止: ${dueDate}</span>` : ''}
                        <span class="status-badge status-${assignment.status}">
                            ${this.getStatusText(assignment.status)}
                        </span>
                    </div>
                    <div class="timeline-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${this.calculateProgress(assignment)}%"></div>
                        </div>
                        <span>${this.calculateProgress(assignment)}%</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染看板视图
    async renderKanbanView() {
        const container = document.getElementById('progress-content');
        if (!container) return;

        const statusColumns = {
            'pending': '待确认',
            'in_progress': '进行中',
            'reviewing': '审核中',
            'completed': '已完成'
        };

        const assignmentsByStatus = {};
        Object.keys(statusColumns).forEach(status => {
            assignmentsByStatus[status] = this.assignments.filter(a => a.status === status);
        });

        container.innerHTML = `
            <div class="kanban-container">
                <div class="kanban-header">
                    <h3>看板视图</h3>
                </div>
                <div class="kanban-board">
                    ${Object.entries(statusColumns).map(([status, title]) => `
                        <div class="kanban-column">
                            <div class="kanban-column-header">
                                <h4>${title}</h4>
                                <span class="count">${assignmentsByStatus[status].length}</span>
                            </div>
                            <div class="kanban-cards">
                                ${assignmentsByStatus[status].map(assignment => this.renderKanbanCard(assignment)).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // 渲染看板卡片
    renderKanbanCard(assignment) {
        const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
        const isOverdue = dueDate && dueDate < new Date() && assignment.status !== 'completed';

        return `
            <div class="kanban-card ${isOverdue ? 'overdue' : ''}" data-id="${assignment.id}">
                <div class="card-title">${assignment.title}</div>
                <div class="card-meta">
                    <div class="card-author">
                        <i class="fas fa-user"></i>
                        ${assignment.lead_author?.email?.split('@')[0] || '未分配'}
                    </div>
                    ${dueDate ? `
                        <div class="card-due-date ${isOverdue ? 'overdue' : ''}">
                            <i class="fas fa-calendar"></i>
                            ${dueDate.toLocaleDateString('zh-CN')}
                        </div>
                    ` : ''}
                </div>
                <div class="card-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${this.calculateProgress(assignment)}%"></div>
                    </div>
                    <span>${this.calculateProgress(assignment)}%</span>
                </div>
            </div>
        `;
    }

    // 渲染甘特图视图
    async renderGanttView() {
        const container = document.getElementById('progress-content');
        if (!container) return;

        // 简化的甘特图实现
        container.innerHTML = `
            <div class="gantt-container">
                <div class="gantt-header">
                    <h3>甘特图</h3>
                    <div class="gantt-controls">
                        <button class="btn btn-secondary" onclick="exportGantt()">
                            <i class="fas fa-download"></i>
                            导出
                        </button>
                    </div>
                </div>
                <div class="gantt-chart">
                    <div class="gantt-timeline">
                        ${this.renderGanttTimeline()}
                    </div>
                    <div class="gantt-tasks">
                        ${this.assignments.map(assignment => this.renderGanttTask(assignment)).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染甘特图时间线
    renderGanttTimeline() {
        const today = new Date();
        const dates = [];

        // 生成未来30天的日期
        for (let i = -7; i < 30; i++) {
            const date = new Date(today);
            date.setDate(date.getDate() + i);
            dates.push(date);
        }

        return `
            <div class="gantt-timeline-header">
                ${dates.map(date => `
                    <div class="gantt-date ${date.toDateString() === today.toDateString() ? 'today' : ''}">
                        ${date.getDate()}
                    </div>
                `).join('')}
            </div>
        `;
    }

    // 渲染甘特图任务
    renderGanttTask(assignment) {
        const createdDate = new Date(assignment.created_at);
        const dueDate = assignment.due_date ? new Date(assignment.due_date) : null;
        const today = new Date();

        // 计算任务条的位置和宽度
        const startOffset = Math.max(0, (createdDate - today) / (1000 * 60 * 60 * 24) + 7);
        const duration = dueDate ? (dueDate - createdDate) / (1000 * 60 * 60 * 24) : 7;

        return `
            <div class="gantt-task">
                <div class="gantt-task-label">${assignment.title}</div>
                <div class="gantt-task-bar" style="left: ${startOffset * 30}px; width: ${duration * 30}px;">
                    <div class="gantt-task-progress" style="width: ${this.calculateProgress(assignment)}%"></div>
                </div>
            </div>
        `;
    }

    // 切换进度视图
    switchProgressView(viewType) {
        this.loadProgressData();
    }

    // 加载审核数据
    async loadReviews() {
        if (!this.currentProject) return;

        try {
            const { data: reviews, error } = await supabaseManager.supabase
                .from('chapter_reviews')
                .select(`
                    *,
                    assignment:chapter_assignments(id, title),
                    reviewer:auth.users!reviewer_id(id, email)
                `)
                .eq('assignment.project_id', this.currentProject.id)
                .order('created_at', { ascending: false });

            if (error) throw error;

            this.reviews = reviews || [];
            this.renderReviews();

        } catch (error) {
            console.error('加载审核数据失败:', error);
            this.showNotification('加载审核数据失败', 'error');
        }
    }

    // 渲染审核列表
    renderReviews() {
        const container = document.getElementById('reviews-content');
        if (!container) return;

        if (this.reviews.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <h3>暂无审核记录</h3>
                    <p>当有章节提交审核时，审核记录将显示在这里</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="reviews-list">
                ${this.reviews.map(review => this.renderReviewItem(review)).join('')}
            </div>
        `;
    }

    // 渲染审核项目
    renderReviewItem(review) {
        const createdDate = new Date(review.created_at).toLocaleDateString('zh-CN');
        const submittedDate = review.submitted_at ? new Date(review.submitted_at).toLocaleDateString('zh-CN') : null;

        return `
            <div class="review-item">
                <div class="review-header">
                    <h4>${review.assignment?.title || '未知章节'}</h4>
                    <span class="status-badge status-${review.status}">
                        ${this.getReviewStatusText(review.status)}
                    </span>
                </div>
                <div class="review-meta">
                    <span>审核人: ${review.reviewer?.email?.split('@')[0] || '未分配'}</span>
                    <span>创建: ${createdDate}</span>
                    ${submittedDate ? `<span>提交: ${submittedDate}</span>` : ''}
                </div>
                ${review.comments ? `
                    <div class="review-comments">
                        <strong>评论:</strong> ${review.comments}
                    </div>
                ` : ''}
                ${this.renderReviewScores(review)}
                <div class="review-actions">
                    <button class="btn btn-secondary" onclick="viewReviewDetails('${review.id}')">
                        查看详情
                    </button>
                    ${this.canEditReview(review) ? `
                        <button class="btn btn-primary" onclick="editReview('${review.id}')">
                            编辑审核
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // 渲染审核评分
    renderReviewScores(review) {
        if (!review.quality_score && !review.completeness_score && !review.originality_score) {
            return '';
        }

        return `
            <div class="review-scores">
                ${review.quality_score ? `<span class="score">质量: ${review.quality_score}/5</span>` : ''}
                ${review.completeness_score ? `<span class="score">完整性: ${review.completeness_score}/5</span>` : ''}
                ${review.originality_score ? `<span class="score">原创性: ${review.originality_score}/5</span>` : ''}
            </div>
        `;
    }

    // 获取审核状态文本
    getReviewStatusText(status) {
        const statusTexts = {
            'pending': '待审核',
            'in_progress': '审核中',
            'approved': '已通过',
            'rejected': '已拒绝',
            'needs_revision': '需要修改'
        };

        return statusTexts[status] || status;
    }

    // 检查是否可以编辑审核
    canEditReview(review) {
        if (!window.rolePermissionManager) return false;

        const isReviewer = review.reviewer_id === this.currentUser.id;
        const canReview = window.rolePermissionManager.canReviewContent(this.currentUserRole);

        return isReviewer || canReview;
    }

    // 过滤审核
    filterReviews(status) {
        const filteredReviews = status ? this.reviews.filter(r => r.status === status) : this.reviews;

        const container = document.getElementById('reviews-content');
        if (!container) return;

        container.innerHTML = `
            <div class="reviews-list">
                ${filteredReviews.map(review => this.renderReviewItem(review)).join('')}
            </div>
        `;
    }

    // 加载报告数据
    async loadReports() {
        const container = document.getElementById('reports-content');
        if (!container) return;

        try {
            const reportData = await this.generateReportData();
            this.renderReports(reportData);
        } catch (error) {
            console.error('加载报告数据失败:', error);
            this.showNotification('加载报告数据失败', 'error');
        }
    }

    // 生成报告数据
    async generateReportData() {
        const data = {
            project: this.currentProject,
            assignments: this.assignments,
            reviews: this.reviews,
            statistics: {},
            charts: {}
        };

        // 计算统计数据
        data.statistics = {
            totalAssignments: this.assignments.length,
            completedAssignments: this.assignments.filter(a => a.status === 'completed').length,
            inProgressAssignments: this.assignments.filter(a => a.status === 'in_progress').length,
            pendingAssignments: this.assignments.filter(a => a.status === 'pending').length,
            totalReviews: this.reviews.length,
            approvedReviews: this.reviews.filter(r => r.status === 'approved').length,
            rejectedReviews: this.reviews.filter(r => r.status === 'rejected').length,
            averageProgress: this.calculateAverageProgress(),
            overdueAssignments: this.getOverdueAssignments().length
        };

        // 计算完成率
        data.statistics.completionRate = data.statistics.totalAssignments > 0
            ? Math.round((data.statistics.completedAssignments / data.statistics.totalAssignments) * 100)
            : 0;

        // 计算审核通过率
        data.statistics.approvalRate = data.statistics.totalReviews > 0
            ? Math.round((data.statistics.approvedReviews / data.statistics.totalReviews) * 100)
            : 0;

        return data;
    }

    // 计算平均进度
    calculateAverageProgress() {
        if (this.assignments.length === 0) return 0;

        const totalProgress = this.assignments.reduce((sum, assignment) => {
            return sum + this.calculateProgress(assignment);
        }, 0);

        return Math.round(totalProgress / this.assignments.length);
    }

    // 获取逾期分配
    getOverdueAssignments() {
        const now = new Date();
        return this.assignments.filter(assignment => {
            if (!assignment.due_date || assignment.status === 'completed') return false;
            return new Date(assignment.due_date) < now;
        });
    }

    // 渲染报告
    renderReports(data) {
        const container = document.getElementById('reports-content');
        if (!container) return;

        container.innerHTML = `
            <div class="reports-container">
                <div class="report-header">
                    <h3>项目报告</h3>
                    <div class="report-meta">
                        <span>生成时间: ${new Date().toLocaleString('zh-CN')}</span>
                        <span>项目: ${data.project.title}</span>
                    </div>
                </div>

                <div class="report-sections">
                    ${this.renderExecutiveSummary(data.statistics)}
                    ${this.renderProgressReport(data)}
                    ${this.renderQualityReport(data)}
                    ${this.renderTeamReport(data)}
                    ${this.renderTimelineReport(data)}
                </div>
            </div>
        `;
    }

    // 渲染执行摘要
    renderExecutiveSummary(stats) {
        return `
            <div class="report-section">
                <h4>执行摘要</h4>
                <div class="summary-grid">
                    <div class="summary-card">
                        <div class="summary-number">${stats.totalAssignments}</div>
                        <div class="summary-label">总分配数</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number">${stats.completionRate}%</div>
                        <div class="summary-label">完成率</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number">${stats.averageProgress}%</div>
                        <div class="summary-label">平均进度</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number">${stats.overdueAssignments}</div>
                        <div class="summary-label">逾期任务</div>
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染进度报告
    renderProgressReport(data) {
        const statusCounts = {
            pending: data.statistics.pendingAssignments,
            in_progress: data.statistics.inProgressAssignments,
            completed: data.statistics.completedAssignments
        };

        return `
            <div class="report-section">
                <h4>进度报告</h4>
                <div class="progress-breakdown">
                    <div class="progress-chart">
                        <canvas id="progress-pie-chart" width="300" height="200"></canvas>
                    </div>
                    <div class="progress-details">
                        <div class="progress-item">
                            <span class="progress-dot pending"></span>
                            <span>待确认: ${statusCounts.pending}</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-dot in-progress"></span>
                            <span>进行中: ${statusCounts.in_progress}</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-dot completed"></span>
                            <span>已完成: ${statusCounts.completed}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染质量报告
    renderQualityReport(data) {
        return `
            <div class="report-section">
                <h4>质量报告</h4>
                <div class="quality-metrics">
                    <div class="metric">
                        <span class="metric-label">审核总数:</span>
                        <span class="metric-value">${data.statistics.totalReviews}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">通过率:</span>
                        <span class="metric-value">${data.statistics.approvalRate}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">已通过:</span>
                        <span class="metric-value">${data.statistics.approvedReviews}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">已拒绝:</span>
                        <span class="metric-value">${data.statistics.rejectedReviews}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 渲染团队报告
    renderTeamReport(data) {
        const authorStats = this.calculateAuthorStats(data.assignments);

        return `
            <div class="report-section">
                <h4>团队报告</h4>
                <div class="team-stats">
                    ${Object.entries(authorStats).map(([authorId, stats]) => `
                        <div class="author-stat">
                            <div class="author-name">${stats.name}</div>
                            <div class="author-metrics">
                                <span>分配: ${stats.assigned}</span>
                                <span>完成: ${stats.completed}</span>
                                <span>进度: ${stats.averageProgress}%</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // 渲染时间线报告
    renderTimelineReport(data) {
        const recentActivities = this.getRecentActivities(data);

        return `
            <div class="report-section">
                <h4>最近活动</h4>
                <div class="timeline-report">
                    ${recentActivities.map(activity => `
                        <div class="activity-item">
                            <div class="activity-date">${activity.date}</div>
                            <div class="activity-description">${activity.description}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // 计算作者统计
    calculateAuthorStats(assignments) {
        const stats = {};

        assignments.forEach(assignment => {
            const authorId = assignment.lead_author_id;
            const authorName = assignment.lead_author?.email?.split('@')[0] || '未知';

            if (!stats[authorId]) {
                stats[authorId] = {
                    name: authorName,
                    assigned: 0,
                    completed: 0,
                    totalProgress: 0
                };
            }

            stats[authorId].assigned++;
            if (assignment.status === 'completed') {
                stats[authorId].completed++;
            }
            stats[authorId].totalProgress += this.calculateProgress(assignment);
        });

        // 计算平均进度
        Object.values(stats).forEach(stat => {
            stat.averageProgress = stat.assigned > 0
                ? Math.round(stat.totalProgress / stat.assigned)
                : 0;
        });

        return stats;
    }

    // 获取最近活动
    getRecentActivities(data) {
        const activities = [];

        // 添加分配活动
        data.assignments.forEach(assignment => {
            activities.push({
                date: new Date(assignment.created_at).toLocaleDateString('zh-CN'),
                description: `创建分配: ${assignment.title}`
            });

            if (assignment.submitted_at) {
                activities.push({
                    date: new Date(assignment.submitted_at).toLocaleDateString('zh-CN'),
                    description: `提交审核: ${assignment.title}`
                });
            }
        });

        // 添加审核活动
        data.reviews.forEach(review => {
            if (review.submitted_at) {
                activities.push({
                    date: new Date(review.submitted_at).toLocaleDateString('zh-CN'),
                    description: `完成审核: ${review.assignment?.title || '未知章节'}`
                });
            }
        });

        // 按日期排序并返回最近10条
        return activities
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 10);
    }

    // 生成报告
    async generateReport() {
        try {
            const reportData = await this.generateReportData();

            // 创建报告内容
            const reportContent = this.createReportDocument(reportData);

            // 下载报告
            this.downloadReport(reportContent, `项目报告_${this.currentProject.title}_${new Date().toISOString().split('T')[0]}.html`);

            this.showNotification('报告生成成功', 'success');

        } catch (error) {
            console.error('生成报告失败:', error);
            this.showNotification('生成报告失败', 'error');
        }
    }

    // 创建报告文档
    createReportDocument(data) {
        return `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>项目报告 - ${data.project.title}</title>
                <style>
                    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; }
                    .header { text-align: center; margin-bottom: 40px; }
                    .section { margin-bottom: 30px; }
                    .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0; }
                    .stat-card { text-align: center; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px; }
                    .stat-number { font-size: 24px; font-weight: bold; color: #1e40af; }
                    .stat-label { color: #6b7280; margin-top: 5px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>项目报告</h1>
                    <h2>${data.project.title}</h2>
                    <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
                </div>

                <div class="section">
                    <h3>项目概览</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${data.statistics.totalAssignments}</div>
                            <div class="stat-label">总分配数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${data.statistics.completionRate}%</div>
                            <div class="stat-label">完成率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${data.statistics.averageProgress}%</div>
                            <div class="stat-label">平均进度</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${data.statistics.overdueAssignments}</div>
                            <div class="stat-label">逾期任务</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>详细数据</h3>
                    <p>待确认任务: ${data.statistics.pendingAssignments}</p>
                    <p>进行中任务: ${data.statistics.inProgressAssignments}</p>
                    <p>已完成任务: ${data.statistics.completedAssignments}</p>
                    <p>审核通过率: ${data.statistics.approvalRate}%</p>
                </div>
            </body>
            </html>
        `;
    }

    // 下载报告
    downloadReport(content, filename) {
        const blob = new Blob([content], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// 全局函数
window.showCreateAssignmentModal = function() {
    if (!window.assignmentManager) return;

    const modalContent = `
        <div class="modal-header">
            <h2 class="modal-title">新建章节分配</h2>
            <button class="modal-close" onclick="window.assignmentManager.closeModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="create-assignment-form">
                <div class="form-group">
                    <label class="form-label">章节标题 *</label>
                    <input type="text" class="form-input" name="title" required>
                    <div class="form-help">输入章节的标题</div>
                </div>

                <div class="form-group">
                    <label class="form-label">章节描述</label>
                    <textarea class="form-textarea" name="description" rows="3"></textarea>
                    <div class="form-help">描述章节的主要内容和要求</div>
                </div>

                <div class="form-group">
                    <label class="form-label">主笔作者 *</label>
                    <select class="form-select" name="lead_author_id" required>
                        <option value="">选择主笔作者</option>
                        <!-- 这里应该动态加载项目成员 -->
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">目标字数</label>
                    <input type="number" class="form-input" name="word_count_target" min="0">
                    <div class="form-help">预期的章节字数</div>
                </div>

                <div class="form-group">
                    <label class="form-label">截止日期</label>
                    <input type="date" class="form-input" name="due_date">
                </div>

                <div class="form-group">
                    <label class="form-label">优先级</label>
                    <select class="form-select" name="priority">
                        <option value="medium">中等</option>
                        <option value="low">低</option>
                        <option value="high">高</option>
                        <option value="urgent">紧急</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">具体要求</label>
                    <textarea class="form-textarea" name="requirements" rows="4"></textarea>
                    <div class="form-help">详细说明章节的写作要求和标准</div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="window.assignmentManager.closeModal()">
                取消
            </button>
            <button type="button" class="btn btn-primary" onclick="submitCreateAssignment()">
                创建分配
            </button>
        </div>
    `;

    window.assignmentManager.showModal(modalContent);
}

window.submitCreateAssignment = function() {
    const form = document.getElementById('create-assignment-form');
    const formData = new FormData(form);

    const assignmentData = {
        title: formData.get('title'),
        description: formData.get('description'),
        lead_author_id: formData.get('lead_author_id'),
        word_count_target: parseInt(formData.get('word_count_target')) || 0,
        due_date: formData.get('due_date') || null,
        priority: formData.get('priority'),
        requirements: formData.get('requirements')
    };

    // 验证必填字段
    if (!assignmentData.title || !assignmentData.lead_author_id) {
        window.assignmentManager.showNotification('请填写所有必填字段', 'warning');
        return;
    }

    window.assignmentManager.createAssignment(assignmentData);
}

window.editAssignment = function(assignmentId) {
    console.log('编辑分配:', assignmentId);
    // 这里将实现编辑功能
};

window.viewAssignmentDetails = function(assignmentId) {
    console.log('查看分配详情:', assignmentId);
    // 这里将实现查看详情功能
};

window.deleteAssignment = function(assignmentId) {
    if (window.assignmentManager) {
        window.assignmentManager.deleteAssignment(assignmentId);
    }
};

window.updateAssignmentStatus = function(assignmentId, newStatus) {
    if (window.assignmentManager) {
        window.assignmentManager.updateAssignmentStatus(assignmentId, newStatus);
    }
};

window.refreshOverview = function() {
    if (window.assignmentManager) {
        window.assignmentManager.loadOverviewData();
    }
};

window.showNewDiscussionModal = function() {
    console.log('显示新建讨论模态框');
};

window.showUploadModal = function() {
    console.log('显示上传文件模态框');
};

window.exportReport = function() {
    if (window.assignmentManager) {
        window.assignmentManager.generateReport();
    }
};

window.exportGantt = function() {
    console.log('导出甘特图');
    // 这里可以实现甘特图导出功能
};

window.viewReviewDetails = function(reviewId) {
    console.log('查看审核详情:', reviewId);
    // 这里将实现查看审核详情功能
};

window.editReview = function(reviewId) {
    console.log('编辑审核:', reviewId);
    // 这里将实现编辑审核功能
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.assignmentManager = new ChapterAssignmentManager();
});
