<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉框样式测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 2rem;
            margin: 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 3rem;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .demo-area {
            background: #f8fafc;
            padding: 2rem;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .demo-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 2rem;">
            <i class="fas fa-palette"></i> 下拉框样式优化测试
        </h1>
        
        <!-- 内联项目选择器测试 -->
        <div class="test-section">
            <h2 class="test-title">内联项目选择器</h2>
            <p class="test-description">
                这是项目概览页面中使用的内联项目选择器，优化了间距、悬停效果和视觉层次。
            </p>
            <div class="demo-area">
                <div class="demo-header">
                    <h3><i class="fas fa-tachometer-alt"></i> 项目概览</h3>
                    <div class="project-selector-inline">
                        <div class="project-dropdown">
                            <button class="project-btn project-btn-inline" onclick="toggleDropdown('inline')">
                                <i class="fas fa-folder"></i>
                                <span>选择项目</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="project-list" id="project-list-inline">
                                <div class="project-item" onclick="selectProject('new')">
                                    <i class="fas fa-plus"></i>
                                    <span>创建新项目</span>
                                </div>
                                <div class="project-divider"></div>
                                <div class="project-item" onclick="selectProject('book1')">
                                    <div class="project-info">
                                        <div class="project-title">大模型技术与油气应用概论</div>
                                        <div class="project-role">项目所有者</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book2')">
                                    <div class="project-info">
                                        <div class="project-title">人工智能在石油勘探中的应用</div>
                                        <div class="project-role">编辑者</div>
                                    </div>
                                    <div class="project-status suspended"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book3')">
                                    <div class="project-info">
                                        <div class="project-title">深度学习与地质建模</div>
                                        <div class="project-role">审阅者</div>
                                    </div>
                                    <div class="project-status archived"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book4')">
                                    <div class="project-info">
                                        <div class="project-title">机器学习在油藏工程中的实践</div>
                                        <div class="project-role">作者</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                                <div class="project-item" onclick="selectProject('book5')">
                                    <div class="project-info">
                                        <div class="project-title">智能钻井技术发展趋势</div>
                                        <div class="project-role">管理员</div>
                                    </div>
                                    <div class="project-status"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 侧边栏项目选择器测试 -->
        <div class="test-section">
            <h2 class="test-title">侧边栏项目选择器</h2>
            <p class="test-description">
                这是左侧边栏中的项目选择器样式，具有更宽的布局和清晰的视觉分层。
            </p>
            <div class="demo-area">
                <div style="width: 280px;">
                    <div class="project-dropdown">
                        <button class="project-btn" onclick="toggleDropdown('sidebar')">
                            <div style="display: flex; align-items: center; gap: 0.75rem;">
                                <i class="fas fa-folder"></i>
                                <span>选择项目</span>
                            </div>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="project-list" id="project-list-sidebar">
                            <div class="project-item" onclick="selectProject('new')">
                                <i class="fas fa-plus"></i>
                                <span>创建新项目</span>
                            </div>
                            <div class="project-divider"></div>
                            <div class="project-item" onclick="selectProject('book1')">
                                <div class="project-info">
                                    <div class="project-title">大模型技术与油气应用概论</div>
                                    <div class="project-role">项目所有者</div>
                                </div>
                                <div class="project-status"></div>
                            </div>
                            <div class="project-item" onclick="selectProject('book2')">
                                <div class="project-info">
                                    <div class="project-title">人工智能在石油勘探中的应用</div>
                                    <div class="project-role">编辑者</div>
                                </div>
                                <div class="project-status suspended"></div>
                            </div>
                            <div class="project-item" onclick="selectProject('book3')">
                                <div class="project-info">
                                    <div class="project-title">深度学习与地质建模</div>
                                    <div class="project-role">审阅者</div>
                                </div>
                                <div class="project-status archived"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 样式特性说明 -->
        <div class="test-section">
            <h2 class="test-title">优化特性</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div style="background: #f0f9ff; padding: 1rem; border-radius: 8px; border-left: 4px solid #3b82f6;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #1e40af;">
                        <i class="fas fa-expand-arrows-alt"></i> 改进的间距
                    </h4>
                    <p style="margin: 0; font-size: 0.875rem; color: #1e40af;">
                        增加了项目选项之间的内边距，避免文字堆积
                    </p>
                </div>
                <div style="background: #f0fdf4; padding: 1rem; border-radius: 8px; border-left: 4px solid #10b981;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #047857;">
                        <i class="fas fa-mouse-pointer"></i> 悬停效果
                    </h4>
                    <p style="margin: 0; font-size: 0.875rem; color: #047857;">
                        添加了平滑的悬停动画和视觉反馈
                    </p>
                </div>
                <div style="background: #fefce8; padding: 1rem; border-radius: 8px; border-left: 4px solid #eab308;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #a16207;">
                        <i class="fas fa-layer-group"></i> 视觉层次
                    </h4>
                    <p style="margin: 0; font-size: 0.875rem; color: #a16207;">
                        通过颜色、字重和大小建立清晰的信息层次
                    </p>
                </div>
                <div style="background: #fdf2f8; padding: 1rem; border-radius: 8px; border-left: 4px solid #ec4899;">
                    <h4 style="margin: 0 0 0.5rem 0; color: #be185d;">
                        <i class="fas fa-palette"></i> 状态指示
                    </h4>
                    <p style="margin: 0; font-size: 0.875rem; color: #be185d;">
                        项目状态通过颜色编码的圆点清晰显示
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleDropdown(type) {
            const dropdown = document.getElementById(`project-list-${type}`);
            const btn = dropdown.previousElementSibling;
            
            dropdown.classList.toggle('show');
            btn.classList.toggle('active');
            
            // 关闭其他下拉菜单
            document.querySelectorAll('.project-list').forEach(list => {
                if (list !== dropdown) {
                    list.classList.remove('show');
                    list.previousElementSibling.classList.remove('active');
                }
            });
        }
        
        function selectProject(projectId) {
            const projectNames = {
                'new': '创建新项目',
                'book1': '大模型技术与油气应用概论',
                'book2': '人工智能在石油勘探中的应用',
                'book3': '深度学习与地质建模',
                'book4': '机器学习在油藏工程中的实践',
                'book5': '智能钻井技术发展趋势'
            };
            
            // 更新按钮文本
            document.querySelectorAll('.project-btn span').forEach(span => {
                if (span.textContent !== '创建新项目') {
                    span.textContent = projectNames[projectId] || '选择项目';
                }
            });
            
            // 关闭所有下拉菜单
            document.querySelectorAll('.project-list').forEach(list => {
                list.classList.remove('show');
                list.previousElementSibling.classList.remove('active');
            });
            
            if (projectId === 'new') {
                alert('创建新项目功能');
            }
        }
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.project-dropdown')) {
                document.querySelectorAll('.project-list').forEach(list => {
                    list.classList.remove('show');
                    list.previousElementSibling.classList.remove('active');
                });
            }
        });
    </script>
</body>
</html>
