// Supabase 配置和 API 封装
// 请在使用前配置您的 Supabase 项目信息

class SupabaseManager {
    constructor() {
        // 从配置管理器加载配置，如果不存在则使用默认配置
        const savedConfig = this.loadConfigFromManager();

        this.supabaseUrl = savedConfig.url || 'https://bigzfjlaypptochqpxzu.supabase.co';
        this.supabaseKey = savedConfig.anonKey || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpZ3pmamxheXBwdG9jaHFweHp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI3MDc1MjUsImV4cCI6MjA2ODI4MzUyNX0.02clbd9p8TCBqsYEkKJlHnbnU60cRSVxgJo0bfNtq9M';

        // 初始化 Supabase 客户端
        this.supabase = null;
        this.currentUser = null;
        this.currentProject = null;

        this.initializeSupabase();
    }

    // 从配置管理器加载配置
    loadConfigFromManager() {
        try {
            if (window.supabaseConfigManager) {
                return window.supabaseConfigManager.getCurrentConfig();
            }

            // 如果配置管理器还未加载，尝试从localStorage直接读取
            const saved = localStorage.getItem('supabase-config');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.warn('加载Supabase配置失败:', error);
            return {};
        }
    }

    // 更新配置
    updateConfig(config) {
        if (config.url && config.url !== this.supabaseUrl) {
            this.supabaseUrl = config.url;
        }

        if (config.anonKey && config.anonKey !== this.supabaseKey) {
            this.supabaseKey = config.anonKey;
        }

        // 重新初始化客户端
        this.initializeSupabase();
    }

    // 初始化 Supabase 客户端
    async initializeSupabase() {
        try {
            // 动态加载 Supabase 客户端库
            if (typeof supabase === 'undefined') {
                await this.loadSupabaseLibrary();
            }
            
            this.supabase = supabase.createClient(this.supabaseUrl, this.supabaseKey);
            
            // 监听认证状态变化
            this.supabase.auth.onAuthStateChange((event, session) => {
                this.handleAuthStateChange(event, session);
            });
            
            // 检查当前用户状态
            await this.getCurrentUser();
            
        } catch (error) {
            console.error('Supabase 初始化失败:', error);
            this.showError('数据库连接失败，请检查配置');
        }
    }

    // 动态加载 Supabase 库
    async loadSupabaseLibrary() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // 处理认证状态变化
    handleAuthStateChange(event, session) {
        if (event === 'SIGNED_IN') {
            this.currentUser = session.user;
            this.onUserSignedIn(session.user);
        } else if (event === 'SIGNED_OUT') {
            this.currentUser = null;
            this.onUserSignedOut();
        }
    }

    // 用户登录成功回调
    onUserSignedIn(user) {
        console.log('用户已登录:', user.email);
        this.loadUserProfile();
        this.showLoginSuccess();
    }

    // 用户登出回调
    onUserSignedOut() {
        console.log('用户已登出');
        this.currentUser = null;
        this.currentProject = null;
        this.showLoginForm();
    }

    // 获取当前用户
    async getCurrentUser() {
        try {
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;
            return user;
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }

    // 用户注册
    async signUp(email, password, userData = {}) {
        try {
            const { data, error } = await this.supabase.auth.signUp({
                email: email,
                password: password,
                options: {
                    data: userData
                }
            });

            if (error) throw error;

            this.showSuccess('注册成功！请检查邮箱验证链接。');
            return data;
        } catch (error) {
            console.error('注册失败:', error);

            // 检查是否是CORS错误
            if (this.isCorsError(error)) {
                this.showError('注册失败: CORS配置问题 - 请联系管理员配置认证API的CORS策略');

                // 显示CORS解决方案
                setTimeout(() => {
                    if (confirm('检测到认证API的CORS配置问题。是否查看解决方案？')) {
                        window.open('auth-cors-test.html', '_blank');
                    }
                }, 1000);
            } else {
                this.showError('注册失败: ' + error.message);
            }

            throw error;
        }
    }

    // 用户登录
    async signIn(email, password) {
        try {
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) throw error;

            return data;
        } catch (error) {
            console.error('登录失败:', error);

            // 检查是否是CORS错误
            if (this.isCorsError(error)) {
                this.showError('登录失败: CORS配置问题 - 请联系管理员配置认证API的CORS策略');

                // 显示CORS解决方案
                setTimeout(() => {
                    if (confirm('检测到认证API的CORS配置问题。是否查看解决方案？')) {
                        window.open('auth-cors-test.html', '_blank');
                    }
                }, 1000);
            } else {
                this.showError('登录失败: ' + error.message);
            }

            throw error;
        }
    }

    // 检查是否是CORS错误
    isCorsError(error) {
        const errorMessage = error.message || '';
        return errorMessage.includes('CORS') ||
               errorMessage.includes('Access-Control-Allow-Origin') ||
               errorMessage.includes('Failed to fetch') ||
               errorMessage.includes('ERR_FAILED') ||
               error.name === 'AuthRetryableFetchError';
    }

    // 用户登出
    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;

            // 清除本地存储
            localStorage.clear();

            // 跳转到登录页面
            window.location.href = 'auth.html';
        } catch (error) {
            console.error('登出失败:', error);
            this.showError('登出失败: ' + error.message);
        }
    }

    // 加载用户配置
    async loadUserProfile() {
        if (!this.currentUser) return null;

        try {
            const { data, error } = await this.supabase
                .from('user_profiles')
                .select('*')
                .eq('id', this.currentUser.id)
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('加载用户配置失败:', error);
            return null;
        }
    }

    // 更新用户配置
    async updateUserProfile(profileData) {
        if (!this.currentUser) throw new Error('用户未登录');

        try {
            const { data, error } = await this.supabase
                .from('user_profiles')
                .update(profileData)
                .eq('id', this.currentUser.id)
                .select()
                .single();

            if (error) throw error;
            
            this.showSuccess('用户配置已更新');
            return data;
        } catch (error) {
            console.error('更新用户配置失败:', error);
            this.showError('更新失败: ' + error.message);
            throw error;
        }
    }

    // 创建项目
    async createProject(projectData) {
        if (!this.currentUser) throw new Error('用户未登录');

        try {
            // 验证必需字段
            if (!projectData.title) {
                throw new Error('项目标题不能为空');
            }

            // 准备插入数据，确保包含所有必需字段
            const insertData = {
                title: projectData.title,
                description: projectData.description || '',
                type: projectData.type || 'book', // 默认类型为书籍
                status: projectData.status || 'active',
                owner_id: this.currentUser.id
            };

            const { data, error } = await this.supabase
                .from('projects')
                .insert(insertData)
                .select()
                .single();

            if (error) {
                // 特殊处理schema相关错误
                if (error.code === 'PGRST204' && error.message.includes("'type' column")) {
                    throw new Error('数据库结构需要更新，请联系管理员执行数据库迁移脚本');
                }
                throw error;
            }

            // 添加项目所有者为项目成员
            const { error: memberError } = await this.supabase
                .from('project_members')
                .insert({
                    project_id: data.id,
                    user_id: this.currentUser.id,
                    role: 'owner',
                    status: 'active'
                });

            if (memberError) {
                console.warn('添加项目所有者为成员失败:', memberError);
                // 不抛出错误，因为项目已创建成功
            }

            // 自动添加创建者为项目管理员
            try {
                await this.addProjectMember(data.id, this.currentUser.id, 'owner');
            } catch (memberError) {
                console.warn('添加项目成员失败，但项目创建成功:', memberError);
                // 项目已创建成功，成员添加失败不应该影响整体流程
            }

            this.showSuccess('项目创建成功');
            return data;
        } catch (error) {
            console.error('创建项目失败:', error);

            // 提供更友好的错误信息
            let errorMessage = '创建项目失败: ';
            if (error.message.includes('type') && error.message.includes('column')) {
                errorMessage += '数据库结构需要更新，请执行数据库迁移脚本';
            } else if (error.message.includes('duplicate key')) {
                errorMessage += '项目名称已存在，请使用不同的名称';
            } else {
                errorMessage += error.message;
            }

            this.showError(errorMessage);
            throw error;
        }
    }

    // 获取用户项目列表
    async getUserProjects() {
        if (!this.currentUser) return [];

        try {
            const { data, error } = await this.supabase
                .from('projects')
                .select(`
                    *,
                    project_members!inner(role)
                `)
                .eq('project_members.user_id', this.currentUser.id);

            if (error) throw error;

            // 去重处理：如果用户在同一项目中有多个角色，可能会返回重复的项目记录
            const uniqueProjects = [];
            const projectIds = new Set();

            if (data && data.length > 0) {
                data.forEach(project => {
                    if (!projectIds.has(project.id)) {
                        projectIds.add(project.id);
                        // 收集该项目的所有角色
                        const allRoles = data
                            .filter(p => p.id === project.id)
                            .map(p => p.project_members.role);

                        // 添加项目，包含所有角色信息
                        uniqueProjects.push({
                            ...project,
                            user_roles: allRoles
                        });
                    }
                });
            }

            console.log(`获取到 ${data?.length || 0} 条记录，去重后 ${uniqueProjects.length} 个项目`);
            return uniqueProjects;
        } catch (error) {
            console.error('获取项目列表失败:', error);
            return [];
        }
    }

    // 添加项目成员
    async addProjectMember(projectId, userId, role = 'member') {
        try {
            const { data, error } = await this.supabase
                .from('project_members')
                .insert({
                    project_id: projectId,
                    user_id: userId,
                    role: role
                })
                .select()
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('添加项目成员失败:', error);
            throw error;
        }
    }

    // 获取项目大纲
    async getProjectOutline(projectId) {
        try {
            const { data, error } = await this.supabase
                .from('outlines')
                .select('*')
                .eq('project_id', projectId)
                .order('sort_order');

            if (error) throw error;
            return this.buildOutlineTree(data);
        } catch (error) {
            console.error('获取项目大纲失败:', error);
            return [];
        }
    }

    // 构建大纲树结构
    buildOutlineTree(flatData) {
        const tree = [];
        const map = {};

        // 创建映射
        flatData.forEach(item => {
            map[item.id] = { ...item, children: [] };
        });

        // 构建树结构
        flatData.forEach(item => {
            if (item.parent_id) {
                if (map[item.parent_id]) {
                    map[item.parent_id].children.push(map[item.id]);
                }
            } else {
                tree.push(map[item.id]);
            }
        });

        return tree;
    }

    // 创建大纲项目
    async createOutlineItem(projectId, itemData) {
        try {
            const { data, error } = await this.supabase
                .from('outlines')
                .insert({
                    ...itemData,
                    project_id: projectId,
                    created_by: this.currentUser.id
                })
                .select()
                .single();

            if (error) throw error;
            
            // 记录活动日志
            await this.logActivity(projectId, 'create_outline', 'outline', data.id);
            
            return data;
        } catch (error) {
            console.error('创建大纲项目失败:', error);
            throw error;
        }
    }

    // 获取章节内容
    async getChapter(chapterId) {
        try {
            const { data, error } = await this.supabase
                .from('chapters')
                .select(`
                    *,
                    outline:outlines(*),
                    created_by_user:user_profiles!chapters_created_by_fkey(*),
                    last_edited_by_user:user_profiles!chapters_last_edited_by_fkey(*)
                `)
                .eq('id', chapterId)
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('获取章节内容失败:', error);
            throw error;
        }
    }

    // 保存章节内容
    async saveChapter(chapterId, chapterData) {
        if (!this.currentUser) throw new Error('用户未登录');

        try {
            // 检查用户权限
            const hasPermission = await this.checkChapterPermission(chapterId, 'write');
            if (!hasPermission) {
                throw new Error('您没有编辑此章节的权限');
            }

            const { data, error } = await this.supabase
                .from('chapters')
                .update({
                    ...chapterData,
                    last_edited_by: this.currentUser.id,
                    version: this.supabase.sql`version + 1`
                })
                .eq('id', chapterId)
                .select()
                .single();

            if (error) throw error;

            // 创建版本历史
            await this.createChapterVersion(chapterId, data);
            
            // 记录活动日志
            await this.logActivity(data.project_id, 'update_chapter', 'chapter', chapterId);
            
            this.showSuccess('章节已保存');
            return data;
        } catch (error) {
            console.error('保存章节失败:', error);
            this.showError('保存失败: ' + error.message);
            throw error;
        }
    }

    // 检查章节权限
    async checkChapterPermission(chapterId, permissionType) {
        if (!this.currentUser) return false;

        try {
            const { data, error } = await this.supabase
                .from('chapter_permissions')
                .select('permission_type')
                .eq('chapter_id', chapterId)
                .eq('user_id', this.currentUser.id);

            if (error) throw error;

            if (data.length === 0) return false;

            const userPermission = data[0].permission_type;
            
            // 权限级别检查
            const permissionLevels = {
                'reader': 1,
                'reviewer': 2,
                'editor': 3,
                'owner': 4
            };

            const requiredLevels = {
                'read': 1,
                'comment': 2,
                'write': 3,
                'admin': 4
            };

            return permissionLevels[userPermission] >= requiredLevels[permissionType];
        } catch (error) {
            console.error('检查权限失败:', error);
            return false;
        }
    }

    // 创建章节版本历史
    async createChapterVersion(chapterId, chapterData) {
        try {
            const { data, error } = await this.supabase
                .from('chapter_versions')
                .insert({
                    chapter_id: chapterId,
                    version_number: chapterData.version,
                    title: chapterData.title,
                    content: chapterData.content,
                    summary: chapterData.summary,
                    created_by: this.currentUser.id
                });

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('创建版本历史失败:', error);
        }
    }

    // 记录活动日志
    async logActivity(projectId, action, targetType, targetId, details = {}) {
        try {
            const { error } = await this.supabase
                .from('activity_logs')
                .insert({
                    project_id: projectId,
                    user_id: this.currentUser.id,
                    action: action,
                    target_type: targetType,
                    target_id: targetId,
                    details: details
                });

            if (error) throw error;
        } catch (error) {
            console.error('记录活动日志失败:', error);
        }
    }

    // 实时订阅章节变化
    subscribeToChapterChanges(chapterId, callback) {
        return this.supabase
            .channel(`chapter-${chapterId}`)
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'chapters',
                filter: `id=eq.${chapterId}`
            }, callback)
            .subscribe();
    }

    // 取消订阅
    unsubscribe(subscription) {
        if (subscription) {
            this.supabase.removeChannel(subscription);
        }
    }

    // 显示成功消息
    showSuccess(message) {
        if (typeof showNotification === 'function') {
            showNotification(message, 'success');
        } else {
            console.log('成功:', message);
        }
    }

    // 显示错误消息
    showError(message) {
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            console.error('错误:', message);
        }
    }

    // 显示登录成功
    showLoginSuccess() {
        if (typeof showLoginInterface === 'function') {
            showLoginInterface(false);
        }
        if (typeof loadUserDashboard === 'function') {
            loadUserDashboard();
        }
    }

    // 显示登录表单
    showLoginForm() {
        if (typeof showLoginInterface === 'function') {
            showLoginInterface(true);
        }
    }
}

// 创建全局 Supabase 管理器实例
const supabaseManager = new SupabaseManager();
