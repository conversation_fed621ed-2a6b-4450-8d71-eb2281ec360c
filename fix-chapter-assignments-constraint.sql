-- 修复章节分配表的约束问题
-- 解决 chapter_id 字段的 NOT NULL 约束导致的插入失败

-- 1. 首先检查当前的表结构
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'chapter_assignments' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. 修改 chapter_id 字段允许 NULL 值
-- 这样可以支持为大纲项创建分配（暂时没有对应章节的情况）
ALTER TABLE public.chapter_assignments 
ALTER COLUMN chapter_id DROP NOT NULL;

-- 3. 检查并修复现有的约束
-- 如果存在外键约束，需要重新创建以支持 NULL 值
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- 查找 chapter_id 的外键约束
    SELECT tc.constraint_name INTO constraint_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
    WHERE tc.table_name = 'chapter_assignments'
    AND tc.constraint_type = 'FOREIGN KEY'
    AND kcu.column_name = 'chapter_id'
    AND tc.table_schema = 'public';
    
    -- 如果找到约束，先删除再重新创建
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE public.chapter_assignments DROP CONSTRAINT ' || constraint_name;
        
        -- 重新创建外键约束，允许 NULL 值
        ALTER TABLE public.chapter_assignments 
        ADD CONSTRAINT chapter_assignments_chapter_id_fkey 
        FOREIGN KEY (chapter_id) REFERENCES public.chapters(id) ON DELETE CASCADE;
        
        RAISE NOTICE '外键约束已重新创建: %', constraint_name;
    END IF;
END $$;

-- 4. 添加检查约束确保数据完整性
-- 确保要么有 chapter_id，要么有明确的 title（用于大纲项分配）
ALTER TABLE public.chapter_assignments 
DROP CONSTRAINT IF EXISTS check_chapter_or_title;

ALTER TABLE public.chapter_assignments 
ADD CONSTRAINT check_chapter_or_title 
CHECK (
    (chapter_id IS NOT NULL) OR 
    (chapter_id IS NULL AND title IS NOT NULL AND length(trim(title)) > 0)
);

-- 5. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_chapter_id_null 
ON public.chapter_assignments(project_id, title) 
WHERE chapter_id IS NULL;

-- 6. 更新 RLS 策略以处理 NULL chapter_id 的情况
DROP POLICY IF EXISTS "Users can view their chapter assignments" ON public.chapter_assignments;
CREATE POLICY "Users can view their chapter assignments" ON public.chapter_assignments
    FOR SELECT USING (
        lead_author_id = auth.uid() OR
        reviewer_id = auth.uid() OR
        assigned_by = auth.uid() OR
        project_id IN (
            SELECT project_id FROM public.project_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

-- 7. 创建一个改进的默认分配创建函数
CREATE OR REPLACE FUNCTION create_chapter_assignments_safe(
    target_project_id UUID,
    default_author_id UUID DEFAULT NULL
)
RETURNS TABLE(
    created_count INTEGER,
    chapter_assignments INTEGER,
    outline_assignments INTEGER,
    error_message TEXT
) AS $$
DECLARE
    assignment_count INTEGER := 0;
    chapter_assignment_count INTEGER := 0;
    outline_assignment_count INTEGER := 0;
    chapter_record RECORD;
    outline_record RECORD;
    project_owner_id UUID;
    error_msg TEXT := NULL;
BEGIN
    -- 获取项目所有者
    SELECT owner_id INTO project_owner_id 
    FROM public.projects 
    WHERE id = target_project_id;
    
    IF project_owner_id IS NULL THEN
        error_message := 'Project not found or no owner';
        created_count := 0;
        chapter_assignments := 0;
        outline_assignments := 0;
        RETURN NEXT;
        RETURN;
    END IF;
    
    -- 如果没有指定默认作者，使用项目所有者
    IF default_author_id IS NULL THEN
        default_author_id := project_owner_id;
    END IF;
    
    BEGIN
        -- 为现有章节创建分配
        FOR chapter_record IN 
            SELECT c.* 
            FROM public.chapters c
            WHERE c.project_id = target_project_id
            AND c.id NOT IN (
                SELECT chapter_id 
                FROM public.chapter_assignments 
                WHERE chapter_id IS NOT NULL
            )
        LOOP
            INSERT INTO public.chapter_assignments (
                chapter_id,
                project_id,
                title,
                description,
                lead_author_id,
                reviewer_id,
                assigned_by,
                due_date,
                status,
                priority,
                word_count_target
            ) VALUES (
                chapter_record.id,
                target_project_id,
                chapter_record.title,
                COALESCE(chapter_record.summary, '请完成本章节的编写工作'),
                default_author_id,
                project_owner_id,
                project_owner_id,
                NOW() + INTERVAL '7 days',
                'pending',
                'medium',
                3000
            );
            
            chapter_assignment_count := chapter_assignment_count + 1;
        END LOOP;
        
        -- 为一级大纲项创建分配（没有对应章节的）
        FOR outline_record IN 
            SELECT o.* 
            FROM public.outlines o
            WHERE o.project_id = target_project_id
            AND o.level <= 2  -- 只处理一级和二级大纲
            AND NOT EXISTS (
                SELECT 1 FROM public.chapters 
                WHERE outline_id = o.id
            )
            AND NOT EXISTS (
                SELECT 1 FROM public.chapter_assignments 
                WHERE project_id = target_project_id 
                AND title = o.title
            )
        LOOP
            INSERT INTO public.chapter_assignments (
                chapter_id,  -- 这里是 NULL
                project_id,
                title,
                description,
                lead_author_id,
                reviewer_id,
                assigned_by,
                due_date,
                status,
                priority,
                word_count_target
            ) VALUES (
                NULL,  -- 大纲项没有对应的章节ID
                target_project_id,
                outline_record.title,
                COALESCE(outline_record.description, '请完成本章节的编写工作'),
                default_author_id,
                project_owner_id,
                project_owner_id,
                NOW() + INTERVAL '7 days',
                'pending',
                'medium',
                3000
            );
            
            outline_assignment_count := outline_assignment_count + 1;
        END LOOP;
        
        assignment_count := chapter_assignment_count + outline_assignment_count;
        
    EXCEPTION WHEN OTHERS THEN
        error_msg := SQLERRM;
        assignment_count := 0;
        chapter_assignment_count := 0;
        outline_assignment_count := 0;
    END;
    
    created_count := assignment_count;
    chapter_assignments := chapter_assignment_count;
    outline_assignments := outline_assignment_count;
    error_message := error_msg;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- 8. 测试新函数（可选）
-- SELECT * FROM create_chapter_assignments_safe('your-project-id');

-- 9. 验证修复结果
SELECT 
    'chapter_assignments table structure updated' as status,
    (SELECT is_nullable FROM information_schema.columns 
     WHERE table_name = 'chapter_assignments' 
     AND column_name = 'chapter_id' 
     AND table_schema = 'public') as chapter_id_nullable;

-- 10. 清理可能存在的无效数据
-- 删除可能存在的重复分配
WITH duplicate_assignments AS (
    SELECT id, ROW_NUMBER() OVER (
        PARTITION BY project_id, title 
        ORDER BY created_at DESC
    ) as rn
    FROM public.chapter_assignments
)
DELETE FROM public.chapter_assignments 
WHERE id IN (
    SELECT id FROM duplicate_assignments WHERE rn > 1
);

-- 完成提示
SELECT 
    'Chapter assignments constraint fix completed!' as message,
    'You can now create assignments for both chapters and outlines' as note,
    'Use: SELECT * FROM create_chapter_assignments_safe(''project-id'');' as usage;
