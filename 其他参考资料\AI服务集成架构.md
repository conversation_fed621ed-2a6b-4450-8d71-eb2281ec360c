# AI服务集成架构设计

## 🤖 AI服务整体架构

### 架构概览

```mermaid
graph TB
    subgraph "前端应用层"
        A[编辑器界面]
        B[AI助手面板]
        C[图表生成器]
        D[文献管理器]
    end
    
    subgraph "AI服务网关"
        E[AI请求路由器]
        F[请求队列管理]
        G[结果缓存]
        H[错误处理]
    end
    
    subgraph "AI模型服务"
        I[OpenRouter API]
        J[DeepSeek Chat]
        K[DeepSeek Coder]
        L[图表生成AI]
    end
    
    subgraph "数据处理层"
        M[文本预处理]
        N[结果后处理]
        O[格式转换]
        P[质量检查]
    end
    
    subgraph "存储层"
        Q[AI任务记录]
        R[生成内容缓存]
        S[用户偏好设置]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    E --> F
    F --> I
    I --> J
    I --> K
    I --> L
    J --> M
    K --> M
    L --> M
    M --> N
    N --> O
    O --> P
    P --> Q
    Q --> R
    R --> S
```

## 🔧 AI服务模块设计

### 1. AI写作辅助服务

#### 核心功能模块

```javascript
// ai-writing-service.js
class AIWritingService {
    constructor() {
        this.openRouterClient = new OpenRouterClient();
        this.cache = new AICache();
        this.preprocessor = new TextPreprocessor();
        this.postprocessor = new TextPostprocessor();
    }

    // 内容生成
    async generateContent(prompt, context = {}) {
        const processedPrompt = this.preprocessor.prepare(prompt, context);
        
        const request = {
            model: "deepseek/deepseek-chat",
            messages: [
                {
                    role: "system",
                    content: this.getSystemPrompt('content_generation')
                },
                {
                    role: "user", 
                    content: processedPrompt
                }
            ],
            max_tokens: 2000,
            temperature: 0.7
        };

        const response = await this.openRouterClient.chat.completions.create(request);
        return this.postprocessor.format(response.choices[0].message.content);
    }

    // 智能续写
    async continueWriting(existingText, context = {}) {
        const prompt = this.buildContinuePrompt(existingText, context);
        return await this.generateContent(prompt, context);
    }

    // 语言润色
    async polishText(text, style = 'academic') {
        const prompt = `请对以下文本进行学术化润色，保持原意的同时提升表达质量：\n\n${text}`;
        
        const request = {
            model: "deepseek/deepseek-chat",
            messages: [
                {
                    role: "system",
                    content: this.getSystemPrompt('text_polishing')
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: Math.min(text.length * 2, 4000),
            temperature: 0.3
        };

        const response = await this.openRouterClient.chat.completions.create(request);
        return this.postprocessor.format(response.choices[0].message.content);
    }

    // 内容扩展
    async expandContent(keyPoints, targetLength = 500) {
        const prompt = `请基于以下要点，扩展为${targetLength}字左右的学术性段落：\n${keyPoints.join('\n')}`;
        return await this.generateContent(prompt);
    }

    // 获取系统提示词
    getSystemPrompt(type) {
        const prompts = {
            content_generation: `你是《大模型技术与油气应用概论》的专业写作助手。请遵循以下要求：
1. 使用学术化、专业的语言风格
2. 确保内容准确、逻辑清晰
3. 适当引用相关技术概念
4. 保持客观、严谨的学术态度
5. 内容应适合大学教材使用`,
            
            text_polishing: `你是专业的学术文本编辑助手。请对文本进行润色，要求：
1. 保持原文的核心观点和逻辑结构
2. 提升语言表达的学术性和专业性
3. 修正语法错误和表达不当之处
4. 确保术语使用的准确性和一致性
5. 保持适合教材的表达风格`
        };
        return prompts[type] || prompts.content_generation;
    }
}
```

### 2. 智能图表生成服务

```javascript
// ai-chart-service.js
class AIChartService {
    constructor() {
        this.mermaidGenerator = new MermaidGenerator();
        this.chartTemplates = new ChartTemplates();
    }

    // 根据描述生成图表
    async generateChart(description, chartType = 'auto') {
        const prompt = this.buildChartPrompt(description, chartType);
        
        const request = {
            model: "deepseek/deepseek-coder",
            messages: [
                {
                    role: "system",
                    content: this.getChartSystemPrompt()
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: 1500,
            temperature: 0.2
        };

        const response = await this.openRouterClient.chat.completions.create(request);
        const mermaidCode = this.extractMermaidCode(response.choices[0].message.content);
        
        return {
            code: mermaidCode,
            type: this.detectChartType(mermaidCode),
            preview: await this.generatePreview(mermaidCode)
        };
    }

    // 架构图生成
    async generateArchitectureChart(components, relationships) {
        const description = `请生成包含以下组件的架构图：
组件：${components.join(', ')}
关系：${relationships.join(', ')}`;
        
        return await this.generateChart(description, 'architecture');
    }

    // 流程图生成
    async generateFlowChart(steps, decisions = []) {
        const description = `请生成包含以下步骤的流程图：
步骤：${steps.join(' -> ')}
决策点：${decisions.join(', ')}`;
        
        return await this.generateChart(description, 'flowchart');
    }

    getChartSystemPrompt() {
        return `你是专业的图表生成助手，专门为《大模型技术与油气应用概论》生成技术图表。
要求：
1. 使用标准的Mermaid语法
2. 图表结构清晰、逻辑合理
3. 适合学术教材使用
4. 包含适当的标签和说明
5. 确保图表的可读性和专业性

支持的图表类型：
- flowchart: 流程图
- graph: 关系图
- sequenceDiagram: 时序图
- classDiagram: 类图
- erDiagram: 实体关系图`;
    }
}
```

### 3. 智能文献管理服务

```javascript
// ai-reference-service.js
class AIReferenceService {
    constructor() {
        this.crossrefAPI = new CrossrefAPI();
        this.semanticScholar = new SemanticScholarAPI();
        this.citationFormatter = new CitationFormatter();
    }

    // 智能文献搜索
    async searchReferences(query, filters = {}) {
        const enhancedQuery = await this.enhanceSearchQuery(query);
        
        const results = await Promise.all([
            this.crossrefAPI.search(enhancedQuery, filters),
            this.semanticScholar.search(enhancedQuery, filters)
        ]);

        const mergedResults = this.mergeSearchResults(results);
        return this.rankResults(mergedResults, query);
    }

    // 增强搜索查询
    async enhanceSearchQuery(originalQuery) {
        const prompt = `请为以下学术搜索查询生成更精确的关键词和同义词：
原查询：${originalQuery}
领域：大模型技术与油气应用
请提供：1. 核心关键词 2. 相关术语 3. 英文对应词`;

        const request = {
            model: "deepseek/deepseek-chat",
            messages: [
                {
                    role: "system",
                    content: "你是专业的学术搜索助手，擅长生成精确的学术搜索关键词。"
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: 500,
            temperature: 0.3
        };

        const response = await this.openRouterClient.chat.completions.create(request);
        return this.parseEnhancedQuery(response.choices[0].message.content);
    }

    // 自动提取文献信息
    async extractReferenceInfo(input) {
        let referenceData = {};

        // 尝试从DOI提取
        if (this.isDOI(input)) {
            referenceData = await this.crossrefAPI.getByDOI(input);
        }
        // 尝试从URL提取
        else if (this.isURL(input)) {
            referenceData = await this.extractFromURL(input);
        }
        // 使用AI解析文本
        else {
            referenceData = await this.parseReferenceText(input);
        }

        return this.standardizeReferenceData(referenceData);
    }

    // AI解析文献文本
    async parseReferenceText(text) {
        const prompt = `请从以下文本中提取文献信息，返回JSON格式：
${text}

请提取：title, authors, journal, year, volume, issue, pages, doi, url`;

        const request = {
            model: "deepseek/deepseek-chat",
            messages: [
                {
                    role: "system",
                    content: "你是专业的文献信息提取助手，能够准确识别和提取各种格式的文献信息。"
                },
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: 800,
            temperature: 0.1
        };

        const response = await this.openRouterClient.chat.completions.create(request);
        return JSON.parse(response.choices[0].message.content);
    }

    // 生成标准引用格式
    async formatCitation(reference, style = 'gb7714') {
        return this.citationFormatter.format(reference, style);
    }

    // 文献去重
    async deduplicateReferences(references) {
        const duplicateGroups = await this.findDuplicates(references);
        return this.mergeDuplicates(duplicateGroups);
    }
}
```

## 🔄 AI服务调度与管理

### 1. 请求队列管理

```javascript
// ai-queue-manager.js
class AIQueueManager {
    constructor() {
        this.queues = {
            high: [], // 实时请求
            normal: [], // 普通请求
            low: [] // 批处理请求
        };
        this.processing = new Set();
        this.rateLimiter = new RateLimiter();
    }

    // 添加AI任务到队列
    async addTask(task, priority = 'normal') {
        const taskId = this.generateTaskId();
        const queueTask = {
            id: taskId,
            ...task,
            priority,
            createdAt: new Date(),
            status: 'queued'
        };

        this.queues[priority].push(queueTask);
        this.processQueue();
        
        return taskId;
    }

    // 处理队列
    async processQueue() {
        if (this.processing.size >= this.getMaxConcurrency()) {
            return;
        }

        const task = this.getNextTask();
        if (!task) return;

        this.processing.add(task.id);
        task.status = 'processing';

        try {
            const result = await this.executeTask(task);
            await this.saveTaskResult(task.id, result);
            task.status = 'completed';
        } catch (error) {
            await this.handleTaskError(task.id, error);
            task.status = 'failed';
        } finally {
            this.processing.delete(task.id);
            this.processQueue(); // 处理下一个任务
        }
    }

    // 获取下一个任务（优先级调度）
    getNextTask() {
        for (const priority of ['high', 'normal', 'low']) {
            if (this.queues[priority].length > 0) {
                return this.queues[priority].shift();
            }
        }
        return null;
    }
}
```

### 2. 结果缓存系统

```javascript
// ai-cache-manager.js
class AICacheManager {
    constructor() {
        this.redis = new RedisClient();
        this.localCache = new Map();
        this.cacheConfig = {
            writing: { ttl: 3600 }, // 1小时
            chart: { ttl: 86400 }, // 24小时
            reference: { ttl: 604800 } // 7天
        };
    }

    // 生成缓存键
    generateCacheKey(service, input) {
        const hash = crypto.createHash('md5')
            .update(JSON.stringify(input))
            .digest('hex');
        return `ai:${service}:${hash}`;
    }

    // 获取缓存结果
    async get(service, input) {
        const key = this.generateCacheKey(service, input);
        
        // 先检查本地缓存
        if (this.localCache.has(key)) {
            return this.localCache.get(key);
        }

        // 检查Redis缓存
        const cached = await this.redis.get(key);
        if (cached) {
            const result = JSON.parse(cached);
            this.localCache.set(key, result);
            return result;
        }

        return null;
    }

    // 设置缓存
    async set(service, input, result) {
        const key = this.generateCacheKey(service, input);
        const ttl = this.cacheConfig[service]?.ttl || 3600;

        // 设置本地缓存
        this.localCache.set(key, result);

        // 设置Redis缓存
        await this.redis.setex(key, ttl, JSON.stringify(result));
    }
}
```

## 📊 AI服务监控与分析

### 1. 性能监控

```javascript
// ai-monitor.js
class AIServiceMonitor {
    constructor() {
        this.metrics = {
            requestCount: 0,
            successCount: 0,
            errorCount: 0,
            averageResponseTime: 0,
            cacheHitRate: 0
        };
        this.startTime = Date.now();
    }

    // 记录请求指标
    recordRequest(service, duration, success, cached = false) {
        this.metrics.requestCount++;
        
        if (success) {
            this.metrics.successCount++;
        } else {
            this.metrics.errorCount++;
        }

        // 更新平均响应时间
        this.updateAverageResponseTime(duration);

        // 更新缓存命中率
        if (cached) {
            this.updateCacheHitRate();
        }

        // 记录到数据库
        this.saveMetrics(service, {
            duration,
            success,
            cached,
            timestamp: new Date()
        });
    }

    // 获取服务状态
    getServiceStatus() {
        const uptime = Date.now() - this.startTime;
        const successRate = this.metrics.successCount / this.metrics.requestCount;
        
        return {
            uptime,
            successRate,
            ...this.metrics,
            status: successRate > 0.95 ? 'healthy' : 'degraded'
        };
    }
}
```

### 2. 成本控制

```javascript
// ai-cost-manager.js
class AICostManager {
    constructor() {
        this.costConfig = {
            'deepseek/deepseek-chat': {
                inputCost: 0.00014, // 每1K tokens
                outputCost: 0.00028
            },
            'deepseek/deepseek-coder': {
                inputCost: 0.00014,
                outputCost: 0.00028
            }
        };
        this.dailyBudget = 50; // 美元
        this.currentDailyCost = 0;
    }

    // 计算请求成本
    calculateCost(model, inputTokens, outputTokens) {
        const config = this.costConfig[model];
        if (!config) return 0;

        const inputCost = (inputTokens / 1000) * config.inputCost;
        const outputCost = (outputTokens / 1000) * config.outputCost;
        
        return inputCost + outputCost;
    }

    // 检查预算限制
    async checkBudget(estimatedCost) {
        const projectedCost = this.currentDailyCost + estimatedCost;
        
        if (projectedCost > this.dailyBudget) {
            throw new Error('Daily budget exceeded');
        }

        return true;
    }

    // 记录实际成本
    recordCost(model, inputTokens, outputTokens) {
        const cost = this.calculateCost(model, inputTokens, outputTokens);
        this.currentDailyCost += cost;
        
        // 保存到数据库
        this.saveCostRecord({
            model,
            inputTokens,
            outputTokens,
            cost,
            timestamp: new Date()
        });

        return cost;
    }
}
```

## 🔧 配置文件

### AI服务配置

```yaml
# ai-config.yml
ai_services:
  openrouter:
    api_key: ${OPENROUTER_API_KEY}
    base_url: "https://openrouter.ai/api/v1"
    timeout: 30000
    retry_attempts: 3
    
  models:
    writing:
      primary: "deepseek/deepseek-chat"
      fallback: "deepseek/deepseek-coder"
      max_tokens: 4000
      temperature: 0.7
      
    coding:
      primary: "deepseek/deepseek-coder"
      max_tokens: 2000
      temperature: 0.2
      
    analysis:
      primary: "deepseek/deepseek-chat"
      max_tokens: 1500
      temperature: 0.3

  rate_limits:
    requests_per_minute: 60
    tokens_per_minute: 100000
    
  cache:
    enabled: true
    ttl:
      writing: 3600
      chart: 86400
      reference: 604800
      
  monitoring:
    enabled: true
    metrics_interval: 60
    alert_thresholds:
      error_rate: 0.05
      response_time: 10000
```

这个AI服务集成架构提供了完整的AI功能支持，包括写作辅助、图表生成、文献管理等核心功能，同时具备良好的性能监控、成本控制和错误处理机制。

接下来我们需要创建完整的数据库结构设计。您觉得这个AI架构设计如何？
