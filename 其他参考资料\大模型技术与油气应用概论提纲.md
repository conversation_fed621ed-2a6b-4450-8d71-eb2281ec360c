《大模型技术与油气应用概论》
通识教材编写提纲

1．编写目的
（1）面向高校人工智能通识课教育目标和油气行业特色，建立以大模型技术原理为基础、以油气应用为方向的专业领域人工智能技术与应用方面的参考材料。
（2）针对油气行业对大模型技术需求大、而实际落地难等现实问题，提供从技术原理到行业适配的基础知识体系。
（3）大模型技术成为构建油气行业新质生产力的核心引擎，实现“数据要素驱动、场景应用主导、协同创新支撑”的新质生产力框架。
（4）通过系统性梳理大模型在油气领域应用的体系架构，助力企业把握数字化转型机遇，制定技术路线图和实施策略，推动智慧能源体系建设和绿色低碳可持续性发展。
2．总体线条
人工智能（AI）技术—大模型技术—工业智能体系—油气应用场景
3．核心内容
（1）理论技术篇：以Transformer为核心介绍大模型技术原理和架构
（2）应用模式篇：以DeepSeek为基础介绍大模型技术落地应用模式
（3）油气实践篇：从工业大模型到油气应用，包括基础架构、模型体系、应用架构、技术挑战等（多元数据融合、地下地上算法、复杂环境轻量化部署等）
（4）发展趋势篇：技术发展前沿、油气应用趋势、大模型评测与风险治理等
4．编写模式
（1）剖析大模型技术特点，着眼落地应用模式，以国内开源大模型为重点分析
（2）建立编写组，先形成基础框架，再逐步扩展的模式，持续完善电子版材料
（3）统一图件格式，构建原文引用体系，形成电子资源，建立共享模式
（4）采用AI工具设计封面和图标，辅助提供代码、文字生成
5．责任编辑
（1）中国石油大学（北京）牵头， 人工智能学院具体负责
（2）中国石油昆仑数智公司联合，智慧油田事业部等部门具体参与
（3）油气应用实践由中国石油、中国石化、中国海油、国家管网相关单位参加
（4）按章节明确具体责任人和参与人，提供版本更新计划及网址


《大模型技术与油气应用概论》
编写目录
第0章 前言
0.1 人工智能发展与大模型时代
(1)人工智能发展的主要阶段，AI各阶段主要特征；
(2)人工智能演化进入大模型时代，即AI2.0时代；
(3)大模型时代的主要标志
0.2 大模型技术的油气应用
(1)大模型技术的工业化应用现状：参考腾讯、艾瑞报告
(2)油气领域大模型应用情况：起步阶段的持续探索；油气大模型应用场景概述；当前典型油气大模型情况
(3)油气领域大模型应用的挑战与方向：数据集基础、模型可解释性、油气工业软件嵌入、油气具身智能体、油气新能源研究新范式等
0.3 本书编写目的与基本结构
(1)大模型技术与应用的人工智能通识课高校参考教材
(2)油气领域大模型技术与应用的基础知识体系
(3)初步构建形成油气新能源的新质生产力框架


【第一篇 理论技术篇】
第1章大模型基本概念与内涵
1.1人工智能进入大模型时代
（1）人工智能演进阶段：从人工智能—机器学习—深度学习—大模型时代（4个阶段）
（2）大模型的演进路线：统计语言模型—神经语言模型—预训练语言模型—生成式大语言模型—多模态大模型—推理大模型（6个阶段）
（3）大模型时代人工智能的主要特征
与机器学习和深度学习时期的人工智能相比，大模型时代的人工智能在技术架构、训练方式、应用能力等方面展现出以下鲜明特征：
模型规模与架构的突破
模型训练范式的变革
泛化与推理能力的提升
资源需求与工程的挑战
垂直应用场景的扩展
1.2大模型的定义与类型
（1）从大语言模型到大模型：LLM与LM
（2）大模型的定义与特点：定义、内涵、特点等
（3）大模型的类型与对比：分类方式包括技术架构分类、任务领域分类、功能类型分类、应用层级分类等
1.3大模型能力的内涵
（1）扩展法则Scaling Law：模型性能与其规模呈现的可预测关系
（2）涌现能力Emergent Abilities：涌现能力的突变性和不可预见性
（3）价值对齐Value Alignment：现状与未来挑战
1.4大模型的优势与挑战
（1）大模型时代的AI特征
（2）大模型技术的特点与优势
（3）大模型应用面临的问题与挑战


第2章大模型架构与关键技术
2.1 Transformer基础模型
  （1）前期深度学习模型架构：CNN/RNN/GAN等
  （2）Transformer基本架构：编码器、解码器等组件和架构图
  （3）Transformer关键技术：自注意力机制、位置编码、前馈神经网络等
  （4）Transformer模型的影响：划时代、新范式、基模型等
2.2 编码器架构模型（Encoder-Only）
（1）模型架构：自编码模型、VAE模型
（2）模型技术特点
（3）模型实例介绍：BERT、GLM等
2.3 解码器架构模型（Decoder-Only）
（1）模型架构：自回归模型
（2）模型技术特点
（3）模型实例介绍：GPT、Llama
2.4 编码器-解码器架构模型（Encoder-Decoder）
（1）模型架构：编码-解码
（2）模型技术特点
（3）模型实例介绍：T5、GLM、Pangu等
2.5 混合架构模型（MoE）
（1）模型架构演进：混合架构模型的变化，强调混合专家模型MoE
（2）模型技术特点：门控方程（Gating Function）、专家网络（Expert Network）、路由机制(Routing Mechanism)等
（3）模型实例介绍：Mixtral、Deepseek等



第3章大模型构建与能力学习
3.1 大模型构建的基础数据集
  （1）大模型的数据集准备：大模型与数据集的关系
    （2）大模型的数据集：预训练数据集、指令微调数据集、人类反馈数据集
  （3）大模型的评测数据集
3.2 大模型的预训练（Pre-Trained）
（1）大模型预训练任务：自监督、无监督
（2）参数优化技术
（3）大模型预训练方法
3.3 大模型的后训练（Post-Trained）
（1）大模型的指令微调：策略、步骤等
（2）参数高效微调：LoRA等
（3）指令微调扩展方法：CoT、自学习等
3.4 大模型的强化学习与对齐（RL Alignment）
（1）大模型监督微调
（2）奖励模型训练
（3）强化学习训练
（4）其它对齐方法



第4章多模态大模型技术体系
4.1 多模态大模型基本架构
  （1）ViT模型架构
    （2）多模态模型基本要素：编码器、投影器等
  （3）多模态模型构建方法
  （4）多模态大模型挑战：架构、对齐、融合、适配等
4.2 多模态生成与扩展技术
（1）变分自编码器和GAN
（2）稳定扩散模型SD
（3）多模态生成模型
（4）多模态扩展技术：ICL、CoT等
4.3 多模态大模型预训练
（1）数据量与参数量分析
（2）多模态大模型训练策略：预训练、指令微调等
（3）多模态对齐：方法、融合等
4.4 多模态大模型实例
（1）Next-GPT
（2）Gemma 3
（3）Qwen2.5-VL

注：自监督学习（SSL）、专家混合（MoE）、人类反馈强化学习（RLHF）和链式思考（CoT）提示是推动MLLMs发展的关键技术。


【第二篇 应用模式篇】
第5章大模型提示工程与思维链
5.1 大模型提示工程应用
  （1）基本方法
    （2）技术关键
  （3）应用模式
5.2 大模型思维链应用
  （1）基本方法
    （2）技术关键
  （3）应用模式
5.3 大模型提示工程应用策略
  （1）多种提示工程对比
    （2）大模型推理能力提升
  （3）大模型提示工程应用实例





第6章大模型知识融合与增强
6.1 大模型知识增强技术
  （1）基本方法
    （2）技术关键
  （3）应用模式
6.2 大模型知识融合
  （1）基本方法
    （2）技术关键
  （3）应用模式
6.3 大模型知识检索增强RAG
  （1）基本方法
    （2）关键技术
  （3）应用模式



第7章大模型驱动的智能体应用
7.1 大模型与智能体
  （1）智能体的定义与内涵
    （2）智能体的类型与特点
  （3）大模型技术与智能体
 7.2 大模型驱动的智能体构建方法
  （1）智能体的框架设计
    （2）智能体的模块与能力
  （3）智能体集成方式
7.3 智能体系统的构建
  （1）多智能体的架构模式
    （2）多智能体的协同技术
  （3）多智能体应用模式
7.4 大模型驱动的智能体应用
  （1）链式结构工作流智能体
    （2）并行模式工作流智能体
  （3）复杂条件自适应智能体




第8章大模型蒸馏与部署评测
8.1 大模型蒸馏技术
  （1）大模型蒸馏技术基本框架
    （2）大模型蒸馏技术的实现
  （3）蒸馏模型量化部署方法
  （4）蒸馏模型知识传递与评测
8.2 大模型部署方法与应用路径
（1）大模型部署架构：规划与造型、部署技术架构
（2）大模型基础设施建设与运维：算力网络、数据底座、运维体系等
（3）大模型应用平台设计与开发
8.3 大模型评测与管理
（1）大模型安全风险控制
（2）大模型应用服务评测
（3）大模型管理标准体系

参考文献：大模型落地路线图-中国信通院



【第三篇 专业实践篇】
第9章油气大模型应用与新质生产力
# 物理—信息—认知三个世界关系
# 不可能三角的包容性对策
# 工业大模型体系架构
# 油气大模型的特点与实践
# 大模型驱动产业发展新范式：科研、运营、人力资源等
# 油气新质生产力框架模型：技术革命性突破、生产要素创新性配置、产业深度转型升级



第10章大模型油气应用场景与模式
# 大模型油气新能源应用的数据基础：标签化、向量化、融合化
# 大模型油气应用方式：问答问数、内容生成、分析优化、智能控制、科学发现等
# 大模型边缘智能应用：智能感知设备、智能传输网关、专业数字人、边缘应用机器人等
# 大模型油气应用模式：垂直领域小模型、多智能体协同、工业软件嵌入插件等

其它维度提纲：
# 专业推理大模型应用场景
智能问答—智能问数—智能生成 VS（矩阵）
勘探评价—开发生产—提高采收系-油气储运
知识管理、科研助理、
    # 大小模型协同应用场景
      边缘智能设备+小模型：现场控制与监测
      领域模型适配+专业大模型：生产优化
      多模态工业数据+多模型大模型：风险管控与设备维护
      工业机器人+云边协同模型
    # 科学计算大模型应用场景
      地震资料智能处理解释
      盆地与油气藏智能模型
      油气全产业链智能优化



第11章大模型油气应用实践
# 油气勘探应用实践
# 油气开发应用实践
# 油气生产运营实践
# 油气输运应用实践
# 油气安全管控应用实践
# 油气与新能源协同实践



第12章 大模型技术与应用展望
# 大模型前沿技术与AGI：ANI-AGI-ASI图，空间智能、具身智能以及世界模型，大模型伦理与治理等
# 大模型油气应用趋势与前景
全生命周期数字资产，跨业务流程运营管理，绿色低碳化生态体系
# 大模型油气应用与信创产业发展：技术自主可控、硬件设备国产化、产业化应用推广等