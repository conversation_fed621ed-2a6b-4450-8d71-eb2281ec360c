<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多媒体功能测试 - 书籍智能编纂系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .test-header p {
            color: #6b7280;
            font-size: 1.125rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }
        
        .test-card h3 {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin: 0 0 1rem 0;
            color: #1f2937;
            font-size: 1.25rem;
        }
        
        .test-card h3 i {
            font-size: 1.5rem;
        }
        
        .test-card p {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .test-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .test-btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .test-btn-primary:hover {
            background: #2563eb;
        }
        
        .test-btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .test-btn-secondary:hover {
            background: #e5e7eb;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-top: 1rem;
        }
        
        .status-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .config-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .config-section h2 {
            margin: 0 0 1.5rem 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .config-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .config-item label {
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }
        
        .config-item input,
        .config-item select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
        }
        
        .test-results {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            margin-top: 2rem;
        }
        
        .test-results h2 {
            margin: 0 0 1.5rem 0;
            color: #1f2937;
        }
        
        .result-item {
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .result-item:last-child {
            margin-bottom: 0;
        }
        
        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .result-title {
            font-weight: 600;
            color: #1f2937;
        }
        
        .result-content {
            color: #6b7280;
            font-size: 0.875rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-flask"></i> 多媒体功能测试</h1>
            <p>测试书籍智能编纂系统的Pollinations多媒体功能集成</p>
        </div>

        <!-- 配置部分 -->
        <div class="config-section">
            <h2><i class="fas fa-cog"></i> 服务配置</h2>
            <div class="config-grid">
                <div class="config-item">
                    <label for="test-api-key">API密钥（可选）：</label>
                    <input type="password" id="test-api-key" placeholder="输入API密钥">
                </div>
                <div class="config-item">
                    <label for="test-referrer">引用标识：</label>
                    <input type="text" id="test-referrer" value="llm-book-system-test">
                </div>
                <div class="config-item">
                    <label for="test-timeout">超时时间（秒）：</label>
                    <input type="number" id="test-timeout" value="60" min="30" max="300">
                </div>
                <div class="config-item">
                    <label for="test-retries">重试次数：</label>
                    <input type="number" id="test-retries" value="2" min="0" max="5">
                </div>
            </div>
            <div class="test-actions" style="margin-top: 1.5rem;">
                <button class="test-btn test-btn-primary" onclick="applyTestConfig()">
                    <i class="fas fa-check"></i> 应用配置
                </button>
                <button class="test-btn test-btn-secondary" onclick="testConnection()">
                    <i class="fas fa-plug"></i> 测试连接
                </button>
            </div>
        </div>

        <!-- 功能测试卡片 -->
        <div class="test-grid">
            <!-- 文生图测试 -->
            <div class="test-card">
                <h3><i class="fas fa-image" style="color: #3b82f6;"></i> 文生图功能</h3>
                <p>测试根据文本描述生成图片的功能，支持多种尺寸和模型选择。</p>
                <div class="test-actions">
                    <button class="test-btn test-btn-primary" onclick="testImageGeneration()">
                        <i class="fas fa-play"></i> 开始测试
                    </button>
                    <button class="test-btn test-btn-secondary" onclick="openImageDialog()">
                        <i class="fas fa-external-link-alt"></i> 打开对话框
                    </button>
                </div>
                <div id="image-status" class="status-indicator" style="display: none;"></div>
            </div>

            <!-- 文本转语音测试 -->
            <div class="test-card">
                <h3><i class="fas fa-volume-up" style="color: #10b981;"></i> 文本转语音</h3>
                <p>测试将文本转换为语音的功能，支持多种语音选择和播放控制。</p>
                <div class="test-actions">
                    <button class="test-btn test-btn-primary" onclick="testTextToSpeech()">
                        <i class="fas fa-play"></i> 开始测试
                    </button>
                    <button class="test-btn test-btn-secondary" onclick="openTTSDialog()">
                        <i class="fas fa-external-link-alt"></i> 打开对话框
                    </button>
                </div>
                <div id="tts-status" class="status-indicator" style="display: none;"></div>
            </div>

            <!-- 语音转文字测试 -->
            <div class="test-card">
                <h3><i class="fas fa-microphone" style="color: #8b5cf6;"></i> 语音转文字</h3>
                <p>测试将语音录音或音频文件转换为文字的功能，支持多种音频格式。</p>
                <div class="test-actions">
                    <button class="test-btn test-btn-primary" onclick="testSpeechToText()">
                        <i class="fas fa-play"></i> 开始测试
                    </button>
                    <button class="test-btn test-btn-secondary" onclick="openSTTDialog()">
                        <i class="fas fa-external-link-alt"></i> 打开对话框
                    </button>
                </div>
                <div id="stt-status" class="status-indicator" style="display: none;"></div>
            </div>

            <!-- 图片识别测试 -->
            <div class="test-card">
                <h3><i class="fas fa-eye" style="color: #f59e0b;"></i> 图片识别</h3>
                <p>测试分析和识别图片内容的功能，支持图片上传和URL输入。</p>
                <div class="test-actions">
                    <button class="test-btn test-btn-primary" onclick="testImageAnalysis()">
                        <i class="fas fa-play"></i> 开始测试
                    </button>
                    <button class="test-btn test-btn-secondary" onclick="openAnalysisDialog()">
                        <i class="fas fa-external-link-alt"></i> 打开对话框
                    </button>
                </div>
                <div id="analysis-status" class="status-indicator" style="display: none;"></div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="test-results" id="test-results" style="display: none;">
            <h2><i class="fas fa-chart-line"></i> 测试结果</h2>
            <div id="results-content"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="pollinations-service.js"></script>
    <script src="multimedia-handlers.js"></script>
    <script>
        // 测试脚本
        let testResults = [];

        // 应用测试配置
        function applyTestConfig() {
            const config = {
                apiKey: document.getElementById('test-api-key').value || null,
                referrer: document.getElementById('test-referrer').value,
                timeout: parseInt(document.getElementById('test-timeout').value) * 1000,
                maxRetries: parseInt(document.getElementById('test-retries').value)
            };

            window.pollinationsService.saveConfig(config);
            showStatus('config', '配置已应用', 'success');
        }

        // 测试连接
        async function testConnection() {
            try {
                showStatus('config', '正在测试连接...', 'info');
                const results = await window.pollinationsService.validateConfig();
                
                let message = '连接测试完成:\n';
                message += `图片生成: ${results.imageGeneration ? '✓' : '✗'}\n`;
                message += `语音生成: ${results.audioGeneration ? '✓' : '✗'}\n`;
                message += `图片识别: ${results.vision ? '✓' : '✗'}`;
                
                if (results.errors.length > 0) {
                    message += '\n错误: ' + results.errors.join(', ');
                    showStatus('config', message, 'warning');
                } else {
                    showStatus('config', message, 'success');
                }
            } catch (error) {
                showStatus('config', `连接测试失败: ${error.message}`, 'error');
            }
        }

        // 显示状态
        function showStatus(elementId, message, type) {
            const statusEl = document.getElementById(elementId + '-status') || 
                           document.querySelector('.status-indicator');
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = `status-indicator status-${type}`;
                statusEl.style.display = 'flex';
            }
        }

        // 添加测试结果
        function addTestResult(title, content, success) {
            testResults.push({ title, content, success, timestamp: new Date() });
            updateTestResults();
        }

        // 更新测试结果显示
        function updateTestResults() {
            const resultsContainer = document.getElementById('test-results');
            const resultsContent = document.getElementById('results-content');
            
            if (testResults.length === 0) {
                resultsContainer.style.display = 'none';
                return;
            }

            resultsContainer.style.display = 'block';
            resultsContent.innerHTML = testResults.map(result => `
                <div class="result-item">
                    <div class="result-header">
                        <span class="result-title">${result.title}</span>
                        <span class="status-indicator status-${result.success ? 'success' : 'error'}">
                            ${result.success ? '成功' : '失败'}
                        </span>
                    </div>
                    <div class="result-content">${result.content}</div>
                </div>
            `).join('');
        }

        // 测试函数
        async function testImageGeneration() {
            try {
                showStatus('image', '正在生成测试图片...', 'info');
                const imageUrl = await window.pollinationsService.generateImage(
                    '一只可爱的小猫在阳光下睡觉', 
                    { width: 512, height: 512 }
                );
                showStatus('image', '图片生成成功！', 'success');
                addTestResult('文生图测试', `成功生成图片: ${imageUrl}`, true);
            } catch (error) {
                showStatus('image', `生成失败: ${error.message}`, 'error');
                addTestResult('文生图测试', `失败: ${error.message}`, false);
            }
        }

        async function testTextToSpeech() {
            try {
                showStatus('tts', '正在生成语音...', 'info');
                const audioBlob = await window.pollinationsService.textToSpeech('这是一个测试语音');
                showStatus('tts', '语音生成成功！', 'success');
                addTestResult('文本转语音测试', '成功生成语音文件', true);
            } catch (error) {
                showStatus('tts', `生成失败: ${error.message}`, 'error');
                addTestResult('文本转语音测试', `失败: ${error.message}`, false);
            }
        }

        async function testSpeechToText() {
            showStatus('stt', '语音转文字需要音频文件', 'warning');
            addTestResult('语音转文字测试', '需要用户提供音频文件进行测试', true);
        }

        async function testImageAnalysis() {
            try {
                showStatus('analysis', '正在分析测试图片...', 'info');
                const analysis = await window.pollinationsService.analyzeImage(
                    'https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/256px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg',
                    '请描述这张图片'
                );
                showStatus('analysis', '图片分析成功！', 'success');
                addTestResult('图片识别测试', `分析结果: ${analysis.substring(0, 100)}...`, true);
            } catch (error) {
                showStatus('analysis', `分析失败: ${error.message}`, 'error');
                addTestResult('图片识别测试', `失败: ${error.message}`, false);
            }
        }

        // 打开对话框函数（需要在主应用中实现）
        function openImageDialog() {
            alert('请在主应用中测试文生图对话框功能');
        }

        function openTTSDialog() {
            alert('请在主应用中测试文本转语音对话框功能');
        }

        function openSTTDialog() {
            alert('请在主应用中测试语音转文字对话框功能');
        }

        function openAnalysisDialog() {
            alert('请在主应用中测试图片识别对话框功能');
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            applyTestConfig();
        });
    </script>
</body>
</html>
