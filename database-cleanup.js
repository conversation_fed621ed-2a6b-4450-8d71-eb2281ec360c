/**
 * 数据库清理和优化脚本
 * 用于修复chapters表中outline_id为null的问题
 */

class DatabaseCleaner {
    constructor(supabaseClient) {
        this.supabase = supabaseClient;
        this.results = {
            analysis: {},
            cleanup: {},
            verification: {}
        };
    }

    // 执行完整的数据库清理流程
    async executeFullCleanup() {
        console.log('🚀 开始数据库清理和优化...');
        
        try {
            // 第一步：数据分析
            await this.analyzeData();
            
            // 第二步：数据清理
            await this.cleanupData();
            
            // 第三步：修复关联
            await this.fixAssociations();
            
            // 第四步：数据一致性检查
            await this.ensureConsistency();
            
            // 第五步：验证结果
            await this.verifyResults();
            
            console.log('✅ 数据库清理完成');
            return this.results;
            
        } catch (error) {
            console.error('❌ 数据库清理失败:', error);
            throw error;
        }
    }

    // 第一步：数据分析
    async analyzeData() {
        console.log('📊 第一步：分析当前数据状况...');
        
        // 分析chapters表
        const { data: chaptersAnalysis } = await this.supabase
            .from('chapters')
            .select('id, title, outline_id, project_id, word_count, status');
        
        const totalChapters = chaptersAnalysis?.length || 0;
        const nullOutlineId = chaptersAnalysis?.filter(c => !c.outline_id).length || 0;
        const validOutlineId = totalChapters - nullOutlineId;
        
        // 分析outlines表
        const { data: outlinesAnalysis } = await this.supabase
            .from('outlines')
            .select('id, title, level, project_id');
        
        const totalOutlines = outlinesAnalysis?.length || 0;
        const chapterLevelOutlines = outlinesAnalysis?.filter(o => o.level > 0).length || 0;
        
        this.results.analysis = {
            totalChapters,
            nullOutlineId,
            validOutlineId,
            totalOutlines,
            chapterLevelOutlines
        };
        
        console.log('📋 数据分析结果:', this.results.analysis);
        
        // 显示有问题的章节
        if (nullOutlineId > 0) {
            const problematicChapters = chaptersAnalysis.filter(c => !c.outline_id);
            console.log('⚠️ 有问题的章节:', problematicChapters.map(c => ({
                id: c.id,
                title: c.title,
                project_id: c.project_id
            })));
        }
    }

    // 第二步：数据清理
    async cleanupData() {
        console.log('🧹 第二步：清理重复和孤立数据...');
        
        let cleanupCount = 0;
        
        // 删除没有对应项目的孤立章节
        const { data: projects } = await this.supabase
            .from('projects')
            .select('id');
        
        const validProjectIds = projects?.map(p => p.id) || [];
        
        if (validProjectIds.length > 0) {
            const { data: orphanChapters } = await this.supabase
                .from('chapters')
                .select('id')
                .not('project_id', 'in', `(${validProjectIds.map(id => `"${id}"`).join(',')})`);
            
            if (orphanChapters && orphanChapters.length > 0) {
                const { error } = await this.supabase
                    .from('chapters')
                    .delete()
                    .not('project_id', 'in', `(${validProjectIds.map(id => `"${id}"`).join(',')})`);
                
                if (!error) {
                    cleanupCount += orphanChapters.length;
                    console.log(`🗑️ 删除了 ${orphanChapters.length} 个孤立章节`);
                }
            }
            
            // 删除没有对应项目的孤立大纲
            const { data: orphanOutlines } = await this.supabase
                .from('outlines')
                .select('id')
                .not('project_id', 'in', `(${validProjectIds.map(id => `"${id}"`).join(',')})`);
            
            if (orphanOutlines && orphanOutlines.length > 0) {
                const { error } = await this.supabase
                    .from('outlines')
                    .delete()
                    .not('project_id', 'in', `(${validProjectIds.map(id => `"${id}"`).join(',')})`);
                
                if (!error) {
                    cleanupCount += orphanOutlines.length;
                    console.log(`🗑️ 删除了 ${orphanOutlines.length} 个孤立大纲`);
                }
            }
        }
        
        this.results.cleanup.deletedRecords = cleanupCount;
    }

    // 第三步：修复关联
    async fixAssociations() {
        console.log('🔗 第三步：修复outline_id关联...');
        
        let fixedCount = 0;
        let createdOutlines = 0;
        
        // 获取所有outline_id为null的章节
        const { data: chaptersWithoutOutline } = await this.supabase
            .from('chapters')
            .select('id, title, project_id, created_by, created_at, updated_at')
            .is('outline_id', null);
        
        if (!chaptersWithoutOutline || chaptersWithoutOutline.length === 0) {
            console.log('✅ 没有需要修复的章节');
            return;
        }
        
        console.log(`🔍 找到 ${chaptersWithoutOutline.length} 个需要修复的章节`);
        
        // 获取所有现有的大纲
        const { data: existingOutlines } = await this.supabase
            .from('outlines')
            .select('id, title, project_id, level');
        
        for (const chapter of chaptersWithoutOutline) {
            try {
                // 尝试通过标题匹配现有大纲
                const matchingOutline = existingOutlines?.find(o => 
                    o.title === chapter.title && 
                    o.project_id === chapter.project_id && 
                    o.level > 0
                );
                
                if (matchingOutline) {
                    // 更新章节的outline_id
                    const { error } = await this.supabase
                        .from('chapters')
                        .update({ outline_id: matchingOutline.id })
                        .eq('id', chapter.id);
                    
                    if (!error) {
                        fixedCount++;
                        console.log(`✅ 修复章节 "${chapter.title}" 的outline_id`);
                    }
                } else {
                    // 创建新的大纲记录
                    const { data: maxSortOrder } = await this.supabase
                        .from('outlines')
                        .select('sort_order')
                        .eq('project_id', chapter.project_id)
                        .order('sort_order', { ascending: false })
                        .limit(1);
                    
                    const nextSortOrder = (maxSortOrder?.[0]?.sort_order || 0) + 1;
                    
                    const { data: newOutline, error: createError } = await this.supabase
                        .from('outlines')
                        .insert({
                            project_id: chapter.project_id,
                            title: chapter.title,
                            level: 1,
                            sort_order: nextSortOrder,
                            description: '自动生成的大纲项',
                            status: 'planned',
                            created_by: chapter.created_by,
                            created_at: chapter.created_at,
                            updated_at: chapter.updated_at
                        })
                        .select()
                        .single();
                    
                    if (!createError && newOutline) {
                        // 更新章节的outline_id
                        const { error: updateError } = await this.supabase
                            .from('chapters')
                            .update({ outline_id: newOutline.id })
                            .eq('id', chapter.id);
                        
                        if (!updateError) {
                            createdOutlines++;
                            fixedCount++;
                            console.log(`🆕 为章节 "${chapter.title}" 创建新大纲并关联`);
                        }
                    }
                }
            } catch (error) {
                console.error(`❌ 修复章节 "${chapter.title}" 失败:`, error);
            }
        }
        
        this.results.cleanup.fixedAssociations = fixedCount;
        this.results.cleanup.createdOutlines = createdOutlines;
        
        console.log(`✅ 修复了 ${fixedCount} 个章节关联，创建了 ${createdOutlines} 个新大纲`);
    }

    // 第四步：数据一致性检查
    async ensureConsistency() {
        console.log('🔧 第四步：确保数据一致性...');
        
        let updatedCount = 0;
        
        // 修复章节状态
        const { error: chapterStatusError } = await this.supabase
            .from('chapters')
            .update({ status: 'draft' })
            .or('status.is.null,status.eq.');
        
        if (!chapterStatusError) {
            console.log('✅ 修复了章节状态');
            updatedCount++;
        }
        
        // 修复大纲状态
        const { error: outlineStatusError } = await this.supabase
            .from('outlines')
            .update({ status: 'planned' })
            .or('status.is.null,status.eq.');
        
        if (!outlineStatusError) {
            console.log('✅ 修复了大纲状态');
            updatedCount++;
        }
        
        // 修复字数统计
        const { error: wordCountError } = await this.supabase
            .from('chapters')
            .update({ word_count: 0 })
            .is('word_count', null);
        
        if (!wordCountError) {
            console.log('✅ 修复了字数统计');
            updatedCount++;
        }
        
        this.results.cleanup.consistencyUpdates = updatedCount;
    }

    // 第五步：验证结果
    async verifyResults() {
        console.log('🔍 第五步：验证修复结果...');
        
        // 重新分析数据
        const { data: chaptersAfter } = await this.supabase
            .from('chapters')
            .select('id, outline_id');
        
        const totalAfter = chaptersAfter?.length || 0;
        const nullAfter = chaptersAfter?.filter(c => !c.outline_id).length || 0;
        const validAfter = totalAfter - nullAfter;
        
        this.results.verification = {
            totalChapters: totalAfter,
            nullOutlineId: nullAfter,
            validOutlineId: validAfter,
            successRate: totalAfter > 0 ? ((validAfter / totalAfter) * 100).toFixed(2) + '%' : '0%'
        };
        
        console.log('📊 验证结果:', this.results.verification);
        
        if (nullAfter === 0) {
            console.log('🎉 所有章节的outline_id都已修复！');
        } else {
            console.log(`⚠️ 仍有 ${nullAfter} 个章节的outline_id为null`);
        }
    }

    // 生成清理报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                before: this.results.analysis,
                after: this.results.verification,
                changes: this.results.cleanup
            },
            recommendations: []
        };
        
        if (this.results.verification.nullOutlineId > 0) {
            report.recommendations.push('仍有章节的outline_id为null，建议手动检查');
        }
        
        if (this.results.cleanup.createdOutlines > 0) {
            report.recommendations.push('创建了新的大纲项，建议检查大纲结构');
        }
        
        return report;
    }
}

// 使用示例
async function runDatabaseCleanup() {
    if (typeof supabaseManager === 'undefined' || !supabaseManager.supabase) {
        console.error('❌ Supabase客户端未初始化');
        return;
    }
    
    const cleaner = new DatabaseCleaner(supabaseManager.supabase);
    
    try {
        const results = await cleaner.executeFullCleanup();
        const report = cleaner.generateReport();
        
        console.log('📋 清理报告:', report);
        
        // 显示用户友好的通知
        if (typeof showNotification === 'function') {
            const successRate = results.verification.successRate;
            showNotification(`数据库清理完成，成功率: ${successRate}`, 'success');
        }
        
        return report;
    } catch (error) {
        console.error('❌ 数据库清理失败:', error);
        if (typeof showNotification === 'function') {
            showNotification('数据库清理失败: ' + error.message, 'error');
        }
        throw error;
    }
}

// 导出函数
if (typeof window !== 'undefined') {
    window.runDatabaseCleanup = runDatabaseCleanup;
    window.DatabaseCleaner = DatabaseCleaner;
}
