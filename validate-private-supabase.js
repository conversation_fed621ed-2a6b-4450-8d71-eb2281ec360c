// 私有化Supabase URL验证测试脚本
// 用于验证修改后的验证逻辑是否正确工作

// 引入配置管理器（在Node.js环境中需要适配）
if (typeof window === 'undefined') {
    // Node.js环境模拟
    global.window = {};
    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {}
    };
}

// 引入SupabaseConfigManager类
class SupabaseConfigManager {
    constructor() {
        this.configKey = 'supabase-config';
        this.defaultConfig = {
            url: '',
            anonKey: '',
            serviceKey: '',
            region: 'auto'
        };
        this.config = this.loadConfig();
        this.isConnected = false;
        this.connectionStatus = 'disconnected';
    }

    loadConfig() {
        try {
            const saved = localStorage.getItem(this.configKey);
            if (saved) {
                const config = JSON.parse(saved);
                return { ...this.defaultConfig, ...config };
            }
            return { ...this.defaultConfig };
        } catch (error) {
            console.error('加载Supabase配置失败:', error);
            return { ...this.defaultConfig };
        }
    }

    // 验证URL格式
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    // 验证Supabase URL（支持私有化部署）
    isValidSupabaseUrl(url) {
        try {
            const urlObj = new URL(url);

            // 检查协议 - 优先使用HTTPS，但允许本地开发和私有化部署使用HTTP
            const isHttps = urlObj.protocol === 'https:';
            const isHttp = urlObj.protocol === 'http:';

            if (!isHttps && !isHttp) {
                return false;
            }

            const hostname = urlObj.hostname;

            // 官方Supabase域名必须使用HTTPS
            if (url.includes('supabase.co')) {
                return isHttps;
            }

            // 特殊处理localhost - 允许HTTP
            if (hostname === 'localhost') {
                return true;
            }

            // 检查是否为IP地址格式
            const isIpAddress = /^\d+\.\d+\.\d+\.\d+$/.test(hostname);
            if (isIpAddress) {
                // 本地开发环境允许HTTP
                const isLocalIp = hostname.startsWith('127.') || hostname.startsWith('192.168.') || hostname.startsWith('10.');
                return isLocalIp;
            }

            // 对于私有化部署域名，检查基本格式
            const domainParts = hostname.split('.');
            if (domainParts.length < 2) {
                return false;
            }

            // 检查是否包含有效的TLD
            const tld = domainParts[domainParts.length - 1];
            if (tld.length < 2) {
                return false;
            }

            // 私有化部署允许HTTP（但建议使用HTTPS）
            return true;

        } catch (error) {
            return false;
        }
    }
}

// 测试用例
const testCases = [
    // 官方域名 - 应该通过
    { url: 'https://bigzfjlaypptochqpxzu.supabase.co', expected: true, description: '官方Supabase域名' },
    { url: 'https://test-project.supabase.co', expected: true, description: '官方Supabase域名（测试）' },
    
    // 私有化部署 - 应该通过
    { url: 'https://supabase.example.com', expected: true, description: '私有化部署域名（HTTPS）' },
    { url: 'https://api.mycompany.com', expected: true, description: '自定义API域名（HTTPS）' },
    { url: 'https://db.internal.corp', expected: true, description: '内部企业域名（HTTPS）' },
    { url: 'http://superbase.ailer.ltd', expected: true, description: '用户实际的私有化部署URL（HTTP）' },
    { url: 'https://superboss.ailer.ltd/rest/v1/', expected: true, description: '私有化部署URL（HTTPS）' },
    
    // 本地开发 - 应该通过
    { url: 'https://localhost:8000', expected: true, description: '本地开发环境' },
    { url: 'https://127.0.0.1:3000', expected: true, description: '本地IP地址' },
    { url: 'https://*************:8080', expected: true, description: '局域网IP地址' },
    
    // 无效URL - 应该失败
    { url: 'http://public-domain.com', expected: true, description: 'HTTP协议私有化部署（允许）' },
    { url: 'https://invalid', expected: false, description: '无效域名格式' },
    { url: 'not-a-url', expected: false, description: '不是URL格式' },
    { url: '', expected: false, description: '空URL' },
    { url: 'https://*******', expected: false, description: '公网IP地址' }
];

// 运行测试
function runTests() {
    const manager = new SupabaseConfigManager();
    let passCount = 0;
    let failCount = 0;
    
    console.log('🧪 开始运行私有化Supabase URL验证测试...\n');
    
    testCases.forEach((testCase, index) => {
        const isValidUrl = manager.isValidUrl(testCase.url);
        const isValidSupabase = isValidUrl ? manager.isValidSupabaseUrl(testCase.url) : false;
        const actualResult = isValidSupabase;
        const passed = actualResult === testCase.expected;
        
        if (passed) {
            passCount++;
            console.log(`✅ 测试 ${index + 1}: ${testCase.description}`);
        } else {
            failCount++;
            console.log(`❌ 测试 ${index + 1}: ${testCase.description}`);
            console.log(`   URL: ${testCase.url}`);
            console.log(`   期望: ${testCase.expected ? '通过' : '失败'}, 实际: ${actualResult ? '通过' : '失败'}`);
        }
    });
    
    console.log(`\n📊 测试总结:`);
    console.log(`   通过: ${passCount} 个`);
    console.log(`   失败: ${failCount} 个`);
    console.log(`   总计: ${testCases.length} 个测试用例`);
    console.log(`   成功率: ${((passCount / testCases.length) * 100).toFixed(1)}%`);
    
    if (failCount === 0) {
        console.log('\n🎉 所有测试都通过了！私有化Supabase URL验证功能正常工作。');
    } else {
        console.log('\n⚠️  有测试失败，请检查验证逻辑。');
    }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SupabaseConfigManager, runTests };
    // 直接运行测试
    runTests();
} else {
    // 在浏览器环境中，将函数暴露到全局
    window.runSupabaseValidationTests = runTests;
}
