<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接受邀请 - 《大模型技术与油气应用概论》协作系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .invitation-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .invitation-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .invitation-header {
            margin-bottom: 30px;
        }
        
        .invitation-icon {
            width: 80px;
            height: 80px;
            background: #4f46e5;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }
        
        .invitation-title {
            font-size: 1.8rem;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .invitation-subtitle {
            color: #6b7280;
            font-size: 1rem;
        }
        
        .invitation-details {
            background: #f9fafb;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .detail-row:last-child {
            margin-bottom: 0;
        }
        
        .detail-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .detail-value {
            color: #1f2937;
            font-weight: 600;
        }
        
        .role-badge {
            display: inline-block;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .role-admin {
            background: #ddd6fe;
            color: #5b21b6;
        }
        
        .role-editor {
            background: #d1fae5;
            color: #065f46;
        }
        
        .role-author {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .role-reviewer {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .invitation-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .btn-accept {
            background: #10b981;
            color: white;
        }
        
        .btn-accept:hover {
            background: #059669;
        }
        
        .btn-decline {
            background: #ef4444;
            color: white;
        }
        
        .btn-decline:hover {
            background: #dc2626;
        }
        
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #fef2f2;
            color: #991b1b;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #fecaca;
            text-align: left;
        }
        
        .success-message {
            background: #ecfdf5;
            color: #065f46;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #a7f3d0;
            text-align: left;
        }
        
        .expired-invitation {
            background: #fef3c7;
            color: #92400e;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border: 1px solid #fde68a;
        }
        
        .expired-invitation h3 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        
        .login-prompt {
            background: #eff6ff;
            color: #1e40af;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border: 1px solid #bfdbfe;
        }
        
        .login-prompt h3 {
            margin: 0 0 10px 0;
            color: #1e40af;
        }
        
        .auth-actions {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .invitation-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="invitation-container">
        <div class="invitation-card">
            <div id="invitation-content">
                <!-- 内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script>
        class InvitationHandler {
            constructor() {
                this.invitation = null;
                this.project = null;
                this.currentUser = null;
                this.invitationToken = null;
            }

            async initialize() {
                // 获取邀请令牌
                const urlParams = new URLSearchParams(window.location.search);
                this.invitationToken = urlParams.get('token');

                if (!this.invitationToken) {
                    this.showError('无效的邀请链接');
                    return;
                }

                // 检查用户登录状态
                this.currentUser = await supabaseManager.getCurrentUser();

                // 加载邀请信息
                await this.loadInvitation();
            }

            async loadInvitation() {
                try {
                    // 获取邀请信息
                    const { data: invitation, error } = await supabaseManager.supabase
                        .from('user_invitations')
                        .select(`
                            *,
                            projects (
                                id,
                                title,
                                description
                            ),
                            invited_by_profile:user_profiles!invited_by (
                                full_name,
                                institution
                            )
                        `)
                        .eq('invitation_token', this.invitationToken)
                        .single();

                    if (error) throw error;

                    if (!invitation) {
                        this.showError('邀请不存在或已失效');
                        return;
                    }

                    this.invitation = invitation;
                    this.project = invitation.projects;

                    // 检查邀请状态
                    if (invitation.status !== 'pending') {
                        this.showInvitationStatus(invitation.status);
                        return;
                    }

                    // 检查是否过期
                    if (new Date(invitation.expires_at) < new Date()) {
                        this.showExpiredInvitation();
                        return;
                    }

                    // 显示邀请详情
                    this.showInvitationDetails();

                } catch (error) {
                    console.error('加载邀请失败:', error);
                    this.showError('加载邀请信息失败: ' + error.message);
                }
            }

            showInvitationDetails() {
                const content = document.getElementById('invitation-content');
                
                if (!this.currentUser) {
                    // 用户未登录，显示登录提示
                    content.innerHTML = this.getLoginPromptHTML();
                } else if (this.currentUser.email === this.invitation.email) {
                    // 邮箱匹配，显示接受邀请界面
                    content.innerHTML = this.getAcceptInvitationHTML();
                } else {
                    // 邮箱不匹配
                    content.innerHTML = this.getEmailMismatchHTML();
                }
            }

            getLoginPromptHTML() {
                return `
                    <div class="invitation-header">
                        <div class="invitation-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h1 class="invitation-title">项目邀请</h1>
                        <p class="invitation-subtitle">您收到了一个项目协作邀请</p>
                    </div>

                    <div class="invitation-details">
                        <div class="detail-row">
                            <span class="detail-label">项目名称:</span>
                            <span class="detail-value">${this.project.title}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">邀请角色:</span>
                            <span class="role-badge role-${this.invitation.role}">${this.getRoleDisplayName(this.invitation.role)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">邀请人:</span>
                            <span class="detail-value">${this.invitation.invited_by_profile?.full_name || '未知'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">邀请邮箱:</span>
                            <span class="detail-value">${this.invitation.email}</span>
                        </div>
                    </div>

                    <div class="login-prompt">
                        <h3><i class="fas fa-info-circle"></i> 需要登录</h3>
                        <p>请先登录或注册账户以接受邀请。如果您还没有账户，请使用邀请邮箱 <strong>${this.invitation.email}</strong> 进行注册。</p>
                        <div class="auth-actions">
                            <a href="auth.html" class="btn btn-accept">
                                <i class="fas fa-sign-in-alt"></i> 登录
                            </a>
                            <a href="auth.html" class="btn btn-secondary">
                                <i class="fas fa-user-plus"></i> 注册
                            </a>
                        </div>
                    </div>

                    <div class="invitation-footer">
                        <p>© 2024 《大模型技术与油气应用概论》编写团队</p>
                    </div>
                `;
            }

            getAcceptInvitationHTML() {
                return `
                    <div class="invitation-header">
                        <div class="invitation-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h1 class="invitation-title">接受项目邀请</h1>
                        <p class="invitation-subtitle">您被邀请加入项目协作</p>
                    </div>

                    <div class="invitation-details">
                        <div class="detail-row">
                            <span class="detail-label">项目名称:</span>
                            <span class="detail-value">${this.project.title}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">项目描述:</span>
                            <span class="detail-value">${this.project.description || '暂无描述'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">您的角色:</span>
                            <span class="role-badge role-${this.invitation.role}">${this.getRoleDisplayName(this.invitation.role)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">邀请人:</span>
                            <span class="detail-value">${this.invitation.invited_by_profile?.full_name || '未知'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">邀请时间:</span>
                            <span class="detail-value">${new Date(this.invitation.created_at).toLocaleDateString()}</span>
                        </div>
                    </div>

                    <div id="invitation-messages"></div>

                    <div class="invitation-actions">
                        <button class="btn btn-accept" onclick="invitationHandler.acceptInvitation()">
                            <i class="fas fa-check"></i> 接受邀请
                        </button>
                        <button class="btn btn-decline" onclick="invitationHandler.declineInvitation()">
                            <i class="fas fa-times"></i> 拒绝邀请
                        </button>
                    </div>

                    <div class="invitation-footer">
                        <p>接受邀请后，您将成为项目成员并获得相应权限</p>
                    </div>
                `;
            }

            getEmailMismatchHTML() {
                return `
                    <div class="invitation-header">
                        <div class="invitation-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h1 class="invitation-title">邮箱不匹配</h1>
                        <p class="invitation-subtitle">当前登录账户与邀请邮箱不符</p>
                    </div>

                    <div class="error-message">
                        <h3><i class="fas fa-exclamation-circle"></i> 邮箱不匹配</h3>
                        <p>此邀请是发送给 <strong>${this.invitation.email}</strong> 的，但您当前登录的账户是 <strong>${this.currentUser.email}</strong>。</p>
                        <p>请使用正确的邮箱账户登录，或联系邀请人重新发送邀请。</p>
                    </div>

                    <div class="invitation-actions">
                        <button class="btn btn-secondary" onclick="supabaseManager.signOut()">
                            <i class="fas fa-sign-out-alt"></i> 切换账户
                        </button>
                    </div>

                    <div class="invitation-footer">
                        <p>如有疑问，请联系项目管理员</p>
                    </div>
                `;
            }

            showExpiredInvitation() {
                const content = document.getElementById('invitation-content');
                content.innerHTML = `
                    <div class="invitation-header">
                        <div class="invitation-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h1 class="invitation-title">邀请已过期</h1>
                        <p class="invitation-subtitle">此邀请链接已失效</p>
                    </div>

                    <div class="expired-invitation">
                        <h3><i class="fas fa-hourglass-end"></i> 邀请已过期</h3>
                        <p>此邀请于 ${new Date(this.invitation.expires_at).toLocaleString()} 过期。</p>
                        <p>请联系项目管理员重新发送邀请。</p>
                    </div>

                    <div class="invitation-footer">
                        <p>如需帮助，请联系 ${this.invitation.invited_by_profile?.full_name || '项目管理员'}</p>
                    </div>
                `;
            }

            showInvitationStatus(status) {
                const content = document.getElementById('invitation-content');
                const statusMessages = {
                    'accepted': {
                        icon: 'check-circle',
                        title: '邀请已接受',
                        message: '您已经接受了此邀请并成为项目成员。',
                        class: 'success-message'
                    },
                    'cancelled': {
                        icon: 'ban',
                        title: '邀请已取消',
                        message: '此邀请已被取消。',
                        class: 'error-message'
                    }
                };

                const statusInfo = statusMessages[status];
                if (!statusInfo) return;

                content.innerHTML = `
                    <div class="invitation-header">
                        <div class="invitation-icon">
                            <i class="fas fa-${statusInfo.icon}"></i>
                        </div>
                        <h1 class="invitation-title">${statusInfo.title}</h1>
                    </div>

                    <div class="${statusInfo.class}">
                        <p>${statusInfo.message}</p>
                    </div>

                    <div class="invitation-actions">
                        <a href="project-management.html" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回项目
                        </a>
                    </div>
                `;
            }

            async acceptInvitation() {
                try {
                    this.setButtonsLoading(true);
                    this.showMessage('正在处理邀请...', 'info');

                    // 检查用户是否已是项目成员
                    const { data: existingMember } = await supabaseManager.supabase
                        .from('project_members')
                        .select('id')
                        .eq('project_id', this.project.id)
                        .eq('user_id', this.currentUser.id)
                        .single();

                    if (existingMember) {
                        this.showMessage('您已经是项目成员', 'error');
                        return;
                    }

                    // 添加用户到项目成员
                    const { error: memberError } = await supabaseManager.supabase
                        .from('project_members')
                        .insert({
                            project_id: this.project.id,
                            user_id: this.currentUser.id,
                            role: this.invitation.role,
                            status: 'active',
                            invited_by: this.invitation.invited_by
                        });

                    if (memberError) throw memberError;

                    // 更新邀请状态
                    const { error: invitationError } = await supabaseManager.supabase
                        .from('user_invitations')
                        .update({
                            status: 'accepted',
                            accepted_at: new Date().toISOString()
                        })
                        .eq('id', this.invitation.id);

                    if (invitationError) throw invitationError;

                    this.showMessage('邀请接受成功！正在跳转到项目...', 'success');

                    // 跳转到项目
                    setTimeout(() => {
                        localStorage.setItem('currentProjectId', this.project.id);
                        window.location.href = 'index.html';
                    }, 2000);

                } catch (error) {
                    console.error('接受邀请失败:', error);
                    this.showMessage('接受邀请失败: ' + error.message, 'error');
                } finally {
                    this.setButtonsLoading(false);
                }
            }

            async declineInvitation() {
                if (!confirm('确定要拒绝此邀请吗？')) {
                    return;
                }

                try {
                    this.setButtonsLoading(true);

                    // 更新邀请状态为已取消
                    const { error } = await supabaseManager.supabase
                        .from('user_invitations')
                        .update({
                            status: 'cancelled'
                        })
                        .eq('id', this.invitation.id);

                    if (error) throw error;

                    this.showMessage('已拒绝邀请', 'success');

                    // 显示拒绝后的界面
                    setTimeout(() => {
                        this.showInvitationStatus('cancelled');
                    }, 1500);

                } catch (error) {
                    console.error('拒绝邀请失败:', error);
                    this.showMessage('操作失败: ' + error.message, 'error');
                } finally {
                    this.setButtonsLoading(false);
                }
            }

            setButtonsLoading(loading) {
                const buttons = document.querySelectorAll('.invitation-actions .btn');
                buttons.forEach(btn => {
                    btn.disabled = loading;
                    if (loading) {
                        btn.innerHTML = '<span class="loading"></span> 处理中...';
                    }
                });
            }

            showMessage(message, type) {
                const messagesDiv = document.getElementById('invitation-messages');
                if (!messagesDiv) return;

                const messageClass = type === 'error' ? 'error-message' : 
                                   type === 'success' ? 'success-message' : 
                                   'login-prompt';

                messagesDiv.innerHTML = `
                    <div class="${messageClass}">
                        <p>${message}</p>
                    </div>
                `;
            }

            showError(message) {
                const content = document.getElementById('invitation-content');
                content.innerHTML = `
                    <div class="invitation-header">
                        <div class="invitation-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h1 class="invitation-title">邀请无效</h1>
                    </div>

                    <div class="error-message">
                        <h3><i class="fas fa-exclamation-circle"></i> 错误</h3>
                        <p>${message}</p>
                    </div>

                    <div class="invitation-actions">
                        <a href="auth.html" class="btn btn-secondary">
                            <i class="fas fa-home"></i> 返回首页
                        </a>
                    </div>
                `;
            }

            getRoleDisplayName(role) {
                const roleNames = {
                    'admin': '管理员',
                    'editor': '编辑者',
                    'author': '作者',
                    'reviewer': '审阅者'
                };
                return roleNames[role] || role;
            }
        }

        // 初始化
        const invitationHandler = new InvitationHandler();
        window.addEventListener('load', () => {
            invitationHandler.initialize();
        });
    </script>
</body>
</html>
