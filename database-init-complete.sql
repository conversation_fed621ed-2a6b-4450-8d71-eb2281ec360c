-- 完整数据库初始化脚本
-- 从零开始创建所有必需的表和结构

-- 1. 启用UUID扩展（如果尚未启用）
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 2. 创建用户配置表
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    full_name VARCHA<PERSON>(100),
    email VARCHAR(255) UNIQUE NOT NULL,
    avatar_url TEXT,
    institution VARCHAR(200),
    department VARCHAR(200),
    bio TEXT,
    global_role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建项目表
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) DEFAULT 'book',
    status VARCHAR(50) DEFAULT 'active',
    owner_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建项目成员表
CREATE TABLE IF NOT EXISTS public.project_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL DEFAULT 'author',
    status VARCHAR(20) DEFAULT 'active',
    invited_by UUID REFERENCES public.user_profiles(id),
    invitation_token VARCHAR(255),
    invitation_expires_at TIMESTAMP WITH TIME ZONE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, user_id)
);

-- 5. 创建大纲表
CREATE TABLE IF NOT EXISTS public.outlines (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES public.outlines(id) ON DELETE CASCADE,
    order_index INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'draft',
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. 创建章节表
CREATE TABLE IF NOT EXISTS public.chapters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    outline_id UUID REFERENCES public.outlines(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    summary TEXT,
    content JSONB DEFAULT '{"ops":[]}',
    status VARCHAR(50) DEFAULT 'draft',
    word_count INTEGER DEFAULT 0,
    order_index INTEGER DEFAULT 0,
    created_by UUID REFERENCES public.user_profiles(id),
    updated_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. 创建章节版本表
CREATE TABLE IF NOT EXISTS public.chapter_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    content JSONB NOT NULL,
    changes_description TEXT,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. 创建章节分配表
CREATE TABLE IF NOT EXISTS public.chapter_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    assigned_by UUID REFERENCES public.user_profiles(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deadline TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'assigned',
    notes TEXT,
    UNIQUE(chapter_id, user_id, role)
);

-- 9. 创建用户邀请表
CREATE TABLE IF NOT EXISTS public.user_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    invited_by UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. 创建审核流程表
CREATE TABLE IF NOT EXISTS public.review_processes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending',
    review_notes TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 11. 创建评论表
CREATE TABLE IF NOT EXISTS public.comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chapter_id UUID REFERENCES public.chapters(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position_start INTEGER,
    position_end INTEGER,
    type VARCHAR(20) DEFAULT 'general',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 12. 创建通知表
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    data JSONB,
    read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 13. 创建文献表
CREATE TABLE IF NOT EXISTS public.references (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    authors TEXT,
    publication VARCHAR(255),
    year INTEGER,
    doi VARCHAR(255),
    url TEXT,
    notes TEXT,
    citation_style VARCHAR(50) DEFAULT 'apa',
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 14. 创建索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON public.user_profiles(username);
CREATE INDEX IF NOT EXISTS idx_projects_owner ON public.projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_project_members_project ON public.project_members(project_id);
CREATE INDEX IF NOT EXISTS idx_project_members_user ON public.project_members(user_id);
CREATE INDEX IF NOT EXISTS idx_outlines_project ON public.outlines(project_id);
CREATE INDEX IF NOT EXISTS idx_outlines_parent ON public.outlines(parent_id);
CREATE INDEX IF NOT EXISTS idx_chapters_project ON public.chapters(project_id);
CREATE INDEX IF NOT EXISTS idx_chapters_outline ON public.chapters(outline_id);
CREATE INDEX IF NOT EXISTS idx_chapter_versions_chapter ON public.chapter_versions(chapter_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_chapter ON public.chapter_assignments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_chapter_assignments_user ON public.chapter_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_user_invitations_email ON public.user_invitations(email);
CREATE INDEX IF NOT EXISTS idx_user_invitations_token ON public.user_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_review_processes_chapter ON public.review_processes(chapter_id);
CREATE INDEX IF NOT EXISTS idx_review_processes_reviewer ON public.review_processes(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_comments_chapter ON public.comments(chapter_id);
CREATE INDEX IF NOT EXISTS idx_comments_user ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_references_project ON public.references(project_id);

-- 15. 启用行级安全
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.outlines ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chapter_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_processes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.references ENABLE ROW LEVEL SECURITY;

-- 16. 创建基本的RLS策略
-- 用户配置表策略
CREATE POLICY IF NOT EXISTS "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY IF NOT EXISTS "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- 项目表策略
CREATE POLICY IF NOT EXISTS "Users can view projects they are members of" ON public.projects
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            WHERE pm.project_id = projects.id AND pm.user_id = auth.uid()
        )
    );

-- 项目成员表策略
CREATE POLICY IF NOT EXISTS "Users can view project members" ON public.project_members
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            WHERE pm.project_id = project_members.project_id AND pm.user_id = auth.uid()
        )
    );

-- 用户邀请表策略
CREATE POLICY IF NOT EXISTS "Users can manage invitations in their projects" ON public.user_invitations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.project_members pm
            WHERE pm.project_id = user_invitations.project_id 
            AND pm.user_id = auth.uid()
            AND pm.role IN ('owner', 'admin')
        )
    );

-- 17. 添加约束
ALTER TABLE public.user_profiles 
ADD CONSTRAINT IF NOT EXISTS user_profiles_global_role_check 
CHECK (global_role IN ('system_admin', 'user'));

ALTER TABLE public.project_members 
ADD CONSTRAINT IF NOT EXISTS project_members_role_check 
CHECK (role IN ('owner', 'admin', 'editor', 'author', 'reviewer'));

ALTER TABLE public.project_members 
ADD CONSTRAINT IF NOT EXISTS project_members_status_check 
CHECK (status IN ('active', 'inactive', 'pending', 'invited'));

ALTER TABLE public.user_invitations 
ADD CONSTRAINT IF NOT EXISTS user_invitations_status_check 
CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled'));

ALTER TABLE public.user_invitations 
ADD CONSTRAINT IF NOT EXISTS user_invitations_role_check 
CHECK (role IN ('admin', 'editor', 'author', 'reviewer'));

-- 完成提示
SELECT 'Complete database initialization completed successfully!' as result;
