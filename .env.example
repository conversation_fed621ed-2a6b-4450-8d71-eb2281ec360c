# AI增强学术专著编写系统 - 环境变量配置模板
# 复制此文件为 .env 并根据实际情况修改配置

# ============================================================================
# 数据库配置
# ============================================================================

# PostgreSQL数据库配置
POSTGRES_DB=llm_book_system
POSTGRES_USER=supabase
POSTGRES_PASSWORD=your_secure_password_here

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_CONNECTIONS=100

# ============================================================================
# 认证和安全配置
# ============================================================================

# JWT密钥配置（必须是32字符以上的随机字符串）
JWT_SECRET=your_jwt_secret_here_32_characters_minimum
SECRET_KEY_BASE=your_secret_key_base_here_64_characters_minimum

# API密钥配置
ANON_KEY=your_anon_key_here
SERVICE_ROLE_KEY=your_service_role_key_here

# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_SPECIAL_CHARS=true

# ============================================================================
# 站点配置
# ============================================================================

# 站点URL配置（生产环境请修改为实际域名）
SITE_URL=http://localhost
ADDITIONAL_REDIRECT_URLS=http://localhost/**

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 会话配置
SESSION_TIMEOUT=3600
REFRESH_TOKEN_LIFETIME=604800

# ============================================================================
# 邮件服务配置
# ============================================================================

# SMTP服务器配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_ADMIN_EMAIL=<EMAIL>

# 邮件模板配置
EMAIL_FROM_NAME=AI学术专著编写系统
EMAIL_REPLY_TO=<EMAIL>

# 邮件功能开关
EMAIL_NOTIFICATIONS_ENABLED=true
EMAIL_VERIFICATION_REQUIRED=true

# ============================================================================
# AI服务配置
# ============================================================================

# OpenRouter API配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# 默认AI模型配置
DEEPSEEK_CHAT_MODEL=deepseek/deepseek-chat
DEEPSEEK_CODER_MODEL=deepseek/deepseek-coder

# AI服务限制
AI_DAILY_BUDGET_USD=50
AI_RATE_LIMIT_PER_MINUTE=60
AI_MAX_CONCURRENT_REQUESTS=5
AI_REQUEST_TIMEOUT=30000

# AI功能开关
AI_WRITING_ENABLED=true
AI_CHART_ENABLED=true
AI_REFERENCE_ENABLED=true

# ============================================================================
# 文件存储配置
# ============================================================================

# 文件上传限制
MAX_FILE_SIZE_MB=50
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,md,png,jpg,jpeg,svg

# 存储配置
STORAGE_BACKEND=file
STORAGE_PATH=/var/lib/storage

# 图片处理
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080
IMAGE_QUALITY=85

# ============================================================================
# 缓存配置
# ============================================================================

# Redis缓存配置
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# 缓存TTL配置（秒）
CACHE_TTL_SHORT=300
CACHE_TTL_MEDIUM=3600
CACHE_TTL_LONG=86400

# AI结果缓存配置
AI_CACHE_ENABLED=true
AI_CACHE_TTL_WRITING=3600
AI_CACHE_TTL_CHART=86400
AI_CACHE_TTL_REFERENCE=604800

# ============================================================================
# 系统配置
# ============================================================================

# 运行环境
ENVIRONMENT=development
NODE_ENV=development

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_MAX_FILES=10
LOG_MAX_SIZE=10m

# 性能配置
MAX_CONCURRENT_USERS=100
REQUEST_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000

# 功能开关
REALTIME_ENABLED=true
COLLABORATION_ENABLED=true
VERSION_CONTROL_ENABLED=true

# ============================================================================
# 内容配置
# ============================================================================

# 内容限制
MAX_CHAPTER_LENGTH=50000
MAX_PROJECT_CHAPTERS=100
MAX_TEAM_MEMBERS=20

# 自动保存配置
AUTO_SAVE_INTERVAL=30
VERSION_RETENTION_DAYS=90

# 协作配置
MAX_CONCURRENT_EDITORS=3
CONFLICT_RESOLUTION_TIMEOUT=300

# ============================================================================
# 监控和分析配置
# ============================================================================

# 监控配置
MONITORING_ENABLED=true
METRICS_COLLECTION_INTERVAL=60
HEALTH_CHECK_INTERVAL=30

# 错误报告
ERROR_REPORTING_ENABLED=true
ERROR_SAMPLE_RATE=0.1

# 性能监控
PERFORMANCE_MONITORING_ENABLED=true
SLOW_QUERY_THRESHOLD=1000

# ============================================================================
# 备份配置
# ============================================================================

# 自动备份配置
AUTO_BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION=true

# 备份存储
BACKUP_STORAGE_PATH=/opt/backups/llm-book-system
BACKUP_ENCRYPTION_ENABLED=false

# ============================================================================
# 第三方服务配置
# ============================================================================

# 文献检索API
CROSSREF_API_URL=https://api.crossref.org
SEMANTIC_SCHOLAR_API_URL=https://api.semanticscholar.org

# 图表生成服务
MERMAID_RENDERER_URL=https://mermaid.ink

# 通知服务
WEBHOOK_URL=
SLACK_WEBHOOK_URL=

# ============================================================================
# 开发和调试配置
# ============================================================================

# 调试模式
DEBUG=false
VERBOSE_LOGGING=false

# 开发工具
HOT_RELOAD_ENABLED=false
SOURCE_MAPS_ENABLED=false

# 测试配置
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5433/test_db
TEST_TIMEOUT=30000

# ============================================================================
# 安全配置
# ============================================================================

# 安全头部
SECURITY_HEADERS_ENABLED=true
CONTENT_SECURITY_POLICY_ENABLED=true

# 速率限制
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=900
RATE_LIMIT_MAX_REQUESTS=100

# IP白名单（可选，用逗号分隔）
IP_WHITELIST=

# 防火墙配置
FIREWALL_ENABLED=true
BLOCK_SUSPICIOUS_IPS=true

# ============================================================================
# 国际化配置
# ============================================================================

# 默认语言
DEFAULT_LANGUAGE=zh-CN
SUPPORTED_LANGUAGES=zh-CN,en-US

# 时区配置
DEFAULT_TIMEZONE=Asia/Shanghai

# ============================================================================
# 高级配置
# ============================================================================

# 集群配置（多实例部署）
CLUSTER_MODE=false
CLUSTER_NODE_ID=node1

# 负载均衡
LOAD_BALANCER_ENABLED=false
STICKY_SESSIONS=true

# 微服务配置
MICROSERVICES_MODE=false
SERVICE_DISCOVERY_URL=

# ============================================================================
# 配置说明
# ============================================================================

# 1. 必须配置项：
#    - POSTGRES_PASSWORD: 数据库密码
#    - JWT_SECRET: JWT密钥
#    - SITE_URL: 站点URL
#    - OPENROUTER_API_KEY: AI服务密钥

# 2. 生产环境建议配置：
#    - 使用HTTPS的SITE_URL
#    - 配置真实的SMTP服务器
#    - 启用备份和监控
#    - 设置合适的缓存TTL

# 3. 安全建议：
#    - 使用强密码
#    - 定期更换密钥
#    - 启用防火墙
#    - 限制IP访问

# 4. 性能优化：
#    - 根据服务器配置调整连接池大小
#    - 启用缓存
#    - 配置CDN（如果需要）

# 5. 监控建议：
#    - 启用监控和日志
#    - 配置告警通知
#    - 定期检查系统状态
