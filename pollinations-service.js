/**
 * Pollinations.AI API Service
 * 为书籍智能编纂系统提供多媒体功能支持
 * 
 * 功能包括：
 * - 文生图 (Text-to-Image)
 * - 文本转语音 (Text-to-Speech)
 * - 语音转文字 (Speech-to-Text)
 * - 图片识别 (Vision)
 * - 文本生成 (Text Generation)
 */

class PollinationsService {
    constructor() {
        this.baseImageUrl = 'https://image.pollinations.ai';
        this.baseTextUrl = 'https://text.pollinations.ai';
        this.config = {
            apiKey: null,
            referrer: 'llm-book-system',
            defaultImageModel: 'flux',
            defaultTextModel: 'openai',
            defaultVoice: 'nova',
            timeout: 300000, // 5分钟超时
            maxRetries: 3
        };
        this.loadConfig();
    }

    /**
     * 加载配置
     */
    loadConfig() {
        try {
            const savedConfig = localStorage.getItem('pollinations-config');
            if (savedConfig) {
                const parsed = JSON.parse(savedConfig);
                this.config = { ...this.config, ...parsed };
            }
        } catch (error) {
            console.warn('加载Pollinations配置失败:', error);
        }
    }

    /**
     * 保存配置
     */
    saveConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        try {
            localStorage.setItem('pollinations-config', JSON.stringify(this.config));
        } catch (error) {
            console.error('保存Pollinations配置失败:', error);
        }
    }

    /**
     * 获取请求头
     */
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (includeAuth && this.config.apiKey) {
            headers['Authorization'] = `Bearer ${this.config.apiKey}`;
        }

        return headers;
    }

    /**
     * 添加通用参数
     */
    addCommonParams(params = {}) {
        const commonParams = {};
        
        if (this.config.referrer) {
            commonParams.referrer = this.config.referrer;
        }
        
        if (this.config.apiKey && !params.token) {
            commonParams.token = this.config.apiKey;
        }

        return { ...commonParams, ...params };
    }

    /**
     * 重试机制
     */
    async withRetry(operation, maxRetries = this.config.maxRetries) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                if (i < maxRetries) {
                    const delay = Math.pow(2, i) * 1000; // 指数退避
                    console.warn(`操作失败，${delay}ms后重试 (${i + 1}/${maxRetries + 1}):`, error.message);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 文生图功能
     * @param {string} prompt - 图片描述
     * @param {Object} options - 选项
     * @returns {Promise<string>} 图片URL
     */
    async generateImage(prompt, options = {}) {
        const params = this.addCommonParams({
            model: options.model || this.config.defaultImageModel,
            width: options.width || 1024,
            height: options.height || 1024,
            seed: options.seed,
            enhance: options.enhance || false,
            nologo: options.nologo || true,
            private: options.private || false,
            safe: options.safe || true
        });

        // 移除undefined值
        Object.keys(params).forEach(key => {
            if (params[key] === undefined) {
                delete params[key];
            }
        });

        const encodedPrompt = encodeURIComponent(prompt);
        const queryString = new URLSearchParams(params).toString();
        const url = `${this.baseImageUrl}/prompt/${encodedPrompt}?${queryString}`;

        return this.withRetry(async () => {
            const response = await fetch(url, {
                method: 'GET',
                timeout: this.config.timeout
            });

            if (!response.ok) {
                throw new Error(`图片生成失败: ${response.status} ${response.statusText}`);
            }

            // 返回图片URL
            return response.url;
        });
    }

    /**
     * 获取可用的图片模型
     * @returns {Promise<Array>} 模型列表
     */
    async getImageModels() {
        return this.withRetry(async () => {
            const response = await fetch(`${this.baseImageUrl}/models`);
            
            if (!response.ok) {
                throw new Error(`获取图片模型失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        });
    }

    /**
     * 文本转语音
     * @param {string} text - 要转换的文本
     * @param {Object} options - 选项
     * @returns {Promise<Blob>} 音频数据
     */
    async textToSpeech(text, options = {}) {
        const params = this.addCommonParams({
            model: 'openai-audio',
            voice: options.voice || this.config.defaultVoice
        });

        const encodedText = encodeURIComponent(text);
        const queryString = new URLSearchParams(params).toString();
        const url = `${this.baseTextUrl}/${encodedText}?${queryString}`;

        return this.withRetry(async () => {
            const response = await fetch(url, {
                method: 'GET',
                timeout: this.config.timeout
            });

            if (!response.ok) {
                throw new Error(`文本转语音失败: ${response.status} ${response.statusText}`);
            }

            const contentType = response.headers.get('Content-Type');
            if (!contentType || !contentType.includes('audio')) {
                throw new Error('返回的不是音频文件');
            }

            return await response.blob();
        });
    }

    /**
     * 语音转文字
     * @param {File|Blob} audioFile - 音频文件
     * @param {Object} options - 选项
     * @returns {Promise<string>} 转录文本
     */
    async speechToText(audioFile, options = {}) {
        return this.withRetry(async () => {
            // 方法1: 尝试使用标准的Whisper API格式
            try {
                const formData = new FormData();
                formData.append('file', audioFile, 'audio.' + this.getAudioFormat(audioFile));
                formData.append('model', 'whisper-1');

                // 添加可选参数
                if (options.prompt) {
                    formData.append('prompt', options.prompt);
                }
                if (options.language) {
                    formData.append('language', options.language);
                }

                // 添加通用参数
                if (this.config.referrer) {
                    formData.append('referrer', this.config.referrer);
                }

                const headers = {};
                if (this.config.apiKey) {
                    headers['Authorization'] = `Bearer ${this.config.apiKey}`;
                }

                const response = await fetch(`${this.baseTextUrl}/audio/transcriptions`, {
                    method: 'POST',
                    headers: headers,
                    body: formData,
                    timeout: this.config.timeout
                });

                if (response.ok) {
                    const result = await response.json();
                    return result.text || result.transcription || result.content;
                }
            } catch (error) {
                console.warn('标准Whisper API失败，尝试备用方法:', error.message);
            }

            // 方法2: 尝试使用OpenAI兼容格式
            try {
                const base64Audio = await this.fileToBase64(audioFile);

                const payload = {
                    model: 'whisper-1',
                    input: base64Audio,
                    response_format: 'text'
                };

                // 添加通用参数
                if (this.config.referrer) {
                    payload.referrer = this.config.referrer;
                }

                const response = await fetch(`${this.baseTextUrl}/audio/transcriptions`, {
                    method: 'POST',
                    headers: this.getHeaders(),
                    body: JSON.stringify(payload),
                    timeout: this.config.timeout
                });

                if (response.ok) {
                    const result = await response.json();
                    return result.text || result.transcription || result.content || result;
                }
            } catch (error) {
                console.warn('OpenAI兼容格式失败:', error.message);
            }

            // 方法3: 使用简化的文本API
            try {
                const prompt = `请转录以下音频内容为文字。音频文件类型: ${this.getAudioFormat(audioFile)}`;

                const response = await fetch(`${this.baseTextUrl}/${encodeURIComponent(prompt)}?model=whisper-1`, {
                    method: 'GET',
                    headers: this.getHeaders(),
                    timeout: this.config.timeout
                });

                if (response.ok) {
                    const text = await response.text();
                    return text;
                }
            } catch (error) {
                console.warn('简化API失败:', error.message);
            }

            throw new Error('所有语音转文字方法都失败了。Pollinations可能暂时不支持语音转文字功能，或需要API密钥。');
        });
    }

    /**
     * 图片识别
     * @param {string|File} image - 图片URL或文件
     * @param {string} question - 询问的问题
     * @param {Object} options - 选项
     * @returns {Promise<string>} 识别结果
     */
    async analyzeImage(image, question = '请描述这张图片', options = {}) {
        return this.withRetry(async () => {
            let imageContent;

            if (typeof image === 'string') {
                // 图片URL
                imageContent = {
                    type: 'image_url',
                    image_url: { url: image }
                };
            } else {
                // 图片文件
                const base64Image = await this.fileToBase64(image);
                const format = this.getImageFormat(image);
                imageContent = {
                    type: 'image_url',
                    image_url: {
                        url: `data:image/${format};base64,${base64Image}`
                    }
                };
            }

            const payload = {
                model: options.model || this.config.defaultTextModel,
                messages: [
                    {
                        role: 'user',
                        content: [
                            { type: 'text', text: question },
                            imageContent
                        ]
                    }
                ],
                max_tokens: options.maxTokens || 500
            };

            // 添加通用参数
            if (this.config.referrer) {
                payload.referrer = this.config.referrer;
            }

            const response = await fetch(`${this.baseTextUrl}/openai`, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify(payload),
                timeout: this.config.timeout
            });

            if (!response.ok) {
                throw new Error(`图片识别失败: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            return result.choices[0].message.content;
        });
    }

    /**
     * 获取可用的文本模型和语音
     * @returns {Promise<Object>} 模型和语音信息
     */
    async getTextModelsAndVoices() {
        return this.withRetry(async () => {
            const response = await fetch(`${this.baseTextUrl}/models`);
            
            if (!response.ok) {
                throw new Error(`获取文本模型失败: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        });
    }

    /**
     * 文件转Base64
     */
    async fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 获取音频格式
     */
    getAudioFormat(file) {
        const name = file.name || '';
        const type = file.type || '';
        
        if (type.includes('wav') || name.endsWith('.wav')) return 'wav';
        if (type.includes('mp3') || name.endsWith('.mp3')) return 'mp3';
        if (type.includes('m4a') || name.endsWith('.m4a')) return 'm4a';
        if (type.includes('ogg') || name.endsWith('.ogg')) return 'ogg';
        
        return 'wav'; // 默认格式
    }

    /**
     * 获取图片格式
     */
    getImageFormat(file) {
        const name = file.name || '';
        const type = file.type || '';
        
        if (type.includes('png') || name.endsWith('.png')) return 'png';
        if (type.includes('gif') || name.endsWith('.gif')) return 'gif';
        if (type.includes('webp') || name.endsWith('.webp')) return 'webp';
        
        return 'jpeg'; // 默认格式
    }

    /**
     * 验证API配置
     * @returns {Promise<Object>} 验证结果
     */
    async validateConfig() {
        const results = {
            imageGeneration: false,
            textGeneration: false,
            audioGeneration: false,
            vision: false,
            errors: []
        };

        try {
            // 测试图片生成
            await this.generateImage('test image', { width: 256, height: 256 });
            results.imageGeneration = true;
        } catch (error) {
            results.errors.push(`图片生成测试失败: ${error.message}`);
        }

        try {
            // 测试文本转语音
            await this.textToSpeech('test');
            results.audioGeneration = true;
        } catch (error) {
            results.errors.push(`语音生成测试失败: ${error.message}`);
        }

        try {
            // 测试图片识别（使用测试图片URL）
            await this.analyzeImage('https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/256px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg', '这是什么？');
            results.vision = true;
        } catch (error) {
            results.errors.push(`图片识别测试失败: ${error.message}`);
        }

        results.textGeneration = true; // 文本生成通常都可用

        return results;
    }

    /**
     * 生成图片并下载
     * @param {string} prompt - 图片描述
     * @param {Object} options - 选项
     * @returns {Promise<Blob>} 图片数据
     */
    async generateImageBlob(prompt, options = {}) {
        const imageUrl = await this.generateImage(prompt, options);

        return this.withRetry(async () => {
            const response = await fetch(imageUrl);

            if (!response.ok) {
                throw new Error(`下载图片失败: ${response.status} ${response.statusText}`);
            }

            return await response.blob();
        });
    }

    /**
     * 播放音频
     * @param {Blob} audioBlob - 音频数据
     * @returns {Promise<void>}
     */
    async playAudio(audioBlob) {
        return new Promise((resolve, reject) => {
            const audioUrl = URL.createObjectURL(audioBlob);
            const audio = new Audio(audioUrl);

            audio.onended = () => {
                URL.revokeObjectURL(audioUrl);
                resolve();
            };

            audio.onerror = (error) => {
                URL.revokeObjectURL(audioUrl);
                reject(new Error('音频播放失败'));
            };

            audio.play().catch(reject);
        });
    }

    /**
     * 下载文件
     * @param {Blob} blob - 文件数据
     * @param {string} filename - 文件名
     */
    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 获取配置信息
     * @returns {Object} 当前配置
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * 重置配置为默认值
     */
    resetConfig() {
        this.config = {
            apiKey: null,
            referrer: 'llm-book-system',
            defaultImageModel: 'flux',
            defaultTextModel: 'openai',
            defaultVoice: 'nova',
            timeout: 300000,
            maxRetries: 3
        };
        localStorage.removeItem('pollinations-config');
    }
}

// 创建全局实例
window.pollinationsService = new PollinationsService();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PollinationsService;
}
