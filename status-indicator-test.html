<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态指示器修复测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 2rem;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .project-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 1rem;
            background: white;
        }
        
        .project-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .project-title {
            font-weight: 500;
            color: #1f2937;
        }
        
        .project-description {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 1rem;
        }
        
        .before, .after {
            padding: 1rem;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .old-status-badge {
            padding: 0.75rem 1.25rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background: #3b82f6;
            color: #ffffff;
            border: 2px solid #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .old-progress-bar {
            width: 200px;
            height: 12px;
            background: #f3f4f6;
            border-radius: 6px;
            overflow: hidden;
            margin: 0.5rem 0;
        }
        
        .old-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            width: 75%;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #1f2937; margin-bottom: 2rem;">
            <i class="fas fa-check-circle" style="color: #10b981;"></i>
            状态指示器修复测试
        </h1>
        
        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-before-after"></i>
                修复前后对比
            </div>
            
            <div class="before-after">
                <div class="before">
                    <h4 style="margin-top: 0; color: #dc2626;">修复前 - 丑陋的长条状指示器</h4>
                    <div class="project-item">
                        <div class="project-info">
                            <i class="fas fa-book project-item-icon"></i>
                            <div>
                                <div class="project-title">大模型技术与油气应用概论</div>
                                <div class="project-description">面向大学生的技术教材</div>
                            </div>
                        </div>
                        <div class="old-status-badge">进行中</div>
                    </div>
                    
                    <div style="margin: 1rem 0;">
                        <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.5rem;">工程能在石油概论中的应用</div>
                        <div class="old-progress-bar">
                            <div class="old-progress-fill"></div>
                        </div>
                    </div>
                    
                    <div style="margin: 1rem 0;">
                        <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.5rem;">深度学习与地质建模</div>
                        <div class="old-progress-bar">
                            <div class="old-progress-fill" style="background: linear-gradient(90deg, #f59e0b, #d97706); width: 45%;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="after">
                    <h4 style="margin-top: 0; color: #059669;">修复后 - 简洁的圆形指示器</h4>
                    <div class="project-item">
                        <div class="project-info">
                            <i class="fas fa-book project-item-icon"></i>
                            <div>
                                <div class="project-title">大模型技术与油气应用概论</div>
                                <div class="project-description">面向大学生的技术教材</div>
                            </div>
                        </div>
                        <div class="project-status active"></div>
                    </div>
                    
                    <div style="margin: 1rem 0;">
                        <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.5rem;">工程能在石油概论中的应用</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                    </div>
                    
                    <div style="margin: 1rem 0;">
                        <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.5rem;">深度学习与地质建模</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-palette"></i>
                不同状态的圆形指示器
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div class="project-item">
                    <div class="project-info">
                        <i class="fas fa-book project-item-icon"></i>
                        <div>
                            <div class="project-title">进行中项目</div>
                            <div class="project-description">正在积极开发</div>
                        </div>
                    </div>
                    <div class="project-status active"></div>
                </div>
                
                <div class="project-item">
                    <div class="project-info">
                        <i class="fas fa-pause project-item-icon"></i>
                        <div>
                            <div class="project-title">暂停项目</div>
                            <div class="project-description">临时暂停开发</div>
                        </div>
                    </div>
                    <div class="project-status suspended"></div>
                </div>
                
                <div class="project-item">
                    <div class="project-info">
                        <i class="fas fa-archive project-item-icon"></i>
                        <div>
                            <div class="project-title">归档项目</div>
                            <div class="project-description">已完成或停止</div>
                        </div>
                    </div>
                    <div class="project-status archived"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-chart-line"></i>
                优化后的进度条
            </div>
            
            <div style="display: grid; gap: 1rem;">
                <div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <span style="font-weight: 500;">总体进度</span>
                        <span style="color: #3b82f6; font-weight: 600;">75%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%;"></div>
                    </div>
                </div>
                
                <div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <span style="font-weight: 500;">章节完成度</span>
                        <span style="color: #3b82f6; font-weight: 600;">60%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%;"></div>
                    </div>
                </div>
                
                <div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <span style="font-weight: 500;">审阅进度</span>
                        <span style="color: #3b82f6; font-weight: 600;">30%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 30%;"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; padding: 2rem; background: #f8fafc; border-radius: 8px; margin-top: 2rem;">
            <h3 style="color: #059669; margin-bottom: 1rem;">
                <i class="fas fa-check-circle"></i>
                修复完成！
            </h3>
            <p style="color: #6b7280; margin: 0;">
                所有丑陋的长条状状态指示器已被替换为简洁美观的圆形指示器，
                进度条也进行了优化，整体界面更加清爽美观。
            </p>
        </div>
    </div>
</body>
</html>
