// 多用户协作功能测试脚本
class CollaborationTester {
    constructor() {
        this.testResults = [];
        this.currentUser = null;
        this.testProject = null;
        this.testUsers = [];
    }

    // 运行所有测试
    async runAllTests() {
        console.log('🚀 开始多用户协作功能测试...');
        
        try {
            await this.testUserManagement();
            await this.testProjectCreation();
            await this.testUserInvitation();
            await this.testPermissionControl();
            await this.testChapterAssignment();
            await this.testCollaborativeEditing();
            await this.testReviewProcess();
            
            this.generateTestReport();
        } catch (error) {
            console.error('❌ 测试过程中发生错误:', error);
        }
    }

    // 测试用户管理功能
    async testUserManagement() {
        console.log('📝 测试用户管理功能...');
        
        const tests = [
            {
                name: '用户注册功能',
                test: async () => {
                    // 测试用户注册
                    const testUser = {
                        email: '<EMAIL>',
                        password: 'test123456',
                        full_name: '测试用户',
                        username: 'test_user'
                    };
                    
                    const result = await supabaseManager.signUp(testUser.email, testUser.password, {
                        full_name: testUser.full_name,
                        username: testUser.username
                    });
                    
                    return result && result.user;
                }
            },
            {
                name: '用户登录功能',
                test: async () => {
                    const result = await supabaseManager.signIn('<EMAIL>', 'test123456');
                    this.currentUser = result.user;
                    return result && result.user;
                }
            },
            {
                name: '用户配置管理',
                test: async () => {
                    const profile = await supabaseManager.loadUserProfile();
                    return profile && profile.username === 'test_user';
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, '用户管理');
            } catch (error) {
                this.addTestResult(test.name, false, '用户管理', error.message);
            }
        }
    }

    // 测试项目创建
    async testProjectCreation() {
        console.log('📁 测试项目创建功能...');
        
        const tests = [
            {
                name: '创建新项目',
                test: async () => {
                    const projectData = {
                        title: '测试协作项目',
                        description: '用于测试多用户协作功能的项目',
                        type: 'book'
                    };
                    
                    const project = await supabaseManager.createProject(projectData);
                    this.testProject = project;
                    return project && project.id;
                }
            },
            {
                name: '项目所有者权限',
                test: async () => {
                    const { data: member } = await supabaseManager.supabase
                        .from('project_members')
                        .select('role')
                        .eq('project_id', this.testProject.id)
                        .eq('user_id', this.currentUser.id)
                        .single();
                    
                    return member && member.role === 'owner';
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, '项目管理');
            } catch (error) {
                this.addTestResult(test.name, false, '项目管理', error.message);
            }
        }
    }

    // 测试用户邀请功能
    async testUserInvitation() {
        console.log('✉️ 测试用户邀请功能...');
        
        const tests = [
            {
                name: '创建用户邀请',
                test: async () => {
                    const invitationData = {
                        email: '<EMAIL>',
                        project_id: this.testProject.id,
                        invited_by: this.currentUser.id,
                        role: 'author',
                        invitation_token: 'test_token_' + Date.now(),
                        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
                    };
                    
                    const { data, error } = await supabaseManager.supabase
                        .from('user_invitations')
                        .insert(invitationData)
                        .select()
                        .single();
                    
                    return !error && data;
                }
            },
            {
                name: '邀请状态管理',
                test: async () => {
                    const { data } = await supabaseManager.supabase
                        .from('user_invitations')
                        .select('status')
                        .eq('project_id', this.testProject.id)
                        .eq('email', '<EMAIL>')
                        .single();
                    
                    return data && data.status === 'pending';
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, '用户邀请');
            } catch (error) {
                this.addTestResult(test.name, false, '用户邀请', error.message);
            }
        }
    }

    // 测试权限控制
    async testPermissionControl() {
        console.log('🔒 测试权限控制功能...');
        
        const tests = [
            {
                name: '项目成员权限检查',
                test: async () => {
                    const hasPermission = await supabaseManager.checkProjectPermission(
                        this.testProject.id, 
                        'admin'
                    );
                    return hasPermission; // 项目所有者应该有管理权限
                }
            },
            {
                name: '角色权限矩阵验证',
                test: async () => {
                    // 验证不同角色的权限设置
                    const roles = ['owner', 'admin', 'editor', 'author', 'reviewer'];
                    const permissions = ['read', 'write', 'admin'];
                    
                    // 这里应该验证权限矩阵的正确性
                    return true; // 简化测试
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, '权限控制');
            } catch (error) {
                this.addTestResult(test.name, false, '权限控制', error.message);
            }
        }
    }

    // 测试章节分配
    async testChapterAssignment() {
        console.log('📋 测试章节分配功能...');
        
        const tests = [
            {
                name: '创建章节',
                test: async () => {
                    const chapterData = {
                        project_id: this.testProject.id,
                        title: '测试章节',
                        summary: '用于测试的章节',
                        content: { ops: [{ insert: '测试内容\n' }] },
                        status: 'draft'
                    };
                    
                    const { data, error } = await supabaseManager.supabase
                        .from('chapters')
                        .insert(chapterData)
                        .select()
                        .single();
                    
                    this.testChapter = data;
                    return !error && data;
                }
            },
            {
                name: '分配章节给用户',
                test: async () => {
                    if (!this.testChapter) return false;
                    
                    const assignmentData = {
                        chapter_id: this.testChapter.id,
                        user_id: this.currentUser.id,
                        role: 'author',
                        assigned_by: this.currentUser.id,
                        status: 'assigned'
                    };
                    
                    const { data, error } = await supabaseManager.supabase
                        .from('chapter_assignments')
                        .insert(assignmentData)
                        .select()
                        .single();
                    
                    return !error && data;
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, '章节分配');
            } catch (error) {
                this.addTestResult(test.name, false, '章节分配', error.message);
            }
        }
    }

    // 测试协作编辑
    async testCollaborativeEditing() {
        console.log('✏️ 测试协作编辑功能...');
        
        const tests = [
            {
                name: '章节内容编辑',
                test: async () => {
                    if (!this.testChapter) return false;
                    
                    const updatedContent = {
                        ops: [
                            { insert: '更新的测试内容\n' },
                            { insert: '这是协作编辑的测试\n' }
                        ]
                    };
                    
                    const { error } = await supabaseManager.supabase
                        .from('chapters')
                        .update({ 
                            content: updatedContent,
                            updated_at: new Date().toISOString()
                        })
                        .eq('id', this.testChapter.id);
                    
                    return !error;
                }
            },
            {
                name: '版本历史记录',
                test: async () => {
                    if (!this.testChapter) return false;
                    
                    const versionData = {
                        chapter_id: this.testChapter.id,
                        version_number: 2,
                        title: this.testChapter.title,
                        content: { ops: [{ insert: '版本2的内容\n' }] },
                        changes_description: '测试版本更新',
                        created_by: this.currentUser.id
                    };
                    
                    const { data, error } = await supabaseManager.supabase
                        .from('chapter_versions')
                        .insert(versionData)
                        .select()
                        .single();
                    
                    return !error && data;
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, '协作编辑');
            } catch (error) {
                this.addTestResult(test.name, false, '协作编辑', error.message);
            }
        }
    }

    // 测试审核流程
    async testReviewProcess() {
        console.log('🔍 测试审核流程功能...');
        
        const tests = [
            {
                name: '创建审核任务',
                test: async () => {
                    if (!this.testChapter) return false;
                    
                    const reviewData = {
                        chapter_id: this.testChapter.id,
                        reviewer_id: this.currentUser.id,
                        status: 'pending',
                        created_by: this.currentUser.id
                    };
                    
                    const { data, error } = await supabaseManager.supabase
                        .from('review_processes')
                        .insert(reviewData)
                        .select()
                        .single();
                    
                    return !error && data;
                }
            },
            {
                name: '添加评论',
                test: async () => {
                    if (!this.testChapter) return false;
                    
                    const commentData = {
                        chapter_id: this.testChapter.id,
                        user_id: this.currentUser.id,
                        content: '这是一个测试评论',
                        type: 'general',
                        status: 'active'
                    };
                    
                    const { data, error } = await supabaseManager.supabase
                        .from('comments')
                        .insert(commentData)
                        .select()
                        .single();
                    
                    return !error && data;
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, '审核流程');
            } catch (error) {
                this.addTestResult(test.name, false, '审核流程', error.message);
            }
        }
    }

    // 添加测试结果
    addTestResult(testName, passed, category, error = null) {
        this.testResults.push({
            name: testName,
            category: category,
            passed: passed,
            error: error,
            timestamp: new Date().toISOString()
        });
        
        const status = passed ? '✅' : '❌';
        const errorMsg = error ? ` (${error})` : '';
        console.log(`${status} ${testName}${errorMsg}`);
    }

    // 生成测试报告
    generateTestReport() {
        console.log('\n📊 测试报告生成中...');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const successRate = ((passedTests / totalTests) * 100).toFixed(2);
        
        console.log(`\n🎯 测试总结:`);
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests}`);
        console.log(`失败: ${failedTests}`);
        console.log(`成功率: ${successRate}%`);
        
        // 按类别分组显示结果
        const categories = [...new Set(this.testResults.map(r => r.category))];
        
        console.log(`\n📋 详细结果:`);
        categories.forEach(category => {
            const categoryTests = this.testResults.filter(r => r.category === category);
            const categoryPassed = categoryTests.filter(r => r.passed).length;
            
            console.log(`\n${category}:`);
            categoryTests.forEach(test => {
                const status = test.passed ? '✅' : '❌';
                const error = test.error ? ` - ${test.error}` : '';
                console.log(`  ${status} ${test.name}${error}`);
            });
            console.log(`  通过率: ${((categoryPassed / categoryTests.length) * 100).toFixed(2)}%`);
        });
        
        // 保存测试报告到本地存储
        localStorage.setItem('collaborationTestReport', JSON.stringify({
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                successRate: successRate
            },
            results: this.testResults,
            timestamp: new Date().toISOString()
        }));
        
        console.log(`\n💾 测试报告已保存到本地存储`);
    }

    // 清理测试数据
    async cleanup() {
        console.log('🧹 清理测试数据...');
        
        try {
            // 删除测试项目（会级联删除相关数据）
            if (this.testProject) {
                await supabaseManager.supabase
                    .from('projects')
                    .delete()
                    .eq('id', this.testProject.id);
            }
            
            // 删除测试用户（需要管理员权限）
            // 这里只是示例，实际可能需要其他方式清理
            
            console.log('✅ 测试数据清理完成');
        } catch (error) {
            console.error('❌ 清理测试数据失败:', error);
        }
    }
}

// 导出测试类
window.CollaborationTester = CollaborationTester;

// 提供快速测试函数
window.runCollaborationTests = async function() {
    const tester = new CollaborationTester();
    await tester.runAllTests();
    return tester;
};

console.log('🔧 协作功能测试脚本已加载');
console.log('💡 使用 runCollaborationTests() 开始测试');
