<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节保存加载诊断工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .debug-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .debug-button:hover {
            background: #2980b9;
        }
        .debug-button.success {
            background: #27ae60;
        }
        .debug-button.error {
            background: #e74c3c;
        }
        .debug-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        .status-info { background: #3498db; }
        .debug-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .debug-item:last-child {
            border-bottom: none;
        }
        .iframe-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>章节保存加载诊断工具</h1>
        <p>用于诊断章节内容保存和加载过程中的问题</p>

        <div class="section">
            <h3>🔧 诊断控制</h3>
            <button class="debug-button" onclick="loadMainApp()">加载主应用</button>
            <button class="debug-button" onclick="runFullDiagnosis()">运行完整诊断</button>
            <button class="debug-button" onclick="testSaveProcess()">测试保存过程</button>
            <button class="debug-button" onclick="testLoadProcess()">测试加载过程</button>
            <button class="debug-button" onclick="clearLog()">清除日志</button>
        </div>

        <div class="section">
            <h3>📱 主应用</h3>
            <div class="iframe-container">
                <iframe id="main-app" src="about:blank"></iframe>
            </div>
        </div>

        <div class="section">
            <h3>🔍 诊断结果</h3>
            <div id="diagnosis-results">
                <div class="debug-item">
                    <span class="status-indicator status-info"></span>
                    <span>等待诊断开始...</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>📋 详细日志</h3>
            <div id="debug-log" class="debug-log">
                诊断日志将在这里显示...
            </div>
        </div>
    </div>

    <script>
        let debugLog = [];
        let diagnosisResults = [];
        let mainAppFrame = null;

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLog.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }

        // 更新日志显示
        function updateLogDisplay() {
            const logElement = document.getElementById('debug-log');
            logElement.textContent = debugLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 添加诊断结果
        function addDiagnosisResult(name, status, message) {
            diagnosisResults.push({ name, status, message });
            updateDiagnosisDisplay();
        }

        // 更新诊断结果显示
        function updateDiagnosisDisplay() {
            const resultsElement = document.getElementById('diagnosis-results');
            if (diagnosisResults.length === 0) {
                resultsElement.innerHTML = '<div class="debug-item"><span class="status-indicator status-info"></span><span>等待诊断开始...</span></div>';
                return;
            }

            resultsElement.innerHTML = diagnosisResults.map(result => {
                const statusClass = result.status === 'success' ? 'status-success' : 
                                  result.status === 'error' ? 'status-error' : 
                                  result.status === 'warning' ? 'status-warning' : 'status-info';
                return `<div class="debug-item">
                    <span class="status-indicator ${statusClass}"></span>
                    <span>${result.name}: ${result.message}</span>
                </div>`;
            }).join('');
        }

        // 清除日志
        function clearLog() {
            debugLog = [];
            diagnosisResults = [];
            updateLogDisplay();
            updateDiagnosisDisplay();
            log('日志已清除');
        }

        // 加载主应用
        function loadMainApp() {
            log('正在加载主应用...');
            mainAppFrame = document.getElementById('main-app');
            mainAppFrame.src = './index.html';
            
            mainAppFrame.onload = function() {
                log('主应用加载完成');
                addDiagnosisResult('应用加载', 'success', '主应用已成功加载');
            };
            
            mainAppFrame.onerror = function() {
                log('主应用加载失败', 'error');
                addDiagnosisResult('应用加载', 'error', '主应用加载失败');
            };
        }

        // 获取应用窗口
        function getAppWindow() {
            if (!mainAppFrame || !mainAppFrame.contentWindow) {
                throw new Error('主应用未加载或无法访问');
            }
            return mainAppFrame.contentWindow;
        }

        // 运行完整诊断
        async function runFullDiagnosis() {
            log('开始运行完整诊断');
            clearLog();
            
            if (!mainAppFrame || !mainAppFrame.contentWindow) {
                addDiagnosisResult('前置检查', 'error', '请先加载主应用');
                log('诊断失败: 主应用未加载', 'error');
                return;
            }
            
            await testEnvironment();
            await testSaveProcess();
            await testLoadProcess();
            await testDataIntegrity();
            
            log('完整诊断完成');
        }

        // 测试环境
        async function testEnvironment() {
            log('开始测试环境...');
            
            try {
                const appWindow = getAppWindow();
                
                // 检查关键对象
                addDiagnosisResult('Quill编辑器', 'running', '检查编辑器状态...');
                if (appWindow.quillEditor && typeof appWindow.quillEditor.getContents === 'function') {
                    addDiagnosisResult('Quill编辑器', 'success', '编辑器正常');
                    log('Quill编辑器检查通过');
                } else {
                    addDiagnosisResult('Quill编辑器', 'error', '编辑器未初始化');
                    log('Quill编辑器检查失败', 'error');
                }

                // 检查协作管理器
                addDiagnosisResult('协作管理器', 'running', '检查协作管理器...');
                if (appWindow.collaborationManager && appWindow.collaborationManager.currentProjectId) {
                    addDiagnosisResult('协作管理器', 'success', `项目ID: ${appWindow.collaborationManager.currentProjectId}`);
                    log(`协作管理器正常，项目ID: ${appWindow.collaborationManager.currentProjectId}`);
                } else {
                    addDiagnosisResult('协作管理器', 'warning', '项目ID未设置');
                    log('协作管理器项目ID未设置', 'warning');
                }

                // 检查Supabase连接
                addDiagnosisResult('Supabase连接', 'running', '检查数据库连接...');
                if (appWindow.supabaseManager && appWindow.supabaseManager.supabase) {
                    const user = await appWindow.supabaseManager.getCurrentUser();
                    if (user) {
                        addDiagnosisResult('Supabase连接', 'success', `用户已登录: ${user.email}`);
                        log(`Supabase连接正常，用户: ${user.email}`);
                    } else {
                        addDiagnosisResult('Supabase连接', 'warning', '用户未登录');
                        log('Supabase连接正常但用户未登录', 'warning');
                    }
                } else {
                    addDiagnosisResult('Supabase连接', 'error', '数据库连接失败');
                    log('Supabase连接失败', 'error');
                }

            } catch (error) {
                addDiagnosisResult('环境测试', 'error', `测试失败: ${error.message}`);
                log(`环境测试失败: ${error.message}`, 'error');
            }
        }

        // 测试保存过程
        async function testSaveProcess() {
            log('开始测试保存过程...');
            
            try {
                const appWindow = getAppWindow();
                
                // 检查当前章节
                addDiagnosisResult('当前章节', 'running', '检查当前章节状态...');
                if (appWindow.currentChapter) {
                    addDiagnosisResult('当前章节', 'success', `章节: ${appWindow.currentChapter.title || '未命名'}`);
                    log(`当前章节: ${JSON.stringify(appWindow.currentChapter, null, 2)}`);
                } else {
                    addDiagnosisResult('当前章节', 'warning', '没有选中的章节');
                    log('没有选中的章节', 'warning');
                }

                // 检查编辑器内容
                addDiagnosisResult('编辑器内容', 'running', '检查编辑器内容...');
                if (appWindow.quillEditor) {
                    const deltaContent = appWindow.quillEditor.getContents();
                    const htmlContent = appWindow.quillEditor.root.innerHTML;
                    const textContent = appWindow.quillEditor.getText();
                    
                    log(`Delta内容: ${JSON.stringify(deltaContent, null, 2)}`);
                    log(`HTML内容: ${htmlContent}`);
                    log(`文本内容: ${textContent}`);
                    
                    if (deltaContent && deltaContent.ops && deltaContent.ops.length > 0) {
                        addDiagnosisResult('编辑器内容', 'success', `内容长度: ${textContent.length} 字符`);
                    } else {
                        addDiagnosisResult('编辑器内容', 'warning', '编辑器内容为空');
                    }
                } else {
                    addDiagnosisResult('编辑器内容', 'error', '编辑器未初始化');
                }

                // 测试保存函数
                addDiagnosisResult('保存函数测试', 'running', '测试保存函数...');
                if (typeof appWindow.saveChapterToServer === 'function') {
                    // 检查函数代码
                    const saveCode = appWindow.saveChapterToServer.toString();
                    log(`保存函数代码: ${saveCode.substring(0, 500)}...`);
                    
                    if (saveCode.includes('deltaContent') && saveCode.includes('content: deltaContent')) {
                        addDiagnosisResult('保存函数测试', 'success', '保存函数使用Delta格式');
                    } else {
                        addDiagnosisResult('保存函数测试', 'error', '保存函数未使用Delta格式');
                    }
                } else {
                    addDiagnosisResult('保存函数测试', 'error', '保存函数不存在');
                }

            } catch (error) {
                addDiagnosisResult('保存过程测试', 'error', `测试失败: ${error.message}`);
                log(`保存过程测试失败: ${error.message}`, 'error');
            }
        }

        // 测试加载过程
        async function testLoadProcess() {
            log('开始测试加载过程...');
            
            try {
                const appWindow = getAppWindow();
                
                // 检查加载函数
                addDiagnosisResult('加载函数测试', 'running', '检查加载函数...');
                if (typeof appWindow.loadChapterFromServer === 'function') {
                    const loadCode = appWindow.loadChapterFromServer.toString();
                    log(`加载函数代码: ${loadCode.substring(0, 500)}...`);
                    
                    if (loadCode.includes('chapter?.content')) {
                        addDiagnosisResult('加载函数测试', 'success', '加载函数返回content字段');
                    } else {
                        addDiagnosisResult('加载函数测试', 'warning', '加载函数可能有问题');
                    }
                } else {
                    addDiagnosisResult('加载函数测试', 'error', '加载函数不存在');
                }

                // 检查内容设置函数
                addDiagnosisResult('内容设置测试', 'running', '检查内容设置逻辑...');
                if (typeof appWindow.loadChapterContent === 'function') {
                    const contentCode = appWindow.loadChapterContent.toString();
                    log(`内容设置函数代码: ${contentCode.substring(0, 500)}...`);
                    
                    if (contentCode.includes('setContents') && contentCode.includes('deltaContent')) {
                        addDiagnosisResult('内容设置测试', 'success', '内容设置使用Delta格式');
                    } else {
                        addDiagnosisResult('内容设置测试', 'error', '内容设置未使用Delta格式');
                    }
                } else {
                    addDiagnosisResult('内容设置测试', 'error', '内容设置函数不存在');
                }

            } catch (error) {
                addDiagnosisResult('加载过程测试', 'error', `测试失败: ${error.message}`);
                log(`加载过程测试失败: ${error.message}`, 'error');
            }
        }

        // 测试数据完整性
        async function testDataIntegrity() {
            log('开始测试数据完整性...');

            try {
                const appWindow = getAppWindow();

                // 测试项目ID获取
                addDiagnosisResult('项目ID测试', 'running', '测试项目ID获取...');
                try {
                    const projectId = await appWindow.ensureProjectId();
                    if (projectId) {
                        addDiagnosisResult('项目ID测试', 'success', `项目ID: ${projectId}`);
                        log(`项目ID获取成功: ${projectId}`);
                    } else {
                        addDiagnosisResult('项目ID测试', 'error', '无法获取项目ID');
                        log('项目ID获取失败', 'error');
                    }
                } catch (projectError) {
                    addDiagnosisResult('项目ID测试', 'error', `项目ID获取异常: ${projectError.message}`);
                    log(`项目ID获取异常: ${projectError.message}`, 'error');
                }

                // 检查数据库查询
                addDiagnosisResult('数据库查询', 'running', '测试数据库查询...');
                if (appWindow.supabaseManager) {
                    try {
                        const projectId = await appWindow.ensureProjectId();
                        const { data: chapters, error } = await appWindow.supabaseManager.supabase
                            .from('chapters')
                            .select('id, title, content, word_count, project_id')
                            .limit(10);

                        if (error) {
                            addDiagnosisResult('数据库查询', 'error', `查询失败: ${error.message}`);
                            log(`数据库查询失败: ${error.message}`, 'error');
                        } else {
                            addDiagnosisResult('数据库查询', 'success', `找到 ${chapters.length} 个章节`);
                            log(`数据库查询成功，章节数据: ${JSON.stringify(chapters, null, 2)}`);

                            // 检查内容格式
                            if (chapters.length > 0) {
                                const chaptersWithContent = chapters.filter(ch => ch.content);
                                if (chaptersWithContent.length > 0) {
                                    const firstChapter = chaptersWithContent[0];
                                    if (typeof firstChapter.content === 'object' && firstChapter.content.ops) {
                                        addDiagnosisResult('内容格式', 'success', '数据库中存储的是Delta格式');
                                    } else {
                                        addDiagnosisResult('内容格式', 'warning', '数据库中可能存储的是其他格式');
                                        log(`内容格式: ${typeof firstChapter.content}, 内容: ${JSON.stringify(firstChapter.content)}`);
                                    }
                                } else {
                                    addDiagnosisResult('内容格式', 'info', '所有章节内容为空');
                                }
                            }
                        }
                    } catch (dbError) {
                        addDiagnosisResult('数据库查询', 'error', `查询异常: ${dbError.message}`);
                        log(`数据库查询异常: ${dbError.message}`, 'error');
                    }
                } else {
                    addDiagnosisResult('数据库查询', 'warning', '数据库连接缺失');
                }

                // 测试保存和加载流程
                addDiagnosisResult('保存加载流程', 'running', '测试完整的保存加载流程...');
                try {
                    if (appWindow.currentChapter && appWindow.quillEditor) {
                        // 模拟保存
                        await appWindow.saveCurrentChapterContent();

                        // 模拟加载
                        const chapterId = appWindow.currentChapter.chapterId;
                        if (chapterId) {
                            const loadedContent = await appWindow.loadChapterFromServer(chapterId);
                            if (loadedContent) {
                                addDiagnosisResult('保存加载流程', 'success', '保存和加载流程正常');
                                log(`加载的内容: ${JSON.stringify(loadedContent)}`);
                            } else {
                                addDiagnosisResult('保存加载流程', 'warning', '加载内容为空');
                            }
                        } else {
                            addDiagnosisResult('保存加载流程', 'warning', '没有章节ID');
                        }
                    } else {
                        addDiagnosisResult('保存加载流程', 'warning', '没有当前章节或编辑器');
                    }
                } catch (flowError) {
                    addDiagnosisResult('保存加载流程', 'error', `流程测试失败: ${flowError.message}`);
                    log(`保存加载流程测试失败: ${flowError.message}`, 'error');
                }

            } catch (error) {
                addDiagnosisResult('数据完整性测试', 'error', `测试失败: ${error.message}`);
                log(`数据完整性测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('章节诊断工具已加载');
            log('请先点击"加载主应用"，然后运行诊断');
        });
    </script>
</body>
</html>
