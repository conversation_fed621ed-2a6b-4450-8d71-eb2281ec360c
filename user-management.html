<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 《大模型技术与油气应用概论》协作系统</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .user-management-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .page-title {
            font-size: 2rem;
            color: #1f2937;
            margin: 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4338ca;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .management-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .tab-button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: #4f46e5;
            border-bottom-color: #4f46e5;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .user-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .user-card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .user-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #4f46e5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .user-info h3 {
            margin: 0;
            color: #1f2937;
            font-size: 1.1rem;
        }
        
        .user-info p {
            margin: 5px 0 0 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .user-details {
            margin-bottom: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .detail-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .detail-value {
            color: #1f2937;
        }
        
        .role-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .role-owner {
            background: #fef3c7;
            color: #92400e;
        }
        
        .role-admin {
            background: #ddd6fe;
            color: #5b21b6;
        }
        
        .role-editor {
            background: #d1fae5;
            color: #065f46;
        }
        
        .role-author {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .role-reviewer {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .user-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        
        .btn-edit {
            background: #f59e0b;
            color: white;
        }
        
        .btn-edit:hover {
            background: #d97706;
        }
        
        .btn-delete {
            background: #ef4444;
            color: white;
        }
        
        .btn-delete:hover {
            background: #dc2626;
        }
        
        .search-filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .filter-select {
            padding: 10px 15px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: white;
            min-width: 150px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 30px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 1.5rem;
            color: #1f2937;
            margin: 0;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6b7280;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            color: #374151;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: white;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }

        .form-help {
            display: block;
            margin-top: 5px;
            font-size: 0.8rem;
            color: #6b7280;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            font-size: 0.9rem;
            color: #374151;
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
        }

        .btn-outline {
            background: transparent;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            color: #374151;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: #10b981;
        }
        
        .notification.error {
            background: #ef4444;
        }
    </style>
</head>
<body>
    <div class="user-management-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-users"></i> 用户管理
            </h1>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="fas fa-user-plus"></i> 创建用户
                </button>
                <button class="btn btn-secondary" onclick="showInviteModal()">
                    <i class="fas fa-envelope"></i> 邀请用户
                </button>
                <a href="project-management.html" class="btn btn-outline">
                    <i class="fas fa-arrow-left"></i> 返回项目
                </a>
            </div>
        </div>
        
        <div class="management-tabs">
            <button class="tab-button active" onclick="switchTab('users')">用户列表</button>
            <button class="tab-button" onclick="switchTab('invitations')">邀请管理</button>
            <button class="tab-button" onclick="switchTab('roles')">角色权限</button>
            <button class="tab-button" onclick="switchTab('statistics')">统计分析</button>
        </div>
        
        <!-- 用户列表标签 -->
        <div id="users-tab" class="tab-content active">
            <div class="search-filter-bar">
                <input type="text" class="search-input" placeholder="搜索用户..." id="user-search">
                <select class="filter-select" id="role-filter">
                    <option value="">所有角色</option>
                    <option value="owner">项目所有者</option>
                    <option value="admin">管理员</option>
                    <option value="editor">编辑者</option>
                    <option value="author">作者</option>
                    <option value="reviewer">审阅者</option>
                </select>
                <select class="filter-select" id="status-filter">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                    <option value="pending">待确认</option>
                </select>
            </div>
            
            <div class="users-grid" id="users-grid">
                <!-- 用户卡片将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 邀请管理标签 -->
        <div id="invitations-tab" class="tab-content">
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-number" id="pending-invitations">0</div>
                    <div class="stat-label">待处理邀请</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="accepted-invitations">0</div>
                    <div class="stat-label">已接受邀请</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="expired-invitations">0</div>
                    <div class="stat-label">已过期邀请</div>
                </div>
            </div>
            
            <div id="invitations-list">
                <!-- 邀请列表将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 角色权限标签 -->
        <div id="roles-tab" class="tab-content">
            <div id="roles-matrix">
                <!-- 权限矩阵将在这里动态生成 -->
            </div>
        </div>
        
        <!-- 统计分析标签 -->
        <div id="statistics-tab" class="tab-content">
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-number" id="total-users">0</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-users">0</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="online-users">0</div>
                    <div class="stat-label">在线用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="new-users-month">0</div>
                    <div class="stat-label">本月新增</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建用户模态框 -->
    <div id="create-user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">创建用户</h2>
                <button class="close-btn" onclick="closeModal('create-user-modal')">&times;</button>
            </div>

            <form id="create-user-form" onsubmit="handleCreateUser(event)">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-input" name="username" required placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label class="form-label">全名</label>
                        <input type="text" class="form-input" name="full_name" required placeholder="请输入全名">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" class="form-input" name="email" required placeholder="请输入邮箱地址，如：<EMAIL>">
                    <small class="form-help">支持所有邮箱域名，如果 Supabase Auth 创建失败会自动使用备用方式</small>
                </div>

                <div class="form-group">
                    <label class="form-label">初始密码</label>
                    <input type="password" class="form-input" name="password" required placeholder="请输入初始密码（至少6位）" minlength="6">
                    <small class="form-help">用户首次登录后可以修改密码</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">机构</label>
                        <input type="text" class="form-input" name="institution" placeholder="请输入所属机构">
                    </div>
                    <div class="form-group">
                        <label class="form-label">部门</label>
                        <input type="text" class="form-input" name="department" placeholder="请输入所属部门">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">项目角色</label>
                    <select class="form-select" name="role" required>
                        <option value="">请选择角色</option>
                        <option value="admin">管理员</option>
                        <option value="editor">编辑者</option>
                        <option value="author">作者</option>
                        <option value="reviewer">审阅者</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">个人简介（可选）</label>
                    <textarea class="form-input" name="bio" rows="3" placeholder="请输入个人简介..."></textarea>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="send_welcome_email" checked>
                        <span class="checkmark"></span>
                        发送欢迎邮件
                    </label>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-user-plus"></i> 创建用户
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 邀请用户模态框 -->
    <div id="invite-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">邀请用户</h2>
                <button class="close-btn" onclick="closeModal('invite-modal')">&times;</button>
            </div>
            
            <form id="invite-form" onsubmit="handleInviteUser(event)">
                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" class="form-input" name="email" required placeholder="请输入邮箱地址">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">角色</label>
                        <select class="form-select" name="role" required>
                            <option value="">请选择角色</option>
                            <option value="admin">管理员</option>
                            <option value="editor">编辑者</option>
                            <option value="author">作者</option>
                            <option value="reviewer">审阅者</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">有效期（天）</label>
                        <select class="form-select" name="expires_days">
                            <option value="7">7天</option>
                            <option value="14" selected>14天</option>
                            <option value="30">30天</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">邀请消息（可选）</label>
                    <textarea class="form-input" name="message" rows="3" placeholder="请输入邀请消息..."></textarea>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-paper-plane"></i> 发送邀请
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="supabase-config.js"></script>
    <script src="user-management.js"></script>
</body>
</html>
