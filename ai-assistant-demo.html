<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手功能演示</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .demo-header h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        .demo-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8fafc;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .section-header h2 {
            margin: 0;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .section-content {
            padding: 2rem;
        }
        
        .editor-demo {
            position: relative;
            min-height: 400px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
        }
        
        .highlight-box {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 80px;
            height: 80px;
            border: 3px solid #ef4444;
            border-radius: 50%;
            background: rgba(239, 68, 68, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
            z-index: 1001;
        }
        
        .highlight-text {
            color: #ef4444;
            font-weight: bold;
            font-size: 0.8rem;
            text-align: center;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .feature-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .feature-icon.polish { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .feature-icon.translate { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
        .feature-icon.explain { background: linear-gradient(135deg, #10b981, #047857); }
        .feature-icon.rewrite { background: linear-gradient(135deg, #ec4899, #be185d); }
        
        .feature-card h3 {
            margin: 0 0 0.5rem 0;
            color: #1f2937;
        }
        
        .feature-card p {
            margin: 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .success-banner {
            background: linear-gradient(135deg, #10b981, #047857);
            color: white;
            padding: 1rem 2rem;
            text-align: center;
            font-weight: 500;
            margin-bottom: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1><i class="fas fa-magic"></i> AI助手功能演示</h1>
        <p>在章节编写的主体内容右上角添加的智能AI助手</p>
    </div>

    <div class="demo-container">
        <div class="success-banner">
            <i class="fas fa-check-circle"></i>
            AI助手功能已成功实现并集成到书籍编撰系统中！
        </div>

        <div class="demo-section">
            <div class="section-header">
                <h2><i class="fas fa-edit"></i> 章节编写界面</h2>
            </div>
            <div class="section-content">
                <p style="margin-bottom: 1.5rem; color: #6b7280;">
                    以下是章节编写界面的演示，AI助手悬浮按钮位于编辑器右上角（红色圆圈标注位置）：
                </p>
                
                <div class="editor-demo">
                    <div class="highlight-box">
                        <div class="highlight-text">AI助手<br>按钮</div>
                    </div>
                    
                    <div class="editor-wrapper">
                        <div id="chapter-content-editor" class="editor-container">
                            <!-- Quill编辑器将在这里初始化 -->
                        </div>
                        
                        <!-- AI助手悬浮按钮 -->
                        <div id="ai-assistant-fab" class="ai-fab">
                            <button class="fab-main-btn" title="AI助手">
                                <i class="fas fa-magic"></i>
                            </button>
                            <div class="fab-menu" id="ai-fab-menu">
                                <button class="fab-menu-item" data-action="polish" title="内容润色">
                                    <i class="fas fa-magic"></i>
                                    <span>润色</span>
                                </button>
                                <button class="fab-menu-item" data-action="translate" title="翻译">
                                    <i class="fas fa-language"></i>
                                    <span>翻译</span>
                                </button>
                                <button class="fab-menu-item" data-action="explain" title="解读">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>解读</span>
                                </button>
                                <button class="fab-menu-item" data-action="rewrite" title="重写">
                                    <i class="fas fa-edit"></i>
                                    <span>重写</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="section-header">
                <h2><i class="fas fa-star"></i> AI助手功能特性</h2>
            </div>
            <div class="section-content">
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon polish">
                            <i class="fas fa-sparkles"></i>
                        </div>
                        <h3>内容润色</h3>
                        <p>优化文本表达，提升可读性和流畅性，让内容更加专业和易懂</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon translate">
                            <i class="fas fa-language"></i>
                        </div>
                        <h3>内容翻译</h3>
                        <p>将内容翻译为指定语言，保持专业术语的准确性和语言的自然性</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon explain">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <h3>内容解读</h3>
                        <p>深入解析内容的含义和背景，提供详细的技术原理和应用场景分析</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon rewrite">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h3>内容重写</h3>
                        <p>用不同风格重新组织和表达内容，适配不同的读者群体和使用场景</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div id="modal-container" class="modal-container">
            <div class="modal-header">
                <h3 id="modal-title"></h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="modal-content" class="modal-content"></div>
            <div id="modal-buttons" class="modal-buttons"></div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="ai-service.js"></script>
    <script>
        // 全局变量
        let quillEditor = null;

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            initializeQuillEditor();
            setupAIAssistantListeners();
            
            // 显示成功提示
            setTimeout(() => {
                showNotification('AI助手功能演示已加载完成！请点击右上角的AI助手按钮体验功能。', 'success', 5000);
            }, 1000);
        });

        // 初始化Quill编辑器
        function initializeQuillEditor() {
            const editorContainer = document.getElementById('chapter-content-editor');
            
            quillEditor = new Quill(editorContainer, {
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline'],
                        ['blockquote', 'code-block'],
                        [{ 'header': 1 }, { 'header': 2 }],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['clean']
                    ]
                },
                theme: 'snow',
                placeholder: '在此输入章节内容，然后点击右上角的AI助手按钮体验智能功能...'
            });

            // 添加示例内容
            quillEditor.setText('大模型（Large Language Model, LLM）是指参数量达到数十亿甚至数千亿级别的深度学习模型，通常基于Transformer架构构建。这些模型通过在大规模文本数据上进行预训练，能够理解和生成人类语言，展现出强大的语言理解和生成能力。');
        }

        // ==================== 通知系统 ====================
        function showNotification(message, type = 'info', duration = 3000) {
            const container = document.getElementById('notification-container');
            if (!container) return;

            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }

        // ==================== 模态框系统 ====================
        function showModal(options) {
            const overlay = document.getElementById('modal-overlay');
            const container = document.getElementById('modal-container');
            const title = document.getElementById('modal-title');
            const content = document.getElementById('modal-content');
            const buttons = document.getElementById('modal-buttons');

            if (!overlay || !container || !title || !content || !buttons) return;

            title.textContent = options.title || '';
            content.innerHTML = options.content || '';

            // 设置自定义类名
            if (options.className) {
                container.className = `modal-container ${options.className}`;
            } else {
                container.className = 'modal-container';
            }

            // 创建按钮
            buttons.innerHTML = '';
            if (options.buttons) {
                options.buttons.forEach(btn => {
                    const button = document.createElement('button');
                    button.textContent = btn.text;
                    button.className = `btn ${btn.className || ''}`;
                    if (btn.id) button.id = btn.id;
                    if (btn.style) button.style.cssText = btn.style;
                    if (btn.onclick) button.onclick = btn.onclick;
                    buttons.appendChild(button);
                });
            }

            overlay.style.display = 'flex';
        }

        function closeModal() {
            const overlay = document.getElementById('modal-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // ==================== AI助手功能 ====================

        // 设置AI助手事件监听器
        function setupAIAssistantListeners() {
            // 监听AI助手菜单项点击
            document.addEventListener('click', function(e) {
                if (e.target.closest('.fab-menu-item')) {
                    const action = e.target.closest('.fab-menu-item').getAttribute('data-action');
                    handleAIAction(action);
                }
            });
        }

        // 处理AI操作
        async function handleAIAction(action) {
            // 检查编辑器是否存在
            if (!quillEditor) {
                showNotification('编辑器未初始化', 'warning');
                return;
            }

            // 获取选中的文本
            const selection = quillEditor.getSelection();
            let selectedText = '';

            if (selection && selection.length > 0) {
                selectedText = quillEditor.getText(selection.index, selection.length);
            } else {
                // 如果没有选中文本，获取全部内容
                selectedText = quillEditor.getText();
            }

            if (!selectedText.trim()) {
                showNotification('请先输入或选择要处理的文本内容', 'warning');
                return;
            }

            // 显示AI交互对话框
            showAIDialog(action, selectedText);
        }

        // 显示AI交互对话框
        function showAIDialog(action, selectedText) {
            const actionConfig = {
                polish: {
                    title: '内容润色',
                    description: '优化文本表达，提升可读性和流畅性',
                    icon: 'fas fa-magic',
                    placeholder: '请描述您的润色要求（可选）...'
                },
                translate: {
                    title: '内容翻译',
                    description: '将内容翻译为指定语言',
                    icon: 'fas fa-language',
                    placeholder: '请指定目标语言和特殊要求...'
                },
                explain: {
                    title: '内容解读',
                    description: '深入解析内容的含义和背景',
                    icon: 'fas fa-lightbulb',
                    placeholder: '请描述您希望了解的方面（可选）...'
                },
                rewrite: {
                    title: '内容重写',
                    description: '用不同风格重新组织和表达内容',
                    icon: 'fas fa-edit',
                    placeholder: '请指定重写风格和要求...'
                }
            };

            const config = actionConfig[action];
            if (!config) return;

            const dialogHTML = `
                <div class="ai-dialog-header">
                    <div class="ai-dialog-icon">
                        <i class="${config.icon}"></i>
                    </div>
                    <div class="ai-dialog-title">
                        <h3>${config.title}</h3>
                        <p>${config.description}</p>
                    </div>
                </div>
                <div class="ai-dialog-content">
                    <div class="ai-selected-text">
                        <h4>选中的内容：</h4>
                        <div class="ai-selected-text-content">${selectedText}</div>
                    </div>

                    <div class="ai-input-section">
                        <textarea class="ai-prompt-input" placeholder="${config.placeholder}"></textarea>

                        <div class="ai-file-upload">
                            <label>上传相关文件（可选）：</label>
                            <input type="file" class="ai-file-input" multiple accept="image/*,.pdf,.doc,.docx,.txt">
                            <div class="ai-file-preview"></div>
                        </div>
                    </div>

                    <div class="ai-response-section" style="display: none;">
                        <h4>AI处理结果：</h4>
                        <div class="ai-response-content"></div>
                    </div>
                </div>
            `;

            showModal({
                title: `AI助手 - ${config.title}`,
                content: dialogHTML,
                className: 'ai-dialog',
                buttons: [
                    {
                        text: '取消',
                        className: 'btn-secondary',
                        onclick: () => closeModal()
                    },
                    {
                        text: '开始处理',
                        className: 'btn-primary',
                        onclick: () => processAIRequest(action, selectedText)
                    },
                    {
                        text: '应用结果',
                        className: 'btn-success',
                        id: 'apply-ai-result',
                        style: 'display: none;',
                        onclick: () => applyAIResult()
                    }
                ]
            });

            // 设置文件上传监听器
            setupFileUploadListener();
        }
    </script>
</body>
</html>
